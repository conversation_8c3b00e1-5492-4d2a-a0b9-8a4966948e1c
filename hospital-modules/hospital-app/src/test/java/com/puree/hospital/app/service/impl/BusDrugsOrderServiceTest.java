package com.puree.hospital.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.puree.hospital.app.AbstractHospitalAppTest;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.domain.vo.OrderDetailVO;
import com.puree.hospital.app.helper.FreightCalculator;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.api.model.dto.BusFreightResultDTO;
import com.puree.hospital.app.api.model.dto.BusFreightQueryDTO;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 药品订单单元测试
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/13 16:03
 */
public class BusDrugsOrderServiceTest extends AbstractHospitalAppTest {

    @Resource
    private IBusDrugsOrderService busDrugsOrderService;

    @Resource
    private FreightCalculator freightCalculator;

    @Test
    public void testQueryOrderDrugsAndGoodsDetail(){
        DrugsOrderDto dto = new DrugsOrderDto();
        dto.setOrderNo("TO20240913153919186");
        dto.setHospitalId(100L);
        List<OrderDetailVO> detailList = busDrugsOrderService.queryOrderDrugsAndGoodsDetail(dto);
        System.out.println(JSON.toJSONString(detailList));
    }
    @Test
    public void testQueryFreight() {
        BusFreightQueryDTO query = new BusFreightQueryDTO();
        query.setHospitalId(-255L);
        query.setProvinceName("河北省");
        query.setCityName("石家庄市");
        query.setAreaName("长安区");
        query.setDeliveryType("1");
        query.setAmount(new BigDecimal("199"));
        BusFreightResultDTO freightDTO = freightCalculator.calculate(query);
        System.out.println("运费："+freightDTO.getFreight());
    }
}

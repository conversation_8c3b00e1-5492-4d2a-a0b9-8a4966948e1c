package com.puree.hospital.app.his.handler;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.dto.BusPatientMedicalRecordDTO;
import com.puree.hospital.app.domain.vo.BusPatientMedicalRecordListVO;
import com.puree.hospital.app.his.domain.HisQueryContext;
import com.puree.hospital.app.his.domain.vo.BusPrescriptionInfoVO;
import com.puree.hospital.app.his.domain.vo.HisPrescriptionInfo;
import com.puree.hospital.app.his.domain.vo.HisRegisteredInfo;
import com.puree.hospital.app.his.helper.DrHisHelper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/30 12:25
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class DrPrescriptionItemHandlerTest {


    @Spy
    @InjectMocks
    DrPrescriptionItemHandler drPrescriptionItemHandler;

    @Mock
    DrHisHelper drHisHelper;

    @Mock
    BusHospitalMapper busHospitalMapper;

    @Mock
    BusPrescriptionMapper busPrescriptionMapper;



    @Test
    void getElectronicMedicalRecordItemList() {
        BusHospital busHospital = new BusHospital();
        busHospital.setHospitalName("众爱");
        Mockito.when(busHospitalMapper.selectById(any())).thenReturn(busHospital);

        String regJson = "[{\"RegFlow\":\"\",\"RegType\":\"普通\",\"DoctorID\":\"Y2983\",\"SeeTime\":\"2025/3/25 9:00:25\",\"RegTime\":\"2025/3/25 8:16:18\",\"SeeNO\":\"-1\",\"PatientName\":\"陈捷\",\"PatientSexID\":\"1\",\"PatientAge\":\"46岁\",\"PatientID\":\"**********\",\"FeeType\":\"本院记账20%\",\"FeeItem\":null,\"DepartmentCode\":null,\"DepartmentName\":\"健康管理科(西区)\",\"DepartmentAddress\":\"\",\"DoctorName\":\"丁明\",\"TotalCost\":null,\"PayTypeName\":\"\",\"TranSerialNO\":\"5077180\",\"BookTime\":\"\",\"ValidFlag\":\"1\",\"WmDiagnose\":\"血脂异常,高血压病2级（中危）\",\"TcmDiagnose\":\"\",\"TcmSyndrome\":\"\"},{\"RegFlow\":\"\",\"RegType\":\"普通\",\"DoctorID\":\"Y0830\",\"SeeTime\":\"2025/3/30 22:15:36\",\"RegTime\":\"2025/3/30 22:13:39\",\"SeeNO\":\"-1\",\"PatientName\":\"陈捷\",\"PatientSexID\":\"1\",\"PatientAge\":\"46岁\",\"PatientID\":\"**********\",\"FeeType\":\"本院记账20%\",\"FeeItem\":null,\"DepartmentCode\":null,\"DepartmentName\":\"急诊外科(西区)\",\"DepartmentAddress\":\"\",\"DoctorName\":\"王靖宇\",\"TotalCost\":null,\"PayTypeName\":\"\",\"TranSerialNO\":\"5092626\",\"BookTime\":\"\",\"ValidFlag\":\"1\",\"WmDiagnose\":\"上呼吸道感染,非器质性睡眠障碍,咳嗽\",\"TcmDiagnose\":\"\",\"TcmSyndrome\":\"\"},{\"RegFlow\":\"\",\"RegType\":\"普通\",\"DoctorID\":\"Y2983\",\"SeeTime\":\"2025/4/17 15:11:47\",\"RegTime\":\"2025/4/1 15:28:38\",\"SeeNO\":\"-1\",\"PatientName\":\"陈捷\",\"PatientSexID\":\"1\",\"PatientAge\":\"46岁\",\"PatientID\":\"**********\",\"FeeType\":\"本院记账20%\",\"FeeItem\":null,\"DepartmentCode\":null,\"DepartmentName\":\"健康管理科(西区)\",\"DepartmentAddress\":\"\",\"DoctorName\":\"丁明\",\"TotalCost\":null,\"PayTypeName\":\"\",\"TranSerialNO\":\"5097374\",\"BookTime\":\"\",\"ValidFlag\":\"1\",\"WmDiagnose\":\"高血压,咳嗽\",\"TcmDiagnose\":\"\",\"TcmSyndrome\":\"\"}]";
        List<HisRegisteredInfo> hisRegisteredInfos = JSON.parseArray(regJson, HisRegisteredInfo.class);
        Mockito.when(drHisHelper.getRegisteredInfo(any())).thenReturn(hisRegisteredInfos);

        String pJson = "[{\"hisMedicalRecId\":\"5077180\",\"hisOrderRecId\":\"35097044\",\"medicalCatId\":\"9003\",\"medicalCatName\":\"本院记账20%\",\"orderTypeId\":\"P\",\"orderTypeName\":\"西药\",\"orderCatId\":\"\",\"orderCatName\":\"\",\"parentId\":\"2\",\"term\":\"\",\"startTime\":\"\",\"groupNum\":\"47922510\",\"groupSeqNum\":\"2\",\"reqFormId\":\"\",\"orderStatusId\":\"1\",\"orderStatusName\":\"正常\",\"orderExecStatusId\":\"\",\"orderExecStatusName\":\"\",\"orderExecTime\":\"2025/3/25 9:11:13\",\"feeStatusId\":\"1\",\"feeStatusName\":\"收费\",\"payStatusId\":\"1\",\"payStatusName\":\"收费\",\"code\":\"Y00000303928\",\"orderItemId\":\"Y00000303928\",\"orderItemSubCatId\":\"\",\"orderItemSubCatName\":\"\",\"orderItemGroupId\":\"\",\"orderItemName\":\"(中选)富马酸比索洛尔片\",\"orderItemGroupName\":\"\",\"price\":\"7.68\",\"amount\":\"0.60\",\"spec\":\"5mg*18片/盒\",\"dosage\":\"5\",\"dosageUnitId\":\"mg\",\"dosageUnitName\":\"mg\",\"frequencyId\":\"QD\",\"frequencyName\":\"每日1次\",\"frequencyInterval\":\"\",\"frequencyCount\":\"\",\"frequencyUnit\":\"\",\"frequencyTime\":\"\",\"firstDayTime\":\"\",\"lastDayTime\":\"\",\"count\":\"7\",\"quantity\":\"7\",\"quantityUnitId\":\"\",\"quantityUnitName\":\"片\",\"execMode\":\"\",\"checkCount\":\"\",\"execDoneCount\":\"\",\"execTotalCount\":\"\",\"diagnose\":\"R77.800x001,I10.x00x026\",\"diagnoseName\":\"血脂异常,高血压病2级（中危）\",\"isUrgent\":\"1\",\"advice\":\"\",\"isMakeup\":\"\",\"medicareCatId\":\"\",\"medicareCatName\":\"\",\"publiclyFundedCatId\":\"\",\"publiclyFundedCatName\":\"\",\"isMedicareLimit\":\"\",\"medicareLimitScope\":\"\",\"isMedicareReimburse\":\"\",\"isFixedOutPat\":\"\",\"isSpecialOutPat\":\"\",\"areaId\":\"\",\"areaName\":\"\",\"deptId\":\"8003\",\"deptName\":\"健康管理科(西区)\",\"checkId\":\"\",\"checkName\":\"\",\"checkTime\":\"\",\"inputDoctorId\":\"Y2983\",\"inputDoctorCode\":\"Y2983\",\"inputDoctorName\":\"丁明\",\"inputTime\":\"2025/3/25 9:11:13\",\"submitDoctorId\":\"Y2983\",\"submitDoctorCode\":\"Y2983\",\"submitDoctorName\":\"丁明\",\"submitDeptId\":\"8003\",\"submitDeptName\":\"健康管理科(西区)\",\"submitTime\":\"2025/3/25 9:11:13\",\"execDeptId\":\"3003\",\"execDeptName\":\"西药房(西区)\",\"usageCode\":\"1\",\"usageName\":\"口服\"},{\"hisMedicalRecId\":\"5077180\",\"hisOrderRecId\":\"35097044\",\"medicalCatId\":\"9003\",\"medicalCatName\":\"本院记账20%\",\"orderTypeId\":\"P\",\"orderTypeName\":\"西药\",\"orderCatId\":\"\",\"orderCatName\":\"\",\"parentId\":\"1\",\"term\":\"\",\"startTime\":\"\",\"groupNum\":\"47922509\",\"groupSeqNum\":\"1\",\"reqFormId\":\"\",\"orderStatusId\":\"1\",\"orderStatusName\":\"正常\",\"orderExecStatusId\":\"\",\"orderExecStatusName\":\"\",\"orderExecTime\":\"2025/3/25 9:11:13\",\"feeStatusId\":\"1\",\"feeStatusName\":\"收费\",\"payStatusId\":\"1\",\"payStatusName\":\"收费\",\"code\":\"Y00000303945\",\"orderItemId\":\"Y00000303945\",\"orderItemSubCatId\":\"\",\"orderItemSubCatName\":\"\",\"orderItemGroupId\":\"\",\"orderItemName\":\"(中选)瑞舒伐他汀钙片\",\"orderItemGroupName\":\"\",\"price\":\"5.49\",\"amount\":\"0.27\",\"spec\":\"10mg*28片/盒\",\"dosage\":\"10\",\"dosageUnitId\":\"mg\",\"dosageUnitName\":\"mg\",\"frequencyId\":\"QD\",\"frequencyName\":\"每日1次\",\"frequencyInterval\":\"\",\"frequencyCount\":\"\",\"frequencyUnit\":\"\",\"frequencyTime\":\"\",\"firstDayTime\":\"\",\"lastDayTime\":\"\",\"count\":\"7\",\"quantity\":\"7\",\"quantityUnitId\":\"\",\"quantityUnitName\":\"片\",\"execMode\":\"\",\"checkCount\":\"\",\"execDoneCount\":\"\",\"execTotalCount\":\"\",\"diagnose\":\"R77.800x001,I10.x00x026\",\"diagnoseName\":\"血脂异常,高血压病2级（中危）\",\"isUrgent\":\"1\",\"advice\":\"\",\"isMakeup\":\"\",\"medicareCatId\":\"\",\"medicareCatName\":\"\",\"publiclyFundedCatId\":\"\",\"publiclyFundedCatName\":\"\",\"isMedicareLimit\":\"\",\"medicareLimitScope\":\"\",\"isMedicareReimburse\":\"\",\"isFixedOutPat\":\"\",\"isSpecialOutPat\":\"\",\"areaId\":\"\",\"areaName\":\"\",\"deptId\":\"8003\",\"deptName\":\"健康管理科(西区)\",\"checkId\":\"\",\"checkName\":\"\",\"checkTime\":\"\",\"inputDoctorId\":\"Y2983\",\"inputDoctorCode\":\"Y2983\",\"inputDoctorName\":\"丁明\",\"inputTime\":\"2025/3/25 9:11:13\",\"submitDoctorId\":\"Y2983\",\"submitDoctorCode\":\"Y2983\",\"submitDoctorName\":\"丁明\",\"submitDeptId\":\"8003\",\"submitDeptName\":\"健康管理科(西区)\",\"submitTime\":\"2025/3/25 9:11:13\",\"execDeptId\":\"3003\",\"execDeptName\":\"西药房(西区)\",\"usageCode\":\"1\",\"usageName\":\"口服\"},{\"hisMedicalRecId\":\"5077180\",\"hisOrderRecId\":\"35096339\",\"medicalCatId\":\"9003\",\"medicalCatName\":\"本院记账20%\",\"orderTypeId\":\"P\",\"orderTypeName\":\"西药\",\"orderCatId\":\"\",\"orderCatName\":\"\",\"parentId\":\"1\",\"term\":\"\",\"startTime\":\"\",\"groupNum\":\"47922728\",\"groupSeqNum\":\"1\",\"reqFormId\":\"\",\"orderStatusId\":\"1\",\"orderStatusName\":\"正常\",\"orderExecStatusId\":\"\",\"orderExecStatusName\":\"\",\"orderExecTime\":\"2025/3/25 9:04:46\",\"feeStatusId\":\"1\",\"feeStatusName\":\"收费\",\"payStatusId\":\"1\",\"payStatusName\":\"收费\",\"code\":\"Y00000301719\",\"orderItemId\":\"Y00000301719\",\"orderItemSubCatId\":\"\",\"orderItemSubCatName\":\"\",\"orderItemGroupId\":\"\",\"orderItemName\":\"艾司唑仑片\",\"orderItemGroupName\":\"\",\"price\":\"7.78\",\"amount\":\"1.09\",\"spec\":\"1mg*20片/盒\",\"dosage\":\"2\",\"dosageUnitId\":\"mg\",\"dosageUnitName\":\"mg\",\"frequencyId\":\"QN\",\"frequencyName\":\"晚睡前\",\"frequencyInterval\":\"\",\"frequencyCount\":\"\",\"frequencyUnit\":\"\",\"frequencyTime\":\"\",\"firstDayTime\":\"\",\"lastDayTime\":\"\",\"count\":\"7\",\"quantity\":\"14\",\"quantityUnitId\":\"\",\"quantityUnitName\":\"片\",\"execMode\":\"\",\"checkCount\":\"\",\"execDoneCount\":\"\",\"execTotalCount\":\"\",\"diagnose\":\"R77.800x001,I10.x00x026\",\"diagnoseName\":\"血脂异常,高血压病2级（中危）\",\"isUrgent\":\"1\",\"advice\":\"\",\"isMakeup\":\"\",\"medicareCatId\":\"\",\"medicareCatName\":\"\",\"publiclyFundedCatId\":\"\",\"publiclyFundedCatName\":\"\",\"isMedicareLimit\":\"\",\"medicareLimitScope\":\"\",\"isMedicareReimburse\":\"\",\"isFixedOutPat\":\"\",\"isSpecialOutPat\":\"\",\"areaId\":\"\",\"areaName\":\"\",\"deptId\":\"8003\",\"deptName\":\"健康管理科(西区)\",\"checkId\":\"\",\"checkName\":\"\",\"checkTime\":\"\",\"inputDoctorId\":\"Y2983\",\"inputDoctorCode\":\"Y2983\",\"inputDoctorName\":\"丁明\",\"inputTime\":\"2025/3/25 9:04:46\",\"submitDoctorId\":\"Y2983\",\"submitDoctorCode\":\"Y2983\",\"submitDoctorName\":\"丁明\",\"submitDeptId\":\"8003\",\"submitDeptName\":\"健康管理科(西区)\",\"submitTime\":\"2025/3/25 9:04:46\",\"execDeptId\":\"3003\",\"execDeptName\":\"西药房(西区)\",\"usageCode\":\"1\",\"usageName\":\"口服\"},{\"hisMedicalRecId\":\"5077180\",\"hisOrderRecId\":\"35102874\",\"medicalCatId\":\"9003\",\"medicalCatName\":\"本院记账20%\",\"orderTypeId\":\"UT\",\"orderTypeName\":\"其它\",\"orderCatId\":\"\",\"orderCatName\":\"\",\"parentId\":\"1\",\"term\":\"\",\"startTime\":\"\",\"groupNum\":\"47926073\",\"groupSeqNum\":\"1\",\"reqFormId\":\"\",\"orderStatusId\":\"1\",\"orderStatusName\":\"正常\",\"orderExecStatusId\":\"\",\"orderExecStatusName\":\"\",\"orderExecTime\":\"2025/3/25 10:13:34\",\"feeStatusId\":\"0\",\"feeStatusName\":\"划价\",\"payStatusId\":\"0\",\"payStatusName\":\"划价\",\"code\":\"F00000254737\",\"orderItemId\":\"F00000254737\",\"orderItemSubCatId\":\"\",\"orderItemSubCatName\":\"\",\"orderItemGroupId\":\"\",\"orderItemName\":\"主任医师诊查费\",\"orderItemGroupName\":\"\",\"price\":\"35\",\"amount\":\"35\",\"spec\":\"\",\"dosage\":\"0\",\"dosageUnitId\":\"\",\"dosageUnitName\":\"\",\"frequencyId\":\"ONCE\",\"frequencyName\":\"一次\",\"frequencyInterval\":\"\",\"frequencyCount\":\"\",\"frequencyUnit\":\"\",\"frequencyTime\":\"\",\"firstDayTime\":\"\",\"lastDayTime\":\"\",\"count\":\"1\",\"quantity\":\"1\",\"quantityUnitId\":\"\",\"quantityUnitName\":\"次\",\"execMode\":\"\",\"checkCount\":\"\",\"execDoneCount\":\"\",\"execTotalCount\":\"\",\"diagnose\":\"R77.800x001,I10.x00x026\",\"diagnoseName\":\"血脂异常,高血压病2级（中危）\",\"isUrgent\":\"0\",\"advice\":\"\",\"isMakeup\":\"\",\"medicareCatId\":\"\",\"medicareCatName\":\"\",\"publiclyFundedCatId\":\"\",\"publiclyFundedCatName\":\"\",\"isMedicareLimit\":\"\",\"medicareLimitScope\":\"\",\"isMedicareReimburse\":\"\",\"isFixedOutPat\":\"\",\"isSpecialOutPat\":\"\",\"areaId\":\"\",\"areaName\":\"\",\"deptId\":\"8003\",\"deptName\":\"健康管理科(西区)\",\"checkId\":\"\",\"checkName\":\"\",\"checkTime\":\"\",\"inputDoctorId\":\"Y2983\",\"inputDoctorCode\":\"Y2983\",\"inputDoctorName\":\"丁明\",\"inputTime\":\"2025/3/25 10:13:34\",\"submitDoctorId\":\"Y2983\",\"submitDoctorCode\":\"Y2983\",\"submitDoctorName\":\"丁明\",\"submitDeptId\":\"8003\",\"submitDeptName\":\"健康管理科(西区)\",\"submitTime\":\"2025/3/25 10:13:34\",\"execDeptId\":\"8003\",\"execDeptName\":\"健康管理科(西区)\",\"usageCode\":\"\",\"usageName\":\"\"},{\"hisMedicalRecId\":\"5077180\",\"hisOrderRecId\":\"35097169\",\"medicalCatId\":\"\",\"medicalCatName\":\"\",\"orderTypeId\":\"UC\",\"orderTypeName\":\"检查\",\"orderCatId\":\"\",\"orderCatName\":\"\",\"parentId\":\"1\",\"term\":\"\",\"startTime\":\"\",\"groupNum\":\"47922667\",\"groupSeqNum\":\"1\",\"reqFormId\":\"\",\"orderStatusId\":\"1\",\"orderStatusName\":\"正常\",\"orderExecStatusId\":\"\",\"orderExecStatusName\":\"\",\"orderExecTime\":\"2025/3/25 9:12:37\",\"feeStatusId\":\"1\",\"feeStatusName\":\"收费\",\"payStatusId\":\"1\",\"payStatusName\":\"收费\",\"code\":\"F00000252253\",\"orderItemId\":\"F00000252253\",\"orderItemSubCatId\":\"\",\"orderItemSubCatName\":\"\",\"orderItemGroupId\":\"\",\"orderItemName\":\"动态血压监测\",\"orderItemGroupName\":\"\",\"price\":\"6.24\",\"amount\":\"29.95\",\"spec\":\"\",\"dosage\":\"0\",\"dosageUnitId\":\"\",\"dosageUnitName\":\"\",\"frequencyId\":\"\",\"frequencyName\":\"\",\"frequencyInterval\":\"\",\"frequencyCount\":\"\",\"frequencyUnit\":\"\",\"frequencyTime\":\"\",\"firstDayTime\":\"\",\"lastDayTime\":\"\",\"count\":\"1\",\"quantity\":\"24\",\"quantityUnitId\":\"\",\"quantityUnitName\":\"小时\",\"execMode\":\"\",\"checkCount\":\"\",\"execDoneCount\":\"\",\"execTotalCount\":\"\",\"diagnose\":\"R77.800x001,I10.x00x026\",\"diagnoseName\":\"血脂异常,高血压病2级（中危）\",\"isUrgent\":\"0\",\"advice\":\"\",\"isMakeup\":\"\",\"medicareCatId\":\"\",\"medicareCatName\":\"\",\"publiclyFundedCatId\":\"\",\"publiclyFundedCatName\":\"\",\"isMedicareLimit\":\"\",\"medicareLimitScope\":\"\",\"isMedicareReimburse\":\"\",\"isFixedOutPat\":\"\",\"isSpecialOutPat\":\"\",\"areaId\":\"\",\"areaName\":\"\",\"deptId\":\"8003\",\"deptName\":\"健康管理科(西区)\",\"checkId\":\"\",\"checkName\":\"\",\"checkTime\":\"\",\"inputDoctorId\":\"Y2983\",\"inputDoctorCode\":\"Y2983\",\"inputDoctorName\":\"丁明\",\"inputTime\":\"2025/3/25 9:12:37\",\"submitDoctorId\":\"Y2983\",\"submitDoctorCode\":\"Y2983\",\"submitDoctorName\":\"丁明\",\"submitDeptId\":\"8003\",\"submitDeptName\":\"健康管理科(西区)\",\"submitTime\":\"2025/3/25 9:12:37\",\"execDeptId\":\"5024\",\"execDeptName\":\"心电图室(西区)\",\"usageCode\":\"\",\"usageName\":\"\"}]";

        Mockito.when(drHisHelper.getPrescriptionInfo(any())).thenReturn(JSONArray.parseArray(pJson, HisPrescriptionInfo.class));

        HisQueryContext hisQueryContext = new HisQueryContext();
        hisQueryContext.setHisPatientId("1");
        BusPatientMedicalRecordDTO busPatientMedicalRecordDTO = new BusPatientMedicalRecordDTO();
        busPatientMedicalRecordDTO.setBeginDate(DateUtil.parse("2022-01-01").toJdkDate());
        busPatientMedicalRecordDTO.setEndDate(DateUtil.parse("2025-05-01").toJdkDate());
        busPatientMedicalRecordDTO.setPatientId(1L);
        hisQueryContext.setQueryDto(busPatientMedicalRecordDTO);
        hisQueryContext.setPatientFamily(new BusPatientFamily());

        List<BusPatientMedicalRecordListVO<BusPrescriptionInfoVO>> electronicMedicalRecordItemList = drPrescriptionItemHandler.getElectronicMedicalRecordItemList(hisQueryContext);
        log.info("{}", electronicMedicalRecordItemList);
    }
}
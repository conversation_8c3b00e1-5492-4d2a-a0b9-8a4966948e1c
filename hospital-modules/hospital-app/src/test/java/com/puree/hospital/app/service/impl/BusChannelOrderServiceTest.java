package com.puree.hospital.app.service.impl;

import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.mapper.BusChannelOrderMapper;
import com.puree.hospital.app.service.IBusChannelPartnerService;
import com.puree.hospital.app.service.IBusChannelPatientAgentRelationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

/**
 * 渠道订单服务测试类
 */
@SuppressWarnings("unused")
@Slf4j
@ExtendWith(MockitoExtension.class)
public class BusChannelOrderServiceTest {

    @InjectMocks
    private BusChannelOrderServiceImpl busChannelOrderService;
    @Mock
    private BusChannelOrderMapper channelOrderMapper;

    @Mock
    private IBusChannelPatientAgentRelationService busChannelPatientAgentRelationService;

    @Mock
    private IBusChannelPartnerService busChannelPartnerService;

    private final BusOrderDto busOrderDto = new BusOrderDto();

    @BeforeEach
    void setUp() {
        busOrderDto.setHospitalId(100L);
        busOrderDto.setPatientId(100L);
        busOrderDto.setId(351651L);
        busOrderDto.setOrderNo("TO615613248135");
        busOrderDto.setOrderType(3);
    }

    @Test
    void testCreateChannelOrder_NotRelation() {
        lenient().when(busChannelPatientAgentRelationService.save(any())).thenReturn(false);
        busChannelOrderService.createBusChannelOrderByOrderDTO(busOrderDto);
    }
}

package com.puree.hospital.app.his.handler;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.app.domain.dto.BusPatientMedicalRecordDTO;
import com.puree.hospital.app.domain.vo.BusPatientMedicalRecordListVO;
import com.puree.hospital.app.his.domain.HisQueryContext;
import com.puree.hospital.app.his.domain.vo.BusPatientMedicalRecordItemVO;
import com.puree.hospital.app.his.domain.vo.HisOutPatientRecord;
import com.puree.hospital.app.his.domain.vo.HisRegisteredInfo;
import com.puree.hospital.app.his.helper.DrHisHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/30 11:38
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class DrEMRItemHandlerTest {

    @InjectMocks
    @Spy
    DrEmrItemHandler drEMRItemHandler;

    @Mock
    DrHisHelper drHisHelper;


    @Test
    void getElectronicMedicalRecordItemList() {
        String regJson = "[{\"RegFlow\":\"\",\"RegType\":\"普通\",\"DoctorID\":\"Y2983\",\"SeeTime\":\"2025/3/25 9:00:25\",\"RegTime\":\"2025/3/25 8:16:18\",\"SeeNO\":\"-1\",\"PatientName\":\"陈捷\",\"PatientSexID\":\"1\",\"PatientAge\":\"46岁\",\"PatientID\":\"**********\",\"FeeType\":\"本院记账20%\",\"FeeItem\":null,\"DepartmentCode\":null,\"DepartmentName\":\"健康管理科(西区)\",\"DepartmentAddress\":\"\",\"DoctorName\":\"丁明\",\"TotalCost\":null,\"PayTypeName\":\"\",\"TranSerialNO\":\"5077180\",\"BookTime\":\"\",\"ValidFlag\":\"1\",\"WmDiagnose\":\"血脂异常,高血压病2级（中危）\",\"TcmDiagnose\":\"\",\"TcmSyndrome\":\"\"},{\"RegFlow\":\"\",\"RegType\":\"普通\",\"DoctorID\":\"Y0830\",\"SeeTime\":\"2025/3/30 22:15:36\",\"RegTime\":\"2025/3/30 22:13:39\",\"SeeNO\":\"-1\",\"PatientName\":\"陈捷\",\"PatientSexID\":\"1\",\"PatientAge\":\"46岁\",\"PatientID\":\"**********\",\"FeeType\":\"本院记账20%\",\"FeeItem\":null,\"DepartmentCode\":null,\"DepartmentName\":\"急诊外科(西区)\",\"DepartmentAddress\":\"\",\"DoctorName\":\"王靖宇\",\"TotalCost\":null,\"PayTypeName\":\"\",\"TranSerialNO\":\"5092626\",\"BookTime\":\"\",\"ValidFlag\":\"1\",\"WmDiagnose\":\"上呼吸道感染,非器质性睡眠障碍,咳嗽\",\"TcmDiagnose\":\"\",\"TcmSyndrome\":\"\"},{\"RegFlow\":\"\",\"RegType\":\"普通\",\"DoctorID\":\"Y2983\",\"SeeTime\":\"2025/4/17 15:11:47\",\"RegTime\":\"2025/4/1 15:28:38\",\"SeeNO\":\"-1\",\"PatientName\":\"陈捷\",\"PatientSexID\":\"1\",\"PatientAge\":\"46岁\",\"PatientID\":\"**********\",\"FeeType\":\"本院记账20%\",\"FeeItem\":null,\"DepartmentCode\":null,\"DepartmentName\":\"健康管理科(西区)\",\"DepartmentAddress\":\"\",\"DoctorName\":\"丁明\",\"TotalCost\":null,\"PayTypeName\":\"\",\"TranSerialNO\":\"5097374\",\"BookTime\":\"\",\"ValidFlag\":\"1\",\"WmDiagnose\":\"高血压,咳嗽\",\"TcmDiagnose\":\"\",\"TcmSyndrome\":\"\"}]";
        String emrJson = "[{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":201038.0,\"WDDMC\":\"现病史(o)\",\"WDNR\":\"头晕减轻，睡眠改善\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":350.0,\"WDDMC\":\"医院名称\",\"WDNR\":\"广州开发区医院\\r\\n广州市黄埔区人民医院\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":603.0,\"WDDMC\":\"姓名(o)\",\"WDNR\":\"陈捷\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":604.0,\"WDDMC\":\"性别(o)\",\"WDNR\":\"男\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":843.0,\"WDDMC\":\"年龄(o)\",\"WDNR\":\"46岁\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":203.0,\"WDDMC\":\"体温\",\"WDNR\":\"36.2\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":197.0,\"WDDMC\":\"门诊号(o)\",\"WDNR\":\"**********\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":614.0,\"WDDMC\":\"联系电话(o)\",\"WDNR\":\"13602458288\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":842.0,\"WDDMC\":\"门诊科室\",\"WDNR\":\"健康管理科(西区)\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":203244.0,\"WDDMC\":\"记录日期(o)\",\"WDNR\":\"2025-04-01 15:32\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":201037.0,\"WDDMC\":\"主诉(o)\",\"WDNR\":\"头晕减轻\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":205317.0,\"WDDMC\":\"流行病学史1（患者来源选择）\",\"WDNR\":\"【否认】\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":33768843.0,\"WDDMC\":\"血压(o)\",\"WDNR\":\"120/70\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":505.0,\"WDDMC\":\"门(急)诊诊断\",\"WDNR\":\"西医：高血压，睡眠障碍\\r\\n中医：\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":205597.0,\"WDDMC\":\"时间1\",\"WDNR\":\"2025-04-01 15:32\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"},{\"TRANSERIALNO\":\"5097374\",\"PATIENTNAME\":\"陈捷\",\"WDBH\":2218914.0,\"WDDDM\":203243.0,\"WDDMC\":\"处方(o)\",\"WDNR\":\"1、继续降压治疗。2、继续生活方式治疗。\",\"CJYSDM\":\"Y2983\",\"CJYSMC\":\"丁明\",\"CJKSDM\":\"8003\",\"CJKSMC\":\"健康管理科(西区)\",\"BLDM\":39534.0,\"BLMC\":\"（院级）复诊病历\",\"BLXSMC\":null,\"WDDXH\":null,\"WDDSM\":null,\"CJSJ\":\"2025-04-01T15:28:44\",\"YQDM\":\"A\",\"YQMC\":\"西区\"}]";
        List<HisRegisteredInfo> hisRegisteredInfos = JSON.parseArray(regJson, HisRegisteredInfo.class);
        List<HisOutPatientRecord> hisOutPatientRecords = JSON.parseArray(emrJson, HisOutPatientRecord.class);

        Mockito.when(drHisHelper.getRegisteredInfo(any())).thenReturn(hisRegisteredInfos);
//        Mockito.when(drHisHelper.getOutPatientRecord(any())).thenReturn(hisOutPatientRecords);
        Mockito.when(drHisHelper.getOutPatientRecord(any())).thenCallRealMethod();

        HisQueryContext hisQueryContext = new HisQueryContext();
        hisQueryContext.setHisPatientId("1");
        BusPatientMedicalRecordDTO busPatientMedicalRecordDTO = new BusPatientMedicalRecordDTO();
        busPatientMedicalRecordDTO.setBeginDate(DateUtil.parse("2022-01-01").toJdkDate());
        busPatientMedicalRecordDTO.setEndDate(DateUtil.parse("2025-05-01").toJdkDate());
        hisQueryContext.setQueryDto(busPatientMedicalRecordDTO);
        List<BusPatientMedicalRecordListVO<BusPatientMedicalRecordItemVO>> electronicMedicalRecordItemList = drEMRItemHandler.getElectronicMedicalRecordItemList(hisQueryContext);

        log.info("{}", electronicMedicalRecordItemList);

    }
}
package com.puree.hospital.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.model.dto.BusFreightBaseSettingDTO;
import com.puree.hospital.app.api.model.dto.BusFreightSettingDTO;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusFreightBaseSetting;
import com.puree.hospital.app.domain.BusFreightSetting;
import com.puree.hospital.app.mapper.BusConsultationSettingsMapper;
import com.puree.hospital.app.mapper.BusFreightBaseSettingMapper;
import com.puree.hospital.app.service.IBusConsultationSettingsService;
import com.puree.hospital.app.api.model.dto.BusFreightQueryDTO;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class BusConsultationSettingsServiceImpl implements IBusConsultationSettingsService {
    private final BusConsultationSettingsMapper consultationSettingsMapper;
    private final BusFreightBaseSettingMapper busFreightSettingMapper;
    @Autowired
    public BusConsultationSettingsServiceImpl(BusConsultationSettingsMapper consultationSettingsMapper,BusFreightBaseSettingMapper busFreightSettingMapper) {
        this.consultationSettingsMapper = consultationSettingsMapper;
        this.busFreightSettingMapper = busFreightSettingMapper;
    }

    /**
     * @param consultationSettings
     * @return BusConsultationSettings 咨询设置，若无数据返回null
     * @description 查询医院咨询设置
     * <AUTHOR>
     * @date 2024/12/16 12:15
     **/
    @Override
    public BusConsultationSettings selectOne(BusConsultationSettings consultationSettings) {
        if (consultationSettings == null || consultationSettings.getHospitalId() == null) {
            return null;
        }

        // 使用LambdaQueryWrapper优化查询条件，提供类型安全
        LambdaQueryWrapper<BusConsultationSettings> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusConsultationSettings::getHospitalId, consultationSettings.getHospitalId());

        BusConsultationSettings settings = consultationSettingsMapper.selectOne(wrapper);
        if (Objects.isNull(settings)) {
            return null;
        }
        // 空值安全检查
        if (settings.getSelfPickupTime() != null) {
            // 使用StringBuilder优化字符串处理
            String pickupTime = settings.getSelfPickupTime();
            if (pickupTime.contains(",")) {
                settings.setSelfPickupTime(pickupTime.replace(',', '-'));
            }
        }
        BusFreightBaseSetting baseSetting = queryFreightBase(consultationSettings);
       if (baseSetting != null) {
           settings.setLogisticsMode(baseSetting.getLogisticsMode());
           settings.setDefaultShowLogistics(baseSetting.getDefaultShowLogistics());
           settings.setSelfPickupTime(baseSetting.getSelfPickupTime());
       }
        return settings;
    }

    public BusFreightBaseSetting queryFreightBase(BusConsultationSettings consultationSettings) {
        LambdaQueryWrapper<BusFreightBaseSetting> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusFreightBaseSetting::getHospitalId, consultationSettings.getHospitalId());
        return busFreightSettingMapper.selectOne(lambdaQuery);
    }

    @Override
    public BusFreightBaseSettingDTO queryFreight(BusFreightQueryDTO query) {
        BusFreightBaseSettingDTO dto = new BusFreightBaseSettingDTO();
        LambdaQueryWrapper<BusFreightBaseSetting> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusFreightBaseSetting::getHospitalId, query.getHospitalId());
        BusFreightBaseSetting settings = busFreightSettingMapper.selectOne(lambdaQuery);
        if (Objects.nonNull(settings)) {
            if (StringUtils.isEmpty(query.getProvinceName())){
                throw new ServiceException("省份不能为空");
            }
            BeanUtils.copyProperties(settings, dto);
            List<BusFreightSetting> freightSettingList = consultationSettingsMapper.getFreight(query);
            List<BusFreightSettingDTO> dtoList =new ArrayList<>();
            freightSettingList.forEach(setting -> {
                BusFreightSettingDTO settingDTO=new BusFreightSettingDTO();
                BeanUtils.copyProperties(setting,settingDTO);
                dtoList.add(settingDTO);
            });
            dto.setFreightSettingList(dtoList);
        }
        return dto;
    }
}

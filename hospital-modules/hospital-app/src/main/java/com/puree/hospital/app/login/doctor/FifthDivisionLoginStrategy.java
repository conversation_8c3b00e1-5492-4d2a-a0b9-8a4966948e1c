package com.puree.hospital.app.login.doctor;

import com.puree.hospital.app.constant.PhysicianLoginCheckConstant;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

import static com.puree.hospital.common.api.constant.Constants.LOGIN_SUCCESS;

/**
 * 五师
 *
 * <AUTHOR>
 * @date 2025/8/12 15:02:39
 */
@Slf4j
@Order(2)
@Component(PhysicianLoginCheckConstant.FIFTH_DIVISION + LoginStrategy.SUFFIX)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FifthDivisionLoginStrategy extends AbstractLoginStrategy implements LoginStrategy {

    private final IBusDoctorService busDoctorService;

    @Override
    public String loginCheck(Map<String, Object> map, String token, String phoneNumber, String passWord, String ipAddr) {
        String encryptedPhone = DESUtil.encrypt(phoneNumber);
        BusDoctorHospital busDoctorHospital = busDoctorService.selectPhysicianByPhone(null, encryptedPhone);
        log.info("五师查询信息 busDoctorHospital={}", busDoctorHospital);
        if (Objects.nonNull(busDoctorHospital)) {
            if (YesNoEnum.NO.getCode().equals(busDoctorHospital.getStatus())) {
                return "用户已被禁用,请联系管理员";
            }
            // 密码登录校验
            String res = checkPassword(passWord, busDoctorHospital.getDoctorId(), busDoctorHospital.getRole());
            if (StringUtils.isNotEmpty(res)) return res;
            // 刷新token
            refreshToken(token, busDoctorHospital.getDoctorId());
            saveMsgAndSetIdentity(map, token, encryptedPhone, ipAddr, busDoctorHospital.getDoctorId(), busDoctorHospital.getRole());
            return LOGIN_SUCCESS;
        }
        return null;
    }

    /**
     * 排序
     */
    @Override
    public int order() {
        return 2;
    }
}

package com.puree.hospital.app.login.doctor;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.constant.PhysicianLoginCheckConstant;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.mapper.BusDoctorHospitalMapper;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.puree.hospital.common.api.constant.Constants.LOGIN_SUCCESS;

/**
 * 医生登录策略
 *
 * <AUTHOR>
 * @date 2025/8/12 15:01:57
 */
@Slf4j
@Order(0)
@Component(PhysicianLoginCheckConstant.DOCTOR + LoginStrategy.SUFFIX)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DoctorLoginStrategy extends AbstractLoginStrategy implements LoginStrategy {

    private final IBusDoctorService busDoctorService;

    private final BusDoctorHospitalMapper busDoctorHospitalMapper;

    @Override
    public String loginCheck(Map<String, Object> map, String token, String phoneNumber, String passWord, String ipAddr) {
        //医生信息
        String encryptedPhone = DESUtil.encrypt(phoneNumber);
        BusDoctorHospital busDoctor = busDoctorService.selectPhysicianByPhone(AppRoleEnum.DOCTOR.getCode(), encryptedPhone);
        log.info("医生查询信息 a={}", busDoctor);
        if (null != busDoctor) {
            //是否被所有医院禁用
            LambdaQueryWrapper<BusDoctorHospital> hospitalQueryWrapper = new LambdaQueryWrapper<>();
            hospitalQueryWrapper.eq(BusDoctorHospital::getDoctorId, busDoctor.getDoctorId());
            long count = busDoctorHospitalMapper.selectCount(hospitalQueryWrapper);
            if (count == 0) {
                return "用户不存在,请联系管理员";
            }
            hospitalQueryWrapper.eq(BusDoctorHospital::getStatus, YesNoEnum.YES.getCode());
            count = busDoctorHospitalMapper.selectCount(hospitalQueryWrapper);
            if (count == 0) {
                return "账号已被禁用，请联系医院管理员";
            }
            //密码登录校验
            String res = checkPassword(passWord, busDoctor.getDoctorId(), AppRoleEnum.DOCTOR.getCode());
            if (StringUtils.isNotEmpty(res)) return res;
            // 更新最新token
            refreshToken(token, busDoctor.getId());
            // 保存登录信息 设置身份标识
            saveMsgAndSetIdentity(map, token, encryptedPhone, ipAddr, busDoctor.getDoctorId(), AppRoleEnum.DOCTOR.getCode());
            return LOGIN_SUCCESS;
        }
        return null;
    }

    /**
     * 排序
     */
    @Override
    public int order() {
        return 0;
    }
}

package com.puree.hospital.app.his.domain.vo;

import lombok.Data;

/**
 * his 病历
 */
@Data
public class HisOutPatientRecord {

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 挂号流水号
     */
    private String tranSerialNO;

    /**
     * 病历文档编号，病历文档医院内唯一号，Y
     */
    private String wdbh;

    /**
     * 病历文档段代码，Y
     */
    private String wdddm;

    /**
     * 病历文档段名称，Y
     */
    private String wddmc;

    /**
     * 病历文本内容
     */
    private String wdnr;

    /**
     * 病历创建医生代码，Y
     */
    private String cjysdm;

    /**
     * 病历创建医生名称，Y
     */
    private String cjysmc;

    /**
     * 病历创建科室代码，Y
     */
    private String cjksdm;

    /**
     * 病历创建科室名称，Y
     */
    private String cjksmc;

    /**
     * 病历代码(病历模板库代码)
     */
    private String bldm;

    /**
     * 病历名称(病历模板库名称)
     */
    private String blmc;

    /**
     * 病历显示名称（0）
     */
    private String blxsmc;

    /**
     * 文件结构代码（序号）病历文档段标识
     */
    private String wddxh;

    /**
     * 文件结构显示名称，别名(主诉)
     */
    private String wddsm;

    /**
     * 病历创建时间，Y
     */
    private String cjsj;

    /**
     * 院区代码
     */
    private String yqdm;

    /**
     * 院区名称
     */
    private String yqmc;

}

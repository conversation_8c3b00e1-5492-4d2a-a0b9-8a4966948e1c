package com.puree.hospital.app.domain.dto;

import com.puree.hospital.app.domain.BusQuickConsultation;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ConsultationOrderDto {

    /** 订单编号 */
    private String orderNo;
    /** 订单状态（0待支付,1待接诊 未使用,2问诊中 使用中, 3问诊中 已开处方,4已完成 已使用,5已失效,6已退号,7已退款,8已取消） */
    private String status;
    /** 退号原因 */
    private String reason;
    /** 订单金额 */
    private BigDecimal amount;
    /** 订单类型（0:图文;1:视频） */
    private String orderType;
    /** 问诊人姓名 */
    private String familyName;
    /** 问诊人性别 */
    private String familySex;
    /** 问诊人年龄 */
    private String familyAge;
    /** 医生照片 */
    private String photo;
    /** 医生姓名 */
    private String doctorName;
    /** 医生职称 */
    private String title;
    /** 科室名称 */
    private String departmentName;
    /** 问诊回合 */
    private Integer round;
    /**
     * 问诊类型（0挂科室 1挂医生）
     */
    private String consultationType;
    /**
     * 视频问诊预约开始时间
     */
    private String subscribeTime;

    private BusQuickConsultation quickConsultation;
    /**
     * 合作机构code
     */
    private String partnersCode;

    private Long doctorId;

    private Long hospitalId;

    private List<Long> orderIds;

    private Integer isGuoHao;

    private String payWay;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 1-患者支付 ，2-医生赠送， 3-健康解读报告
     */
    private Integer payType ;

    /** 应用 类型 */
    private String appType = ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType();

    /**
     * 挂号流水号
     */
    private String serialNo;

    /**
     * 就诊记录
     */
    private String medicalRecord;

    /**
     * 就诊记录图片
     */
    private String picture;

}

package com.puree.hospital.app.hispay.upload;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * His 药品订单信息上传
 * <AUTHOR>
 * @date 2025/1/9 14:54
 */
@Slf4j
@Component(OrderTypeConstant.DRUGS_ORDER + IOrderUploadHandler.SUFFIX)
public class DrugOrderUploadHandler extends BaseOrderUploadHandler implements IOrderUploadHandler {

    @Resource
    private IBusDrugsOrderService busDrugsOrderService;
    @Resource
    private BusPrescriptionMapper busPrescriptionMapper;
    @Resource
    private BusOrderMapper busOrderMapper;
    @Resource
    private IBusOrderService busOrderService;

    /**
     * 药品订单信息上传
     * @param hospitalId 医院id
     * @param orderNo 药品订单号
     */
    @Override
    public HisPayUploadDTO uploadOrder(Long hospitalId, String orderNo) {
        BusDrugsOrder drugsOrder = busDrugsOrderService.queryDrugsOrder(orderNo);
        if(drugsOrder == null){
            log.error("HIS支付信息上报-未查询到药品订单：{}", orderNo);
            throw new ServiceException("未查询到药品订单");
        }

        BusPrescription prescription = busPrescriptionMapper.selectById(drugsOrder.getPrescriptionId());
        if(prescription == null){
            log.error("HIS支付信息上报-未查询到处方信息：{}", drugsOrder);
            throw new ServiceException("未查询到处方信息");
        }

        BusOrder busOrder = busOrderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                .eq(BusOrder::getSubOrderType, CodeEnum.NO.getCode())
                .eq(BusOrder::getSubOrderId, drugsOrder.getId()));
        if(busOrder == null){
            log.error("HIS支付信息上报-未查询到总订单信息：{}", drugsOrder);
            throw new ServiceException("未查询到总订单信息");
        }

        HisPayUploadDTO uploadDTO = assembleUploadDTO(busOrder, Collections.singletonList(prescription));
        log.info("药品订单信息上传，数据组装结果：{}", uploadDTO);
        upload(uploadDTO, busOrder.getOrderNo());
        log.info("药品订单信息上传完成");

        // 同步药品收货信息
        log.info("同步药品收货信息：{}", busOrder);
        busOrderService.hisHandler(busOrder, "1");

        return uploadDTO;
    }
}

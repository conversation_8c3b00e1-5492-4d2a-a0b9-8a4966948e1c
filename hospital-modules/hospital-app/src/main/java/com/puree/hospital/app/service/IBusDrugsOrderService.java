package com.puree.hospital.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusDrugOrderPackage;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.dto.BusDoctorOrderDTO;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.domain.vo.*;

import java.util.List;
import java.util.Map;

public interface IBusDrugsOrderService extends IService<BusDrugsOrder> {
    List<DrugsOrderVo> selectList(DrugsOrderDto dto);
    BusDrugsOrder select(DrugsOrderDto dto);
    Map<String, Object> insert(BusDrugsOrder busDrugsOrder);
    int cancel(DrugsOrderDto dto);
    int update(BusDrugsOrder busDrugsOrder);
    int updateStatus(BusDrugsOrder busDrugsOrder);
    BusPrescriptionVo queryDrugsOrderInfo(DrugsOrderDto dto);
    int applyReplacement(BusAfterSale afterSale);
    BusDrugsOrder queryDrugsOrder(String outTradeNo);
    List<BusDrugsOrder> queryZXDeliverStatus(DrugsOrderDto dto);
    /**
     * 确认收货/自提
     *
     * @param orderNo 总订单编号
     * @return 操作结果
     */
    int confirm(String orderNo);

    int updateRefundStatus(BusDrugsOrder drugsOrder, String orderStatus);
    int remove(String orderNo);
    HospitalVo selectHospitalTel(Long hospitalId);
    List<BusDrugsOrder> selectOrderNoList(Long hospitalId);
    BusOrder checkPayment(Long hospitalId, Long id);
    String selectPatientInfo(String orderNo);
    List<OrderPrescriptionVo> selectPrescriptionList();
    List<BusDrugsOrder> queryXXDeliverStatus(DrugsOrderDto drugsOrderDto);

    /**
     *
     * @param hospitalId
     * @return
     */
    String getHospitalPayWay(Long hospitalId);

    BusDrugsOrder selectOrderInfo(Long subOrderId);

    /**
     * 校验非处方药库存
     * @param busDrugsOrder
     * @return
     */
    Map<String, Object> checkOtcDrugsStock(BusDrugsOrder busDrugsOrder);

    /**
     * 新增药品订单
     * @param busDrugsOrder
     * @return
     */
    int addDrugsOrder(BusDrugsOrder busDrugsOrder);

    /**
     * 修改订单状态
     * @param drugsOrder
     */
    int updateOrderStatus(BusDrugsOrder drugsOrder);

    /**
     * 查询患者端订单列表
     * @param dto
     * @return
     */
    List<OrderVo> queryList4Patient(DrugsOrderDto dto);

    /**
     * 查询患者端药品/商品订单详情
     * @param dto
     * @return
     */
    List<OrderDetailVO> queryOrderDrugsAndGoodsDetail(DrugsOrderDto dto);

    /**
     * 释放药品库存
     * @param drugsOrder
     */
     void releaseStock(BusDrugsOrder drugsOrder);

    /**
     * 医生端订单列表
     * @param busDoctorOrderDTO
     * @return
     */
    List<OrderVo> listShopAndDrugsOrder(BusDoctorOrderDTO busDoctorOrderDTO);

    /**
     * 交易订单加入待支付队列
     * @param hospitalId
     * @param id
     * @param orderNo
     * @return
     */
    boolean delayQueue(Long hospitalId, String id, String orderNo);

    /**
     * @Param drugsOrder
     * @Return void
     * @Description 药品订单重新放回购物车
     * <AUTHOR>
     * @Date 2024/11/18 16:32
     **/
    void putRxCart(BusDrugsOrder drugsOrder);

    /**
     * 释放药品订单库存
     * @param drugsOrder
     */
    void increaseDrugStock(BusDrugsOrder drugsOrder);

    /**
     * 查询订单物流信息
     * @param orderNo
     * @return
     */
    List<BusDrugOrderPackage> queryLogisticsList(String orderNo);

    /**
     * @Param subOrderId
     * @Return BusDrugsOrder
     * @Description 根据ID查询
     * <AUTHOR>
     * @Date 2024/11/18 16:33
     **/
    BusDrugsOrder selectById(Long subOrderId);
}

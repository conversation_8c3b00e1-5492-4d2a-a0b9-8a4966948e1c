package com.puree.hospital.app.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.puree.hospital.app.api.model.BusConsultationOrderDTO;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.dto.BusDoctorDto;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.dto.ConsultationOrderDto;
import com.puree.hospital.app.domain.dto.QuickConsultationDto;
import com.puree.hospital.app.domain.vo.BusConsultationOrderVo;

import java.util.List;
import java.util.Map;

public interface IBusConsultationOrderService extends IService<BusConsultationOrder> {
    long insert(ConsultationOrderDto orderDto);
    BusConsultationOrder queryOrderInfo(Long id);
    int cancelOrder(BusConsultationOrder order);
    List<BusConsultationOrderVo> patientOrderList(BusConsultationOrder consultationOrder);
    Map<String, Object> receiveConsultation(BusConsultationOrder dto);
    List<BusConsultationOrder> doctorOrderList(Long hospitalId, Long doctorId);
    int updateStatus(BusConsultationOrder consultationOrder);
    Long register(ConsultationOrderDto orderDto);
    int guohao(ConsultationOrderDto orderDto);
    int applyRefund(BusAfterSale afterSale);
    BusConsultationOrder queryConsultationOrder(String outTradeNo);
    Long selectDoctor(BusDoctorDto busDoctorDto);
    int updateRefundStatus(BusConsultationOrder consultationOrder);
    Long getConsultationOrderId(BusPrescriptionDto dto);
    int notFollow(QuickConsultationDto dto);
    boolean checkOrderState(BusPrescriptionDto busPrescription);
    BusConsultationOrder selectOrderStatus(long hospitalId, Long consultationOrderId);
    BusConsultationOrder queryConsultationOrderByOrderNo(String orderNo);
    BusConsultationOrder call(ConsultationOrderDto orderDto);
    int videoOrderUpdate(BusConsultationOrder order);
    Long endOrder(BusConsultationOrder order);
    boolean check(BusConsultationOrder order);
    BusConsultationOrderVo selectNewest(BusConsultationOrder order);
    BusConsultationOrder queryOrderNo(String orderNo);
    int backNumber(ConsultationOrderDto orderDto);
    String selectPatientInfo(String orderNo);
    BusConsultationOrder selectBusConsultationOrder(BusConsultationOrder order);
    int checkUpdateFamily(BusConsultationOrder order);
    BusConsultationOrderVo queryInfo(Long id, Long loginUserid);
    int remove(Long id);
    BusConsultationOrder selectNewestConsultationOrder(BusPrescriptionDto dto);
    List<BusConsultationOrder> queryAccepted();

    JSONObject checkNoPayOrder(ConsultationOrderDto orderDto);

    /**
     * 查询已过号的视频问诊订单
     * @return
     */
    List<BusConsultationOrder> queryVideoOrder();

    /**
     * 推送图文消息给医生
     * @param consultationOrder
     */
    void pushTwMsgToDr(BusConsultationOrder consultationOrder);

    /**
     * 保存医保授权码
     *
     * @param consultationOrderId 问诊单id
     * @param miAuthCode          医保授权
     * @param doctorId            医生id
     */
    void saveMiAuthCode(Long consultationOrderId, String miAuthCode, Long doctorId);

    /**
     * 根据订单id或者订单编号查询订单详情
     *
     * @param orderId 订单id
     * @param orderNo 订单编号
     * @return 问诊订单详细
     */
    BusConsultationOrderDTO getOrderInfo(Long orderId, String orderNo);


    /**
     * 结束问诊订单相关的医生代办事项
     * @param orderId
     * @param doctorId
     */
    void endTdl(Long orderId, Long doctorId);

    /**
     * 问诊订单退款
     * @param order
     */
    void refund(BusConsultationOrder order);


    /**
     * 无线下HIS就诊记录是否允许问诊
     *
     * @param orderDto 订单信息
     */
    void checkHasMedicalRecord(ConsultationOrderDto orderDto);
}

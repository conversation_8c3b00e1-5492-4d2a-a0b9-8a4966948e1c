package com.puree.hospital.app.service.impl.payment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.model.event.consultation.BaseConsultationEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationRefundSuccessEvent;
import com.puree.hospital.app.constant.AfterSaleStatus;
import com.puree.hospital.app.constant.ConsultationOrderStatus;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDrugOrderPackage;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusEnterprise;
import com.puree.hospital.app.domain.BusFiveServicePackOrder;
import com.puree.hospital.app.domain.BusHospitalInvoiceConfig;
import com.puree.hospital.app.domain.BusHospitalPa;
import com.puree.hospital.app.domain.BusHospitalPaDrugs;
import com.puree.hospital.app.domain.BusHospitalWechatConfig;
import com.puree.hospital.app.domain.BusInvoiceHeader;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderAfterSales;
import com.puree.hospital.app.domain.BusOtcDrugs;
import com.puree.hospital.app.domain.BusPartners;
import com.puree.hospital.app.domain.BusPatientOrderInfo;
import com.puree.hospital.app.domain.BusShopOrder;
import com.puree.hospital.app.enums.BusOrderSubOrderTypeEnum;
import com.puree.hospital.app.helper.BusPayConfigHelper;
import com.puree.hospital.app.mapper.BusShopOrderMapper;
import com.puree.hospital.app.queue.producer.event.consultation.ConsultationRefundSuccessEventProducer;
import com.puree.hospital.app.service.IBusHospitalWechatConfigService;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusProductInvoiceConfig;
import com.puree.hospital.app.domain.dto.BusPatientHospitalDto;
import com.puree.hospital.app.domain.vo.BusHospitalPaDrugsVo;
import com.puree.hospital.app.domain.vo.BusPatientHospitalVo;
import com.puree.hospital.app.infrastructure.supplier.SupplierFactory;
import com.puree.hospital.app.mapper.BusAfterSaleMapper;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.app.mapper.BusDrugsOrderMapper;
import com.puree.hospital.app.mapper.BusEnterpriseMapper;
import com.puree.hospital.app.mapper.BusFiveServicePackOrderMapper;
import com.puree.hospital.app.mapper.BusHospitalInvoiceConfigMapper;
import com.puree.hospital.app.mapper.BusHospitalPaDrugsMapper;
import com.puree.hospital.app.mapper.BusHospitalPaMapper;
import com.puree.hospital.app.mapper.BusInvoiceHeaderMapper;
import com.puree.hospital.app.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.app.mapper.BusOtcDrugsMapper;
import com.puree.hospital.app.mapper.BusPartnersMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusProductInvoiceConfigMapper;
import com.puree.hospital.app.service.IAsyncJoinFollowUpService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusImSendMessageService;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.service.IBusPatientPartnersService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.app.service.ITongLianPayService;
import com.puree.hospital.app.service.IWxPayService;
import com.puree.hospital.app.service.impl.BusFiveServicePackInvoiceService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.InvoiceStatusEnum;
import com.puree.hospital.common.core.enums.InvoiceTypeEnum;
import com.puree.hospital.common.core.enums.NemberEnum;
import com.puree.hospital.common.core.enums.OrderStatusEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.ProductTypeInvoiceEnum;
import com.puree.hospital.common.core.enums.ServicePackOrderStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
import com.puree.hospital.five.api.RemoteServicePackOrderService;
import com.puree.hospital.five.api.model.ServicePackOrderRequest;
import com.puree.hospital.five.api.model.ServicePackOrderResponse;
import com.puree.hospital.followup.api.model.PatientFollowUpJoinRuleDTO;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.order.api.RemoteBusConsultationOrderService;
import com.puree.hospital.order.api.RemoteBusDrugsOrderService;
import com.puree.hospital.order.api.RemoteBusFiveServicePackOrderService;
import com.puree.hospital.order.api.model.BusConsultationOrderRequest;
import com.puree.hospital.order.api.model.BusDrugsOrderRequest;
import com.puree.hospital.order.api.model.BusFiveServicePackOrderRequest;
import com.puree.hospital.order.api.model.vo.BusDrugsOrderVO;
import com.puree.hospital.pay.api.RemotePayService;
import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.api.model.dto.BusPayDTO;
import com.puree.hospital.pay.api.model.dto.BusQueryDTO;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2022/11/09/17:54
 * @Description:
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TongLianPayServiceImpl implements ITongLianPayService {
    @Autowired
    private RemotePayService remotePayService;
    private final RemoteBusDrugsOrderService remoteBusDrugsOrderService;
    private final RemoteBusConsultationOrderService remoteBusConsultationOrderService;
    private final RemoteBusFiveServicePackOrderService remoteBusFiveServicePackOrderService;
    private final BusConsultationOrderMapper busConsultationOrderMapper;
    private final BusOtcDrugsMapper busOtcDrugsMapper;
    private final IBusPrescriptionService busPrescriptionService;
    private final SupplierFactory supplierFactory;
    private final IBusPatientPartnersService busPatientPartnersService;
    private final IBusPatientHospitalService busPatientHospitalService;
    @Autowired
    private IBusConsultationOrderService consultationOrderService;
    private final RemoteServicePackOrderService remoteServicePackOrderService;
    private final BusAfterSaleMapper busAfterSaleMapper;
    private final static String SUCCESS = "0000";
    private final IBusDrugsOrderService busDrugsOrderService;
    private final BusPartnersMapper busPartnersMapper;
    @Autowired
    @Lazy
    private IWxPayService wxPayService;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final BusEnterpriseMapper busEnterpriseMapper;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final IBusOrderService busOrderService;
    private final BusDrugsOrderMapper busDrugsOrderMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final IBusImSendMessageService iBusImSendMessageService;
    private final BusDoctorMapper busDoctorMapper;
    private final BusHospitalPaMapper busHospitalPaMapper;
    private final BusHospitalPaDrugsMapper busHospitalPaDrugsMapper;
    private final BusOrderAfterSalesMapper busOrderAfterSalesMapper;
    private final BusInvoiceHeaderMapper invoiceHeaderMapper;
    private final BusProductInvoiceConfigMapper productInvoiceConfigMapper;
    private final BusHospitalInvoiceConfigMapper hospitalInvoiceConfigMapper;
    private final BusFiveServicePackOrderMapper fiveServicePackOrderMapper;
    private final BusFiveServicePackInvoiceService fiveServicePackInvoiceService;
    private final IAsyncJoinFollowUpService asyncJoinFollowUpService;
    private final BusShopOrderMapper busShopOrderMapper;

    private final IBusHospitalWechatConfigService busHospitalWechatConfigService;
    @Resource
    private PureeRabbitProducer pureeRabbitProducer;


    @Lazy
    @Resource
    private ConsultationRefundSuccessEventProducer consultationRefundSuccessEventProducer;
    @Resource
    private BusPayConfigHelper busPayConfigHelper;


    /**
     * 通联支付发起
     */
    @Override
    public R<JSONObject> tongLianPayment(BusPayDTO busPayDTO) {
        busPayDTO.setPayWay(PaysTypeEnum.TONGLIAN_PAY.getCode());
        log.info("通联支付参数busPayDTO ={}", busPayDTO);
        /*如果订单是:药品订单*/
        if (busPayDTO.getOrderType().equals(OrderTypeEnum.DRUGS.getCode())) {
            this.payBeforeHandleDrugsOrderInfo(busPayDTO);
            /*如果订单是:问诊订单*/
        } else if (busPayDTO.getOrderType().equals(OrderTypeEnum.CONSULTATION.getCode())) {
            this.payBeforeHandleConsultationOrderInfo(busPayDTO);
            /*如果订单是:服务包订单*/
        } else if (busPayDTO.getOrderType().equals(OrderTypeEnum.SERVICE_PACK.getCode())) {
            this.payBeforeHandleServicePackOrderInfo(busPayDTO);
            /*如果订单是:购物车订单*/
        } else if (busPayDTO.getOrderType().equals(OrderTypeEnum.GOODS.getCode())) {
            this.payBeforeHandleGoodsOrderInfo(busPayDTO);
        }
        /*判空*/
        if (ObjectUtil.isNull(busPayDTO.getPayAmount()) || ObjectUtil.isNull(busPayDTO.getOrderNo())) {
            throw new ServiceException("交易金额、商户交易单号不能为空");
        } else if (BigDecimal.ZERO.compareTo(busPayDTO.getPayAmount()) >= 0) {
            throw new ServiceException("订单实际支付金额必须大于0");
        }
        BusHospitalWechatConfig busHospitalWechatConfig = busHospitalWechatConfigService.selectByHospitalId(busPayDTO.getHospitalId(), busPayDTO.getAppType());
        busPayDTO.setPayConfigId(busHospitalWechatConfig.getPayConfigId());
        busPayDTO.setAppid(busHospitalWechatConfig.getAppid());
        /*支付发起*/
        R<JSONObject> pay = remotePayService.pay(busPayDTO);
        if (!pay.isSuccess()){
            throw new ServiceException(pay.getMsg());
        }
        JSONObject data = pay.getData();
        if (null != data && SUCCESS.equals(data.getString("trxstatus"))) {
            String trxid = data.getString("trxid");
            if (busPayDTO.getOrderType().equals(OrderTypeEnum.DRUGS.getCode())) {
                BusDrugsOrderRequest busDrugsOrder = new BusDrugsOrderRequest();
                busDrugsOrder.setId(busPayDTO.getOrderId());
                busDrugsOrder.setTonglianTrxid(trxid);
                remoteBusDrugsOrderService.updateBusDrugsOrderById(busDrugsOrder);
                /*如果订单是:问诊订单*/
            } else if (busPayDTO.getOrderType().equals(OrderTypeEnum.CONSULTATION.getCode())) {
                BusConsultationOrderRequest busConsultationOrder = new BusConsultationOrderRequest();
                busConsultationOrder.setId(busPayDTO.getOrderId());
                busConsultationOrder.setTonglianTrxid(trxid);
                remoteBusConsultationOrderService.updateBusConsultationOrderById(busConsultationOrder);
                /*如果订单是:服务包订单*/
            } else if (busPayDTO.getOrderType().equals(OrderTypeEnum.SERVICE_PACK.getCode())) {
                BusFiveServicePackOrderRequest busFiveServicePackOrder = new BusFiveServicePackOrderRequest();
                busFiveServicePackOrder.setId(busPayDTO.getOrderId());
                busFiveServicePackOrder.setTonglianTrxid(trxid);
                remoteBusFiveServicePackOrderService.updateBusFiveServicePackOrderById(busFiveServicePackOrder);
            } else if (busPayDTO.getOrderType().equals(OrderTypeEnum.GOODS.getCode())) {
                BusOrder busOrder = new BusOrder();
                busOrder.setOrderNo(busPayDTO.getOrderNo());
                busOrder.setTonglianTrxid(trxid);
                busOrderService.updateOrderStatus(busOrder);
            }
        } else if ("2008".equals(data.getString("trxstatus")) || "2000 ".equals(data.getString("trxstatus"))) {
            throw new ServiceException("交易进行中");
        } else {
            throw new ServiceException("支付失败");
        }
        log.info("通联支付出参 return={}", JSON.toJSONString(pay));
        return pay;
    }

    private void payBeforeHandleGoodsOrderInfo(BusPayDTO busPayDTO) {
        // 查询购物车关联订单信息
        List<BusOrder> busOrders = busOrderService.selectOrederByNo(busPayDTO.getOrderNo());
        if (CollUtil.isNotEmpty(busOrders)) {
            BusOrder busOrder = busOrders.get(0);
            // 设置订单的下单用户id还有对应的患者id --- 原来的patientId还有familyId
            setPayDtoUserIdAndPatientId(busPayDTO, busOrder);
            if (busOrders.size() == 1) {
                if (CodeEnum.YES.getCode().equals(busOrder.getSubOrderType())) {
                    busPayDTO.setBody("商品订单");
                } else {
                    busPayDTO.setBody("药品订单");
                }
            } else {
                busPayDTO.setBody("药品/商品订单");
            }
            busPayDTO.setPayAmount(BigDecimal.valueOf(busOrder.getRelPrice()));
            busPayDTO.setOrderNo(busOrder.getOrderNo());
            busPayDTO.setPayWay(busOrder.getPayWay());
            if (OrderStatusEnum.FREIGHT_WAIT_PAY.getCode().equals(busOrder.getOrderStatus())) {
                busPayDTO.setPayAmount(BigDecimal.valueOf(busOrder.getFreight()));
            }
            if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay())) {
                //通联的医保切换成通联支付？
                BusPayConfig payConfig = busPayConfigHelper.getPayConfig(busOrder.getHospitalId(), busPayDTO.getAppType());
                busPayDTO.setPayWay(payConfig.getPayWay());
            }
            busPayDTO.setHospitalId(busOrder.getHospitalId());
            checkOpenid(busPayDTO,busOrder.getPartnersCode(),busOrder.getHospitalId(),busOrder.getPatientId());

        }
    }

    /**
     * 支付前处理药品订单信息- 设置对应的就诊人id和患者id
     * @param busPayDTO - 支付参数 -需要设置进去就诊人id和患者id
     * @param busOrder - 订单信息 - 附加查询依据
     */
    private void setPayDtoUserIdAndPatientId(BusPayDTO busPayDTO, BusOrder busOrder) {
        if (BusOrderSubOrderTypeEnum.DRUG_ORDER.getCode().equals(busOrder.getSubOrderType())) {
            BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectById(busOrder.getSubOrderId());
            if (ObjectUtil.isNull(busDrugsOrder)) {
                throw new ServiceException("未查到药品订单信息");
            }
            BusPrescription busPrescription = busPrescriptionMapper.selectById(busDrugsOrder.getPrescriptionId());
            if (ObjectUtil.isNull(busPrescription)) {
                throw new ServiceException("未查到处方信息");
            }
            // 药品订单的下单用户id -原来的patientId
            busPayDTO.setUserId(busOrder.getPatientId());
            // 药品订单的真实患者id -原来的familyId
            busPayDTO.setPatientId(busPrescription.getFamilyId());
        } else if (BusOrderSubOrderTypeEnum.GOODS_ORDER.getCode().equals(busOrder.getSubOrderType())) {
            BusShopOrder busShopOrder = busShopOrderMapper.selectById(busOrder.getSubOrderId());
            if (ObjectUtil.isNull(busShopOrder)) {
                throw new ServiceException("未查到商品信息");
            }
            // 商品订单的下单用户id -原来的patientId
            busPayDTO.setUserId(busOrder.getPatientId());
            // 商品订单的真实患者id -原来的familyId
            busPayDTO.setPatientId(busShopOrder.getFamilyId());
        }
    }


    @Override
    public R<JSONObject> tongLianRefund(BusRefundPayDTO busRefundPayDTO) {
        log.info("通联退款参数busRefundPayDTO ={}", busRefundPayDTO);
        if (busRefundPayDTO.getOrderType().equals(OrderTypeEnum.DRUGS.getCode())) {
            R<BusDrugsOrderVO> data = remoteBusDrugsOrderService.getBusDrugsOrderById(busRefundPayDTO.getOrderId());
            this.remoteCheck(data);
            log.info("通联退款查询药品订单参数 data={}", data.getData());
            BusDrugsOrderVO order = data.getData();
            busRefundPayDTO.setOrderNo(order.getOrderNo());
            busRefundPayDTO.setOldtrxid(order.getTonglianTrxid());
            busRefundPayDTO.setRefundAmount(Double.valueOf(order.getAmount()));
            busRefundPayDTO.setOrderAmount(Double.valueOf(order.getAmount()));
            busRefundPayDTO.setHospitalId(order.getHospitalId());
            busRefundPayDTO.setPayWay(order.getPayWay());
            /*如果订单是:问诊订单*/
        } else if (busRefundPayDTO.getOrderType().equals(OrderTypeEnum.CONSULTATION.getCode())) {
            R<com.puree.hospital.order.api.model.BusConsultationOrder> data = remoteBusConsultationOrderService.getBusConsultationOrderById(busRefundPayDTO.getOrderId());
            this.remoteCheck(data);
            log.info("通联退款查询问诊订单参数 data={}", JSON.toJSONString(data));
            com.puree.hospital.order.api.model.BusConsultationOrder order = data.getData();
            busRefundPayDTO.setOrderNo(order.getOrderNo());
            busRefundPayDTO.setOldtrxid(order.getTonglianTrxid());
            busRefundPayDTO.setOrderAmount(Double.valueOf(order.getAmount()));
            busRefundPayDTO.setRefundAmount(Double.valueOf(order.getAmount()));
            busRefundPayDTO.setHospitalId(order.getHospitalId());
            busRefundPayDTO.setPayWay(order.getPayWay());
            /*如果订单是:服务包订单*/
        } else if (busRefundPayDTO.getOrderType().equals(OrderTypeEnum.SERVICE_PACK.getCode())) {
            R<com.puree.hospital.order.api.model.BusFiveServicePackOrder> data = remoteBusFiveServicePackOrderService.getBusFiveServicePackOrderById(busRefundPayDTO.getOrderId());
            this.remoteCheck(data);
            log.info("通联退款查询服务包订单参数 data={}", JSON.toJSONString(data));
            com.puree.hospital.order.api.model.BusFiveServicePackOrder order = data.getData();
            busRefundPayDTO.setOrderNo(order.getOrderNo());
            busRefundPayDTO.setOldtrxid(order.getTonglianTrxid());
            busRefundPayDTO.setHospitalId(order.getHospitalId());
            busRefundPayDTO.setPayWay(order.getPayWay());
            busRefundPayDTO.setOrderAmount(Double.valueOf(order.getAmount()));
            if (ObjectUtil.isNull(busRefundPayDTO.getRefundAmount())) {
                busRefundPayDTO.setRefundAmount(Double.valueOf(order.getAmount()));
            }
        } else if (busRefundPayDTO.getOrderType().equals(OrderTypeEnum.GOODS.getCode())) {
            // 查询总订单信息
            List<BusOrder> busOrders = busOrderService.selectOrederByNo(busRefundPayDTO.getOrderNo());
            if (CollUtil.isNotEmpty(busOrders)) {
                BusOrder busOrder = busOrders.get(0);
                busRefundPayDTO.setOrderNo(busOrder.getOrderNo());
                busRefundPayDTO.setOldtrxid(busOrder.getTonglianTrxid());
                busRefundPayDTO.setHospitalId(busOrder.getHospitalId());
                busRefundPayDTO.setPayWay(busOrder.getPayWay());
                BusOrderAfterSales afterSales = busOrderAfterSalesMapper.selectById(busRefundPayDTO.getOrderAfterSaleId());
                if (ObjectUtil.isNotNull(afterSales)) {
                    busRefundPayDTO.setRefundAmount(afterSales.getRefundAmount().doubleValue());
                } else {
                    busRefundPayDTO.setRefundAmount(busRefundPayDTO.getRefundAmount());
                }
                busRefundPayDTO.setOrderAmount(busOrders.stream().mapToDouble(BusOrder::getRelPrice).sum());
            }
        }
        BusPayConfig config = busPayConfigHelper.getPayConfigByTransactionId(busRefundPayDTO.getHospitalId(),
                PaysTypeEnum.TONGLIAN_PAY.getCode(), busRefundPayDTO.getOldtrxid());
        busRefundPayDTO.setPayConfigId(config.getId());
        R<JSONObject> refund = remotePayService.refund(busRefundPayDTO);
        if (Constants.FAIL.equals(refund.getCode())) {
            BusOrderAfterSales updateOrderAfterSales = new BusOrderAfterSales();
            updateOrderAfterSales.setRefundStatus(Integer.valueOf(CodeEnum.YES.getCode()));
            updateOrderAfterSales.setId(busRefundPayDTO.getOrderAfterSaleId());
            busOrderAfterSalesMapper.updateById(updateOrderAfterSales);
            throw new ServiceException(refund.getMsg());
        }
        JSONObject data = refund.getData();
        if (SUCCESS.equals(data.getString("trxstatus"))) {
            /*修改问诊订单状态,代办任务状态*/
            if (busRefundPayDTO.getOrderType().equals(OrderTypeEnum.CONSULTATION.getCode())) {
                R<com.puree.hospital.order.api.model.BusConsultationOrder> consultationOrderR = remoteBusConsultationOrderService.getBusConsultationOrderById(busRefundPayDTO.getOrderId());
                this.remoteCheck(consultationOrderR);
                BusConsultationOrder consultationOrder = new BusConsultationOrder();
                BeanUtils.copyProperties(consultationOrderR.getData(), consultationOrder);
                wxPayService.updateStatus(consultationOrder, busRefundPayDTO.getAutomaticRefund());
            }
            tongLianRefundNotify(busRefundPayDTO);
        } else {
            throw new ServiceException(data.getString("errmsg"));
        }
        return refund;
    }

    @Override
    public R<JSONObject> tongLianOrderQuery(BusQueryDTO busQueryDTO) {
        log.info("通联查询参数busQueryDTO ={}", busQueryDTO.toString());
        // TODO - 总订单类型未支持，后续补充
        // 接口兼容
        String orderNo = busQueryDTO.getOrderNo();
        busQueryDTO.setOutTradeNo(orderNo);
        busQueryDTO.setTransactionId(busQueryDTO.getOrderOtherNo());
        busQueryDTO.setPayWay(PaysTypeEnum.TONGLIAN_PAY.getCode());
        if (ObjectUtil.isNull(orderNo)) {
            throw new ServiceException("订单编号为空，通联订单查询失败");
        }
        JSONObject jsonObject = new JSONObject();
        /*如果订单是:药品订单*/
        if (OrderTypeConstant.DRUGS_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
            R<BusDrugsOrderVO> orderByOrderNo = remoteBusDrugsOrderService.getBusDrugsOrderByOrderNo(orderNo);
            this.remoteCheck(orderByOrderNo);
            BusDrugsOrderVO drugsOrder = orderByOrderNo.getData();
            busQueryDTO.setPayWay(drugsOrder.getPayWay());
            busQueryDTO.setReqsn(drugsOrder.getOrderNo());
            busQueryDTO.setTrxid(drugsOrder.getTonglianTrxid());
            busQueryDTO.setStatus(drugsOrder.getStatus());
            String status = drugsOrder.getStatus();
            if (!"0".equals(status) && !"11".equals(status)) {
                jsonObject.put("isTrue", "0");
                return R.ok(jsonObject);
            }
            /*如果订单是:问诊订单*/
        } else if (OrderTypeConstant.CONSULATION_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
            R<com.puree.hospital.order.api.model.BusConsultationOrder> orderRequestR = remoteBusConsultationOrderService.getBusConsultationOrderByOrderNo(orderNo);
            this.remoteCheck(orderRequestR);
            com.puree.hospital.order.api.model.BusConsultationOrder consultationOrder = orderRequestR.getData();
            busQueryDTO.setPayWay(consultationOrder.getPayWay());
            busQueryDTO.setReqsn(consultationOrder.getOrderNo());
            busQueryDTO.setTrxid(consultationOrder.getTonglianTrxid());
            String status;
            if (consultationOrder.getOrderType().equals(String.valueOf(YesNoEnum.NO.getCode()))) {
                status = consultationOrder.getStatus();
                busQueryDTO.setStatus(status);
            } else {
                status = consultationOrder.getVideoStatus();
                busQueryDTO.setStatus(status);
            }
            if (!"0".equals(status) && !"9".equals(status)) {
                jsonObject.put("isTrue", "0");
                return R.ok(jsonObject);
            }
            /*如果订单是:服务包订单*/
        } else if (OrderTypeConstant.SERVICE_PACK_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
            R<com.puree.hospital.order.api.model.BusFiveServicePackOrder> orderByOrderNo = remoteBusFiveServicePackOrderService.getBusFiveServicePackOrderByOrderNo(orderNo);
            this.remoteCheck(orderByOrderNo);
            com.puree.hospital.order.api.model.BusFiveServicePackOrder packOrderResponse = orderByOrderNo.getData();
            busQueryDTO.setPayWay(packOrderResponse.getPayWay());
            busQueryDTO.setReqsn(packOrderResponse.getOrderNo());
            busQueryDTO.setTrxid(packOrderResponse.getTonglianTrxid());
            busQueryDTO.setStatus(packOrderResponse.getStatus());
            String status = packOrderResponse.getStatus();
            if (!"0".equals(status) && !"7".equals(status)) {
                jsonObject.put("isTrue", "0");
                return R.ok(jsonObject);
            }
        }
        // 通联没上小程序-默认走公众号可以的
        BusPayConfig payConfig = busPayConfigHelper.getPayConfig(busQueryDTO.getHospitalId(), busQueryDTO.getAppType());
        busQueryDTO.setPayConfigId(payConfig.getId());
        R<JSONObject> query = remotePayService.query(busQueryDTO);
        if (Constants.FAIL.equals(query.getCode())) {
            throw new ServiceException(query.getMsg());
        }
        JSONObject data = query.getData();
        String trxstatus = data.getString("trxstatus");
        String errmsg = data.getString("errmsg");
        if (SUCCESS.equals(query.getData().getString("trxstatus"))) {
            /*如果订单是:药品订单*/
            String orderStatus = busQueryDTO.getStatus();
            if (OrderTypeConstant.DRUGS_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
                /*待支付*/
                if ("0".equals(orderStatus)) {
                    this.payAfterHandleDrugsOrderInfo(orderNo);
                    /*退款中*/
                } else if ("11".equals(orderStatus)) {
                    this.refundAfterHandleDrugsOrderInfo(orderNo);
                }
                /*如果订单是:问诊订单*/
            } else if (OrderTypeConstant.CONSULATION_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
                /*待支付*/
                if ("0".equals(orderStatus)) {
                    wxPayService.payUpdateConsultationOrderStatus(orderNo);
                    /*退款中*/
                } else if ("9".equals(orderStatus)) {
                    this.refundAfterHandleConsulationOrderInfo(orderNo);
                }
                /*如果订单是:服务包订单*/
            } else if (OrderTypeConstant.SERVICE_PACK_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
                /*待支付*/
                if ("0".equals(orderStatus)) {
                    this.payAfterHandleServicePackOrderInfo(orderNo);
                    /*退款中*/
                } else if ("7".equals(orderStatus)) {
                    this.refundAfterHandleServicePackOrderInfo(orderNo);
                }
            }
        } else if ("2008".equals(query.getData().getString("trxstatus")) || "2000".equals(query.getData().getString("trxstatus"))) {
            log.info("通联订单查询订单号orderNo={}，结果码trxstatus = {},错误消息errmsg ={}", orderNo, trxstatus, errmsg);
            data.put("isTrue", "1");
        } else {
            log.info("通联订单查询订单号orderNo={}，结果码trxstatus = {},错误消息errmsg ={}", orderNo, trxstatus, errmsg);
            data.put("isTrue", "2");
        }
        return query;
    }

    private void tongLianRefundNotify(BusRefundPayDTO busRefundPayDTO) {
        String orderNo = busRefundPayDTO.getOrderNo();
        /*如果订单是:药品订单*/
        if (OrderTypeConstant.DRUGS_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
            this.refundAfterHandleDrugsOrderInfo(orderNo);
            /*如果订单是:问诊订单*/
        } else if (OrderTypeConstant.CONSULATION_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
            this.refundAfterHandleConsulationOrderInfo(orderNo);
            /*如果订单是:服务包订单*/
        } else if (OrderTypeConstant.SERVICE_PACK_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
            this.refundAfterHandleServicePackOrderInfo(orderNo);
        } else if (OrderTypeConstant.TOTAL_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
            wxPayService.refundTotalOrderStatus(busRefundPayDTO.getOrderAfterSaleId(), orderNo);
        }
    }

    /**
     * 支付前处理药品订单信息
     *
     * @param busPayDTO
     */
    @Override
    public void payBeforeHandleDrugsOrderInfo(BusPayDTO busPayDTO) {
        // 查询总订单编号
        BusOrder busOrder = busOrderService.selectOreder(CodeEnum.NO.getCode(), busPayDTO.getOrderId());
        BusDrugsOrder drugsOrder = busDrugsOrderService.selectById(busOrder.getSubOrderId());
        if (drugsOrder == null){
            throw new ServiceException("药品订单不存在");
        }
        BusPrescription busPrescription = busPrescriptionService.getById(drugsOrder.getPrescriptionId());
        if (busPrescription == null){
            throw new ServiceException("处方信息不存在");
        }
        // 设置对应的用户id
        busPayDTO.setUserId(busOrder.getPatientId());
        // 设置对应的就诊人id
        busPayDTO.setPatientId(busPrescription.getFamilyId());
        busPayDTO.setPayAmount(BigDecimal.valueOf(busOrder.getRelPrice()));
        busPayDTO.setOrderNo(busOrder.getOrderNo());
        busPayDTO.setPayWay(busOrder.getPayWay());
        busPayDTO.setBody("药品订单");
    }

    /**
     * 支付前处理服务包订单信息
     *
     * @param busPayDTO
     */

    @Override
    public void payBeforeHandleServicePackOrderInfo(BusPayDTO busPayDTO) {
        R<com.puree.hospital.order.api.model.BusFiveServicePackOrder> data = remoteBusFiveServicePackOrderService
                .getBusFiveServicePackOrderById(busPayDTO.getOrderId());
        this.remoteCheck(data);
        com.puree.hospital.order.api.model.BusFiveServicePackOrder servicePackOrder = data.getData();
        busPayDTO.setBody("服务包订单");
        busPayDTO.setPayAmount(new BigDecimal(servicePackOrder.getAmount()));
        busPayDTO.setOrderNo(servicePackOrder.getOrderNo());
        busPayDTO.setPayWay(servicePackOrder.getPayWay());
        Long hospitalId = servicePackOrder.getHospitalId();
        Long patientId = servicePackOrder.getPatientId();
        // 设置对应的用户id - 原来的patientId
        busPayDTO.setUserId(patientId);
        // 设置对应的就诊人id - 就是原来的familyId
        busPayDTO.setPatientId(servicePackOrder.getFamilyId());
        busPayDTO.setHospitalId(hospitalId);
        checkOpenid(busPayDTO, servicePackOrder.getPartnersCode(), hospitalId, patientId);
    }

    /**
     * 支付前处理问诊订单信息
     *
     * @param busPayDTO
     */
    @Override
    public void payBeforeHandleConsultationOrderInfo(BusPayDTO busPayDTO) {
        R<com.puree.hospital.order.api.model.BusConsultationOrder> data = remoteBusConsultationOrderService
                .getBusConsultationOrderById(busPayDTO.getOrderId());
        this.remoteCheck(data);
        busPayDTO.setBody("问诊订单");
        com.puree.hospital.order.api.model.BusConsultationOrder consultationOrder = data.getData();
        busPayDTO.setPayAmount(new BigDecimal(consultationOrder.getAmount()));
        busPayDTO.setOrderNo(consultationOrder.getOrderNo());
        busPayDTO.setPayWay(consultationOrder.getPayWay());
        Long patientId = consultationOrder.getPatientId();
        // 设置对应的用户id - 原来的patientId
        busPayDTO.setUserId(patientId);
        // 设置对应的就诊人id - 就是原来的familyId
        busPayDTO.setPatientId(consultationOrder.getFamilyId());
        Long hospitalId = consultationOrder.getHospitalId();
        busPayDTO.setHospitalId(hospitalId);
        checkOpenid(busPayDTO, consultationOrder.getPartnersCode(), hospitalId, patientId);
    }

    /**
     * 校验openid-合作机构的切换到合作机构的openid 小程序的切换到小程序的openid
     * @param busPayDTO 通联支付参数
     * @param partnersCode 合作机构编码
     * @param hospitalId 医院id
     * @param patientId 患者id
     */
    private void checkOpenid(BusPayDTO busPayDTO, String partnersCode, Long hospitalId, Long patientId) {
        // 订单有合作机构，需要修改
        if (StringUtils.isNotEmpty(partnersCode)) {
            // 查询机构信息
            LambdaQueryWrapper<BusPartners> queryWrapper = new LambdaQueryWrapper<BusPartners>()
                    .eq(BusPartners::getHospitalId, hospitalId)
                    .eq(BusPartners::getCode, partnersCode);
            BusPartners busPartners = busPartnersMapper.selectOne(queryWrapper);
            log.info("机构信息=" + busPartners.getId());
            String tokenUrl = busPartners.getTokenUrl() + "/pages/order/index";
            busPayDTO.setFrontUrl(tokenUrl);
            // 需要传递appid（微信对应的appid）
            busPayDTO.setAppid(busPartners.getAppId());
            // 查询合作机构患者openid
            String openId = busPatientPartnersService.queryOpenidByCode(partnersCode, hospitalId, patientId);
            busPayDTO.setOpenid(openId);
        } else {
            // appid前端传
            // 查询医院患者openid
            BusPatientHospitalDto dto = new BusPatientHospitalDto();
            dto.setHospitalId(SecurityUtils.getHospitalId());
            dto.setPatientId(SecurityUtils.getUserId());
            BusPatientHospitalVo patientHospitalVo = busPatientHospitalService.selectPatientHospitalByPid(dto);
            if (ClientTypeEnum.isWxUniApp(busPayDTO.getAppType())){
                // 微信小程序用户openid
                busPayDTO.setOpenid(patientHospitalVo.getUniAppOpenid());
            }else {
                // 微信公众号用户openid
                busPayDTO.setOpenid(patientHospitalVo.getOpenid());
            }
        }
    }


    /**
     * 支付后  处理药品订单信息
     *
     * @return
     */
    @Override
    public void payAfterHandleDrugsOrderInfo(String orderNo) {
        BusPrescription prescription;
        /*远程调用   通过订单编号获取订单信息*/
        R<BusDrugsOrderVO> orderByOrderNo = remoteBusDrugsOrderService.getBusDrugsOrderByOrderNo(orderNo);
        this.remoteCheck(orderByOrderNo);
        BusDrugsOrderVO drugsOrder = orderByOrderNo.getData();
        log.info("通联处理药品订单信息支付回调执行了，药品订单信息={}", drugsOrder);
        Long prescriptionId = drugsOrder.getPrescriptionId();
        // 查询包裹信息
        QueryWrapper<BusDrugOrderPackage> wrapper = Wrappers.query();
        wrapper.isNotNull("enterprise_id");
        if (ObjectUtil.isNotNull(prescriptionId)) {
            wrapper.eq("prescription_id", prescriptionId);
            // 查询处方信息
            prescription = busPrescriptionService.selectPrescription(drugsOrder.getHospitalId(), prescriptionId);
            log.info("处方信息={}", prescription);
        } else {
            wrapper.eq("drugs_order_id", drugsOrder.getId());
            prescription = new BusPrescription();
            prescription.setPrescriptionNumber(drugsOrder.getOrderNo());
            prescription.setPrescriptionAmount(new BigDecimal(drugsOrder.getAmount()));
        }
        List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(busDrugOrderPackages)) {
            for (BusDrugOrderPackage orderPackage : busDrugOrderPackages) {
                // 查询配送企业标识
                BusEnterprise busEnterprise = busEnterpriseMapper.selectById(orderPackage.getEnterpriseId());
                if (ObjectUtil.isNotNull(busEnterprise)) {
                    String identifying = busEnterprise.getIdentifying();
                    // 非处方药品包裹信息
                    if (YesNoEnum.NO.getCode().equals(orderPackage.getPackageDrugType())) {
                        LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusOtcDrugs::getHospitalId, drugsOrder.getHospitalId());
                        lambdaQuery.eq(BusOtcDrugs::getEnterpriseId, orderPackage.getEnterpriseId());
                        lambdaQuery.eq(BusOtcDrugs::getDrugsOrderId, drugsOrder.getId());
                        lambdaQuery.eq(BusOtcDrugs::getOtcOrNot, YesNoEnum.YES.getCode());
                        List<BusOtcDrugs> busOtcDrugsList = busOtcDrugsMapper.selectList(lambdaQuery);
                        prescription.setOtcDrugsList(busOtcDrugsList);
                    } else { // 处方药品包裹信息
                        LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusPrescriptionDrugs::getHospitalId, drugsOrder.getHospitalId());
                        lambdaQuery.eq(BusPrescriptionDrugs::getEnterpriseId, orderPackage.getEnterpriseId());
                        lambdaQuery.eq(BusPrescriptionDrugs::getPrescriptionId, prescriptionId);
                        List<BusPrescriptionDrugs> pdDrugsList = busPrescriptionDrugsMapper.selectList(lambdaQuery);
                        prescription.setPdDrugsList(pdDrugsList);
                    }
                    //发送订单
                    prescription.setEnterpriseId(orderPackage.getEnterpriseId());
                    BusDrugsOrder newDrugsOrder = new BusDrugsOrder();
                    BeanUtils.copyProperties(drugsOrder, newDrugsOrder);
                    int order = supplierFactory.getProcessor(identifying).createOrder(newDrugsOrder, prescription);
                    /*如果更新失败则提示需要补单*/
                    if (order == 0) {
                        BusDrugsOrder busDrugOrder = new BusDrugsOrder();
                        busDrugOrder.setId(drugsOrder.getId());
                        busDrugOrder.setIsSendSuccess(YesNoEnum.NO.getCode());
                        busDrugsOrderMapper.updateById(busDrugOrder);
                    }
                }
            }
        }

        // 修改订单状态
        BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
        busDrugsOrder.setId(drugsOrder.getId());
        busDrugsOrder.setOrderNo(orderNo);
        busDrugsOrder.setPaymentTime(DateUtils.getNowDate());
        // 自提
        if (CodeEnum.NO.getCode().equals(drugsOrder.getDeliveryType())) {
            busDrugsOrder.setStatus(DrugsOrderEnum.TOBEPICKEDUP.getCode());
        } else {
            busDrugsOrder.setStatus(DrugsOrderEnum.TO_BE_SHIPPED.getCode());
        }
        BusDrugsOrderRequest drugsOrderRequestDTO = OrikaUtils.convert(busDrugsOrder, BusDrugsOrderRequest.class);
        //更新通联订单号到药品订单表中
        R<Integer> objectR = remoteBusDrugsOrderService.updateBusDrugsOrderById(drugsOrderRequestDTO);
        //发送收费事件
        sendChargeEvent(drugsOrder.getHospitalId(), orderNo);
        if (!Constants.SUCCESS.equals(objectR.getCode())) {
            log.error("通联订单更新失败");
        }
        //患者入组随访(异步处理)
        if (prescription != null && prescription.getId() != null) {
            List<BusPatientOrderInfo> orders = busDrugsOrderMapper.getDrugsIdsByOrderId(drugsOrder.getId(), prescription.getHospitalId());
            List<Long> drugIds = new ArrayList<>(orders.stream().map(BusPatientOrderInfo::getId).collect(Collectors.toList()));
            asyncJoinFollowUpService.joinInFollowUp(new PatientFollowUpJoinRuleDTO(null, drugIds, prescription.getHospitalId(),
                    prescription.getPatientId(), prescription.getFamilyId(), null, orderNo));
        }
    }


    /**
     * 支付后  处理服务包订单信息
     *
     * @return
     */
    @Override
    public void payAfterHandleServicePackOrderInfo(String orderNo) {
        log.info("通联支付成功服务包订单号={}", orderNo);
        //远程调用five模块，处理支付成功后的服务包相关更新
        remoteServicePackOrderService.paySuccessServicePackOrderHandler(orderNo);
    }

    @Override
    public void isFurther(Long furtherConsultationId, Long groupId,String familyName) {
        R<com.puree.hospital.order.api.model.BusConsultationOrder> oldConsulationOrderR = remoteBusConsultationOrderService.getBusConsultationOrderById(furtherConsultationId);
        this.remoteCheck(oldConsulationOrderR);
        com.puree.hospital.order.api.model.BusConsultationOrder oldConsulationOrder = oldConsulationOrderR.getData();
        log.info("被复诊订单oldConsulationOrder={}", oldConsulationOrder);
        List<BusPrescription> busPrescriptions = busPrescriptionMapper.selectList(
                new LambdaQueryWrapper<BusPrescription>()
                        .eq(BusPrescription::getConsultationOrderId, oldConsulationOrder.getId())
                        .in(BusPrescription::getStatus, PrescriptionStatus.PASS, PrescriptionStatus.USED, PrescriptionStatus.INVALID)
                        .orderByDesc(BusPrescription::getReviewTime)
        );
        for (BusPrescription busPrescription : busPrescriptions) {
            if (ObjectUtil.isNotNull(busPrescription.getPaId())) {
                BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(busPrescription.getPaId());
                BusHospitalPaDrugs dto = new BusHospitalPaDrugs();
                dto.setHospitalId(busPrescription.getHospitalId());
                dto.setPaId(busPrescription.getPaId());
                List<BusHospitalPaDrugsVo> busHospitalPaDrugs = busHospitalPaDrugsMapper.selectPaDrugsList(dto);
                busPrescription.setZyfjType(busHospitalPa.getType());
                busPrescription.setBusHospitalPaDrugs(busHospitalPaDrugs);
            } else {
                List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectList(
                        new LambdaQueryWrapper<BusPrescriptionDrugs>()
                                .eq(BusPrescriptionDrugs::getPrescriptionId, busPrescription.getId()));
                busPrescription.setPdDrugsList(busPrescriptionDrugs);
            }
            //发送自定义消息
            JSONObject data1 = new JSONObject();
            data1.put("type", CustomMsgConstants.FURTHER_CONSULTATION);
            data1.put("isFurther", YesNoEnum.NO.getCode());
            data1.put("msg", busPrescription);
            boolean sendGeneralMsg1 = iBusImSendMessageService.sendGeneralMsg(groupId, TencentyunImConstants.TIM_CUSTOM_ELEM,
                    data1, TencentyunImConstants.ADMINISTRATOR,familyName);
            if (!sendGeneralMsg1) {
                throw new ServiceException("发送激活消息失败");
            }
        }
        //发送文本消息
        JSONObject data2 = new JSONObject();
        BusDoctor busDoctor = busDoctorMapper.selectById(oldConsulationOrder.getDoctorId());
        data2.put("text", busDoctor.getFullName() + "医生您好，上面是我的购药需求清单，麻烦您抽空查看确认一下，谢谢您。");
        boolean sendGeneralMsg2 = iBusImSendMessageService.sendGeneralMsg(groupId, TencentyunImConstants.TIM_TEXT_ELEM,
                data2, TencentyunImConstants.PATIENT_IM_ACCOUNT + oldConsulationOrder.getPatientId(),familyName);
        if (!sendGeneralMsg2) {
            throw new ServiceException("发送激活消息失败");
        }
    }


    /**
     * 退款后  处理药品订单信息
     *
     * @return
     */
    @Override
    public void refundAfterHandleDrugsOrderInfo(String orderNo) {
        // 查询服务包订单信息
        R<BusDrugsOrderVO> orderByOrderNo = remoteBusDrugsOrderService.getBusDrugsOrderByOrderNo(orderNo);
        this.remoteCheck(orderByOrderNo);
        BusDrugsOrderVO drugsOrder = orderByOrderNo.getData();
        String doStatus = drugsOrder.getStatus();
        log.info("订单状态={}", doStatus);
        if (!DrugsOrderEnum.REFUNDED.getCode().equals(doStatus)) {
            QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("order_no", orderNo);
            BusAfterSale busAfterSale = busAfterSaleMapper.selectOne(queryWrapper);
            if (ObjectUtil.isNotNull(busAfterSale) && ("全额退款".equals(busAfterSale.getRefundType()) ||
                    "退货且退款".equals(busAfterSale.getRefundType()))) {
                BusDrugsOrder newDrugsOrder = new BusDrugsOrder();
                BeanUtils.copyProperties(drugsOrder, newDrugsOrder);
                busDrugsOrderService.updateRefundStatus(newDrugsOrder, busAfterSale.getOrderStatus());
                sendRefundEvent(drugsOrder.getHospitalId(), orderNo);

                //患者终止随访(异步处理)
                PatientFollowUpJoinRuleDTO dto = new PatientFollowUpJoinRuleDTO();
                dto.setHospitalId(drugsOrder.getHospitalId());
                dto.setOrderNo(orderNo);
                asyncJoinFollowUpService.terminateFollowUp(dto);
            }
        }
    }


    /**
     * 退款后  处理服务包订单信息
     *
     * @return
     */
    @Override
    public void refundAfterHandleServicePackOrderInfo(String orderNo) {
        R<com.puree.hospital.order.api.model.BusFiveServicePackOrder> orderByOrderNo = remoteBusFiveServicePackOrderService.getBusFiveServicePackOrderByOrderNo(orderNo);
        if (Constants.FAIL.equals(orderByOrderNo.getCode())) {
            throw new ServiceException("服务包退款回调失败");
        }
        Object orderNoData = orderByOrderNo.getData();
        ServicePackOrderResponse data = JSON.parseObject(JSON.toJSONString(orderNoData), ServicePackOrderResponse.class);
        log.info("远程调用code={},服务包订单查询结果={}", orderByOrderNo.getCode(), data);
        if (!ServicePackOrderStatusEnum.REFUNDED.getCode().equals(data.getStatus())) {
            // 修改订单状态
            ServicePackOrderRequest request1 = new ServicePackOrderRequest();
            request1.setOrderNo(orderNo);
            request1.setStatus(ServicePackOrderStatusEnum.REFUNDED.getCode());
            request1.setRefundTime(DateUtils.getNowDate());

            if (Arrays.asList(InvoiceStatusEnum.CAN_ISSUE.getStatus(), InvoiceStatusEnum.TO_ISSUE.getStatus()).contains(data.getInvoiceStatus())) {
                BusAfterSale afterSale = busAfterSaleMapper.selectOne(new LambdaQueryWrapper<BusAfterSale>().eq(BusAfterSale::getOrderNo, orderNo));
                BigDecimal refundDecimal = (null == afterSale || CharSequenceUtil.isBlank(afterSale.getRefundAmount())) ? BigDecimal.ZERO : new BigDecimal(afterSale.getRefundAmount());
                BigDecimal amountDecimal = CharSequenceUtil.isBlank(data.getAmount()) ? BigDecimal.ZERO : new BigDecimal(data.getAmount());
                if (0 == amountDecimal.compareTo(refundDecimal)) {
                    // 全额退款
                    request1.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());
                } else {
                    if (InvoiceStatusEnum.TO_ISSUE.getStatus().equals(data.getInvoiceStatus())) {
                        //----------宏捷荣---------------
                        CompletableFuture.runAsync(() -> {
                            BusFiveServicePackOrder fiveServicePackOrder = fiveServicePackOrderMapper.selectOne(new LambdaQueryWrapper<BusFiveServicePackOrder>().eq(BusFiveServicePackOrder::getOrderNo, orderNo));
                            BusInvoiceHeader invoiceHeader = invoiceHeaderMapper.selectById(fiveServicePackOrder.getInvoiceHeaderId());
                            // 发票 - 购买物品信息
                            BusProductInvoiceConfig productInvoiceConfig = productInvoiceConfigMapper.selectOne(new LambdaQueryWrapper<BusProductInvoiceConfig>().eq(BusProductInvoiceConfig::getProductType, ProductTypeInvoiceEnum.CONSULT.getCode()));
                            // 发票 - 卖方信息
                            BusHospitalInvoiceConfig hospitalInvoiceConfig = hospitalInvoiceConfigMapper.selectOne(new LambdaQueryWrapper<BusHospitalInvoiceConfig>().eq(BusHospitalInvoiceConfig::getHospitalId, fiveServicePackOrder.getHospitalId()));
                            if (InvoiceTypeEnum.TAX_CONTROl.getId().equals(hospitalInvoiceConfig.getType())) {
                                try {
                                    fiveServicePackInvoiceService.taxControlIssueInvoice(invoiceHeader, productInvoiceConfig, hospitalInvoiceConfig, fiveServicePackOrder);
                                } catch (IOException | NoSuchPaddingException | NoSuchAlgorithmException |
                                         IllegalBlockSizeException | BadPaddingException | InvalidKeyException |
                                         UnrecoverableKeyException | CertificateException | KeyStoreException |
                                         KeyManagementException e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        });
                        //----------宏捷荣---------------
                    }
                }
            }

            //----------宏捷荣---------------
            if (InvoiceStatusEnum.ISSUE_SUCCESS.getStatus().equals(data.getInvoiceStatus())) {
                fiveServicePackInvoiceService.asyncRedBlueInvoice(orderNo, data.getAmount());
            }
            //----------宏捷荣---------------

            R<Integer> objectR = remoteServicePackOrderService.remoteModifyOrderStatus(request1,
                    SecurityConstants.INNER);
            log.info("远程调用修改服务包订单状态code={}", objectR.getCode());

            //远程调用修改服务包服务记录表，更新服务统计数据--包含im消息推送
            remoteServicePackOrderService.remoteUpdateServicePackPatientRecord(orderNo);

            if (HttpStatus.SUCCESS == objectR.getCode()) {
                // 修改售后状态
                BusAfterSale busAfterSale = new BusAfterSale();
                busAfterSale.setUpdateTime(DateUtils.getNowDate());
                busAfterSale.setStatus(AfterSaleStatus.REFUND_SUCCESS);
                LambdaQueryWrapper<BusAfterSale> wrapper =
                        new LambdaQueryWrapper<BusAfterSale>().eq(BusAfterSale::getOrderNo, orderNo);
                busAfterSaleMapper.update(busAfterSale, wrapper);

                //患者终止随访(异步处理)
                PatientFollowUpJoinRuleDTO dto = new PatientFollowUpJoinRuleDTO();
                dto.setHospitalId(data.getHospitalId());
                dto.setOrderNo(orderNo);
                asyncJoinFollowUpService.terminateFollowUp(dto);
            }
        }
    }

    /**
     * 退款后  处理问诊订单信息
     *
     * @return
     */
    @Override
    public void refundAfterHandleConsulationOrderInfo(String orderNo) {
        log.info("处理通联问诊订单信息退款回调执行了 orderNo{}", orderNo);
        R<com.puree.hospital.order.api.model.BusConsultationOrder> orderRequestR = remoteBusConsultationOrderService.getBusConsultationOrderByOrderNo(orderNo);
        if (Constants.FAIL.equals(orderRequestR.getCode())) {
            throw new ServiceException("问诊支付回调失败");
        }
        com.puree.hospital.order.api.model.BusConsultationOrder consultationOrder = orderRequestR.getData();
        String coStatus;
        // 图文问诊
        if (CodeEnum.NO.getCode().equals(consultationOrder.getOrderType())) {
            coStatus = consultationOrder.getStatus();
        } else { // 视频问诊
            coStatus = consultationOrder.getVideoStatus();
        }
        if (ConsultationOrderStatus.RETURNED.equals(consultationOrder.getPreStatus()) ||
                ConsultationOrderStatus.PAID.equals(consultationOrder.getPreStatus())) {
            coStatus = consultationOrder.getPreStatus();
        }
        log.info("问诊订单状态={}", coStatus);
        if (!ConsultationOrderStatus.REFUNDED.equals(coStatus)) {
            if (ConsultationOrderStatus.PAID.equals(coStatus) || ConsultationOrderStatus.RETURNED.equals(coStatus)) {
                BusConsultationOrder busConsultationOrder = new BusConsultationOrder();
                busConsultationOrder.setUpdateTime(DateUtils.getNowDate());
                busConsultationOrder.setRefundTime(DateUtils.getNowDate());
                // 图文问诊
                if (ConsultationOrderTypeEnum.isImageText(consultationOrder.getOrderType())) {
                    if (ConsultationOrderStatus.RETURNED.equals(coStatus)) {
                        busConsultationOrder.setStatus(ConsultationOrderStatus.RETURNED);
                    } else {
                        busConsultationOrder.setStatus(ConsultationOrderStatus.REFUNDED);
                    }
                } else { // 视频问诊
                    busConsultationOrder.setVideoStatus(ConsultationOrderStatus.REFUNDED);
                }
                QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("order_no", orderNo);
                busConsultationOrder.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());
                busConsultationOrderMapper.update(busConsultationOrder, queryWrapper);
                //后续需要将整个方法放到事件中
                ConsultationRefundSuccessEvent event = new ConsultationRefundSuccessEvent();
                event.setConsultationOrderId(consultationOrder.getId());
                event.setOrderNo(orderNo);
                event.setEventType(BaseConsultationEvent.EventType.REFUND_SUCCESS);
                event.setOriginalStatus(coStatus);
                consultationRefundSuccessEventProducer.send(event);
            } else {
                BusConsultationOrder consultationOrderNew = new BusConsultationOrder();
                BeanUtils.copyProperties(consultationOrder, consultationOrderNew);
                consultationOrderService.updateRefundStatus(consultationOrderNew);
            }
            //发送退号事件
            sendCancelOrderEvent(consultationOrder);
            //发送退费事件
            sendRefundEvent(consultationOrder.getHospitalId(), orderNo);
        }
    }



    /**
     * 回调判断
     *
     * @param r
     */
    void remoteCheck(R r) {
        if (Constants.FAIL.equals(r.getCode())) {
            throw new ServiceException("订单回调失败");
        } else if (null == r.getData()) {
            throw new ServiceException("订单不存在");
        }
    }

    /**
     * 发送取消问诊订单事件
     *
     * @param order 问诊订单信息
     */
    private void sendCancelOrderEvent(com.puree.hospital.order.api.model.BusConsultationOrder order) {
        if (Objects.nonNull(order)) {
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(order.getHospitalId(), RegulatoryEventTypeEnum.CONSULTATION_CANCEL_ORDER, order.getId() + "");
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        }
    }

    /**
     * 发送收费事件
     *
     * @param orderNo 问诊订单信息
     */
    private void sendChargeEvent(Long hospitalId, String orderNo) {
        if (Objects.nonNull(orderNo)) {
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(hospitalId, RegulatoryEventTypeEnum.CHARGE, orderNo);
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        }
    }

    /**
     * 发送退费事件
     *
     * @param orderNo 问诊订单信息
     */
    private void sendRefundEvent(Long hospitalId, String orderNo) {
        if (Objects.nonNull(orderNo)) {
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(hospitalId, RegulatoryEventTypeEnum.REFUND, orderNo);
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        }
    }

}

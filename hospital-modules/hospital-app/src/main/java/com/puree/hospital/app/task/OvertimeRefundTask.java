package com.puree.hospital.app.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationPackage;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusPartners;
import com.puree.hospital.app.infrastructure.wechat.properties.WxPayProperties;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusConsultationPackageMapper;
import com.puree.hospital.app.mapper.BusConsultationSettingsMapper;
import com.puree.hospital.app.mapper.BusPartnersMapper;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.ITongLianPayService;
import com.puree.hospital.app.service.IWxPayService;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.ConsultationOrderStatusEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName OvertimeRefundTask
 * @date 2024/1/9 18:28
 * <AUTHOR>
 * @description OvertimeRefundTask
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OvertimeRefundTask {

    private final IBusConsultationOrderService busConsultationOrderService;
    private final BusConsultationOrderMapper busConsultationOrderMapper;
    private final BusConsultationPackageMapper busConsultationPackageMapper;
    private final BusConsultationSettingsMapper busConsultationSettingsMapper;
    private final IWxPayService wxPayService;
    private final BusPartnersMapper busPartnersMapper;
    private final WxPayProperties wxPayProperties;
    private final ITongLianPayService iTongLianPayService;

    /**
     * 图文问诊订单超时未回复，自动退款
     * 每天12点/24点各执行一次
     */
    @SuppressWarnings("unused")
    @XxlJob("queryNoReplyfund")
    public void queryNoReplyReFund() {
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusConsultationOrder::getOrderType, ConsultationOrderTypeEnum.IMAGETEXT.getCode());
        queryWrapper.eq(BusConsultationOrder::getStatus, ConsultationOrderStatusEnum.VISITING.getCode());
        List<BusConsultationOrder> orders = busConsultationOrderMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(orders)) {
            return;
        }
        Map<Long, BusConsultationSettings> settingsMap = getSettings(orders);
        for (BusConsultationOrder order : orders) {
            BusConsultationSettings settings = settingsMap.get(order.getHospitalId());
            if (Objects.isNull(settings) || Objects.isNull(settings.getImagetextNoreplyExpire())) {
                continue;
            }
            Date now = new Date();
            XxlJobHelper.log("问诊订单超时未回复自动退款的订单={}，当前时间={}", JSONObject.toJSONString(orders), now);
            Date lastUpdateTime = Objects.nonNull(order.getUpdateTime()) ? order.getUpdateTime() : now;

            Date expireTime = DateUtil.offsetHour(lastUpdateTime, settings.getImagetextNoreplyExpire());
            BigDecimal amount = StringUtils.isNotBlank(order.getAmount()) ? new BigDecimal(order.getAmount()) : BigDecimal.ZERO;
            if (amount.compareTo(BigDecimal.ZERO) > 0 && now.after(expireTime)) {
                // 查询问诊包使用次数
                LambdaQueryWrapper<BusConsultationPackage> wrapper = new LambdaQueryWrapper<BusConsultationPackage>()
                        .eq(BusConsultationPackage::getOrderId, order.getId());
                BusConsultationPackage coPackage = busConsultationPackageMapper.selectOne(wrapper);
                if (Objects.nonNull(coPackage) && coPackage.getUseTimes().equals(coPackage.getTotalTimes())) {
                    // 退款
                    refund(order);

                }
            }
        }
    }

    /**
     * 图文问诊订单超时未接诊，自动退款
     * 每天12点/24点各执行一次
     */
    @SuppressWarnings("unused")
    @XxlJob("queryNotReceivedRefund")
    public void queryNotReceivedRefund() {
        // 查询待接诊的订单
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusConsultationOrder::getOrderType, ConsultationOrderTypeEnum.IMAGETEXT.getCode());
        queryWrapper.eq(BusConsultationOrder::getStatus, ConsultationOrderStatusEnum.PAID.getCode());
        List<BusConsultationOrder> orders = busConsultationOrderMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(orders)) {
            return;
        }
        Map<Long, BusConsultationSettings> settingsMap = getSettings(orders);
        for(BusConsultationOrder order : orders) {
            BusConsultationSettings settings = settingsMap.get(order.getHospitalId());
            if (Objects.isNull(settings) || Objects.isNull(settings.getImagetextNoreplyExpire())) {
                continue;
            }
            Date now = new Date();
            XxlJobHelper.log("问诊订单超时未接诊订单={}，当前时间={}", JSONObject.toJSONString(orders), now);
            Date lastUpdateTime = Objects.nonNull(order.getPaymentTime()) ? order.getPaymentTime() : now;
            Date expireTime = DateUtil.offsetHour(lastUpdateTime, settings.getImagetextNoreplyExpire());
            BigDecimal amount = StringUtils.isNotBlank(order.getAmount()) ? new BigDecimal(order.getAmount()) : BigDecimal.ZERO;
            if (amount.compareTo(BigDecimal.ZERO) > 0 && now.after(expireTime)) {
                refund(order);
            }
        }
    }

    /**
     * 获取医院问诊运营配置
     *
     * @param orders 问诊订单列表
     * @return 医院问诊运营配置
     */
    private Map<Long, BusConsultationSettings> getSettings(List<BusConsultationOrder> orders) {
        Map<Long, BusConsultationSettings> settingsMap = new HashMap<>();
        //医院id列表
        List<Long> hospitalIds = orders.stream().map(BusConsultationOrder::getHospitalId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //设置医院运营配置集合
        if (CollectionUtil.isNotEmpty(hospitalIds)) {
            LambdaQueryWrapper<BusConsultationSettings> settingsQueryWrapper = new LambdaQueryWrapper<BusConsultationSettings>()
                    .in(BusConsultationSettings::getHospitalId, hospitalIds);
            List<BusConsultationSettings> settingsList = busConsultationSettingsMapper.selectList(settingsQueryWrapper);
            if (CollectionUtil.isNotEmpty(settingsList)) {
                settingsMap.putAll(settingsList.stream().collect(Collectors.toMap(BusConsultationSettings::getHospitalId, Function.identity())));
            }
        }
        return settingsMap;
    }

    /**
     * 退款
     *
     * @param order 订单
     */
    private void refund(BusConsultationOrder order) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("outTradeNo", order.getOrderNo());
            map.put("refundAmount", new BigDecimal(order.getAmount()));
            map.put("orderAmount", new BigDecimal(order.getAmount()));
            map.put("mchType", "2");
            map.put("hospitalId", order.getHospitalId());
            map.put("automaticRefund", "1");
            map.put("consultationOrder", order);
            if (StringUtils.isNotEmpty(order.getPartnersCode())) {
                // 查询机构信息
                LambdaQueryWrapper<BusPartners> queryWrapper = new LambdaQueryWrapper<BusPartners>()
                        .eq(BusPartners::getHospitalId, order.getHospitalId())
                        .eq(BusPartners::getCode, order.getPartnersCode());
                BusPartners busPartners = busPartnersMapper.selectOne(queryWrapper);
                XxlJobHelper.log("机构信息=" + busPartners.getId());
                //todo 退款通知url 确定没用 待删除
                String tokenUrl = busPartners.getTokenUrl();
                String refundNotifyUrl = tokenUrl.replace(wxPayProperties.getModelName(), "stage-api/app/wxPay/refundNotify");
                map.put("notifyUrl", refundNotifyUrl);
            }
            if (PaysTypeEnum.WECHAT_PAY.getCode().equals(order.getPayWay())){
                JSONObject jsonObject = wxPayService.refund(map);
                XxlJobHelper.log("微信退款结果={}", jsonObject);
            } else {
                BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
                busRefundPayDTO.setOrderId(order.getId());
                busRefundPayDTO.setOrderType(OrderTypeEnum.CONSULTATION.getCode());
                R<JSONObject> objectR = iTongLianPayService.tongLianRefund(busRefundPayDTO);
                XxlJobHelper.log("通联退款结果={}，退款信息={}", objectR.getCode(), objectR.getData());
            }
        } catch (Exception e) {
            log.error("退款失败，订单号={}，退款金额={}", order.getOrderNo(), order.getAmount());
        }

    }

}

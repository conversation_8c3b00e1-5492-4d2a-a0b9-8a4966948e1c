package com.puree.hospital.app.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusInvoiceHeader;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderAfterSales;
import com.puree.hospital.app.domain.dto.ConsultationInvoiceDTO;
import com.puree.hospital.app.domain.dto.InvoiceHeaderDTO;
import com.puree.hospital.app.domain.dto.OrderInvoiceDTO;
import com.puree.hospital.app.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.service.impl.BusConsultationInvoiceService;
import com.puree.hospital.app.service.impl.BusHospitalInvoiceService;
import com.puree.hospital.app.service.impl.BusInvoiceHeaderService;
import com.puree.hospital.app.service.impl.BusOrderInvoiceService;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.math.BigDecimal;
import java.security.*;
import java.security.cert.CertificateException;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName: BusInvoiceController
 * @Date 2023/10/23 18:12
 * <AUTHOR> jian
 * @Description: 申请开票
 * @Version 1.0
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping("/invoice")
public class BusInvoiceController extends BaseController {

    public final BusInvoiceHeaderService invoiceHeaderService ;
    public final BusConsultationInvoiceService consultationInvoiceService;
    public final BusOrderInvoiceService orderInvoiceService ;
    public final BusHospitalInvoiceService hospitalInvoiceService;





    @Log(title = "新增抬头")
    @PostMapping("/header/add")
    public AjaxResult addInvoiceHeader(@RequestBody InvoiceHeaderDTO req) {
        return AjaxResult.success(invoiceHeaderService.addInvoiceHeader(req)) ;
    }

    @Log(title = "删除抬头")
    @PostMapping("/header/delete")
    public AjaxResult deleteInvoiceHeader(@RequestParam Long id) {
        invoiceHeaderService.deleteInvoiceHeader(id);
        return AjaxResult.success() ;
    }

    @Log(title = "修改抬头")
    @PostMapping("/header/update")
    public AjaxResult updateInvoiceHeader(@RequestBody InvoiceHeaderDTO req) {
        invoiceHeaderService.updateInvoiceHeader(req);
        return AjaxResult.success() ;
    }

    @Log(title = "分页查询发票抬头")
    @PostMapping("/header/page")
    public TableDataInfo pageInvoiceHeader(@RequestParam Long hospitalId, @RequestParam Long patientId) {
        startPage();
        List<BusInvoiceHeader> invoiceHeaderList = invoiceHeaderService.listInvoiceHeader(hospitalId, patientId);
        return getDataTable(invoiceHeaderList) ;
    }

    @Log(title = "查询所有发票抬头")
    @PostMapping("/header/list")
    public AjaxResult listInvoiceHeader(@RequestParam Long hospitalId, @RequestParam Long patientId) {
        List<BusInvoiceHeader> invoiceHeaderList = invoiceHeaderService.listInvoiceHeader(hospitalId, patientId);
        return AjaxResult.success(invoiceHeaderList) ;
    }

    @Log(title = "查询发票抬头详情")
    @PostMapping("/header/detail")
    public AjaxResult findInvoiceHeaderDetail(@RequestParam Long id) {
        return AjaxResult.success( invoiceHeaderService.findInvoiceHeaderDetail(id) ) ;
    }

    @Log(title = "问诊单-申请发票")
    @PostMapping("/consultation/apply")
    public AjaxResult applyConsultationInvoice(@RequestBody ConsultationInvoiceDTO req) throws UnrecoverableKeyException, NoSuchPaddingException, IllegalBlockSizeException, CertificateException, IOException, NoSuchAlgorithmException, BadPaddingException, KeyStoreException, InvalidKeyException, KeyManagementException {
        consultationInvoiceService.applyConsultationInvoice(req);
        return AjaxResult.success() ;
    }

    @Log(title = "药品商品订单-申请发票")
    @PostMapping("/order/apply")
    public AjaxResult applyOrderInvoice(@RequestBody OrderInvoiceDTO req) throws UnrecoverableKeyException, NoSuchPaddingException, IllegalBlockSizeException, CertificateException, IOException, NoSuchAlgorithmException, BadPaddingException, KeyStoreException, InvalidKeyException, KeyManagementException {
        orderInvoiceService.applyOrderInvoice(req);
        return AjaxResult.success() ;
    }

    @Log(title = "问诊单,商品药品订单的发票显示权限")
    @PostMapping("/order/get-hospital-invoice-status")
    public AjaxResult getHospitalInvoiceStatus(@RequestParam Long hospitalId ) {
        return AjaxResult.success(hospitalInvoiceService.getHospitalInvoiceStatus(hospitalId)) ;
    }

    @Log(title = "商品药品订单的蓝色,红冲发票显示")
    @PostMapping("/order/get-order-invoice")
    public AjaxResult getOrderInvoice(@RequestParam String orderNo ) {
        return AjaxResult.success(orderInvoiceService.getOrderInvoice(orderNo)) ;
    }

    /*
    *  仅仅用于测试，不对外暴露
    * */
    private final BusOrderMapper orderMapper;
    private final BusOrderAfterSalesMapper orderAfterSalesMapper ;
    @PostMapping("/order/test-taxControlOrderRedInvoiceAndThenBlueInvoice")
    public AjaxResult testTaxControlOrderRedInvoiceAndThenBlueInvoice(@RequestParam String orderNo , @RequestParam Long orderAfterSaleId ) throws UnrecoverableKeyException, NoSuchPaddingException, IllegalBlockSizeException, CertificateException, IOException, NoSuchAlgorithmException, BadPaddingException, KeyStoreException, InvalidKeyException, KeyManagementException {

        List<BusOrder> busOrderList = orderMapper.selectList( new LambdaQueryWrapper<BusOrder>().eq(BusOrder::getOrderNo, orderNo)) ;

        BusOrderAfterSales currentOrderAfterSales = orderAfterSalesMapper.selectById(orderAfterSaleId);
        BigDecimal totalRefund = currentOrderAfterSales.getRefundAmount() ;

        List<BusOrderAfterSales> orderAfterSalesList = orderAfterSalesMapper.selectList(new LambdaQueryWrapper<BusOrderAfterSales>().eq(BusOrderAfterSales::getOrderNo, orderNo)
                .eq(BusOrderAfterSales::getAfterSalesStatus, "7").in(BusOrderAfterSales::getRefundType, Arrays.asList("0", "1")) ); // 7-售后结束 ;  0仅退款 1退货退款

        if (null!=orderAfterSalesList) {
            for ( BusOrderAfterSales e : orderAfterSalesList) {
                if (null!=e.getRefundAmount()) {
                    totalRefund = totalRefund.add(e.getRefundAmount()) ;
                }
            }
        }

        BigDecimal relPriceDecimal = (null == busOrderList.get(0).getRelPrice()) ? BigDecimal.ZERO :  new BigDecimal(String.valueOf(busOrderList.get(0).getRelPrice()));
        BigDecimal freightDecimal = (null == busOrderList.get(0).getFreight()) ? BigDecimal.ZERO :  new BigDecimal(String.valueOf(busOrderList.get(0).getFreight()));

        Boolean isAllRefund =false ;
        if ( 0==totalRefund.compareTo(relPriceDecimal.subtract(freightDecimal))
                || 0==totalRefund.compareTo(relPriceDecimal)) {
            isAllRefund = true ;
        }

        currentOrderAfterSales.setAfterSalesStatus("7");
        orderAfterSalesMapper.updateById(currentOrderAfterSales) ;
        orderInvoiceService.taxControlOrderRedInvoiceAndThenBlueInvoice(busOrderList,  orderAfterSaleId, isAllRefund) ;
        return AjaxResult.success() ;
    }









}

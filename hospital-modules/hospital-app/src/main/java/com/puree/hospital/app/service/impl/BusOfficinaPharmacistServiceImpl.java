package com.puree.hospital.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.dto.DispensingPharmacistDTO;
import com.puree.hospital.app.domain.vo.BusDoctorHospitalVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.BusOfficinaPharmacistVo;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusOfficinaPharmacistMapper;
import com.puree.hospital.app.mapper.BusSignatureMapper;
import com.puree.hospital.app.service.IBusOfficinaPharmacistService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.SignatureRole;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class BusOfficinaPharmacistServiceImpl implements IBusOfficinaPharmacistService {
    private final BusOfficinaPharmacistMapper busOfficinaPharmacistMapper;
    private final BusDoctorMapper busDoctorMapper;
    private final BusSignatureMapper busSignatureMapper;
    private final BusHospitalMapper busHospitalMapper;

    private final static String SENSITIVE_PASSWORD = "******";

    @Autowired
    public BusOfficinaPharmacistServiceImpl(BusOfficinaPharmacistMapper busOfficinaPharmacistMapper,
                                            BusDoctorMapper busDoctorMapper, BusSignatureMapper busSignatureMapper,
                                            BusHospitalMapper busHospitalMapper) {
        this.busOfficinaPharmacistMapper = busOfficinaPharmacistMapper;
        this.busDoctorMapper = busDoctorMapper;
        this.busSignatureMapper = busSignatureMapper;
        this.busHospitalMapper = busHospitalMapper;
    }

    @Override
    public BusOfficinaPharmacist selectPharmacistByPhone(String phoneNumber) {
        LambdaQueryWrapper<BusOfficinaPharmacist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusOfficinaPharmacist::getPhoneNumber, phoneNumber);
        queryWrapper.eq(BusOfficinaPharmacist::getDelStatus, "0");
        return busOfficinaPharmacistMapper.selectOne(queryWrapper);
    }

    @Override
    public BusOfficinaPharmacist selectPharmacistByIdNumber(String idNo) {
        LambdaQueryWrapper<BusOfficinaPharmacist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusOfficinaPharmacist::getIdNumber, DESUtil.encrypt(idNo));
        queryWrapper.eq(BusOfficinaPharmacist::getDelStatus, "0");
        queryWrapper.last(" limit 1");
        return busOfficinaPharmacistMapper.selectOne(queryWrapper);
    }

    @Override
    public int changePassWord(BusSignature busSignature) {
        QueryWrapper<BusSignature> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("object_id", busSignature.getObjectId());
        queryWrapper.eq("object_type", busSignature.getObjectType());
        BusSignature signature = busSignatureMapper.selectOne(queryWrapper);
        String passWord = busSignature.getPassword();
        if (SecurityUtils.matchesPassword(passWord, signature.getPassword())) {
            throw new ServiceException("密码相同,无法修改");
        }
        busSignature.setPassword(SecurityUtils.encryptPassword(passWord));
        busSignature.setUpdateTime(DateUtils.getNowDate());
        UpdateWrapper<BusSignature> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("object_id", busSignature.getObjectId());
        updateWrapper.eq("object_type", busSignature.getObjectType());
        return busSignatureMapper.update(busSignature, updateWrapper);
    }

    @Override
    public int updatePhoneNumber(String phoneNumber, String oldPhoneNumber, Long pharmacistId) {
        LambdaQueryWrapper<BusDoctor> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusDoctor::getPhoneNumber, phoneNumber);
        BusDoctor doctor = busDoctorMapper.selectOne(queryWrapper.last("limit 1"));
        if (Objects.nonNull(doctor)) {
            throw new ServiceException("手机号已被注册,无法修改");
        }
        LambdaQueryWrapper<BusOfficinaPharmacist> pharmacistQueryWrapper = new LambdaQueryWrapper<>();
        pharmacistQueryWrapper.eq(BusOfficinaPharmacist::getPhoneNumber, phoneNumber);
        pharmacistQueryWrapper.eq(BusOfficinaPharmacist::getDelStatus, "0");
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectOne(pharmacistQueryWrapper.last("limit 1"));
        if (Objects.nonNull(pharmacist) && !pharmacist.getId().equals(pharmacistId)) {
            throw new ServiceException("手机号已被注册,无法修改");
        }
        BusOfficinaPharmacist busOfficinaPharmacist = new BusOfficinaPharmacist();
        busOfficinaPharmacist.setUpdateTime(DateUtils.getNowDate());
        busOfficinaPharmacist.setPhoneNumber(phoneNumber);
        busOfficinaPharmacist.setId(pharmacistId);
        return busOfficinaPharmacistMapper.updateById(busOfficinaPharmacist);
    }

    @Override
    public List<BusOfficinaPharmacist> selectList(BusOfficinaPharmacist busOfficinaPharmacist) {
        return busOfficinaPharmacistMapper.selectOfficinaPharmacistList(busOfficinaPharmacist);
    }

    /**
     * 复核药师设置登录密码
     *
     * @param dto 登录信息
     * @return 设置结果
     */
    @Override
    public Integer modifyPassword(DispensingPharmacistDTO dto) {
        // 通过复核药师ID设置密码
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectById(SecurityUtils.getUserId());
        checkReq(pharmacist, dto.getOldPassword(), dto.getPassword());
        String encryptedPassword = SecurityUtils.encryptPassword(dto.getPassword());
        BusOfficinaPharmacist update = new BusOfficinaPharmacist();
        update.setId(pharmacist.getId());
        update.setPassword(encryptedPassword);
        return busOfficinaPharmacistMapper.updateById(update);
    }

    private static void checkReq(BusOfficinaPharmacist busOfficinaPharmacist, String oldPassword, String password) {
        if (Objects.isNull(busOfficinaPharmacist)) {
            throw new ServiceException("请联系管理员修改密码！");
        }
        if (StringUtils.isNotEmpty(busOfficinaPharmacist.getPassword())) {
            if (StringUtils.isEmpty(oldPassword)) {
                throw new ServiceException("请输入原密码！");
            }
            if (!SecurityUtils.matchesPassword(oldPassword, busOfficinaPharmacist.getPassword())) {
                throw new ServiceException("原密码错误，请重新输入！");
            }
        }
        if (StringUtils.isEmpty(password)) {
            throw new ServiceException("请输入新密码！");
        }
    }

    /**
     * 获取复核药师信息
     *
     * @param id 复核药师id
     * @return 复核药师信息
     */
    @Override
    public BusDoctorVo getReviewPharmacistInfoById(Long id) {
        // 查询复核药师信息 未删除
        LambdaQueryWrapper<BusOfficinaPharmacist> queryWrapper = new LambdaQueryWrapper<BusOfficinaPharmacist>()
                .eq(BusOfficinaPharmacist::getId, id)
                .eq(BusOfficinaPharmacist::getDelStatus, YesNoEnum.NO.getCode())
                .last("limit 1");
        BusOfficinaPharmacist busOfficinaPharmacist = busOfficinaPharmacistMapper.selectOne(queryWrapper);
        if (Objects.isNull(busOfficinaPharmacist)) {
            throw new ServiceException("未查询到该复核药师，或已被禁用！");
        }
        // 查询签名
        LambdaQueryWrapper<BusSignature> querySignature = new LambdaQueryWrapper<BusSignature>()
                .eq(BusSignature::getObjectId, busOfficinaPharmacist.getId())
                .eq(BusSignature::getObjectType, SignatureRole.CHEMIST.getValue())
                .last("limit 1");
        BusSignature busSignature = busSignatureMapper.selectOne(querySignature);
        if (Objects.nonNull(busSignature)) {
            busOfficinaPharmacist.setPharmacistSignature(busSignature.getCertSignature());
        }
        // 查询医院信息
        BusHospital busHospital = busHospitalMapper.selectById(busOfficinaPharmacist.getHospitalId());
        if (Objects.isNull(busHospital)) {
            throw new ServiceException("医院信息缺失");
        }
        return assembledResult(busOfficinaPharmacist, busHospital);
    }

    /**
     * 获取当前登录的药师信息
     *
     * @param userid id
     * @return 药师信息
     */
    @Override
    public BusOfficinaPharmacistVo getInfo(Long userid) {
        LambdaQueryWrapper<BusOfficinaPharmacist> queryWrapper = new LambdaQueryWrapper<BusOfficinaPharmacist>()
                .eq(BusOfficinaPharmacist::getId, userid)
                .eq(BusOfficinaPharmacist::getHospitalId, SecurityUtils.getHospitalId())
                .eq(BusOfficinaPharmacist::getDelStatus, YesNoEnum.NO.getCode());
        BusOfficinaPharmacist busOfficinaPharmacist = busOfficinaPharmacistMapper.selectOne(queryWrapper, false);
        if (Objects.isNull(busOfficinaPharmacist)) {
            throw new ServiceException("药师信息缺失");
        }
        BusOfficinaPharmacistVo vo = BeanUtil.copyProperties(busOfficinaPharmacist, BusOfficinaPharmacistVo.class);
        vo.setPhoneNumber(DESUtil.decrypt(vo.getPhoneNumber()));
        if (StringUtils.isNotEmpty(vo.getPassword())) {
            vo.setPassword(SENSITIVE_PASSWORD);
        }
        return vo;
    }

    private static BusDoctorVo assembledResult(BusOfficinaPharmacist busOfficinaPharmacist, BusHospital busHospital) {
        BusDoctorVo doctorVo = new BusDoctorVo();
        doctorVo.setId(busOfficinaPharmacist.getId());
        doctorVo.setFullName(busOfficinaPharmacist.getPharmacistName());
        doctorVo.setSex(Integer.valueOf(busOfficinaPharmacist.getPharmacistSex()));
        doctorVo.setPhoto(busOfficinaPharmacist.getPharmacistAvatar());
        doctorVo.setTitleValue(AppRoleEnum.DISPENSING_PHARMACIST.getInfo());
        doctorVo.setPhoneNumber(DESUtil.decrypt(busOfficinaPharmacist.getPhoneNumber()));
        doctorVo.setIdCardNo(busOfficinaPharmacist.getIdNumber());
        doctorVo.setIdCardNoImg(busOfficinaPharmacist.getPharmacistIdCard());
        doctorVo.setDoctorNumber(busOfficinaPharmacist.getPharmacistNumber());
        doctorVo.setCertSignature(busOfficinaPharmacist.getPharmacistSignature());
        doctorVo.setHospitalId(busOfficinaPharmacist.getHospitalId());
        doctorVo.setHospitalName(busHospital.getHospitalName());
        doctorVo.setCreateBy(busOfficinaPharmacist.getCreateBy());
        doctorVo.setUpdateBy(busOfficinaPharmacist.getUpdateBy());
        doctorVo.setCreateTime(busOfficinaPharmacist.getCreateTime());
        doctorVo.setUpdateTime(busOfficinaPharmacist.getUpdateTime());
        BusDoctorHospitalVo busDoctorHospitalVo = BeanUtil.copyProperties(busHospital, BusDoctorHospitalVo.class);
        busDoctorHospitalVo.setHospitalId(busOfficinaPharmacist.getHospitalId());
        busDoctorHospitalVo.setDoctorName(busOfficinaPharmacist.getPharmacistName());
        doctorVo.setDoctorHospitalList(Collections.singletonList(busDoctorHospitalVo));
        return doctorVo;
    }

}

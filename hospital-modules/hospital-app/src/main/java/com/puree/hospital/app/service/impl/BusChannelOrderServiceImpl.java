package com.puree.hospital.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.puree.hospital.app.domain.BusChannelOrder;
import com.puree.hospital.app.domain.BusChannelPatientAgentRelation;
import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.domain.vo.ChannelPartnerAndAgentInfoVO;
import com.puree.hospital.app.enums.ChannelOrderCommissionStatus;
import com.puree.hospital.app.mapper.BusChannelOrderMapper;
import com.puree.hospital.app.service.IBusChannelOrderService;
import com.puree.hospital.app.service.IBusChannelPartnerService;
import com.puree.hospital.app.service.IBusChannelPatientAgentRelationService;
import com.puree.hospital.common.core.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/10/28 15:36
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusChannelOrderServiceImpl implements IBusChannelOrderService {
    @Resource
    private BusChannelOrderMapper channelOrderMapper;
    private final IBusChannelPatientAgentRelationService busChannelPatientAgentRelationService;
    private final IBusChannelPartnerService busChannelPartnerService;

    @Override
    public void insertChannelOrder(BusChannelOrder channelOrder) {
        try {
            channelOrderMapper.insert(channelOrder);
        } catch (DuplicateKeyException e) {
            log.warn("重复插入订单，orderNo: {},orderId: {}, hospitalId: {}", channelOrder.getOrderNo(), channelOrder.getOrderId(), channelOrder.getHospitalId());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createBusChannelOrderByOrderDTO(BusOrderDto busOrderDto) {
        BusChannelPatientAgentRelation relation = busChannelPatientAgentRelationService.selectByPatientIdAndHospitalId(busOrderDto.getPatientId(), busOrderDto.getHospitalId());
        if (Objects.isNull(relation)) {
            // 如果没有经纪人关系，则不需要创建合作渠道订单 - 直接返回true
            return true;
        }
        ChannelPartnerAndAgentInfoVO channelInfo = busChannelPartnerService.selectChannelInfo(relation.getAgentId());
        if (ObjectUtil.isNull(channelInfo)) {
            // 如果没有经纪人和合伙人信息，则无法创建合作渠道订单
            log.error("没有找到合作渠道信息，无法创建合作渠道订单,patientId: {},  agentId: {}, hospitalId: {}", busOrderDto.getPatientId(),
                    relation.getAgentId(), busOrderDto.getHospitalId());
            return false;
        }
        // 有绑定关系，则创建合作渠道订单
        if (Objects.isNull(busOrderDto.getOrderType())) {
            // 如果订单类型都不存在，则不创建合作渠道订单
            log.error("订单号和订单类型都不存在，无法创建合作渠道订单, patientId: {}, agentId: {}, hospitalId: {}", busOrderDto.getPatientId(),
                        relation.getAgentId(), busOrderDto.getHospitalId());
            return false;
        }
        BusChannelOrder channelOrder = getBusChannelOrder(busOrderDto, channelInfo);
        if (Integer.valueOf(1).compareTo(busOrderDto.getOrderType()) == 0 && busOrderDto.isHasShopOrder()) {
            // 只有类型为总订单的 ， 然后子类型为商品订单的情况下，才标记合作渠道订单的佣金状态为创建，后续才会走佣金结算流程
            channelOrder.setCommissionStatus(ChannelOrderCommissionStatus.CREATED.name());
        }
        insertChannelOrder(channelOrder);
        return true;
    }

    /**
     * 获取合作渠道订单
     * @param busOrderDto 订单信息
     * @param channelInfo 渠道信息
     * @return 合作渠道订单
     */
    private @NotNull BusChannelOrder getBusChannelOrder(BusOrderDto busOrderDto, ChannelPartnerAndAgentInfoVO channelInfo) {
        BusChannelOrder channelOrder = new BusChannelOrder();
        // 设置合作渠道订单- 渠道信息
        setChannelOrderChannelInfo(channelOrder, channelInfo);
        // 设置合作渠道订单 订单关联信息
        channelOrder.setOrderNo(busOrderDto.getOrderNo());
        channelOrder.setHospitalId(busOrderDto.getHospitalId());
        // 总订单的情况下，订单号只记录第一个总订单的id，后续的总订单id不记录，查询只能根据订单号来查询了
        channelOrder.setOrderId(busOrderDto.getId());
        channelOrder.setType(busOrderDto.getOrderType());
        channelOrder.setCreateTime(DateUtils.getNowDate());
        if (Objects.nonNull(busOrderDto.getGive())) {
            channelOrder.setGive(busOrderDto.getGive());
        }
        return channelOrder;
    }

    /**
     * 设置合作渠道订单的渠道信息
     * @param channelOrder 合作渠道订单
     * @param channelInfo 渠道信息
     */
    private void setChannelOrderChannelInfo(BusChannelOrder channelOrder, ChannelPartnerAndAgentInfoVO channelInfo) {
        channelOrder.setAgentId(channelInfo.getAgentId());
        channelOrder.setAgentName(channelInfo.getAgentName());
        channelOrder.setPartnerName(channelInfo.getChannelPartnerName());
        channelOrder.setPartnerId(channelInfo.getChannelPartnerId());
        channelOrder.setAgentStatusSnapshot(channelInfo.getAgentStatus());
        channelOrder.setPartnerStatusSnapshot(channelInfo.getChannelPartnerStatus());
        channelOrder.setPartnerIdentityId(channelInfo.getChannelIdentityId());
        channelOrder.setAgentIdentityId(channelInfo.getAgentIdentityId());
    }
}

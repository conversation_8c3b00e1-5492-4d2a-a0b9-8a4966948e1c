package com.puree.hospital.app.his.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.dto.BusPatientMedicalRecordDTO;
import com.puree.hospital.app.domain.vo.BusExamineReportDetail;
import com.puree.hospital.app.domain.vo.BusExamineReportDetailVO;
import com.puree.hospital.app.domain.vo.BusInspectReportDetail;
import com.puree.hospital.app.domain.vo.BusPatientRegRecordVO;
import com.puree.hospital.app.his.constants.HisConstant;
import com.puree.hospital.app.his.domain.HisPatientInfo;
import com.puree.hospital.app.his.domain.HisQueryContext;
import com.puree.hospital.app.his.domain.query.HisPatientInfoQuery;
import com.puree.hospital.app.his.domain.vo.BusEmrDetailVO;
import com.puree.hospital.app.his.domain.vo.BusPrescriptionInfoVO;
import com.puree.hospital.app.his.domain.vo.HisDrugsVO;
import com.puree.hospital.app.his.domain.vo.KbjEmrVO;
import com.puree.hospital.app.his.domain.vo.KbjExamineReportInfoVO;
import com.puree.hospital.app.his.domain.vo.KbjInspectReportInfoVO;
import com.puree.hospital.app.his.domain.vo.KbjPatientMedicalRecordsVO;
import com.puree.hospital.app.his.domain.vo.KbjPatientVO;
import com.puree.hospital.app.his.domain.vo.KbjPrescriptionVO;
import com.puree.hospital.app.his.enums.HisPrescriptionTypeEnum;
import com.puree.hospital.app.his.helper.HospitalHisHelper;
import com.puree.hospital.app.his.helper.KbjHisHelper;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 康博嘉患者病历信息实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/15 21:55
 */
@Slf4j
@AllArgsConstructor(onConstructor = @__(@Autowired))
@Component
public class KbjPatientMedicalRecordHandler extends BasePatientMedicalRecordHandler {

    private final KbjHisHelper kbjHisHelper;
    private final HospitalHisHelper hospitalHisHelper;

    /**
     * 获取患者就诊记录列表
     *
     * @param queryContext 查询参数上下文
     * @return 就诊记录列表
     */
    @Override
    public List<BusPatientRegRecordVO> getPatientMedicalRecordList(HisQueryContext queryContext) {
        initQueryParam(queryContext);
        // 查询HIS就诊记录
        List<KbjPatientMedicalRecordsVO> hisMedicalRecords = kbjHisHelper.getHisMedicalRecords(queryContext);
        if (CollectionUtil.isEmpty(hisMedicalRecords)) {
            log.debug("未查询到患者就诊记录，患者ID: {}", queryContext.getHisPatientId());
            return Collections.emptyList();
        }
        return hisMedicalRecords.stream()
                .filter(this::isValidRecord)
                .map(this::convertToBusPatientRegRecordVO)
                .collect(Collectors.toList());
    }

    /**
     *  获取电子病历详情
     * @param queryContext  查询上下文
     * @return  电子病历详情
     */
    @Override
    public List<BusEmrDetailVO> getEmrDetail(HisQueryContext queryContext) {
        if (Objects.isNull(queryContext.getPatientFamily())) {
            throw new ServiceException("请选择患者");
        }
        KbjEmrVO kbjEmrInfo = kbjHisHelper.getKbjEmrInfo(queryContext);
        if (Objects.isNull(kbjEmrInfo)) {
            return Collections.emptyList();
        }
        String consultationTime = DateUtil.format(DateUtil.parse(kbjEmrInfo.getCreatedDatetime()), DatePattern.NORM_DATETIME_FORMAT);
        String diagnosticsTime = DateUtil.format(DateUtil.parse(kbjEmrInfo.getLastUpdatedDatetime()), DatePattern.NORM_DATETIME_FORMAT);
        log.debug("电子病历详情结果：{}", kbjEmrInfo);
        String patientAge = null;
        if (queryContext.getPatientFamily().getDateOfBirth() != null) {
            patientAge = DateUtils.getAge(queryContext.getPatientFamily().getDateOfBirth()) + "岁";
        }
        List<BusEmrDetailVO> basicInfoList = assembleBasicInfo(kbjEmrInfo.getDoctorName(), kbjEmrInfo.getClinicDesc(), getHospitalName(queryContext.getHospitalId()),
                kbjEmrInfo.getPatientName(), patientAge, "0".equals(queryContext.getPatientFamily().getSex()) ? "女" : "男", kbjEmrInfo.getVisitNo(),
                queryContext.getPatientFamily().getCellPhoneNumber(), consultationTime, diagnosticsTime);
        List<BusEmrDetailVO> result = kbjEmrInfo.getEmrItemList().stream().map(item -> {
                    BusEmrDetailVO busEmrDetailVO = new BusEmrDetailVO();
                    busEmrDetailVO.setTitle(item.getEmrItemDesc());
                    busEmrDetailVO.setContent(item.getEmrItemContent());
                    return busEmrDetailVO;
                })
                .collect(Collectors.toList());
        return assembleEmrVO(basicInfoList, result, BusEmrDetailVO::getTitle, BusEmrDetailVO::getContent);

    }

    /**
     * 获取患者检查记录详情
     *
     * @param queryContext 查询参数上下文
     * @return 检查记录详情
     */
    @Override
    public List<BusInspectReportDetail> getInspectDetail(HisQueryContext queryContext) {
        initQueryParam(queryContext);
        KbjInspectReportInfoVO kbjInspectReportInfo = kbjHisHelper.getKbjInspectReportInfo(queryContext);
        List<KbjInspectReportInfoVO.ExamItem> examItemList = kbjInspectReportInfo.getExamItemList();
        if (CollUtil.isEmpty(examItemList)) {
            return Collections.emptyList();
        }
        return examItemList.stream().map(examItem -> {
                    BusInspectReportDetail inspectReportDetail = new BusInspectReportDetail();
                    inspectReportDetail.setItemName(examItem.getOrderItemDesc());
                    inspectReportDetail.setConclusion(examItem.getExamItemDesc());
                    inspectReportDetail.setItemResult(examItem.getExamItemDescLang1());
                    return inspectReportDetail;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取患者检验记录详情
     *
     * @param queryContext 查询参数上下文
     * @return 检验记录详情
     */
    @Override
    public BusExamineReportDetailVO getExamineDetail(HisQueryContext queryContext) {
        initQueryParam(queryContext);
        KbjExamineReportInfoVO kbjExamineReportInfo = kbjHisHelper.getKbjExamineReportInfo(queryContext);
        if (log.isDebugEnabled()) {
            log.debug("获取患者检验记录详情-查询结果：{}", kbjExamineReportInfo);
        }
        List<KbjExamineReportInfoVO.Subitem> subitemList = kbjExamineReportInfo.getSubitemList();
        BusExamineReportDetailVO vo = new BusExamineReportDetailVO();
        List<BusExamineReportDetail> reportDetails = subitemList.stream().map(subitem -> {
            BusExamineReportDetail detail = new BusExamineReportDetail();
            detail.setChineseName(subitem.getSubItemDesc());
            detail.setQuantitativeResult(subitem.getValue());
            detail.setTestItemReference((StringUtils.isNotEmpty(subitem.getNormalMin()) ? subitem.getNormalMin() : " ") + "-" + (StringUtils.isNotEmpty(subitem.getNormalMax()) ? subitem.getNormalMax() : " "));
            return detail;
        }).collect(Collectors.toList());
        vo.setReportDetails(reportDetails);
        if(CollectionUtils.isNotEmpty(subitemList)){
            vo.setTotalNum(subitemList.size());
            vo.setExceptionNum((int) subitemList.stream().filter(subitem -> StringUtils.isNotEmpty(subitem.getNonNormalType())).count());
        }
        return vo;
    }

    @Override
    public BusPrescriptionInfoVO getPrescriptionInfo(HisQueryContext queryContext) {
        initQueryParam(queryContext);
        BusPatientMedicalRecordDTO dto = queryContext.getQueryDto();
        checkReq(dto);
        // 获取处方列表
        List<KbjPrescriptionVO> kbjPrescriptionList = kbjHisHelper.getKbjPrescriptionList(queryContext);
        if (CollectionUtils.isEmpty(kbjPrescriptionList)) {
            throw new ServiceException("暂无处方信息");
        }
        handlePrescriptionType(kbjPrescriptionList);
        List<BusPrescriptionInfoVO> busPrescriptionInfoVOList = kbjPrescriptionList.stream()
                .filter(k -> queryContext.getQueryDto().getTranSerialNo().equals(k.getFormNo()) && queryContext.getQueryDto().getPrescriptionTypeName().equals(k.getOrderTypeName()))
                .map(k -> {
                    BusPrescriptionInfoVO busPrescriptionInfoVO = new BusPrescriptionInfoVO();
                    busPrescriptionInfoVO.setOrderTypeName(k.getOrderTypeName());
                    busPrescriptionInfoVO.setInputDoctorName(k.getEnteredDoctorName());
                    busPrescriptionInfoVO.setSubmitDoctorName(k.getEnteredDoctorName());
                    // 科室、诊断无数据
                    busPrescriptionInfoVO.setSubmitDeptName("-");
                    busPrescriptionInfoVO.setDiagnoseName("-");
                    busPrescriptionInfoVO.setOrderDateTime(DateUtil.parse(DateUtil.format(DateUtil.parse(k.getEnteredDateTime(), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN)));
                    busPrescriptionInfoVO.setInputTime(DateUtil.format(busPrescriptionInfoVO.getOrderDateTime(), DatePattern.NORM_DATE_PATTERN));
                    List<HisDrugsVO> drugs = k.getItemList().stream().map(d -> {
                        HisDrugsVO hisDrugsVO = new HisDrugsVO();
                        hisDrugsVO.setOrderItemName(d.getItemName());
                        busPrescriptionInfoVO.setFrequencyName(d.getFrequency());
                        busPrescriptionInfoVO.setUsageName(d.getUsage());
                        switch (Integer.parseInt(k.getFormType())) {
                            case 1:
                            case 12:
                                hisDrugsVO.setSpec(d.getDoseQty() + d.getDoseUnit() + "/" + d.getUnit());
                                break;
                            case 2:
                            case 6:
                                hisDrugsVO.setSpec(d.getSpecs());
                                break;
                            default:
                                break;
                        }
                        hisDrugsVO.setOrderTypeName(String.valueOf(k.getOrderTypeName()));
                        hisDrugsVO.setDosage(d.getDoseQty());
                        hisDrugsVO.setDosageUnitName(d.getDoseUnit());
                        hisDrugsVO.setFrequencyName(d.getFrequency());
                        hisDrugsVO.setUsageName(d.getUsage());
                        hisDrugsVO.setQuantity(String.valueOf(d.getQty()));
                        hisDrugsVO.setQuantityUnitName(d.getUnit());
                        return hisDrugsVO;
                    }).collect(Collectors.toList());
                    busPrescriptionInfoVO.setDrugs(drugs);
                    return busPrescriptionInfoVO;
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(busPrescriptionInfoVOList)) {
            throw new ServiceException("未找到此处方信息");
        }
        return busPrescriptionInfoVOList.get(0);
    }

    /**
     * 校验参数
     * @param dto 入参
     */
    private void checkReq(BusPatientMedicalRecordDTO dto) {
        if (Objects.isNull(dto)|| Objects.isNull(dto.getPatientId())
                || Objects.isNull(dto.getBeginDate()) || Objects.isNull(dto.getEndDate()) || Objects.isNull(dto.getPrescriptionTypeName())) {
            throw new ServiceException("参数错误");
        }
        if (Objects.isNull(dto.getTranSerialNo())) {
            throw new ServiceException("门诊流水号不能为空");
        }
        if (Objects.isNull(dto.getHisOrderRecId())) {
            throw new ServiceException("处方号不能为空");
        }
    }

    /**
     * 康博嘉转换处方类型
     * @param kbjPrescriptionList 处方信息
     */
    public void handlePrescriptionType(List<KbjPrescriptionVO> kbjPrescriptionList) {
        for (KbjPrescriptionVO k : kbjPrescriptionList) {
            if (k.getFormType() != null) {
                // 1 中成药处方【2】/3 草药处方【3】/4 检查单【4】/5 检验单【5】/6 精二处方（西药）12？2？ 毒麻药处方【1】
                switch (Integer.parseInt(k.getFormType())) {
                    case 1:
                        k.setOrderTypeName(HisPrescriptionTypeEnum.PCZ_MEDICINE.getCode());
                        break;
                    case 2:
                    case 6:
                    case 12:
                        k.setOrderTypeName(HisPrescriptionTypeEnum.WESTERN_MEDICINE.getCode());
                        break;
                    case 3:
                        k.setOrderTypeName(HisPrescriptionTypeEnum.CHINESE_MEDICINE.getCode());
                        break;
                    case 4:
                        k.setOrderTypeName(HisPrescriptionTypeEnum.PHYSICAL_EXAMINATION.getCode());
                        break;
                    case 5:
                        k.setOrderTypeName(HisPrescriptionTypeEnum.INSPECT_REPORT.getCode());
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     *  获取渠道名称
     * @return  渠道名称
     */
    @Override
    public String getChannelName() {
        return HisConstant.HisChannel.KBJ.name();
    }

    /**
     * 是否是有效数据
     *  - 只保留状态不为0和1的记录
     *
     * @param item  记录
     * @return  Boolean
     */
    private boolean isValidRecord(KbjPatientMedicalRecordsVO item) {
        return !"0".equals(item.getVisitStatus()) && !"1".equals(item.getVisitStatus());
    }

    /**
     * 转换为 BusPatientRegRecordVO
     *
     * @param record HIS记录
     * @return 转换后的对象
     */
    private BusPatientRegRecordVO convertToBusPatientRegRecordVO(KbjPatientMedicalRecordsVO record) {
        BusPatientRegRecordVO regRecordVO = new BusPatientRegRecordVO();
        regRecordVO.setTranSerialNo(record.getVisitNo());
        regRecordVO.setRegDate(record.getVisitDateTime());
        regRecordVO.setDoctor(String.format(HisConstant.DOCTOR_DEPARTMENT_FORMAT, record.getDoctorName(), record.getSpecialtyDesc()));
        regRecordVO.setDisease(record.getVisitDiagnosis());
        return regRecordVO;
    }

    /**
     *  初始化查询参数
     * @param queryContext 查询参数上下文
     */
    protected void initQueryParam(HisQueryContext queryContext) {
        // 用户未绑定patientId,需要去his系统匹配
//        if (StrUtil.isBlank(queryContext.getHisPatientId())) {
            // 填充HIS认证信息、患者号
        setHisPatientId(queryContext);
//        }
    }

    /**
     *  需要实现填充his患者Id
     * @param queryContext  查询参数上下文
     */
    protected void setHisPatientId(HisQueryContext queryContext) {
        KbjPatientVO hisPatient = kbjHisHelper.getHisPatient(queryContext);
        if (Objects.nonNull(hisPatient)) {
            queryContext.setHisPatientId(hisPatient.getPatientNo());
            // 关联当前his_patient_id
//            hospitalHisHelper.bindHisPatientId(queryContext.getPatientId(), hisPatient.getPatientNo());
        }
    }

    /**
     * 获取患者信息
     *
     * @param query 查询参数
     * @return 患者信息
     */
    @Override
    public HisPatientInfo getPatientInfo(HisPatientInfoQuery query) {
        BusPatientFamily patientFamily = new BusPatientFamily();
        patientFamily.setName(query.getPatientName());
        patientFamily.setIdNumber(query.getIDCardNO());
        HisQueryContext queryContext = new HisQueryContext();
        queryContext.setPatientFamily(patientFamily);
        queryContext.setHospitalId(query.getHospitalId());
        // 目前除东软外，其余需要填充His系统凭证信息
        hospitalHisHelper.setHisCredentialConfig(queryContext);
        KbjPatientVO hisPatient = kbjHisHelper.getHisPatient(queryContext);
        if (Objects.isNull(hisPatient)) {
            return null;
        }
        HisPatientInfo patientInfo = new HisPatientInfo();
        patientInfo.setPatientId(hisPatient.getPatientNo());
        patientInfo.setPatientName(hisPatient.getPatientName());
        patientInfo.setSex(hisPatient.getSex());
        patientInfo.setPatientBirthday(hisPatient.getBirthday());
        patientInfo.setPhoneNumber(hisPatient.getPhoneNo());
        return patientInfo;
    }

}

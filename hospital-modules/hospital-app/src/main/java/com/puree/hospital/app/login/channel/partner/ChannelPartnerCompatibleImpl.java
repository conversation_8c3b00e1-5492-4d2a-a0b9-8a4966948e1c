package com.puree.hospital.app.login.channel.partner;

import com.puree.hospital.app.domain.BusChannelIdentity;
import com.puree.hospital.app.domain.BusChannelPartner;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.vo.BaseChannelPartnerVO;
import com.puree.hospital.app.domain.vo.BusChannelPartnerAgentVO;
import com.puree.hospital.app.domain.vo.BusChannelPartnerVO;
import com.puree.hospital.app.service.IBusChannelIdentityService;
import com.puree.hospital.app.service.IBusChannelPartnerAgentService;
import com.puree.hospital.app.service.IBusChannelPartnerService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.business.api.model.enums.ChannelIdentityTypeEnum;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.BeanUtils;
import com.puree.hospital.common.redis.service.RedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 医伴经纪人兼容器实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/16 10:37
 */
@Service
public class ChannelPartnerCompatibleImpl implements IChannelPartnerCompatible {

    @Resource
    private IBusChannelPartnerService busChannelPartnerService;

    @Resource
    private IBusChannelPartnerAgentService busChannelPartnerAgentService;

    @Resource
    private RedisService redisService;

    @Resource
    private IBusPatientService busPatientService;

    @Resource
    private IBusChannelIdentityService busChannelIdentityService;

    @Override
    public BaseChannelPartnerVO getChannelPartner() {
        SMSLoginUser smsLoginUser = redisService.getCacheObject(CacheConstants.LOGIN_TOKEN_KEY + SecurityUtils.getToken());
        if (Objects.isNull(smsLoginUser)) {
            throw new ServiceException("请登录后操作", HttpStatus.UNAUTHORIZED);
        }
        String loginType = StringUtils.isNotBlank(smsLoginUser.getLoginType()) ? smsLoginUser.getLoginType() : "nil";
        String key = String.format(CacheConstants.CHANNEL_PARTNER_KEY, smsLoginUser.getHospitalId(), loginType, smsLoginUser.getUserid());
        BaseChannelPartnerVO channelPartner = redisService.getCacheObject(key);
        if (Objects.nonNull(channelPartner)) {
            return channelPartner;
        }
        //如果是合作渠道或者经纪人
        if (AppRoleEnum.isChannelOrAgent(smsLoginUser.getIdentity())) {
            channelPartner = getById(smsLoginUser.getUserid(), smsLoginUser.getIdentity());
        }
        //如果是患者端小程序登录
        else if (StringUtils.isBlank(smsLoginUser.getIdentity()) || AppRoleEnum.isPatient(smsLoginUser.getIdentity())) {
            BusPatient patient = busPatientService.getById(smsLoginUser.getUserid());
            if (Objects.nonNull(patient)) {
                channelPartner = getByPhoneNumber(patient.getPhoneNumber(), smsLoginUser.getHospitalId());
            }
        }
        if (Objects.nonNull(channelPartner)) {
            redisService.setCacheObject(key, channelPartner, 1L, TimeUnit.DAYS);
        }
        return channelPartner;
    }

    @Override
    public BaseChannelPartnerVO checkRelationChannelPartner() {
        BaseChannelPartnerVO channelPartner = getChannelPartner();
        if (Objects.isNull(channelPartner)) {
            throw new ServiceException("非法请求", HttpStatus.BAD_REQUEST);
        }
        return channelPartner;
    }

    /**
     * 根据id获取合作渠道或者经纪人信息
     *
     * @param id        经纪人/合作渠道
     * @param identity  身份标识
     * @return          医伴经纪人/合作渠道信息
     */
    private BaseChannelPartnerVO getById(Long id, String identity) {
        if (AppRoleEnum.isChannel(identity)) {
            BusChannelPartner busChannelPartner = busChannelPartnerService.getById(id);
            if (Objects.nonNull(busChannelPartner)) {
                return getChannelPartner(busChannelPartner);
            }
        } else if (AppRoleEnum.isAgent(identity)) {
            BusChannelPartnerAgentVO agentVO = busChannelPartnerAgentService.getById(id);
            if (Objects.nonNull(agentVO) && EnableStatusEnum.isEnabled(agentVO.getStatus())) {
                agentVO.setIdentity(AppRoleEnum.AGENT.getCode());
                return agentVO;
            }
        }
        return null;
    }

    /**
     * 根据手机号获取合作渠道或者经纪人信息
     *
     * @param phoneNumber   手机号
     * @return              医伴经纪人/合作渠道信息
     */
    private BaseChannelPartnerVO getByPhoneNumber(String phoneNumber, Long hospitalId) {
        // 根据手机号和医院id获取合作渠道身份信息
        BusChannelIdentity channelIdentity = busChannelIdentityService.getByPhoneNumber(phoneNumber, hospitalId);
        if (Objects.isNull(channelIdentity)) {
            return null;
        }
        if (ChannelIdentityTypeEnum.isChannel(channelIdentity.getIdentityType())) {
            // 如果是渠道身份，则获取渠道信息
            BusChannelPartner busChannelPartner = busChannelPartnerService.getByIdentityId(channelIdentity.getId(), hospitalId);
            if (Objects.isNull(busChannelPartner)) {
                return null;
            }
            busChannelPartner.setStatus(channelIdentity.getStatus());
            busChannelPartner.setPhonenumber(channelIdentity.getPhoneNumber());
            return getChannelPartner(busChannelPartner);
        }
        if (ChannelIdentityTypeEnum.isAgent(channelIdentity.getIdentityType())) {
            BusChannelPartnerAgentVO agent = busChannelPartnerAgentService.getByIdentityId(channelIdentity.getId(), hospitalId);
            if (Objects.isNull(agent)) {
                return null;
            }
            agent.setStatus(channelIdentity.getStatus());
            agent.setPhonenumber(channelIdentity.getPhoneNumber());
            if (EnableStatusEnum.isEnabled(agent.getStatus())) {
                return agent;
            }
        }
         return null;
    }

    /**
     * 获取合作渠道信息
     *
     * @param busChannelPartner  合作渠道信息
     * @return                   医伴经纪人/合作渠道信息
     */
    private BaseChannelPartnerVO getChannelPartner(BusChannelPartner busChannelPartner) {
        if (Objects.nonNull(busChannelPartner) && EnableStatusEnum.isEnabled(busChannelPartner.getStatus())) {
            BusChannelPartnerVO channelPartner = new BusChannelPartnerVO();
            channelPartner.setIdentity(AppRoleEnum.CHANNEL.getCode());
            BeanUtils.copyProperties(busChannelPartner, channelPartner);
            return channelPartner;
        }
        return null;
    }
}

package com.puree.hospital.app.his.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.puree.hospital.app.domain.vo.BusPatientRegRecordVO;
import com.puree.hospital.app.domain.vo.BusExamineReportDetail;
import com.puree.hospital.app.domain.vo.BusExamineReportDetailVO;
import com.puree.hospital.app.domain.vo.BusInspectReportDetail;
import com.puree.hospital.app.his.constants.HisConstant;
import com.puree.hospital.app.his.domain.HisPatientInfo;
import com.puree.hospital.app.his.domain.HisQueryContext;
import com.puree.hospital.app.his.domain.query.HisPatientInfoQuery;
import com.puree.hospital.app.his.domain.vo.BusEmrDetailVO;
import com.puree.hospital.app.his.domain.vo.BusPrescriptionInfoVO;
import com.puree.hospital.app.his.domain.vo.ZbExamineReportDetailVO;
import com.puree.hospital.app.his.domain.vo.ZbInspectReportDetailVO;
import com.puree.hospital.app.his.domain.vo.ZbOutPatientRecordVO;
import com.puree.hospital.app.his.domain.vo.ZbPatientMedicalRecordVO;
import com.puree.hospital.app.his.helper.ZbHisHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 坐标患者病历信息实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/5/6 19:04
 */
@Slf4j
@AllArgsConstructor(onConstructor = @__(@Autowired))
@Component
public class ZbPatientMedicalRecordHandler extends BasePatientMedicalRecordHandler {

    private final ZbHisHelper zbHisHelper;

    /**
     * 获取患者就诊记录列表
     *
     * @param queryContext 查询参数上下文
     * @return 就诊记录列表
     */
    @Override
    public List<BusPatientRegRecordVO> getPatientMedicalRecordList(HisQueryContext queryContext) {
        List<ZbPatientMedicalRecordVO> hisMedicalRecords = zbHisHelper.getHisMedicalRecords(queryContext);
        if (CollUtil.isEmpty(hisMedicalRecords)) {
            log.debug("未查询到患者就诊记录，患者身份证: {}", queryContext.getPatientFamily().getIdNumber());
            return Collections.emptyList();
        }
        return hisMedicalRecords.stream().map(this::convertToBusPatientRegRecordVO).collect(Collectors.toList());
    }

    /**
     *  转换患者就诊记录
     * @param zbPatientMedicalRecordVO  坐标HIS 就诊记录集合
     * @return  统一就诊记录集合
     */
    private BusPatientRegRecordVO convertToBusPatientRegRecordVO(ZbPatientMedicalRecordVO zbPatientMedicalRecordVO) {
        BusPatientRegRecordVO busPatientRegRecordVO = new BusPatientRegRecordVO();
        busPatientRegRecordVO.setTranSerialNo(zbPatientMedicalRecordVO.getId());
        busPatientRegRecordVO.setRegDate(zbPatientMedicalRecordVO.getLastSessionTime());
        busPatientRegRecordVO.setDoctor(String.format(HisConstant.DOCTOR_DEPARTMENT_FORMAT, zbPatientMedicalRecordVO.getDoctorName(), zbPatientMedicalRecordVO.getDepartmentName()));
        // 西医诊断为空取中医诊断
        busPatientRegRecordVO.setDisease(StrUtil.isNotBlank(zbPatientMedicalRecordVO.getWmDiagnose()) ?
                zbPatientMedicalRecordVO.getWmDiagnose() :
                String.format(HisConstant.TCM_DIAGNOSE_FORMAT, zbPatientMedicalRecordVO.getTcmDiagnose(), zbPatientMedicalRecordVO.getTcmSyndrome()));
        return busPatientRegRecordVO;
    }

    /**
     *  获取电子病历详情
     * @param queryContext  查询上下文
     * @return  电子病历详情
     */
    @Override
    public List<BusEmrDetailVO> getEmrDetail(HisQueryContext queryContext) {
        ZbOutPatientRecordVO hisOutPatientRecords = zbHisHelper.getHisOutPatientRecords(queryContext);
        if (hisOutPatientRecords != null) {
            String consultationTime = DateUtil.format(hisOutPatientRecords.getConsultationTime(), DatePattern.NORM_DATETIME_FORMAT);
            String diagnosticsTime = DateUtil.format(hisOutPatientRecords.getDiagnosticsTime(), DatePattern.NORM_DATETIME_FORMAT);
            log.debug("电子病历详情结果：{}", hisOutPatientRecords);
            String patientAge = hisOutPatientRecords.getPatientAge();
            if (Objects.nonNull(hisOutPatientRecords.getPatientAge())) {
                patientAge += "岁";
            }
            List<BusEmrDetailVO> basicInfoList = assembleBasicInfo(hisOutPatientRecords.getDoctorName(), hisOutPatientRecords.getDepartmentName(), getHospitalName(queryContext.getHospitalId()),
                    hisOutPatientRecords.getPatientName(), patientAge, hisOutPatientRecords.getPatientSex(), hisOutPatientRecords.getOutpatientNumber(),
                    hisOutPatientRecords.getPatientTel(), consultationTime, diagnosticsTime);
            return assembleEmrVO(basicInfoList, hisOutPatientRecords.getDiseaseItemList(), ZbOutPatientRecordVO.DiseaseItem::getTitle, ZbOutPatientRecordVO.DiseaseItem::getContent);
        }
        return Collections.emptyList();
    }

    @Override
    public List<BusInspectReportDetail> getInspectDetail(HisQueryContext queryContext) {
        List<ZbInspectReportDetailVO> zbInspectReportDetailVO = zbHisHelper.getHisInspectReportDetail(queryContext);
        if (CollUtil.isEmpty(zbInspectReportDetailVO)) {
            return Collections.emptyList();
        }
        return zbInspectReportDetailVO.stream().flatMap(item -> assembleInspectDetail(item).stream()).collect(Collectors.toList());
    }

    /**
     *  处理检查报告详情
     * @param zbInspectReportDetailVO   HIS检查报告详情
     * @return  检验报告
     */
    private List<BusInspectReportDetail> assembleInspectDetail(ZbInspectReportDetailVO zbInspectReportDetailVO) {
        List<BusInspectReportDetail> list = new ArrayList<>();
        List<ZbInspectReportDetailVO.Items> items = zbInspectReportDetailVO.getItems();
        if (CollUtil.isEmpty(items)) {
            return list;
        }
        // 转换数据
        items.forEach(item -> {
            BusInspectReportDetail hisInspectReportDetail = new BusInspectReportDetail();
            hisInspectReportDetail.setItemId(zbInspectReportDetailVO.getId());
            hisInspectReportDetail.setItemName(item.getName());
            hisInspectReportDetail.setItemResult(item.getDesc());
            hisInspectReportDetail.setConclusion(zbInspectReportDetailVO.getSummary());
            list.add(hisInspectReportDetail);
        });
        return list;
    }

    @Override
    public BusExamineReportDetailVO getExamineDetail(HisQueryContext queryContext) {
        ZbExamineReportDetailVO zbExamineReportDetailVO = zbHisHelper.getHisExamineReportDetail(queryContext);
        return assembleExamineDetail(zbExamineReportDetailVO);
    }

    /**
     *  组装检验报告详情
     * @param zbExamineReportDetailVO   HIS检验报告
     * @return  检查报告
     */
    private BusExamineReportDetailVO assembleExamineDetail(ZbExamineReportDetailVO zbExamineReportDetailVO) {
        BusExamineReportDetailVO hisExamineReportDetailVO = new BusExamineReportDetailVO();
        if (Objects.isNull(zbExamineReportDetailVO) || CollUtil.isEmpty(zbExamineReportDetailVO.getItems())) {
            return hisExamineReportDetailVO;
        }
        List<ZbExamineReportDetailVO.Items> items = zbExamineReportDetailVO.getItems();
        List<BusExamineReportDetail> collect = items.stream().map(item -> {
            BusExamineReportDetail hisExamineReportDetail = new BusExamineReportDetail();
            hisExamineReportDetail.setReportId(zbExamineReportDetailVO.getId());
            hisExamineReportDetail.setTestitemId(item.getId());
            hisExamineReportDetail.setChineseName(item.getName());
            hisExamineReportDetail.setQuantitativeResult(item.getResultStr());
            hisExamineReportDetail.setTestItemUnit(item.getUnit());
            hisExamineReportDetail.setTestItemReference(item.getRange());
            ZbExamineReportDetailVO.AbnormalEnum abnormalEnum = ZbExamineReportDetailVO.AbnormalEnum.getByValue(item.getAbnormal());
            if (Objects.nonNull(abnormalEnum)) {
                hisExamineReportDetail.setQualitativeResult(abnormalEnum.getCode());
            }
            return hisExamineReportDetail;
        }).collect(Collectors.toList());
        hisExamineReportDetailVO.setReportDetails(collect);
        hisExamineReportDetailVO.setTotalNum(items.size());
        hisExamineReportDetailVO.setExceptionNum((int) items.stream().filter(ZbExamineReportDetailVO.Items::checkException).count());
        return hisExamineReportDetailVO;
    }


    @Override
    public BusPrescriptionInfoVO getPrescriptionInfo(HisQueryContext queryContext) {
        // 目前还没有处方的信息，先返回为空，预留
        return null;
    }

    @Override
    public String getChannelName() {
        return HisConstant.HisChannel.ZB.name();
    }

    /**
     * 获取患者信息
     *
     * @param query 查询参数
     * @return 患者信息
     */
    @Override
    public HisPatientInfo getPatientInfo(HisPatientInfoQuery query) {
        return null;
    }

}

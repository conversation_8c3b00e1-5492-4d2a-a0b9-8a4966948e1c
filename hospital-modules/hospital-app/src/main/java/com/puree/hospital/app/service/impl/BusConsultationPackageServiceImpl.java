package com.puree.hospital.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.puree.hospital.app.constant.ConsultationOrderStatus;
import com.puree.hospital.app.constant.ConsultationPriorStatus;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationPackage;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDoctorConsultation;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusQuickConsultation;
import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.domain.dto.CommunicationMessageDTO;
import com.puree.hospital.app.mapper.BusConsultationPackageMapper;
import com.puree.hospital.app.mapper.BusDoctorConsultationMapper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusQuickConsultationMapper;
import com.puree.hospital.app.queue.QueueConstant;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.ConsultationOrderAutoCompleteProducer;
import com.puree.hospital.app.service.IBusChannelOrderService;
import com.puree.hospital.app.service.IBusChannelPatientAgentRelationService;
import com.puree.hospital.app.service.IBusCommunicationMessageService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusConsultationPackageService;
import com.puree.hospital.app.service.IBusConsultationSettingsService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.service.ITongLianPayService;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderPayTypeEnum;
import com.puree.hospital.common.core.enums.InvoiceStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.five.api.RemoteServicePackImService;
import com.puree.hospital.five.api.model.BusDoctorPatientGroupRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusConsultationPackageServiceImpl implements IBusConsultationPackageService {
    private static final Logger log = LoggerFactory.getLogger(BusConsultationPackageServiceImpl.class);
    private final BusConsultationPackageMapper busConsultationPackageMapper;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    private final BusDoctorConsultationMapper busDoctorConsultationMapper;
    private final RemoteServicePackImService remoteServicePackImService;
    private final BusHospitalMapper busHospitalMapper;
    private final ITongLianPayService iTongLianPayService;
    private final IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final IBusConsultationOrderService busConsultationOrderService;
    @Autowired @Lazy
    private ConsultationOrderAutoCompleteProducer consultationOrderAutoCompleteProducer ;

    private final BusQuickConsultationMapper busQuickConsultationMapper;

    private final IBusCommunicationMessageService busCommunicationMessageService;
    private final IBusChannelOrderService busChannelOrderService;

    @Override
    public List<BusConsultationPackage> selectList(BusConsultationPackage busConsultationPackage) {
        return busConsultationPackageMapper.selectConsultationPackageList(busConsultationPackage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public long insert(BusConsultationOrder consultationOrder) {
        return this.add(consultationOrder,true, ConsultationOrderPayTypeEnum.PATIENT_PAY.getStatus());
    }

    @Override
    public int updateUseTimes(BusConsultationPackage busConsultationPackage) {
        return busConsultationPackageMapper.updateUseTimes(busConsultationPackage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public long give(BusConsultationOrder consultationOrder,boolean flat) {
        return this.add(consultationOrder,flat, ConsultationOrderPayTypeEnum.DOCTOR_GIVE.getStatus());
    }

    @Override
    public long add(BusConsultationOrder consultationOrder, boolean flat, Integer payType) {
        log.info("未处理问诊订单信息,order={}", consultationOrder);
        consultationOrder.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus()) ;
        consultationOrder.setPayType(payType) ;
        String doctorName = consultationOrder.getDoctorName();
        String departmentName = consultationOrder.getDepartmentName();
        String familyName = consultationOrder.getFamilyName();
        if (StringUtils.isEmpty(doctorName) || StringUtils.isEmpty(departmentName) || StringUtils.isEmpty(familyName)) {
            throw new ServiceException("问诊包参数缺失");
        }
        //校验问诊包是否用完
        boolean b = this.checkOrderState(consultationOrder);
        if (b) {
            throw new ServiceException("就诊人已购买问诊，不可重复！");
        }

        // 查询医院问诊优先设置
        BusConsultationSettings bs = new BusConsultationSettings();
        bs.setHospitalId(consultationOrder.getHospitalId());
        bs = busConsultationSettingsService.selectOne(bs);

        // 设置问诊订单参数
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        consultationOrder.setOrderNo(OrderTypeConstant.CONSULATION_ORDER + simpleDateFormat.format(new Date()));
        consultationOrder.setOrderTime(DateUtils.getNowDate());
        consultationOrder.setBuyType(CodeEnum.NO.getCode());
        consultationOrder.setConsultationType(CodeEnum.YES.getCode());
        consultationOrder.setCreateTime(DateUtils.getNowDate());

        // 设置问诊包参数
        BusConsultationPackage busConsultationPackage = new BusConsultationPackage();
        busConsultationPackage.setPayTime(DateUtils.getNowDate());
        busConsultationPackage.setType(consultationOrder.getOrderType());
        busConsultationPackage.setHospitalId(consultationOrder.getHospitalId());
        busConsultationPackage.setDoctorId(consultationOrder.getDoctorId());
        busConsultationPackage.setPatientId(consultationOrder.getPatientId());
        busConsultationPackage.setFamilyId(consultationOrder.getFamilyId());
        busConsultationPackage.setTotalTimes(consultationOrder.getRound());
        busConsultationPackage.setDepartmentId(consultationOrder.getDepartmentId());
        busConsultationPackage.setCreateTime(DateUtils.getNowDate());

        // 拼接群组参数
        BusDoctorPatientGroup group = new BusDoctorPatientGroup();
        group.setHospitalId(consultationOrder.getHospitalId());
        group.setDoctorId(consultationOrder.getDoctorId());
        group.setPatientId(consultationOrder.getPatientId());
        group.setFamilyId(consultationOrder.getFamilyId());
        group.setDepartmentId(consultationOrder.getDepartmentId());
        group.setType(CodeEnum.NO.getCode());

        // 拼接问诊订单参数
        BusConsultationOrder order = new BusConsultationOrder();
        order.setHospitalId(consultationOrder.getHospitalId());
        order.setDoctorId(consultationOrder.getDoctorId());
        order.setDepartmentId(consultationOrder.getDepartmentId());
        order.setPatientId(consultationOrder.getPatientId());
        order.setFamilyId(consultationOrder.getFamilyId());
        //关联的极速问诊id为空
        if (Objects.isNull(consultationOrder.getConsultationId())) {
            //复诊
            if (Objects.nonNull(consultationOrder.getFurtherConsultationId())) {
                BusConsultationOrder furtherConsultation = busConsultationOrderService.queryOrderInfo(consultationOrder.getFurtherConsultationId());
                if (Objects.nonNull(furtherConsultation)) {
                    consultationOrder.setConsultationId(furtherConsultation.getConsultationId());
                }
            } else {
                //根据患者id、医生id、科室id、就诊人id、医院id查询是否已存在问诊订单
                LambdaQueryWrapper<BusQuickConsultation> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(BusQuickConsultation::getPatientId, consultationOrder.getPatientId());
                wrapper.eq(BusQuickConsultation::getDoctorId, consultationOrder.getDoctorId());
                wrapper.eq(BusQuickConsultation::getDepartmentId, consultationOrder.getDepartmentId());
                wrapper.eq(BusQuickConsultation::getFamilyId, consultationOrder.getFamilyId());
                wrapper.eq(BusQuickConsultation::getHospitalId, consultationOrder.getHospitalId());
                wrapper.orderByDesc(BusQuickConsultation::getId);
                wrapper.last("limit 1");
                BusQuickConsultation quickConsultation = busQuickConsultationMapper.selectOne(wrapper);
                // 续购，赠送等场景需要将问诊id设置回去，方便监管上报
                if (Objects.nonNull(quickConsultation)) {
                    consultationOrder.setConsultationId(quickConsultation.getId());
                }
            }
        }
        // 拼接患者参数
        BusOrderDto dto = new BusOrderDto();
        dto.setHospitalId(consultationOrder.getHospitalId());
        dto.setPatientId(consultationOrder.getPatientId());
        dto.setOrderNo(consultationOrder.getOrderNo());
        // 订单类型设置为问诊单
        dto.setOrderType(0);

        // 购买问诊包
        if (StringUtils.isNotEmpty(consultationOrder.getAmount())) {
            // 查询该就诊人是否购买视频问诊
            order.setOrderType(CodeEnum.NO.getCode());
            order.setVideoStatus("0,2,3,9,10,11,12");
            BusConsultationOrder busConsultationOrder = busConsultationOrderService.selectBusConsultationOrder(order);
            if (ObjectUtil.isNotNull(busConsultationOrder)) {
                throw new ServiceException("视频问诊还未完成，不能续购！");
            }
            if (ObjectUtil.isNotNull(bs) && ConsultationPriorStatus.DOCTOR_PRIOR.equals(bs.getConsultationPrior())) {
                // 查询医生问诊信息
                QueryWrapper<BusDoctorConsultation> queryWrapper = new QueryWrapper<>();
                queryWrapper
                        .eq("hospital_id", consultationOrder.getHospitalId())
                        .eq("doctor_id", consultationOrder.getDoctorId())
                        .eq("type", "0")
                        .last(" limit 1");
                BusDoctorConsultation doctorConsultation = busDoctorConsultationMapper.selectOne(queryWrapper);
                if (ObjectUtil.isNotNull(doctorConsultation) && doctorConsultation.getStatus().equals(0)) {
                    throw new ServiceException("医生未开启图文问诊，不能续购！");
                }
            }
            BusHospital hospital = busHospitalMapper.selectById(consultationOrder.getHospitalId());
            // 购买零元问诊包
            if (CodeEnum.NO.getCode().equals(consultationOrder.getAmount())) {
                consultationOrder.setPaymentTime(DateUtils.getNowDate());
                consultationOrder.setStatus(ConsultationOrderStatus.VISITING);
                if (ObjectUtil.isNotNull(consultationOrder.getFurtherConsultationId())) {
                    BusDoctorPatientGroup busDoctorPatientGroup = busDoctorPatientGroupService.checkGroup(group);
                    iTongLianPayService.isFurther(consultationOrder.getFurtherConsultationId(), busDoctorPatientGroup.getId(),familyName);
                }
                // 校验群组是否解散
                BusDoctorPatientGroup doctorPatientGroup = busDoctorPatientGroupService.checkGroup(group);
                log.info("查询是否存在群组:group={}", JSON.toJSON(doctorPatientGroup));
                if (ObjectUtil.isNotNull(doctorPatientGroup) && CodeEnum.YES.getCode().equals(doctorPatientGroup.getDelFlag())) {
                    Long groupId = busDoctorPatientGroupService.createGroup(doctorPatientGroup, doctorPatientGroup.getDelFlag());
                    if (ObjectUtil.isNull(groupId)) {
                        throw new ServiceException("群组激活失败！");
                    }
                }
            } else {
                consultationOrder.setStatus(ConsultationOrderStatus.UNPAID);
                consultationOrder.setInvoiceStatus(InvoiceStatusEnum.CAN_ISSUE.getStatus()) ;
            }
            consultationOrder.setPayWay(hospital.getPayWay());
            log.info("插入问诊订单信息,order={}", consultationOrder);
            busConsultationOrderService.save(consultationOrder);

            // 推送消息给医生
            if (ObjectUtil.isNotNull(consultationOrder.getDoctorId()) && CodeEnum.NO.getCode().equals(consultationOrder.getAmount())) {
                busConsultationOrderService.pushTwMsgToDr(consultationOrder);
            }

            // 校验患者是否是经纪人邀请
            dto.setId(consultationOrder.getId());
            busChannelOrderService.createBusChannelOrderByOrderDTO(dto);

            // 保存问诊包
            busConsultationPackage.setOrderId(consultationOrder.getId());
            busConsultationPackage.setPayAmount(consultationOrder.getAmount());
            log.info("插入问诊包信息,order={}", busConsultationPackage);
            busConsultationPackageMapper.insert(busConsultationPackage);
            BigDecimal amount = StringUtils.isNotBlank(consultationOrder.getAmount()) ? new BigDecimal(consultationOrder.getAmount()) : BigDecimal.ZERO;
            //如果是零元单，发送im消息，保证医生端聊天室数据更新
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                // 0元购加入队列
                addQueue(consultationOrder, "purchase", bs);

                //给医生发送自定义消息，方便医生端im自动刷新
                BusDoctorPatientGroup query = new BusDoctorPatientGroup();
                query.setHospitalId(consultationOrder.getHospitalId());
                query.setDoctorId(consultationOrder.getDoctorId());
                query.setDepartmentId(consultationOrder.getDepartmentId());
                query.setPatientId(consultationOrder.getPatientId());
                query.setFamilyId(consultationOrder.getFamilyId());
                BusDoctorPatientGroup groupInfo = busDoctorPatientGroupService.selectOne(query);
                CommunicationMessageDTO messageDTO = new CommunicationMessageDTO();
                messageDTO.setDoctorPatientGroup(groupInfo);
                messageDTO.setGroupId(groupInfo.getId());
                messageDTO.setFromAccount(TencentyunImConstants.ADMINISTRATOR);
                messageDTO.setNickName(TencentyunImConstants.ADMINISTRATOR);
                messageDTO.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
                Map<String, Object> map = new HashMap<>();
                map.put("type", CustomMsgConstants.CONSULTATION_GO_ON);
                messageDTO.setMap(map);
                messageDTO.setSendNotification(false);
                messageDTO.setSaveMessage(false);
                messageDTO.setSendControls(Lists.newArrayList(TencentyunImConstants.NO_UN_READ));
                busCommunicationMessageService.sendCustomImMsg(messageDTO);
            }
        } else { // 赠送问诊包
            // 查询最新问诊订单
            order.setStatus("4");
            order.setVideoStatus("2,3,4");
            BusConsultationOrder busConsultationOrder = busConsultationOrderService.selectBusConsultationOrder(order);
            if (ObjectUtil.isNotNull(busConsultationOrder)) {
                log.info("最新完成订单={}", busConsultationOrder.getId());
                String partnersCode = busConsultationOrder.getPartnersCode();
                consultationOrder.setAmount("0");
                consultationOrder.setPaymentTime(DateUtils.getNowDate());
                consultationOrder.setStatus(ConsultationOrderStatus.VISITING);
                // 图文
                if (CodeEnum.NO.getCode().equals(busConsultationOrder.getOrderType())) {
                    if (StringUtils.isNotEmpty(partnersCode)) {
                        consultationOrder.setPartnersCode(partnersCode);
                    }
                    log.info("插入问诊订单信息,order={}", consultationOrder);
                    busConsultationOrderService.save(consultationOrder);

                    // 校验患者是否是经纪人邀请
                    dto.setId(consultationOrder.getId());
                    dto.setGive(YesNoEnum.YES.getCode());
                    busChannelOrderService.createBusChannelOrderByOrderDTO(dto);

                    busConsultationPackage.setOrderId(consultationOrder.getId());
                } else { // 视频
                    String videoStatus = busConsultationOrder.getVideoStatus();
                    if (ConsultationOrderStatus.COMPLETE.equals(videoStatus)) {
                        if (StringUtils.isNotEmpty(partnersCode)) {
                            consultationOrder.setPartnersCode(partnersCode);
                        }
                        busConsultationOrderService.save(consultationOrder);

                        // 校验患者是否是经纪人邀请
                        dto.setId(consultationOrder.getId());
                        dto.setGive(YesNoEnum.YES.getCode());
                        busChannelOrderService.createBusChannelOrderByOrderDTO(dto);

                        busConsultationPackage.setOrderId(consultationOrder.getId());
                    } else {
                        busConsultationPackage.setOrderId(busConsultationOrder.getId());
                    }
                    busConsultationPackage.setType(CodeEnum.YES.getCode());
                }
            } else {
                //查询为空 判断为加入诊室未生成订单
                consultationOrder.setAmount("0");
                consultationOrder.setPaymentTime(DateUtils.getNowDate());
                consultationOrder.setStatus(ConsultationOrderStatus.VISITING);
                log.info("插入问诊订单信息,order={}", consultationOrder);
                busConsultationOrderService.save(consultationOrder);

                // 校验患者是否是经纪人邀请
                dto.setId(consultationOrder.getId());
                dto.setGive(YesNoEnum.YES.getCode());
                busChannelOrderService.createBusChannelOrderByOrderDTO(dto);

                busConsultationPackage.setOrderId(consultationOrder.getId());
            }
            log.info("插入问诊包信息,order={}", busConsultationPackage);
            busConsultationPackageMapper.insert(busConsultationPackage);

            //加入到消息队列
            addQueue(consultationOrder, "give", bs);

            BusDoctorPatientGroup doctorPatientGroup = busDoctorPatientGroupService.checkGroup(group);
            log.info("查询是否存在群组:group={}", JSON.toJSON(doctorPatientGroup));
            if(ObjectUtil.isNotNull(doctorPatientGroup) && flat) {
                if (CodeEnum.NO.getCode().equals(doctorPatientGroup.getDelFlag())) {
                    BusDoctorPatientGroupRequest groupRequest = OrikaUtils.convert(doctorPatientGroup, BusDoctorPatientGroupRequest.class);
                    remoteServicePackImService.renewalDoctorAndAssistantGroup(groupRequest);
                } else {
                    Long groupId = busDoctorPatientGroupService.createGroup(doctorPatientGroup, doctorPatientGroup.getDelFlag());
                    if (ObjectUtil.isNull(groupId)) {
                        throw new ServiceException("群组激活失败！");
                    }
                }
            }
        }
        return consultationOrder.getId();
    }

    @Override
    public boolean checkOrderState(BusConsultationOrder consultationOrder) {
        boolean returnData = false;
        // 查询该科室下该就诊人是否还有回合数
        BusQuickConsultation quickConsultation = new BusQuickConsultation();
        quickConsultation.setFamilyId(consultationOrder.getFamilyId());
        quickConsultation.setDoctorId(consultationOrder.getDoctorId());
        quickConsultation.setHospitalId(consultationOrder.getHospitalId());
        quickConsultation.setDepartmentId(consultationOrder.getDepartmentId());
        quickConsultation.setPatientId(consultationOrder.getPatientId());
        List<BusConsultationPackage> busConsultationPackages =
                busConsultationPackageMapper.selectPackageList(quickConsultation);
        if (CollectionUtils.isNotEmpty(busConsultationPackages)) {
            for (BusConsultationPackage consultationPackage : busConsultationPackages) {
                if (!consultationPackage.getTotalTimes().equals(consultationPackage.getUseTimes())) {
                    returnData = true;
                    break;
                }
            }
        }
        return returnData;
    }


    @Override
    public boolean checkrFequency(BusConsultationPackage busConsultationPackage) {
        List<BusConsultationPackage> consultationPackages =
                busConsultationPackageMapper.selectConsultationPackageList(busConsultationPackage);
        boolean returnDate = false;
        for (BusConsultationPackage b : consultationPackages) {
            if (!b.getTotalTimes().equals(b.getUseTimes())) {
                returnDate = true;
                break;
            }
        }
        return returnDate;
    }

    @Override
    public int remainingTimes(BusConsultationPackage busConsultationPackage) {
        int num = 0;
        List<BusConsultationPackage> packageList =
                busConsultationPackageMapper.selectConsultationPackageList(busConsultationPackage);
        for (BusConsultationPackage b : packageList) {
            if (!b.getTotalTimes().equals(b.getUseTimes()) && b.getTotalTimes() > b.getUseTimes()) {
                int i = b.getTotalTimes() - b.getUseTimes();
                num = num + i;
            }
        }
        return num;
    }

    /**
     * 问诊订单加入队列
     * @param consultationOrder 问诊订单
     * @param type purchase：购买；give：赠送
     * @param bs 问诊设置
     */
    private void addQueue(BusConsultationOrder consultationOrder, String type, BusConsultationSettings bs) {
        Message message = new Message();
        message.setId(consultationOrder.getId());
        message.setHospitalId(consultationOrder.getHospitalId());
        message.setExtend(type);

        if (bs == null || bs.getImagetextTotalExpire() == null) {
            message.setFireTime(QueueConstant.QUEUE_CONSULTATION_ORDER_AUTO_COMPLETE_TIME);
        } else {
            long time = bs.getImagetextTotalExpire() * 60 * 60 * 1000L;
            message.setFireTime(time);
        }
        consultationOrderAutoCompleteProducer.delaySend(message, message.getFireTime());
    }

}

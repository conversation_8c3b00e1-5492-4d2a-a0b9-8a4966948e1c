package com.puree.hospital.app.listener;

import com.puree.hospital.app.api.model.event.order.DrugsOrderAgreeRefundEvent;
import com.puree.hospital.app.api.model.event.order.OrderAgreeRefundEvent;
import com.puree.hospital.app.api.model.event.order.OrderCancelEvent;
import com.puree.hospital.app.queue.producer.event.order.DrugsOrderAgreeRefundEventProducer;
import com.puree.hospital.app.queue.producer.event.order.OrderAgreeRefundEventProducer;
import com.puree.hospital.app.queue.producer.event.order.OrderCancelEventProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <p>
 * 订单事件监听器
 * 注意：此处之所以使用SpringEventListener, 原因是因为代码逻辑上存在事务嵌套问题，
 * 消息发送必须保证事务提交后再发送，因此需要依赖TransactionalEventListener，进行保证事务提交后在发送消息
 * </p>
 *
 * <AUTHOR>
 * @date 2025/2/28 14:23
 */
@Slf4j
@Component
public class OrderEventListener {

    @Resource @Lazy
    private OrderAgreeRefundEventProducer orderAgreeRefundEventProducer;

    @Resource @Lazy
    private DrugsOrderAgreeRefundEventProducer drugsOrderAgreeRefundEventProducer;

    @Resource @Lazy
    private OrderCancelEventProducer orderCancelEventProducer;

    @Async
    @TransactionalEventListener(classes = OrderAgreeRefundEvent.class)
    public void orderAgreeRefundEvent(OrderAgreeRefundEvent event) {
        log.info("订单同意退款事件监听：{}", event);
        orderAgreeRefundEventProducer.send(event);
    }

    @Async
    @TransactionalEventListener(classes = DrugsOrderAgreeRefundEvent.class)
    public void drugsOrderAgreeRefundEvent(DrugsOrderAgreeRefundEvent event) {
        log.info("药品订单同意退款事件监听：{}", event);
        drugsOrderAgreeRefundEventProducer.send(event);
    }

    @Async
    @TransactionalEventListener(classes = OrderCancelEvent.class)
    public void orderCancelEvent(OrderCancelEvent event) {
        log.info("后台订单取消事件监听：{}", event);
        orderCancelEventProducer.send(event);
    }

}

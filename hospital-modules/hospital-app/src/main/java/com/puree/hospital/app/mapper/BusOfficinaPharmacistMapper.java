package com.puree.hospital.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.dto.CAPersonInfoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BusOfficinaPharmacistMapper extends BaseMapper<BusOfficinaPharmacist> {

    /**
     * CA证书申请需要的药师信息
     *
     * @param phoneNumber 手机号码
     * @return CAPersonInfoDto
     */
    CAPersonInfoDto getPharmacistInfo(@Param("phoneNumber") String phoneNumber);

    /**
     * 获取药师信息
     * @param id id
     * @return 药师信息
     */
    BusOfficinaPharmacist selectById(Long id);


    List<BusOfficinaPharmacist> selectOfficinaPharmacistList(BusOfficinaPharmacist busOfficinaPharmacist);

}

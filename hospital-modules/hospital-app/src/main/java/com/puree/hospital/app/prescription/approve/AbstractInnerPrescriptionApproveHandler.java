package com.puree.hospital.app.prescription.approve;

import cn.hutool.core.date.DateUtil;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDrugOrderPackage;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusShopCart;
import com.puree.hospital.app.api.model.ApprovePrescriptionDTO;
import com.puree.hospital.app.prescription.helper.ExaminationFeeHelper;
import com.puree.hospital.app.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.queue.QueueConstant;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.PrescriptionLastValidProducer;
import com.puree.hospital.app.queue.producer.PrescriptionValidProducer;
import com.puree.hospital.app.service.IBusShopCartService;
import com.puree.hospital.common.core.enums.ShopCartTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 抽象的院内处方审批执行器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/25 19:29
 */
@Slf4j
public abstract class AbstractInnerPrescriptionApproveHandler extends AbstractPrescriptionApproveHandler {

    @Lazy
    @Resource
    private PrescriptionLastValidProducer prescriptionLastValidProducer ;

    @Lazy
    @Resource
    private PrescriptionValidProducer prescriptionValidProducer ;

    @Resource
    private BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;

    @Resource
    private BusDrugOrderPackageMapper busDrugOrderPackageMapper;

    @Lazy
    @Resource
    private IBusShopCartService busShopCartService;

    @Resource
    private ExaminationFeeHelper examinationFeeHelper;

    @Override
    protected Date getValidTime(BusPrescription prescription, BusConsultationSettings setting) {
        long expiredMillisecond;
        if (setting != null && setting.getPrescrptionOrderExpire() !=  null) {
            expiredMillisecond = setting.getPrescrptionOrderExpire() * 60 * 60 * 1000L;
        } else {
            expiredMillisecond = QueueConstant.QUEUE_PRESCRIPTION_INVALID_TIME;
        }
        return DateUtil.offsetMillisecond(new Date(), (int) expiredMillisecond);
    }

    @Override
    protected void doPassUpdate(ApprovePrescriptionDTO dto, BusPrescription prescription, BusConsultationSettings setting) {
        // 根据配送企业分组查询处方药品信息
        List<BusPrescriptionDrugs> prescriptionDrugsList = busPrescriptionDrugsMapper.selectPackageList(dto);
        //如果是第三方配送企业发货，则创建配送包裹
        prescriptionDrugsList.stream()
                .filter(pd ->Objects.nonNull(pd.getEnterpriseId()))
                .forEach(pd -> {
                    BusDrugOrderPackage orderPackage = new BusDrugOrderPackage();
                    orderPackage.setHospitalId(pd.getHospitalId());
                    orderPackage.setPackageDrugType(YesNoEnum.YES.getCode());
                    orderPackage.setPackageType(YesNoEnum.NO.getCode());
                    orderPackage.setPrescriptionId(dto.getId());
                    orderPackage.setEnterpriseId(pd.getEnterpriseId());
                    orderPackage.setEnterpriseName(pd.getEnterpriseName());
                    orderPackage.setDrugsGoodsId(pd.getDrugsId());
                    // 只拆分配送企业包裹，药房包裹在后台发货时拆分
                    orderPackage.setCreateTime(DateUtils.getNowDate());
                    busDrugOrderPackageMapper.insert(orderPackage);
                });

        //如果不是用药预订订单，则需要加入购物车
        if (Objects.isNull(prescription.getPreorderId())) {
            BusShopCart shopCart = new BusShopCart();
            shopCart.setType(ShopCartTypeEnum.PRESCRIPTION.getCode());
            shopCart.setPatientId(dto.getPatientId());
            shopCart.setHospitalId(dto.getHospitalId());
            shopCart.setBusinessId(dto.getId());
            busShopCartService.insert(shopCart);
        }
        //审批通过增加诊查费
        addExaminationFee(prescription, setting);
    }

    @Override
    protected void sendMsg(ApprovePrescriptionDTO dto, BusConsultationSettings setting) {
        //发送处方正在生效中的事件
        sendInEffectiveMsg(dto, setting);
        //发送处方过期最后提醒事件
        sendLastReminderMsg(dto, setting);
        //发送处方过期事件
        super.sendExpiredMsg(dto);
    }

    @Override
    protected void doRejectUpdate(ApprovePrescriptionDTO dto) {
        busPrescriptionService.increaseDrugsStock(dto.getHospitalId(), dto.getId());
    }

    /**
     * 发送处方生效中的消息事件
     *
     * @param dto      处方审核参数
     * @param setting  运营设置
     */
    protected void sendInEffectiveMsg(ApprovePrescriptionDTO dto, BusConsultationSettings setting) {
        //进行中队列
        Message inMessage = new Message();
        inMessage.setId(dto.getId());
        inMessage.setHospitalId(dto.getHospitalId());
        if (setting == null || setting.getPrescrptionInValid() == null) {
            inMessage.setFireTime(QueueConstant.QUEUE_PRESCRIPTION_IN_INVALID_TIME);
        } else {
            //分钟计算
            long time = setting.getPrescrptionInValid() * 60 * 1000;
            inMessage.setFireTime(time);
        }
        prescriptionValidProducer.delaySend(inMessage, inMessage.getFireTime()) ;
    }

    /**
     * 发送处方最后提醒事件
     *
     * @param dto      处方审核参数
     * @param setting  运营设置
     */
    protected void sendLastReminderMsg(ApprovePrescriptionDTO dto, BusConsultationSettings setting) {
        Message lastMessage = new Message();
        lastMessage.setId(dto.getId());
        lastMessage.setHospitalId(dto.getHospitalId());
        if (setting == null || setting.getPrescrptionLastValid() == null) {
            lastMessage.setFireTime(QueueConstant.QUEUE_PRESCRIPTION_LAST_INVALID_TIME);
            lastMessage.setHoursRemaining("1小时0分钟");
        } else {
            //处方最后通知时间
            int prescriptionLastValid = setting.getPrescrptionLastValid();
            if (prescriptionLastValid > 0) {
                prescriptionLastValid = Math.negateExact(prescriptionLastValid);
            }
            //过期时间 - 最后通知的分钟数
            Date notifyTime = DateUtil.offsetMinute(dto.getValidTime(), prescriptionLastValid);
            //通知触发时间
            lastMessage.setFireTime(notifyTime.getTime() - dto.getReviewTime().getTime());
            lastMessage.setHoursRemaining(DateUtils.minZHour(setting.getPrescrptionLastValid()));
        }
        prescriptionLastValidProducer.delaySend(lastMessage, lastMessage.getFireTime()) ;
    }

    /**
     * 设置诊查费
     *
     * @param busPrescription 处方信息
     */
    private void addExaminationFee(BusPrescription busPrescription, BusConsultationSettings setting) {
        if (Objects.isNull(setting)) {
            log.warn("未找到医院id：{}的问诊设置信息，无法设置诊查费", busPrescription.getHospitalId());
            return;
        }
        //设置诊查费
        if (Boolean.TRUE.equals(setting.getPrescriptionAddExaminationFee())) {
            //用药处方不走诊查费, 普通问诊则走诊查费费
            if (!Objects.equals("1", busPrescription.getIdentity()) && Objects.nonNull(busPrescription.getConsultationOrderId())) {
                //判断是否可以添加诊查费（处方开立当天，同一科室、同一医生、同一就诊人）
                Boolean canAddExaminationFee = examinationFeeHelper.canAddExaminationFee(busPrescription);
                if (!Boolean.TRUE.equals(canAddExaminationFee)) {
                    return;
                }
                BusPrescription update = new BusPrescription();
                BigDecimal prescriptionAmount = busPrescription.getPrescriptionAmount();
                BigDecimal examinationPrice = Objects.nonNull(setting.getExaminationPrice()) ? setting.getExaminationPrice() : BigDecimal.ZERO;
                update.setId(busPrescription.getId());
                update.setPrescriptionAmount(prescriptionAmount.add(examinationPrice));
                update.setExaminationFee(examinationPrice);
                update.setExaminationName(setting.getExaminationName());
                busPrescriptionMapper.updateById(update);
            }
        }
    }
}

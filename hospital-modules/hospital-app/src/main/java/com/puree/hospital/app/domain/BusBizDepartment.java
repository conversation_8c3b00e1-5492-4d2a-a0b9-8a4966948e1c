package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.puree.hospital.common.api.domain.entity.BaseEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BusBizDepartment extends BaseEntity {

    @TableId(type= IdType.AUTO)
    private Long id;
    /** 祖级列表 */
    private String ancestors;
    /** 业务科室名称 */
    private String departmentName;
    /** 业务科室编号 */
    private String departmentNumber;
    /** 医院ID */
    private Long hospitalId;
    /** 是否开放问诊（0否 1是） */
    private Integer openConsultation;
    /** 父业务科室ID */
    private Long parentId;
    /** 排序 */
    private Integer sort;
    /** 状态（0停用 1正常） */
    private Integer status;
    /**科室图片*/
    private String imageUrl;
    /** 子菜单 */
    @TableField(exist = false)
    private List<BusBizDepartment> children = new ArrayList<>();

}
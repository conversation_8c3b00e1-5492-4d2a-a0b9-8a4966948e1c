package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class BusDrugsOrder extends Entity {
    private String orderNo;
    private Long prescriptionId;
    private String deliveryType;
    /**
     * 0待支付 1待发货 2待自提 3待收货 4已取消 5已失效 6售后中 7已退款 8已完成 9退货/换货完成
     * 10售后完成 11退款中 12部分发货
     */
    private String status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;
    private String receivingUser;
    private String receivingTel;
    private String receivingAddress;
    private String amount;
    private String logisticsCompany;
    private String deliveryNo;
    /**
     * 订单药品类型（0:中药；1：西药；2协定方）
     */
    private String orderDrugsType;
    private Long drugsStoreId;
    private String drugsStoreName;
    private Long hospitalId;
    private String hospitalName;
    private String pickUpTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;
    private String orderClassify;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    /**
     * 是否删除（0否 1是）
     */
    private String delStatus;
    /**
     * 来源 0药房 1药店
     */
    private String source;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区/县
     */
    private String area;
    /**
     * 详细地址
     */
    private String detailedAddress;
    /**
     * 物流公司代码
     */
    private String expressCode;
    /**
     * 0药房订单 1配送企业订单
     */
    private String orderType;
    /**
     * 机构code
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String partnersCode;
    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;
    /**
     * 香雪返回的订单号
     */
    private String xxOrderNumber;
    /**
     * 至信返回的订单号
     */
    private String zxOrderNumber;
    /** 患者ID */
    private Long patientId;
    /**
     * 运费
     */
    private Double freight;
    /**
     * 支付方式 0-微信支付 1-通联支付
     */
    private String payWay;

    /** 非处方中药品列表 */
    @TableField(exist = false)
    private List<BusOtcDrugs> list;
    /**
     * 是否有售后信息（1代表有）
     */
    @TableField(exist = false)
    private Integer exitAfterSale;

    /** 不予退款原因 */
    @TableField(exist = false)
    private String reason;

    /**
     * 通联订单编号
     */
    private String tonglianTrxid;
    /**
     * 订单包裹信息
     */
    @TableField(exist = false)
    private List<BusDrugOrderPackage> packageList;
    /**
     * 是否推送成功 0失败 1成功
     */
    private Integer isSendSuccess;
    /**
     * 中药加工费
     */
    @TableField(exist = false)
    private BigDecimal processPrice;
    /**
     * 提货码
     */
    @TableField(exist = false)
    private String pickupCode;
    /**
     * 倒计时时间戳
     */
    @TableField(exist = false)
    private Long expirationTime;
    /**
     * 就诊人ID
     */
    @TableField(exist = false)
    private Long familyId;
    /**
     * 就诊人姓名
     */
    @TableField(exist = false)
    private String familyName;
    /**
     * 就诊人性别
     */
    @TableField(exist = false)
    private Integer familySex;
    /**
     * 就诊人年龄
     */
    @TableField(exist = false)
    private String familyAge;
    /**
     * 备注
     */
    @TableField(exist = false)
    private String remarks;
    /**
     * 是否修改地址（0否 1是）
     */
    @TableField(exist = false)
    private Integer modifyAddress;
    /**
     * 现金支付
     */
    @TableField(exist = false)
    private BigDecimal ownPayAmt;
    /**
     * 医保基金支付
     */
    @TableField(exist = false)
    private BigDecimal fundPay;
    /**
     * 个人账户支出
     */
    @TableField(exist = false)
    private BigDecimal psnAcctPay;

    @Override
    public BusDrugsOrder setId(Long id){
        super.setId(id);
        return this;
    }

    @TableField(exist = false)
    private BusOrder busOrder ;

    /**
     * 诊查费
     */
    private BigDecimal examinationFee;

    /**
     * 诊查费名称
     */
    private String examinationName;

    /**
     * 其他现金支付金额（运费 + 诊查费）
     */
    @TableField(exist = false)
    private BigDecimal otherCashAmount;

    /**
     * 医保类型：11-普通门诊，14-门特门慢
     */
    @TableField(exist = false)
    private String miMedType;

    /**
     * 运费方式
     * */
    @TableField(exist = false)
    private String freightType;
}

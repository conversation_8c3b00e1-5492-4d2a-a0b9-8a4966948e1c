package com.puree.hospital.app.hispay.upload;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.MiPayResult;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.MiPayResultMapper;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.core.enums.FeeSettleTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.insurance.api.RemotePreprocessorService;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadResultDTO;
import com.puree.hospital.his.api.feign.RemoteHisPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * His 订单信息上传
 * <AUTHOR>
 * @date 2025/1/9 14:54
 */
@Slf4j
@RefreshScope
public abstract class BaseOrderUploadHandler {

    @Resource
    private MiPayResultMapper miPayResultMapper;
    @Resource
    private BusPatientFamilyMapper busPatientFamilyMapper;
    @Resource
    private BusConsultationOrderMapper busConsultationOrderMapper;
    @Resource
    private RemotePreprocessorService remotePreprocessorService;
    @Resource
    private BusOrderMapper busOrderMapper;
    @Resource
    private RemoteHisPayService remoteHisPayService;
    @Value("${soap.userId:HLW001}")
    public String hisUserId;

    /**
     * 缴费DOT组装
     * @param busOrder 总订单号
     * @param prescriptions 处方列表信息
     * @return 缴费DTO
     */
    HisPayUploadDTO assembleUploadDTO(BusOrder busOrder, List<BusPrescription> prescriptions){
        BusPrescription prescription = prescriptions.get(0);
        log.info("HIS支付信息上报-数据组装开始，订单：{}，处方：{}", busOrder, prescription);
        Long hospitalId = prescription.getHospitalId();
        BusPatientFamily patientFamily = busPatientFamilyMapper.selectById(prescription.getFamilyId());
        if(patientFamily == null){
            log.error("HIS支付信息上报-根据处方信息未获取到就诊人信息：{}", prescription);
            throw new ServiceException("未获取到就诊人信息");
        }
        Long consultationId = prescription.getConsultationOrderId();
        if(consultationId == null){
            log.error("HIS支付信息上报-处方问诊信息缺失：{}", prescription);
            throw new ServiceException("处方问诊信息缺失");
        }
        BusConsultationOrder consultationOrder = busConsultationOrderMapper.selectById(consultationId);
        if(consultationOrder == null){
            log.error("HIS支付信息上报-问诊订单信息缺失：{}", prescription);
            throw new ServiceException("问诊订单信息缺失");
        }
        HisPayUploadDTO uploadDTO = new HisPayUploadDTO();
        String recipeSEQStr = prescriptions.stream().map(item -> {
            if(StringUtils.isNotEmpty(item.getExaminationFeeNumber())){
                return String.format("%s|%s", item.getPrescriptionNumber(), item.getExaminationFeeNumber());
            }else{
                return item.getPrescriptionNumber();
            }
        }).collect(Collectors.joining("|"));
        uploadDTO.setRecipeSEQ(recipeSEQStr);
        uploadDTO.setCardNO(patientFamily.getHisPatientId());
        uploadDTO.setTotCost(String.valueOf(busOrder.getRelPrice()));
        uploadDTO.setPrepayCost("0");
        uploadDTO.setPubCost("0");
        uploadDTO.setRegisterNO(consultationOrder.getSerialNo());
        uploadDTO.setUserID(hisUserId);
        uploadDTO.setOwnCost(String.valueOf(busOrder.getRelPrice()));
        uploadDTO.setPayOrdId(busOrder.getTonglianTrxid());
        uploadDTO.assemblePayInfo(new BigDecimal(String.valueOf(busOrder.getRelPrice())), null);

        if(FeeSettleTypeEnum.isMiSettle(prescription.getFeeSettleType())
                && PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay())){
            MiPayResult miPayResult = miPayResultMapper.selectOne(new LambdaQueryWrapper<MiPayResult>()
                    .eq(MiPayResult::getOrderNo, busOrder.getOrderNo())
                    .last(" limit 1"));
            log.info("医保支付结果查询：{}", miPayResult);
            if(miPayResult == null){
                log.error("HIS支付信息上报-医保支付结果查询为空，hospitalId：{}，orderNo：{}", hospitalId, busOrder.getOrderNo());
                throw new ServiceException("医保支付结果查询为空");
            }
            String totalCost = miPayResult.getFeeSumamt().add(miPayResult.getOtherCashAmount()).toString();
            uploadDTO.assemblePayInfo(miPayResult.getPsnCashPay(), miPayResult.getPsnAcctPay());
            uploadDTO.setTotCost(totalCost);
            uploadDTO.setRegNo(miPayResult.getMdtrtId());
            String settleId = remotePreprocessorService.getSettleId(hospitalId, miPayResult.getOrderNo()).getData();
            if(StringUtils.isEmpty(settleId)){
                log.error("HIS支付信息上报-结算id获取失败，hospitalId：{}，orderNo：{}", hospitalId, miPayResult.getOrderNo());
                throw new ServiceException("结算id获取失败");
            }
            uploadDTO.setSetlId(settleId);
//            uploadDTO.assembleTransInfo(miPayResult.getPatientInfo(), miPayResult.getRequestData(), miPayResult.getResponseData());
            uploadDTO.assembleTransInfo(null, null, miPayResult.getResponseData());
            uploadDTO.setOwnCost(miPayResult.getPsnCashPay().add(miPayResult.getPsnAcctPay().add(miPayResult.getExaminationFee())).toString());
            uploadDTO.setPayOrdId(miPayResult.getMedTransId());
            uploadDTO.setPayType(FeeSettleTypeEnum.MI_GENERAL.getType());
            log.info("HIS支付信息上报-数据组装结束：{}", uploadDTO);
        }

        return uploadDTO;
    }

    /**
     * 缴费上报
     * @param uploadDTO 上传请求参数
     * @param orderNo 总订单号
     */
    void upload(HisPayUploadDTO uploadDTO, String orderNo){
        log.info("缴费信息上报接口请求参数：{}", JSONObject.toJSONString(uploadDTO));
        try {
            AjaxResult<HisPayUploadResultDTO> uploadResult = remoteHisPayService.payUpload(uploadDTO);
            log.info("缴费信息上报接口返回：{}", uploadResult);
            if(uploadResult == null || uploadResult.getData() == null){
                log.error("缴费信息上报失败，请求参数：{}，失败信息：{}", uploadDTO, uploadResult);
                throw new ServiceException(String.format("缴费信息上报失败：%s", Optional.ofNullable(uploadResult).map(AjaxResult::getMsg).orElse("")));
            }
            if(StringUtils.isEmpty(uploadResult.getData().getTranSerialNO())){
                log.error("缴费信息上报-获取结算流水号失败，请求参数：{}，结果信息：{}", uploadDTO, uploadResult.getData());
                throw new ServiceException("缴费信息上报-获取结算流水号失败");
            }
            BusOrder busOrder = new BusOrder();
            busOrder.setIsPayUpload(true);
            busOrder.setSerialNo(uploadResult.getData().getTranSerialNO());
            busOrder.setRegisterNo(uploadDTO.getRegisterNO());
            busOrderMapper.update(busOrder, new LambdaQueryWrapper<BusOrder>().eq(BusOrder::getOrderNo, orderNo));
        } catch (Exception e) {
            log.error("缴费信息上报失败, 错误: {}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

}

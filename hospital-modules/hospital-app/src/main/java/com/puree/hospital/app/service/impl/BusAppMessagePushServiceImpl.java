package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.domain.BusAppMessagePush;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusDoctorPushSetup;
import com.puree.hospital.app.domain.BusImGroupMember;
import com.puree.hospital.app.infrastructure.getui.GetuiUtil;
import com.puree.hospital.app.infrastructure.getui.model.Notification;
import com.puree.hospital.app.mapper.BusAppMessagePushMapper;
import com.puree.hospital.app.mapper.BusImGroupMemberMapper;
import com.puree.hospital.app.service.IBusAppMessagePushService;
import com.puree.hospital.app.service.IBusDoctorPushSetupService;
import com.puree.hospital.common.core.enums.ImMemberEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusAppMessagePushServiceImpl implements IBusAppMessagePushService {
    private final BusAppMessagePushMapper busAppMessagePushMapper;
    private final IBusDoctorPushSetupService busDoctorPushSetupService;
    private final GetuiUtil getuiUtil;
    private final BusImGroupMemberMapper busImGroupMemberMapper;

    @Override
    public BusAppMessagePush select(BusAppMessagePush busAppMessagePush) {
        QueryWrapper<BusAppMessagePush> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(busAppMessagePush.getPusherId()), "pusher_id",
                busAppMessagePush.getPusherId());
        queryWrapper.eq(Objects.nonNull(busAppMessagePush.getType()), "type", busAppMessagePush.getType());
        return busAppMessagePushMapper.selectOne(queryWrapper);
    }

    @Override
    public int insert(BusAppMessagePush busAppMessagePush) {
        return busAppMessagePushMapper.insert(busAppMessagePush);
    }

    @Override
    public int delete(BusAppMessagePush busAppMessagePush) {
        QueryWrapper<BusAppMessagePush> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("client_id", busAppMessagePush.getClientId());
        queryWrapper.eq("type", busAppMessagePush.getType());
        queryWrapper.eq("pusher_id", busAppMessagePush.getPusherId());
        return busAppMessagePushMapper.delete(queryWrapper);
    }

    @Override
    public int refreshCid(BusAppMessagePush busAppMessagePush) {
        UpdateWrapper<BusAppMessagePush> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("type", busAppMessagePush.getType());
        updateWrapper.eq("pusher_id", busAppMessagePush.getPusherId());
        return busAppMessagePushMapper.update(busAppMessagePush, updateWrapper);
    }

    @Override
    public void pushListByCid(Long pusherId, String type, Notification notice) {
        log.info("推送消息给医生或药师:{}", JSON.toJSONString(notice));
        log.info("推送消息给医生或药师id: pusherId={},type={}", pusherId, type);
        BusDoctorPushSetup pushSetup = busDoctorPushSetupService.selectBusDoctorPushSetup(null, pusherId, type);
        if (Objects.isNull(pushSetup)) {
            return;
        }
        if (YesNoEnum.NO.getCode().equals(pushSetup.getIsPush())) {
            return;
        }
        BusAppMessagePush busAppMessagePush = new BusAppMessagePush();
        busAppMessagePush.setPusherId(pusherId);
        //修改为不只是医生角色可用
        BusAppMessagePush messagePush = this.select(busAppMessagePush);
        if (Objects.nonNull(messagePush) && StringUtils.isNotEmpty(messagePush.getClientId())) {
            notice.setCid(messagePush.getClientId());
            getuiUtil.pushToSingleByCid(notice);
        }
    }

    @Override
    public void pushListByCid(BusDoctorPatientGroup patientGroup, Notification notice) {
        log.info("推送消息给服务包群组信息={}，推送消息={}", JSONObject.toJSONString(patientGroup), JSONObject.toJSONString(notice));
        // 查询服务包群组成员信息
        LambdaQueryWrapper<BusImGroupMember> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusImGroupMember::getGroupId, patientGroup.getId());
        lambdaQuery.ne(BusImGroupMember::getRole, ImMemberEnum.PATIENT.getCode());
        List<BusImGroupMember> imGroupMembers = busImGroupMemberMapper.selectList(lambdaQuery);
        List<String> cidList = new ArrayList<>();
        for (BusImGroupMember imGroupMember : imGroupMembers) {
            BusDoctorPushSetup pushSetup = busDoctorPushSetupService
                    .selectBusDoctorPushSetup(null, imGroupMember.getPersonnelId(), imGroupMember.getRole());
            if (YesNoEnum.YES.getCode().equals(pushSetup.getIsPush())) {
                BusAppMessagePush busAppMessagePush = new BusAppMessagePush();
                busAppMessagePush.setPusherId(imGroupMember.getPersonnelId());
                busAppMessagePush.setType(imGroupMember.getRole());
                BusAppMessagePush messagePush = this.select(busAppMessagePush);
                if (Objects.nonNull(messagePush) && StringUtils.isNotEmpty(messagePush.getClientId())) {
                    cidList.add(messagePush.getClientId());
                }
            }
        }
        if (CollectionUtil.isNotEmpty(cidList)) {
            notice.setCidList(cidList);
            Map<String, String> resultMap = getuiUtil.pushListByCid(notice);
            log.info("批量推送消息结果={}", JSON.toJSONString(resultMap));
        }
    }
}

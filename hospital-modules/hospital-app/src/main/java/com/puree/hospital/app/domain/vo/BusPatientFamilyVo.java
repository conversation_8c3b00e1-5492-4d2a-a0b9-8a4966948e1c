package com.puree.hospital.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 就诊人信息表
 * <AUTHOR>
 */
@Data
public class BusPatientFamilyVo implements Serializable {

    private static final long serialVersionUID = -2575087089826677021L;

    /** 姓名 */
    private Long id;
    /** 姓名 */
    private String name;
    /** 手机号 */
    private String cellPhoneNumber;
    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfBirth;
    /** 性别（0女 1男） */
    private String sex;
    /** 患者关系（字典表获取） */
    private String patientRelationship;
    /** 是否默认 */
    private Integer status;
    /** 患者ID */
    private Long patientId;
    /** 医院id */
    private Long hospitalId;
    /** 民族 */
    private String nation;

    /** 身份证 */
    private String idNumber;

    private String diagnosisArchives;
    /**
     * 门诊号
     */
    private String outpatientNumber;

    private Integer age;
    private Integer height;
    private Integer weight;

    /**
     * 就诊人年龄 （小于1岁时显示月和天）
     */
    private String familyAge;
    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 婚姻状况（0 未婚 1 已婚）
     */
    private Integer maritalStatus;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 住址所在地区
     */
    private String location;
    /**
     * 详细地址
     */
    private String detailAddress;

    private String province;
    private String city;
    private String area;
    /**
     * 职业
     */
    private String profession;
    /**
     * 文化程度
     */
    private String educationLevel;

    /**
     * 添加来源 0普通添加 1扫码添加
     */
    private String source;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 证件类型  01：居民身份证  03：护照  06：港澳居民来往内地通行证  07：台湾居民来往内地通行证
     * {@link IdCardTypeEnum}
     */
    private String idType ;

    /**
     * 监护人ID
     */
    private Long guardianId;

    /** 注册时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 患者ID
     */
    private String hisPatientNo;
}

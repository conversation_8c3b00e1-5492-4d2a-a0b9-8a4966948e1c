package com.puree.hospital.app.queue.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.puree.hospital.app.api.model.event.order.OrderCancelEvent;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.event.order.OrderCancelEventProducer;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @ClassName: DrugsOrderCancelConsumer
 * @Date 2024/2/27 9:07
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
@Slf4j
public class DrugsOrderCancelConsumer extends RedisStreamConsumer<Message> {

    @Autowired
    private IBusDrugsOrderService busDrugsOrderService;
    @Autowired
    private IBusOrderService busOrderService;
    @Autowired
    private OrderCancelEventProducer orderCancelEventProducer;

    @Override
    public void onMessage(RedisMessage<Message> message) throws Exception {
        List<BusOrder> busOrders = null;
        if (null != message.getBody().getId()){
            busOrders = busOrderService.selectOrderById(message.getBody().getId());
        } else if (StrUtil.isNotBlank(message.getBody().getOrderNo())) {
            busOrders = busOrderService.selectOrederByNo(message.getBody().getOrderNo());
        }

        if (CollectionUtil.isNotEmpty(busOrders)) {
            BusOrder busOrder = busOrders.get(0);
            //订单状态为待支付，再取消
            if (busOrder.getOrderStatus().equals(DrugsOrderEnum.PENDING_PAYMENT.getCode())){
                DrugsOrderDto dto = new DrugsOrderDto();
                dto.setOrderNo(busOrder.getOrderNo());
                busDrugsOrderService.cancel(dto);
                log.info("药品订单取消成功!:{}", dto.getOrderNo());

                // 发送订单已取消事件通知
                OrderCancelEvent orderCancelEvent = new OrderCancelEvent();
                orderCancelEvent.setHospitalId(busOrder.getHospitalId());
                orderCancelEvent.setTotalOrderNo(busOrder.getOrderNo());
                orderCancelEventProducer.send(orderCancelEvent);
            }
        }
    }

}

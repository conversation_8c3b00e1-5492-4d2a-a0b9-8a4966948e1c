package com.puree.hospital.app.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.app.domain.BusDrugsInstruction;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 医生药房药品包装类
 */
@Data
public class DoctorOfficinaDrugsVo implements Comparable<DoctorOfficinaDrugsVo> {

    private Long id;
    /**
     * 国药准字(英文简写)
     */
    private String nmpn;
    /**
     * 建议用量
     */
    private String recommendedDosage;
    /**
     * 药理/功效分类(字典表关联)
     */
    private String classifyName;
    /**
     * 医院药房或药店药品ID
     */
    private Long drugsSourceId;

    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 目录类型 1院内 2双通道
     */
    private String directoryType;
    /**
     * 药品ID
     */
    private Long drugsId;
    /**
     * 药品名称
     */
    private String drugsName;
    /**
     * 标准通用名
     */
    private String standardCommonName;
    /**
     * 药品厂商
     */
    private String drugsManufacturer;
    /**
     * 药品规格
     */
    private String drugsSpecification;
    /**
     * 药品图片
     */
    private String drugsImg;
    /**
     * 药品分类Id
     */
    private Long classifyId;
    /**
     * 药品用法
     */
    private Long drugsUsage;
    /**
     * 药品用法值
     */
    private String drugsUsageValue;
    /**
     * 销售价
     */
    private BigDecimal sellingPrice;
    /**
     * 库存
     */
    private Integer stock;
    /**
     * 是否常用（1是 0否）
     */
    private Integer status;
    /**
     * 是否历史用药（1是 0否）
     */
    private Integer usedStatus;
    /**
     * 药品祖级列表
     */
    private String ancestors;
    /**
     * 药品祖级ID
     */
    private Long ancestorsId;
    /**
     * 详细规格
     */
    private String detailedSpecifications;
    /**
     * 配送方ID
     */
    private Long enterpriseId;
    /**
     * 是否处方药
     * 0 是
     * 1 否
     */
    private String preType;
    /**
     * 药品详情图*/
    private String drugsImgDetail;
    /**
     * 药品主图*/
    private String mainImg;

    /**
     * 最小包装数量
     * */
    private Integer  minPackNum;
    /**
     * 最小制剂单位
     * */
    private String  minMakeUnit;
    /**
     * 最小包装单位
     * */
    private String  minPackUnit;

    /**
     * 药品上下架状态
     */
    private Integer drugStatus;
    /**
     * 有无药品说明书
     */
    @TableField(exist = false)
    private Boolean isDrugsInstruction;
    /**
     * 药品说明书
     */
    @TableField(exist = false)
    private BusDrugsInstruction drugsInstruction;
    /**
     * 购物车药品数量
     */
    @TableField(exist = false)
    private Integer quantity;
    /**
     * 购物车ID
     */
    @TableField(exist = false)
    private Long cartId;
    /**
     * 药品详情
     */
    private String drugsDetails;

    /**
     * 医保类型
     */
    private Long medicalInsuranceType;

    /**
     * 医保编码
     */
    private String nationalDrugCode;

    /**
     * 推荐人id
     */
    private Long referrer;

    /**
     * 药品包装单位(字典表关联)
     */
    private Long drugsPackagingUnit;

    /**
     * 药品包装单位(字典表关联)
     */
    private String drugsPackagingUnitName;

    /**
     * 基本剂量
     */
    private BigDecimal baseDose;

    /**
     * 基本剂量单位
     */
    private String baseDoseUnit;

    /**
     * 每次用量
     */
    private BigDecimal singleDose;

    /**
     * 默认频次，参考频次字典表
     */
    private String defaultFrequency;

    /**
     * 推荐使用天数
     */
    private String recommendUseDays;

    /**
     * 药品剂型 药品剂型(字典表关联) dict_type bus_drugs_dosage_form
     */
    private Long drugsDosageForm;

    /**
     * 药品剂型
     */
    private String drugsDosageFormName;

    /**
     * 无参构造函数
     */
    public DoctorOfficinaDrugsVo() {
        // 默认无说明书
        isDrugsInstruction = false;
    }

    @Override
    public int compareTo(DoctorOfficinaDrugsVo p) {
        p.setStock(null == p.getStock() ? 0:p.getStock());
        this.setStock(null == this.getStock() ? 0 : this.getStock());
        return p.getStock() - this.getStock();
    }
}

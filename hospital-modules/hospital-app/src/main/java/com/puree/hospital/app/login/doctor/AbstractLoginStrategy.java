package com.puree.hospital.app.login.doctor;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.mapper.BusDoctorHospitalMapper;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.app.service.IBusSignatureService;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.redis.service.RedisService;

import javax.annotation.Resource;
import java.util.Map;

import static com.puree.hospital.common.core.constant.CacheConstants.LOGIN_TOKEN_KEY;

/**
 * 抽象登录策略
 *
 * <AUTHOR>
 * @date 2025/7/31 10:31:22
 */
public abstract class AbstractLoginStrategy {

    @Resource
    private IBusDoctorService busDoctorService;

    @Resource
    private RedisService redisService;

    @Resource
    private BusDoctorHospitalMapper busDoctorHospitalMapper;

    @Resource
    private IBusPatientService busPatientService;

    @Resource
    private IBusSignatureService busSignatureService;

    protected void refreshToken(String token, Long doctorId) {
        LambdaQueryWrapper<BusDoctorHospital> qw = new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getDoctorId, doctorId)
                .isNotNull(BusDoctorHospital::getDoctorToken)
                .last("limit 1");
        BusDoctorHospital doctorHospital = busDoctorHospitalMapper.selectOne(qw);
        if (doctorHospital != null) {
            String doctorToken = doctorHospital.getDoctorToken();
            if (StringUtils.isNotBlank(doctorToken)) {
                //删除token
                redisService.deleteObject(LOGIN_TOKEN_KEY + doctorToken);
            }
        }
        BusDoctorHospital busDoctorHospital = new BusDoctorHospital();
        busDoctorHospital.setId(doctorId);
        busDoctorHospital.setDoctorToken(token);
        //将医生token存入数据库
        busDoctorService.updateToken(busDoctorHospital);
    }

    protected void saveMsgAndSetIdentity(Map<String, Object> map, String token, String phoneNumbers, String ipAddr, Long userid, String role) {
        SMSLoginUser smsLoginUser = new SMSLoginUser();
        smsLoginUser.setToken(token);
        smsLoginUser.setUserid(userid);
        smsLoginUser.setIdentity(role);
        smsLoginUser.setUsername(DESUtil.decrypt(phoneNumbers));
        //缓存短信登录信息
        busPatientService.setTokenCache(token, smsLoginUser);
        // 保存登录信息
        busPatientService.saveMsg(null, userid, token, ipAddr, role, null);
        map.put("identity", role);
    }

    protected String checkPassword(String passWord, Long id, String role) {
        if (StringUtils.isNotEmpty(passWord)) {
            BusSignature busSignature = new BusSignature();
            busSignature.setObjectId(id);
            busSignature.setObjectType(role);
            BusSignature signature = busSignatureService.select(busSignature);
            if (signature == null || StringUtils.isEmpty(signature.getPassword())) {
                return "该手机号未设置密码,请使用验证码登录";
            } else if (!SecurityUtils.matchesPassword(passWord, signature.getPassword())) {
                return "用户名或密码错误";
            }
        }
        return null;
    }

}

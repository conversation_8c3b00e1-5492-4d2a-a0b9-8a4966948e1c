package com.puree.hospital.app.service;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.dto.PayDTO;

import java.util.HashMap;
import java.util.Map;

public interface IWxPayService {
    JSONObject wxPay(Map<String, String> map);

    JSONObject refund(Map<String, Object> map);

    void payUpdateDrugsOrderStatus(String orderNo);

    void payUpdateConsultationOrderStatus(String orderNo);

    void payUpdateServicePackOrderStatus(String orderNo, String autoActivate);

    void refundUpdateDrugsOrderStatus(String orderNo);

    void refundConsultationOrderStatus(String orderNo);

    void refundServicePackOrderStatus(String orderNo);

    JSONObject queryOrder(Long hospitalId, String appType,String mchType, String orderNo);

    void updateStatus(BusConsultationOrder consultationOrder, String automaticRefund);

    void consultationOrder(HashMap<String, String> map, Long subOrderId, Integer pCode);

    void drugsOrder(HashMap<String, String> map, BusOrder busOrder, Integer pCode);

    void goodsOrder(HashMap<String, String> map, PayDTO dto);

    void payUpdateDrugsAndGoodsOrderStatus(BusOrder order);

    void refundTotalOrderStatus(Long orderAfterSaleId, String orderNo);

    void pushDrugOrderToEnterprise(BusOrder order);

    void pushShopOrderToEnterprise(BusOrder order);

    /**
     * 更新医保售后单退款状态
     * @param orderNo - 订单号
     */
    void updateInsuranceRefundAfterSaleOrderStatus(String orderNo);

    /**
     * 检查订单openid和appid-是否是合作机构订单-是就替换
     * @param map - 支付订单信息
     * @param hospitalId - 医院ID
     * @param partnersCode - 合作机构编码
     * @param patientId - 换着id
     * @param pCode - 合作机构标识 null是合作机构订单 1是医院订单
     */
    void checkAppidAndOpenid(HashMap<String, String> map, Long hospitalId, String partnersCode, Long patientId, Integer pCode);
}

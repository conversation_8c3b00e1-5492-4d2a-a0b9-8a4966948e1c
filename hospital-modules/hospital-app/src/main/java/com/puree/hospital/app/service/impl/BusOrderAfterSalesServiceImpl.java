package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.model.event.aftersale.UserCloseAfterSaleEvent;
import com.puree.hospital.app.domain.BusDrugOrderPackage;
import com.puree.hospital.app.domain.BusDrugs;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusHospitalPa;
import com.puree.hospital.app.domain.BusNegotiationRecord;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderAfterSales;
import com.puree.hospital.app.domain.BusOrderShop;
import com.puree.hospital.app.domain.BusOtcDrugs;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.dto.BusOrderAfterSalesDTO;
import com.puree.hospital.app.domain.dto.OrderAfterSalesDTO;
import com.puree.hospital.app.domain.dto.RefundAmountDTO;
import com.puree.hospital.app.domain.vo.BusAfterSaleDetailVO;
import com.puree.hospital.app.domain.vo.BusAfterSaleGood;
import com.puree.hospital.app.domain.vo.DrugsAndGoodsDetailVO;
import com.puree.hospital.app.domain.vo.OrderDetailVO;
import com.puree.hospital.app.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.app.mapper.BusDrugsMapper;
import com.puree.hospital.app.mapper.BusHospitalPaMapper;
import com.puree.hospital.app.mapper.BusNegotiationRecordMapper;
import com.puree.hospital.app.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.mapper.BusOrderShopMapper;
import com.puree.hospital.app.mapper.BusOtcDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusNegotiationRecordService;
import com.puree.hospital.app.service.IBusOrderAfterSalesService;
import com.puree.hospital.common.core.calculator.price.IPriceCalculator;
import com.puree.hospital.common.core.calculator.price.drug.MmDrugPriceCalculator;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.OrderAfterSalesConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AfterSaleRefundTypeEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.RefundOnlyEnum;
import com.puree.hospital.common.core.enums.RefundReturnEnum;
import com.puree.hospital.common.core.enums.RefundsExchangesEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.order.api.RemoteBusShopOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单售后信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOrderAfterSalesServiceImpl implements IBusOrderAfterSalesService {
    private final BusOrderAfterSalesMapper orderAfterSalesMapper;
    private final BusNegotiationRecordMapper negotiationRecordMapper;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final BusPrescriptionDrugsMapper rxDrugsMapper;
    private final BusDrugsMapper drugsMapper;
    private final RemoteBusShopOrderService remoteBusShopOrderService;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusOrderMapper busOrderMapper;
    private final BusPrescriptionMapper rxMapper;
    private final BusHospitalPaMapper paMapper;
    private final BusOtcDrugsMapper otcDrugsMapper;
    private final IBusNegotiationRecordService negotiationService;
    private final IBusDrugsOrderService busDrugsOrderService;
    private final BusOrderShopMapper busOrderShopMapper;
    private final IPriceCalculator<DrugsAndGoodsDetailVO> wmDrugPriceCalculator = new MmDrugPriceCalculator<>();

    @Resource
    private ApplicationEventPublisher publisher;
    /**
     * 提交售后信息
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submit(OrderAfterSalesDTO dto) {
        log.info("提交售后入参={}", JSONObject.toJSONString(dto));
        /*
         * 拆分售后单规则：
         * ①处方：一个处方对应一个售后单
         * ②otc药和商品：一个相同otc药或商品（非件数）对应一个售后单
         * */
        List<BusOrderAfterSales> tcmPrescriptionList = dto.getPrescriptionIdList();
        List<BusOrderAfterSales> goodsAndDrugsList = dto.getGoodsAndDrugsList();
        // 定义售后单集合
        List<BusOrderAfterSales> orderAfterSalesList = new ArrayList<>();
        LambdaQueryWrapper<BusOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BusOrder::getOrderNo, dto.getOrderNo());
        List<BusOrder> busOrders = busOrderMapper.selectList(wrapper);
        // 定义订单可申请的售后单数
        int number = 0;
        // 订单运费
        Double freight = 0d;
        if (StringUtils.isNotEmpty(busOrders)) {
            Double orderFreight = busOrders.get(0).getFreight();
            if (!freight.equals(orderFreight)) {
                freight = orderFreight;
                number = calOrderAfterSaleNumber(busOrders);
            }
        }
        if (StringUtils.isNotEmpty(tcmPrescriptionList)) {
            // 查询中药处方金额
            LambdaQueryWrapper<BusPrescription> lambdaQuery = Wrappers.lambdaQuery();
            List<Long> tcmPrescriptionIds = tcmPrescriptionList.stream().map(BusOrderAfterSales::getPrescriptionId).collect(Collectors.toList());
            lambdaQuery.in(BusPrescription::getId, tcmPrescriptionIds);
            List<BusPrescription> prescriptions = busPrescriptionMapper.selectList(lambdaQuery);
            for (BusOrderAfterSales afterSales : tcmPrescriptionList) {
                for (BusPrescription prescription : prescriptions) {
                    if (afterSales.getPrescriptionId().equals(prescription.getId())) {
                        afterSales.setAfterSalesType(CodeEnum.NO.getCode());
                        afterSales.setRefundAmount(prescription.getPrescriptionAmount());
                    }
                }
            }
            orderAfterSalesList.addAll(tcmPrescriptionList);
        }
        if (StringUtils.isNotEmpty(goodsAndDrugsList)) {
            // 西药处方集合
            List<BusOrderAfterSales> wmPrescriptionList = new ArrayList<>();
            Iterator<BusOrderAfterSales> iterator = goodsAndDrugsList.iterator();
            while (iterator.hasNext()) {
                BusOrderAfterSales next = iterator.next();
                if (StringUtils.isNotNull(next.getPrescriptionId())) {
                    next.setGoodsId(null);
                    next.setSellingPrice(null);
                    next.setQuantity(null);
                    wmPrescriptionList.add(next);
                    iterator.remove();
                }
            }
            if (StringUtils.isNotEmpty(wmPrescriptionList)) {
                Map<Long, List<BusOrderAfterSales>> map = wmPrescriptionList.stream()
                        .collect(Collectors.groupingBy(BusOrderAfterSales::getPrescriptionId));
                for (Long prescriptionId : map.keySet()) {
                    List<BusOrderAfterSales> busOrderAfterSales = map.get(prescriptionId);
                    BusOrderAfterSales afterSales = new BusOrderAfterSales();
                    afterSales.setPrescriptionId(prescriptionId);
                    afterSales.setOrderId(busOrderAfterSales.get(0).getOrderId());
                    afterSales.setAfterSalesType(CodeEnum.NO.getCode());
                    // 查询处方信息
                    BusPrescription prescription = busPrescriptionMapper.selectById(prescriptionId);
                    afterSales.setRefundAmount(prescription.getPrescriptionAmount());
                    orderAfterSalesList.add(afterSales);
                }
            }
            // 校验云仓商品是否全部申请售后
            checkCloudWarehouse(goodsAndDrugsList);
            orderAfterSalesList.addAll(goodsAndDrugsList);
        }
        if (StringUtils.isNotEmpty(orderAfterSalesList)) {
            // 订单是否全部售后
            boolean flag = false;
            if (number != 0) {
                // 查询该订单已申请的售后单数
                LambdaQueryWrapper<BusOrderAfterSales> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusOrderAfterSales::getOrderNo, dto.getOrderNo());
                //筛掉售后状态为售后结束的情况
                lambdaQuery.ne(BusOrderAfterSales::getAfterSalesStatus,OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode());
                List<BusOrderAfterSales> afterSalesList = orderAfterSalesMapper.selectList(lambdaQuery);
                if (StringUtils.isNotEmpty(afterSalesList)) {
                    List<String> refundTypeList = afterSalesList.stream().map(BusOrderAfterSales::getRefundType).collect(Collectors.toList());
                    // 已申请的售后单数
                    int afterSalesNumber = refundTypeList.size() + orderAfterSalesList.size();
                    if (!refundTypeList.contains("2") && number == afterSalesNumber) {
                        flag = true;
                    }
                } else if (number == orderAfterSalesList.size()) {
                    flag = true;
                }
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            for (int i = 0; i < orderAfterSalesList.size(); i++) {
                BusOrderAfterSales afterSales = orderAfterSalesList.get(i);
                afterSales.setHospitalId(dto.getHospitalId());
                Calendar instance = Calendar.getInstance();
                instance.add(Calendar.SECOND, i + 1);
                afterSales.setAfterSalesNumber(simpleDateFormat.format(instance.getTime()));
                afterSales.setApplyTime(DateUtils.getNowDate());
                afterSales.setRefundType(dto.getRefundType());
                afterSales.setAfterSalesCause(dto.getAfterSalesCause());
                //退换货金额为零
                if(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode().equals(dto.getRefundType())){
                    afterSales.setRefundAmount(new BigDecimal(0.00));
                }else{
                    if (StringUtils.isNull(afterSales.getPrescriptionId())) {
                        BigDecimal refundAmount;
                        // 申请单个otc或商品取修改金额
                        if (orderAfterSalesList.size() == 1) {
                            refundAmount = dto.getRefundAmount();
                        } else {
                            refundAmount = afterSales.getSellingPrice().multiply(new BigDecimal(afterSales.getQuantity()));
                        }
                        afterSales.setRefundAmount(refundAmount);
                    }
                }
                afterSales.setDescription(dto.getDescription());
                afterSales.setAfterSalesVoucher(dto.getAfterSalesVoucher());
                afterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.APPLY_AFTER_SALES_SERVICE.getCode());
                afterSales.setOrderNo(dto.getOrderNo());
                afterSales.setCreateTime(DateUtils.getNowDate());
                // 订单全部售后，最后一个售后单退运费
                if (flag && i + 1 == orderAfterSalesList.size()) {
                    afterSales.setRefundAmount(afterSales.getRefundAmount().add(new BigDecimal(freight)));
                    log.info("最后一个售后单信息={}", JSONObject.toJSONString(afterSales));
                }
            }
            // 批量新增售后单
            orderAfterSalesMapper.batchInsert(orderAfterSalesList);

            // 批量新增协商记录
            List<BusNegotiationRecord> negotiationRecords = new ArrayList<>();
            for (BusOrderAfterSales afterSales : orderAfterSalesList) {
                BusNegotiationRecord negotiationRecord = OrikaUtils.convert(dto, BusNegotiationRecord.class);
                negotiationRecord.setRefundAmount(afterSales.getRefundAmount());
                negotiationRecord.setRoleType(CodeEnum.NO.getCode());
                negotiationRecord.setAfterSalesStatusCode(OrderAfterSalesStatusEnum.APPLY_AFTER_SALES_SERVICE.getCode());
                String refundType = dto.getRefundType();
                String afterSalesCause = dto.getAfterSalesCause();
                if (AfterSaleRefundTypeEnum.REFUND_ONLY.getCode().equals(refundType)) {
                    negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.SQTK);
                    RefundOnlyEnum typeByCode = RefundOnlyEnum.getTypeByCode(afterSalesCause);
                    if (StringUtils.isNotNull(typeByCode)) {
                        negotiationRecord.setCause(typeByCode.getInfo());
                    }
                } else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(refundType)) {
                    negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.SQTHTK);
                    RefundReturnEnum typeByCode = RefundReturnEnum.getTypeByCode(afterSalesCause);
                    if (StringUtils.isNotNull(typeByCode)) {
                        negotiationRecord.setCause(typeByCode.getInfo());
                    }
                } else {
                    negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.THH);
                    RefundsExchangesEnum typeByCode = RefundsExchangesEnum.getTypeByCode(afterSalesCause);
                    if (StringUtils.isNotNull(typeByCode)) {
                        negotiationRecord.setCause(typeByCode.getInfo());
                    }
                }
                negotiationRecord.setReplenishDescription(dto.getDescription());
                negotiationRecord.setReplenishVoucher(dto.getAfterSalesVoucher());
                negotiationRecord.setCreateTime(DateUtils.getNowDate());
                negotiationRecord.setOrderAfterSalesId(afterSales.getId());
                negotiationRecords.add(negotiationRecord);
            }
            negotiationRecordMapper.batchInsert(negotiationRecords);
        }
        return 1L;
    }

    private void checkCloudWarehouse(List<BusOrderAfterSales> goodsAndDrugsList) {
        if (ObjectUtil.isNotEmpty(goodsAndDrugsList)) {
            Long orderId = goodsAndDrugsList.get(0).getOrderId();
            LambdaQueryWrapper<BusOrderShop> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusOrderShop::getOrderId, orderId);
            List<BusOrderShop> shopList = busOrderShopMapper.selectList(lambdaQuery);
            // 过滤出云仓商品id
            List<Long> goodsIdList = shopList.stream()
                    .filter(s -> s.getEnterpriseId() != null && s.getEnterpriseId() == 0L)
                    .map(BusOrderShop::getShopId)
                    .collect(Collectors.toList());
            // 申请售后的商品id
            List<Long> saleGoodsIdList = goodsAndDrugsList.stream()
                    .map(BusOrderAfterSales::getGoodsId)
                    .collect(Collectors.toList());
            // 判断云仓商品是否全部申请售后
            List<Long> cloudWarehouse = goodsIdList.stream()
                    .filter(saleGoodsIdList::contains).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(cloudWarehouse)) {
                for (BusOrderShop shop : shopList) {
                    for (BusOrderAfterSales sales : goodsAndDrugsList) {
                        if (sales.getGoodsId().equals(shop.getShopId()) && !sales.getQuantity().equals(shop.getQuantity())) {
                            throw new ServiceException("云仓商品不支持单个商品退款！");
                        }
                    }
                }
            }
        }
    }

    private int calOrderAfterSaleNumber(List<BusOrder> busOrders) {
        // 定义订单可申请的售后单数
        int i = 0;
        for (BusOrder order : busOrders) {
            if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                BusDrugsOrder drugsOrder = busDrugsOrderService.selectOrderInfo(order.getSubOrderId());
                if (StringUtils.isNotNull(drugsOrder)) {
                    if (StringUtils.isNotNull(drugsOrder.getPrescriptionId())) {
                        i += 1;
                    } else {
                        LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusOtcDrugs::getDrugsOrderId, order.getSubOrderId());
                        List<BusOtcDrugs> otcDrugsList = otcDrugsMapper.selectList(lambdaQuery);
                        i = i + otcDrugsList.size();
                    }
                }
            } else {
                LambdaQueryWrapper<BusOrderShop> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusOrderShop::getOrderId, order.getSubOrderId());
                List<BusOrderShop> shopList = busOrderShopMapper.selectList(lambdaQuery);
                i = i + shopList.size();
            }
        }
        return i;
    }


    /**
     * 查询售后方式
     *
     * @param dto
     * @return
     */
    @Override
    public List<String> way(OrderAfterSalesDTO dto) {
        log.info("查询售后方式入参={}", JSONObject.toJSONString(dto));
        List<String> resultList = new ArrayList<>();
        // 查询总订单
        BusOrder order = busOrderMapper.selectOne(
                new LambdaQueryWrapper<BusOrder>()
                        .eq(BusOrder::getOrderNo, dto.getOrderNo())
                        .orderByDesc(BusOrder::getId)
                        .last("limit 1"));
        if (Objects.isNull(order)) {
            return resultList;
        }
        // 自提订单时 + 无需物流时  只有仅退款
        // DeliveryType=1是物流 0是自提 2是无需物流
        if(!"1".equals(order.getDeliveryType())){
            resultList.add(AfterSaleRefundTypeEnum.REFUND_ONLY.getCode());
            return resultList;
        }
        //判断是否是换货订单
        BigDecimal relPrice = order.getRelPrice();
        BigDecimal a = new BigDecimal(0);
        if(a.compareTo(relPrice) == 0){
            resultList.add(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode());
            return resultList;
        }
        //// 中西药处方集合
        //List<BusOrderAfterSales> prescriptionList = dto.getPrescriptionIdList();
        //// otc药、商品集合
        //List<BusOrderAfterSales> goodsAndDrugsList = dto.getGoodsAndDrugsList();
        //List<BusDrugOrderPackage> packageList = new ArrayList<>();
        //Map<String, Object> params = new HashMap<>();
        //if (StringUtils.isNotEmpty(prescriptionList)) {
        //    List<Long> prescriptionIdList = prescriptionList.stream().map(BusOrderAfterSales::getPrescriptionId).collect(Collectors.toList());
        //    params.put("type", 0);
        //    params.put("prescriptionIdList", prescriptionIdList);
        //    packageList.addAll(busDrugOrderPackageMapper.selectDeliveryStatus(params));
        //}
        //if (StringUtils.isNotEmpty(goodsAndDrugsList)) {
        //    for (BusOrderAfterSales goodsAndDrugs : goodsAndDrugsList) {
        //        params.put("type", 1);
        //        params.put("businessType", goodsAndDrugs.getAfterSalesType());
        //        params.put("orderId", goodsAndDrugs.getOrderId());
        //        params.put("businessId", goodsAndDrugs.getGoodsId());
        //        packageList.addAll(busDrugOrderPackageMapper.selectDeliveryStatus(params));
        //    }
        //}
        List<BusDrugOrderPackage> packageList = busDrugOrderPackageMapper.selectDeliveryStatus(dto.getOrderNo());
        if (StringUtils.isEmpty(packageList)) {
            resultList.add(AfterSaleRefundTypeEnum.REFUND_ONLY.getCode());
        } else {
            for (int i = 0; i < packageList.size(); i++) {
                BusDrugOrderPackage orderPackage = packageList.get(i);
                boolean flag = resultList.contains(AfterSaleRefundTypeEnum.REFUND_RETURN.getCode());
                if (!flag) {
                    resultList.add(AfterSaleRefundTypeEnum.REFUND_RETURN.getCode());
                }
                if (YesNoEnum.YES.getCode().equals(orderPackage.getDeliveryType()) &&
                        !resultList.contains(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode())) {
                    resultList.add(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode());
                }
            }
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int returnGoods(BusOrderAfterSalesDTO orderAfterSalesDTO) {
        BusOrderAfterSales afterSales = orderAfterSalesMapper.selectById(orderAfterSalesDTO.getId());
        //更新
        if (!OrderAfterSalesStatusEnum.AGREE_TO_RETURN_AFTER_SALE.getCode().equals(afterSales.getAfterSalesStatus())) {
            throw new ServiceException("售后状态改变，无法退货");
        }
        BusOrderAfterSales updateAfterSales = new BusOrderAfterSales();
        updateAfterSales.setId(orderAfterSalesDTO.getId());
        updateAfterSales.setLogisticsCompany(orderAfterSalesDTO.getLogisticsCompany());
        updateAfterSales.setExpressCode(orderAfterSalesDTO.getExpressCode());
        updateAfterSales.setDeliveryNo(orderAfterSalesDTO.getDeliveryNo());
        updateAfterSales.setReturnTime(DateUtils.getNowDate());
        updateAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.RETURNED_GOODS_TO_BE_CONFIRMED.getCode());
        updateAfterSales.setUpdateTime(DateUtils.getNowDate());
        orderAfterSalesMapper.updateById(updateAfterSales);
        //插入协商记录
        BusNegotiationRecord negotiationRecord = new BusNegotiationRecord();
        negotiationRecord.setLogisticsCompany(orderAfterSalesDTO.getLogisticsCompany());
        negotiationRecord.setDeliveryNo(orderAfterSalesDTO.getDeliveryNo());
        negotiationRecord.setReplenishVoucher(orderAfterSalesDTO.getReplenishVoucher());
        negotiationRecord.setReplenishDescription(orderAfterSalesDTO.getReplenishDescription());
        negotiationRecord.setOrderAfterSalesId(orderAfterSalesDTO.getId());
        negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.YTHDDSF);
        negotiationRecord.setAfterSalesStatusCode(OrderAfterSalesStatusEnum.RETURNED_GOODS_TO_BE_CONFIRMED.getCode());
        negotiationRecord.setRoleType(CodeEnum.NO.getCode());
        negotiationRecord.setHospitalId(afterSales.getHospitalId());
        negotiationRecord.setRefundType(afterSales.getRefundType());
        negotiationRecord.setCreateBy(SecurityUtils.getUsername());
        negotiationRecord.setCreateTime(DateUtils.getNowDate());
        negotiationRecordMapper.insert(negotiationRecord);
        return 1;
    }

    @Override
    public int updateReturnGoods(BusOrderAfterSalesDTO orderAfterSalesDTO) {
        BusOrderAfterSales afterSales = orderAfterSalesMapper.selectById(orderAfterSalesDTO.getId());
        if (!(OrderAfterSalesStatusEnum.RETURNED_GOODS_TO_BE_CONFIRMED.getCode().equals(afterSales.getAfterSalesStatus())
                || OrderAfterSalesStatusEnum.REFUSE_RECEIVE_GOODS.getCode().equals(afterSales.getAfterSalesStatus()))) {
            throw new ServiceException("售后状态改变，无法修改物流信息");
        }
        //更新
        BusOrderAfterSales updateAfterSales = new BusOrderAfterSales();
        updateAfterSales.setId(orderAfterSalesDTO.getId());
        updateAfterSales.setDeliveryNo(orderAfterSalesDTO.getDeliveryNo());
        updateAfterSales.setExpressCode(orderAfterSalesDTO.getExpressCode());
        updateAfterSales.setLogisticsCompany(orderAfterSalesDTO.getLogisticsCompany());
        updateAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.RETURNED_GOODS_TO_BE_CONFIRMED.getCode());
        updateAfterSales.setReturnTime(DateUtils.getNowDate());
        updateAfterSales.setUpdateTime(DateUtils.getNowDate());
        orderAfterSalesMapper.updateById(updateAfterSales);
        //插入协商记录
        BusNegotiationRecord negotiationRecord = new BusNegotiationRecord();
        negotiationRecord.setLogisticsCompany(orderAfterSalesDTO.getLogisticsCompany());
        negotiationRecord.setDeliveryNo(orderAfterSalesDTO.getDeliveryNo());
        negotiationRecord.setRoleType(CodeEnum.NO.getCode());
        negotiationRecord.setLogisticsCompany(orderAfterSalesDTO.getLogisticsCompany());
        negotiationRecord.setReplenishVoucher(orderAfterSalesDTO.getReplenishVoucher());
        negotiationRecord.setReplenishDescription(orderAfterSalesDTO.getReplenishDescription());
        negotiationRecord.setOrderAfterSalesId(orderAfterSalesDTO.getId());
        negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.XGTHXX);
        negotiationRecord.setAfterSalesStatusCode(OrderAfterSalesStatusEnum.RETURNED_GOODS_TO_BE_CONFIRMED.getCode());
        negotiationRecord.setHospitalId(afterSales.getHospitalId());
        negotiationRecord.setRefundType(afterSales.getRefundType());
        negotiationRecord.setCreateBy(SecurityUtils.getUsername());
        negotiationRecord.setCreateTime(DateUtils.getNowDate());
        negotiationRecordMapper.insert(negotiationRecord);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int revocation(BusOrderAfterSalesDTO orderAfterSalesDTO) {
        BusOrderAfterSales afterSales = orderAfterSalesMapper.selectById(orderAfterSalesDTO.getId());
        if (OrderAfterSalesStatusEnum.AFTER_SALE.getCode().equals(afterSales.getAfterSalesStatus())
                || OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode().equals(afterSales.getAfterSalesStatus())) {
            throw new ServiceException("售后状态改变，无法撤销");
        }
        //更新
        BusOrderAfterSales updateAfterSales = new BusOrderAfterSales();
        updateAfterSales.setId(orderAfterSalesDTO.getId());
        updateAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode());
        updateAfterSales.setUpdateTime(DateUtils.getNowDate());
        orderAfterSalesMapper.updateById(updateAfterSales);
        //插入协商记录
        BusNegotiationRecord negotiationRecord = new BusNegotiationRecord();
        negotiationRecord.setRoleType(CodeEnum.NO.getCode());
        negotiationRecord.setOrderAfterSalesId(orderAfterSalesDTO.getId());
        negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.CXSH);
        negotiationRecord.setAfterSalesStatusCode(OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode());
        negotiationRecord.setRefundType(afterSales.getRefundType());
        negotiationRecord.setHospitalId(afterSales.getHospitalId());
        negotiationRecord.setRefundType(afterSales.getRefundType());
        negotiationRecord.setCreateBy(SecurityUtils.getUsername());
        negotiationRecord.setCreateTime(DateUtils.getNowDate());
        negotiationRecordMapper.insert(negotiationRecord);

        // 发生用户主动关闭售后事件
        UserCloseAfterSaleEvent userCloseAfterSaleEvent = new UserCloseAfterSaleEvent();
        userCloseAfterSaleEvent.setTotalOrderNo(afterSales.getOrderNo());
        userCloseAfterSaleEvent.setHospitalId(afterSales.getHospitalId());
        publisher.publishEvent(userCloseAfterSaleEvent);

        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int modifyApplyFor(BusOrderAfterSalesDTO orderAfterSalesDTO) {
        BusOrderAfterSales afterSales = orderAfterSalesMapper.selectById(orderAfterSalesDTO.getId());
        if (OrderAfterSalesStatusEnum.AFTER_SALE.getCode().equals(afterSales.getAfterSalesStatus())
                || OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode().equals(afterSales.getAfterSalesStatus())) {
            throw new ServiceException("售后状态改变，无法修改");
        }
        //更新
        BusOrderAfterSales updateAfterSales = new BusOrderAfterSales();
        updateAfterSales.setId(orderAfterSalesDTO.getId());
        updateAfterSales.setRefundAmount(Objects.isNull(orderAfterSalesDTO.getRefundAmount())?null
                :orderAfterSalesDTO.getRefundAmount());
        updateAfterSales.setQuantity(orderAfterSalesDTO.getQuantity());
        updateAfterSales.setAfterSalesCause(orderAfterSalesDTO.getAfterSalesCause());
        updateAfterSales.setRefundType(orderAfterSalesDTO.getRefundType());
        if(OrderAfterSalesStatusEnum.APPLY_AFTER_SALES_SERVICE.getCode().equals(afterSales.getAfterSalesStatus())
                ||OrderAfterSalesStatusEnum.DISAPPROVED_AFTER_SALE.getCode().equals(afterSales.getAfterSalesStatus())){
            updateAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.APPLY_AFTER_SALES_SERVICE.getCode());
        }
        //退换货金额为零
        if(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode().equals(orderAfterSalesDTO.getRefundType())){
            afterSales.setRefundAmount(new BigDecimal(0.00));
        }
        updateAfterSales.setUpdateTime(DateUtils.getNowDate());
        updateAfterSales.setAfterSalesVoucher(orderAfterSalesDTO.getAfterSalesVoucher());
        updateAfterSales.setDescription(orderAfterSalesDTO.getDescription());
        orderAfterSalesMapper.updateById(updateAfterSales);
        //插入协商记录
        BusNegotiationRecord negotiationRecord = new BusNegotiationRecord();
        String refundType = "";
        String cause = "";
        if (AfterSaleRefundTypeEnum.REFUND_ONLY.getCode().equals(orderAfterSalesDTO.getRefundType())) {
            refundType = OrderAfterSalesConstants.SQTK;
            RefundOnlyEnum refundCauseEnum = RefundOnlyEnum.getTypeByCode(orderAfterSalesDTO.getAfterSalesCause());
            cause = refundCauseEnum.getInfo();
        } else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(orderAfterSalesDTO.getRefundType())) {
            refundType = OrderAfterSalesConstants.SQTHTK;
            RefundReturnEnum refundReturnEnum = RefundReturnEnum.getTypeByCode(orderAfterSalesDTO.getAfterSalesCause());
            cause = refundReturnEnum.getInfo();
        } else {
            refundType = OrderAfterSalesConstants.THH;
            RefundsExchangesEnum exchangesEnum = RefundsExchangesEnum.getTypeByCode(orderAfterSalesDTO.getAfterSalesCause());
            cause = exchangesEnum.getInfo();
        }
        negotiationRecord.setOrderAfterSalesId(orderAfterSalesDTO.getId());
        negotiationRecord.setAfterSalesStatusCode(OrderAfterSalesStatusEnum.APPLY_AFTER_SALES_SERVICE.getCode());
        negotiationRecord.setRoleType(CodeEnum.NO.getCode());
        negotiationRecord.setRefundType(afterSales.getRefundType());
        negotiationRecord.setAfterSalesStatus(refundType);
        negotiationRecord.setRefundAmount(orderAfterSalesDTO.getRefundAmount());
        negotiationRecord.setCause(cause);
        negotiationRecord.setReplenishVoucher(orderAfterSalesDTO.getAfterSalesVoucher());
        negotiationRecord.setReplenishDescription(orderAfterSalesDTO.getDescription());
        negotiationRecord.setHospitalId(afterSales.getHospitalId());
        negotiationRecord.setRefundType(afterSales.getRefundType());
        negotiationRecord.setCreateBy(SecurityUtils.getUsername());
        negotiationRecord.setCreateTime(DateUtils.getNowDate());
        negotiationRecordMapper.insert(negotiationRecord);
        return 1;
    }

    /**
     * 计算退款金额
     *
     * @param dto
     * @return
     */
    @Override
    public BigDecimal calculateRefundAmount(RefundAmountDTO dto) {
        BigDecimal refundAmount = new BigDecimal("0");
        List<DrugsAndGoodsDetailVO> productList = dto.getProductList();
        List<OrderDetailVO> rxList = dto.getRxList();
        // 查询订单信息
        LambdaQueryWrapper<BusOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BusOrder::getOrderNo, dto.getOrderNo());
        wrapper.last(" limit 1");
        BusOrder busOrder = busOrderMapper.selectOne(wrapper);
        if (productList != null && !productList.isEmpty()) {
            for (DrugsAndGoodsDetailVO vo : productList) {
                refundAmount = refundAmount.add(wmDrugPriceCalculator.calculate(vo));
            }
            //药品按照处方分组
            Map<Long, List<DrugsAndGoodsDetailVO>> prescriptionMap = productList.stream()
                    //处方id不为空
                    .filter(s-> Objects.nonNull(s) && Objects.nonNull(s.getPrescriptionId()))
                    //是药品
                    .filter(s -> Objects.equals("0", s.getPreType()))
                    .collect(Collectors.groupingBy(DrugsAndGoodsDetailVO::getPrescriptionId));
            //计算诊查费
            if (MapUtil.isNotEmpty(prescriptionMap)) {
                for (Map.Entry<Long, List<DrugsAndGoodsDetailVO>> entry : prescriptionMap.entrySet()) {
                    List<DrugsAndGoodsDetailVO> list = entry.getValue();
                    if (CollectionUtil.isNotEmpty(list)) {
                        BigDecimal examinationFee = list.stream().map(DrugsAndGoodsDetailVO::getExaminationFee).filter(Objects::nonNull).findFirst().orElse(BigDecimal.ZERO);
                        refundAmount = refundAmount.add(examinationFee);
                    }
                }
            }
            // 订单改价取实付金额
            if (refundAmount.compareTo(busOrder.getRelPrice()) >= 0) {
                refundAmount = busOrder.getRelPrice();
            }
        }
        if (null != rxList && !rxList.isEmpty()) {
            for (OrderDetailVO vo : rxList) {
                BusPrescription prescription = busPrescriptionMapper.selectById(vo.getPrescriptionId());
                refundAmount = refundAmount.add(prescription.getPrescriptionAmount());
            }
        }
        // 查询该订单是否全部售后结束
        LambdaQueryWrapper<BusOrderAfterSales> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrderAfterSales::getOrderNo, dto.getOrderNo());
        List<BusOrderAfterSales> busOrderAfterSales = orderAfterSalesMapper.selectList(lambdaQuery);
        List<String> afterStatus = busOrderAfterSales.stream()
                .map(BusOrderAfterSales::getAfterSalesStatus).distinct().collect(Collectors.toList());
        if (afterStatus.size() == 1 && OrderAfterSalesStatusEnum.AFTER_SALE.getCode().equals(afterStatus.get(0))) {
            // 订单运费
            BigDecimal freight = BigDecimal.valueOf(busOrder.getFreight());
            refundAmount = refundAmount.add(freight);
        }
        return refundAmount;
    }

    @Override
    public BusAfterSaleDetailVO getAfterSaleDetail(Long afterSaleId) {
        BusAfterSaleDetailVO afterSaleDetail = new BusAfterSaleDetailVO();
        // 查询售后单
        BusOrderAfterSales afterSale = orderAfterSalesMapper.selectById(afterSaleId);
        StringUtils.isNullThrowExp(afterSale, "售后单已删除！");
        // 查询订单信息(子订单类型+子订单id+总订单编号)
        BusOrder order = busOrderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                .eq(BusOrder::getSubOrderType, afterSale.getAfterSalesType())
                .eq(BusOrder::getSubOrderId, afterSale.getOrderId())
                .eq(BusOrder::getOrderNo, afterSale.getOrderNo()));
        StringUtils.isNullThrowExp(order, "申请售后的订单已删除！");
        BeanUtils.copyProperties(afterSale, afterSaleDetail);
        // 总订单信息
        BigDecimal afterSaleAmount;
        if (null != order.getPaymentTime() || null != afterSale.getPrescriptionId()) {
            afterSaleAmount = order.getRelPrice();
        } else {
            afterSaleAmount = afterSale.getSellingPrice().multiply(BigDecimal.valueOf(afterSale.getQuantity()));
        }
        afterSaleDetail.setAfterSaleAmount(afterSaleAmount);
        afterSaleDetail.setOrderId(order.getId());
        afterSaleDetail.setOrderNo(order.getOrderNo());
        // 售后商品/药品信息
        List<BusAfterSaleGood> saleGoods = new ArrayList<>();
        if ((YesNoEnum.YES.getCode() + "").equals(afterSale.getAfterSalesType())) {
            // 商品类型
            R<com.puree.hospital.order.api.model.BusOrderShop> r = remoteBusShopOrderService.getOrderShop(afterSale.getOrderId(), afterSale.getGoodsId());
            log.info("远程调用商品订单查询商品返回结果：" + JSON.toJSONString(r));
            if (Constants.SUCCESS.equals(r.getCode())) {
                //BusOrderShop good = JSON.parseObject(JSON.toJSONString(r.getData()), BusOrderShop.class);
                com.puree.hospital.order.api.model.BusOrderShop good = r.getData();
                BusAfterSaleGood saleGood = new BusAfterSaleGood(
                        afterSale.getGoodsId(), good.getShopImg(), good.getShopTitle(),
                        good.getSellingPrice(), afterSale.getQuantity(), good.getShopSpecification());
                saleGood.setSubOrderId(afterSale.getOrderId());
                saleGoods.add(saleGood);
            } else {
                throw new ServiceException("商品订单查询失败");
            }
        } else {
            if (StringUtils.isNotNull(afterSale.getPrescriptionId())) {
                // 处方类型
                getRxDrugs(afterSale.getPrescriptionId(), saleGoods,afterSale.getOrderId());
            } else {
                // 查询OTC药品(子订单id+药品id)
                BusOtcDrugs otcDrug = otcDrugsMapper.selectOne(new LambdaQueryWrapper<BusOtcDrugs>()
                        .eq(BusOtcDrugs::getDrugsId, afterSale.getGoodsId())
                        .eq(BusOtcDrugs::getDrugsOrderId, afterSale.getOrderId()));
                BusAfterSaleGood saleGood = new BusAfterSaleGood(afterSale.getGoodsId(), otcDrug.getDrugsImg(), otcDrug.getDrugsName(),
                        otcDrug.getSellingPrice(), afterSale.getQuantity(), otcDrug.getDrugsSpecification());
                saleGood.setIsOtc(YesNoEnum.YES.getCode());
                saleGood.setSubOrderId(afterSale.getOrderId());
                saleGoods.add(saleGood);
            }
        }
        afterSaleDetail.setSaleItemList(saleGoods);
        // 查询售后协商记录
        List<BusNegotiationRecord> negotiationList = negotiationService.listNegotiationRecord(afterSaleId);
        afterSaleDetail.setNegotiationList(negotiationList);
        // 倒计时
        if (afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode())) {
            afterSaleDetail.setCloseTime(negotiationList.get(0).getCreateTime());
        } else if (afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.DISAPPROVED_AFTER_SALE.getCode())
                || afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.AGREE_TO_RETURN_AFTER_SALE.getCode())
                || afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.RESHIP_BUYER_RECEIVES_GOODS.getCode())
                || afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.REFUSE_RECEIVE_GOODS.getCode())) {
            if (StringUtils.isNotEmpty(negotiationList)) {
                long delayTime = negotiationList.get(0).getCreateTime().getTime() + (1000 * 60 * 60 * 24 * 7);
                long countDownTime = delayTime - System.currentTimeMillis();
                afterSaleDetail.setCountDownTime(countDownTime / 1000);
            }
        }
        return afterSaleDetail;
    }

    /**
     * 查询售后单信息
     *
     * @param orderAfterSaleId
     * @return
     */
    @Override
    public BusOrderAfterSales selectAfterSales(Long orderAfterSaleId) {
        return orderAfterSalesMapper.selectById(orderAfterSaleId);
    }

    /**
     * 修改售后单状态
     *
     * @param busOrderAfterSales
     * @return
     */
    @Override
    public int updateAfterSaleStatus(BusOrderAfterSales busOrderAfterSales) {
        return orderAfterSalesMapper.updateById(busOrderAfterSales);
    }

    /**
     * 查询售后单列表
     *
     * @param orderAfterSales
     * @return
     */
    @Override
    public List<BusOrderAfterSales> selectAfterSaleList(BusOrderAfterSales orderAfterSales) {
        LambdaQueryWrapper<BusOrderAfterSales> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrderAfterSales::getOrderNo, orderAfterSales.getOrderNo());
        lambdaQuery.eq(StringUtils.isNotEmpty(orderAfterSales.getAfterSalesStatus()),
                BusOrderAfterSales::getAfterSalesStatus, orderAfterSales.getAfterSalesStatus());
        lambdaQuery.ne(StringUtils.isNotEmpty(orderAfterSales.getRefundType()), BusOrderAfterSales::getRefundType,
                orderAfterSales.getRefundType());
        return orderAfterSalesMapper.selectList(lambdaQuery);
    }

    /**
     * 成功退款更新售后订单状态
     * @param orderAfterSaleIds - 售后单id列表
     */
    @Override
    public void updateAfterSaleStatusRefundSuccessByIds(List<Long> orderAfterSaleIds) {
        if (CollectionUtil.isEmpty(orderAfterSaleIds)) {
            return;
        }
        orderAfterSalesMapper.updateAfterSaleStatusRefundSuccessByIds(orderAfterSaleIds);
    }

    /**
     * 根据订单号查询售后单列表
     * @param orderNo - 订单号
     * @return 售后单列表
     */
    @Override
    public List<BusOrderAfterSales> selectAfterSalesListByOrderNo(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return null;
        }
        return orderAfterSalesMapper.selectAfterSalesListByOrderNo(orderNo);
    }

    /**
     * 处方售后药品封装
     *
     * @param rxId      处方id
     * @param saleGoods 售后药品项集合
     */
    private void getRxDrugs(Long rxId, List<BusAfterSaleGood> saleGoods,Long subOrderId) {
        BusPrescription rx = rxMapper.selectById(rxId);
        List<BusPrescriptionDrugs> rxDrugs = rxDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
                .eq(BusPrescriptionDrugs::getPrescriptionId, rx.getId()));
        if (PrescriptionTypeEnum.isMm(rx.getPrescriptionType())) {
            // 西药处方
            for (BusPrescriptionDrugs rxDrug : rxDrugs) {
                BusAfterSaleGood saleGood = new BusAfterSaleGood(rxDrug.getDrugsId(), rxDrug.getDrugsImg(),
                        rxDrug.getDrugsName(), rxDrug.getSellingPrice(), rxDrug.getQuantity(), rxDrug.getDrugsSpecification());
                saleGood.setPrescriptionId(rx.getId());
                saleGood.setPrescriptionType(YesNoEnum.YES.getCode());
                saleGood.setPrescriptionCreateType(rx.getIdentity());
                saleGood.setDoctorName(rx.getDoctorName());
                saleGood.setIsOtc(isOtc(rxDrug.getDrugsId()));
                saleGood.setSubOrderId(subOrderId);
                saleGoods.add(saleGood);
            }
        } else {
            // 中药处方
            StringBuilder stringBuilder = new StringBuilder();
            rxDrugs.forEach(rxDrug -> stringBuilder.append(rxDrug.getDrugsName())
                    .append(rxDrug.getDrugsSpecification())
                    .append("×")
                    .append(rxDrug.getWeight())
                    .append("、"));
            String drugName = stringBuilder.substring(0, stringBuilder.length() - 1);
            // 单剂药材价格 = (处方金额 - 加工费)/剂数
            String dosage = rx.getUsages().split(",")[1];
            BigDecimal drugsPrice = (rx.getPrescriptionAmount().subtract(rx.getProcessPrice()==null ? BigDecimal.ZERO:rx.getProcessPrice())).divide(new BigDecimal(dosage));
            BusAfterSaleGood saleGood = new BusAfterSaleGood(-1L, null, drugName, drugsPrice, Integer.parseInt(dosage), null);
            saleGood.setPrescriptionId(rx.getId());
            saleGood.setPrescriptionCreateType(rx.getIdentity());
            saleGood.setDoctorName(rx.getDoctorName());
            saleGood.setDecoctingPrice(rx.getProcessPrice());
            saleGood.setDecoctingWay(rx.getProcessingMethod());
            saleGood.setPrescriptionType(YesNoEnum.NO.getCode());
            saleGood.setPaName("中草药" + rxDrugs.size() + "味");
            saleGood.setSubOrderId(subOrderId);
            if (rx.getPaId() != null) {
                BusHospitalPa pa = paMapper.selectById(rx.getPaId());
                // 协定方
                if (YesNoEnum.NO.getCode().equals(pa.getType())) {
                    saleGood.setPaName(rx.getPaName());
                    saleGood.setPrescriptionType(YesNoEnum.OTHER.getCode());
                }
            }
            saleGoods.add(saleGood);
        }
    }

    /**
     * 判断是否为OTC药
     *
     * @param drugId 药品id
     * @return 0-处方 1-OTC
     */
    private Integer isOtc(Long drugId) {
        BusDrugs drugs = drugsMapper.selectById(drugId);
        if (391 == drugs.getPrescriptionIdentification()) {
            return 0;
        } else if (392 == drugs.getPrescriptionIdentification()) {
            return 1;
        }
        return null;
    }
}

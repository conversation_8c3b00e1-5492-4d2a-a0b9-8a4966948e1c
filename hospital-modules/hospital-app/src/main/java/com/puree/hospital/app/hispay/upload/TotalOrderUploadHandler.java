package com.puree.hospital.app.hispay.upload;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.mapper.BusDrugsOrderMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * His 总订单信息上传
 * <AUTHOR>
 * @date 2025/1/9 14:54
 */
@Slf4j
@Component(OrderTypeConstant.TOTAL_ORDER + IOrderUploadHandler.SUFFIX)
public class TotalOrderUploadHandler extends BaseOrderUploadHandler implements IOrderUploadHandler {

    @Resource
    private BusDrugsOrderMapper busDrugsOrderMapper;
    @Resource
    private BusPrescriptionMapper busPrescriptionMapper;
    @Resource
    private BusOrderMapper busOrderMapper;
    @Resource
    private IBusOrderService busOrderService;


    /**
     * 药品订单信息上传（合并支付）
     * @param hospitalId 医院id
     * @param orderNo 药品订单号
     */
    @Override
    public HisPayUploadDTO uploadOrder(Long hospitalId, String orderNo) {
        List<BusOrder> busOrderList = busOrderMapper.selectList(new LambdaQueryWrapper<BusOrder>().eq(BusOrder::getOrderNo, orderNo));
        if(CollectionUtils.isEmpty(busOrderList)){
            log.error("HIS支付信息上报-未查询到药品总订单：{}", orderNo);
            throw new ServiceException("未查询到总订单信息");
        }
        List<BusDrugsOrder> busDrugsOrderList = busDrugsOrderMapper.selectList(new LambdaQueryWrapper<BusDrugsOrder>().in(BusDrugsOrder::getId, busOrderList.stream().map(BusOrder::getSubOrderId).collect(Collectors.toList())));
        if(CollectionUtils.isEmpty(busDrugsOrderList)){
            log.error("HIS支付信息上报-未查询到药品订单：{}", orderNo);
            throw new ServiceException("未查询到药品订单");
        }

        List<BusPrescription> prescriptionList = busPrescriptionMapper.selectList(new LambdaQueryWrapper<BusPrescription>().in(BusPrescription::getId, busDrugsOrderList.stream().map(BusDrugsOrder::getPrescriptionId).collect(Collectors.toList())));
        if(CollectionUtils.isEmpty(prescriptionList)){
            log.error("HIS支付信息上报-未查询到处方列表信息：{}", busDrugsOrderList);
            throw new ServiceException("未查询到处方信息");
        }

        BusOrder busOrder = busOrderList.get(0);
        HisPayUploadDTO uploadDTO = assembleUploadDTO(busOrder, prescriptionList);
        log.info("药品订单信息上传，数据组装结果：{}", uploadDTO);
        upload(uploadDTO, orderNo);
        log.info("药品订单信息上传完成");

        // 同步药品收货信息（邮寄订单）
        log.info("同步药品收货信息：{}", busOrder);
        busOrderService.hisHandler(busOrder, "1");
        return uploadDTO;
    }
}


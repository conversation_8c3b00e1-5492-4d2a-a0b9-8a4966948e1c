package com.puree.hospital.app.login.doctor;

import cn.hutool.core.util.BooleanUtil;
import com.puree.hospital.app.constant.PhysicianLoginCheckConstant;
import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.service.IBusOfficinaPharmacistService;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.PharmacistCategoryEnum;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

import static com.puree.hospital.common.api.constant.Constants.LOGIN_SUCCESS;

/**
 * 复核药师loginCheck
 *
 * <AUTHOR>
 * @date 2025/7/30 14:34:58
 */
@Slf4j
@Order(3)
@Component(PhysicianLoginCheckConstant.DISPENSING_PHARMACIST + LoginStrategy.SUFFIX)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DispensingPharmacistLoginStrategy extends AbstractLoginStrategy implements LoginStrategy{

    private final IBusOfficinaPharmacistService busOfficinaPharmacistService;

    private final RemoteHospitalSettingApi remoteHospitalSettingApi;

    /** 医院是否开启复核药师 */
    private final String IS_ENABLE_REVIEW_PHARMACIST = "setting.isEnableReviewPharmacist";

    /**
     * 登录
     *
     * @param map         登录参数
     * @param token       登录token
     * @param phoneNumber 手机号
     * @param passWord    密码
     * @param ipAddr      ip
     * @return 登录结果
     */
    @Override
    public String loginCheck(Map<String, Object> map, String token, String phoneNumber, String passWord, String ipAddr) {
        String encryptedPhone = DESUtil.encrypt(phoneNumber);
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistService.selectPharmacistByPhone(encryptedPhone);
        if (Objects.nonNull(pharmacist)) {
            // 查询配置项
            String config = remoteHospitalSettingApi.getSettingValue(IS_ENABLE_REVIEW_PHARMACIST, pharmacist.getHospitalId()).getData();
            if (StringUtils.isEmpty(config)) {
                return "该账户为发货/调剂药师，暂未开发App功能，请前往医院后台操作";
            }
            boolean isEnableReviewPharmacist = BooleanUtil.toBoolean(config);
            if (!isEnableReviewPharmacist) {
                return "该账户为发货/调剂药师，暂未开发App功能，请前往医院后台操作";
            }
            if (!PharmacistCategoryEnum.DISPENSING_PHARMACIST.getValue().equals(pharmacist.getPharmacistCategory())) {
                return "该账户为发货药师，暂未开发App功能，请前往医院后台操作";
            }
            //密码登录校验
            if (StringUtils.isNotEmpty(passWord)) {
                if (StringUtils.isEmpty(pharmacist.getPassword())) {
                    return "该手机号未设置密码,请使用验证码登录";
                } else if (!SecurityUtils.matchesPassword(passWord, pharmacist.getPassword())) {
                    return "用户名或密码错误";
                }
            }
            refreshToken(token, pharmacist.getId());
            saveMsgAndSetIdentity(map, token, encryptedPhone, ipAddr, pharmacist.getId(), AppRoleEnum.DISPENSING_PHARMACIST.getCode());
            return LOGIN_SUCCESS;
        }
        return "手机号未注册，请联系医院管理员创建账号";
    }

    /**
     * 排序
     */
    @Override
    public int order() {
        return 3;
    }
}

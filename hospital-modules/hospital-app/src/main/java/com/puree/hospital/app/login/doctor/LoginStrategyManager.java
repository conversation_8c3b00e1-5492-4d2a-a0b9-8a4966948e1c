package com.puree.hospital.app.login.doctor;

import com.puree.hospital.common.core.constant.LoginConstants;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.puree.hospital.common.api.constant.Constants.LOGIN_SUCCESS;

/**
 * 登录检查策略管理器
 */
@Component
@Slf4j
public class LoginStrategyManager {

    private final List<LoginStrategy> strategies;
    
    public LoginStrategyManager(List<LoginStrategy> strategies) {
        // 按照顺序排序
        this.strategies = strategies.stream()
                .sorted(Comparator.comparing(LoginStrategy::order))
                .collect(Collectors.toList());
    }
    
    /**
     * 执行登录检查
     * @param map 登录结果map
     * @param token 登录token
     * @param phoneNumbers 手机号
     * @param passWord 密码
     * @param ipAddr IP地址
     * @return 错误信息，如果为null表示登录成功
     */
    public String loginCheck(Map<String, Object> map, String token, String phoneNumbers, String passWord, String ipAddr) {
        for (LoginStrategy strategy : strategies) {
            String result = strategy.loginCheck(map, token, phoneNumbers, passWord, ipAddr);
            if (LOGIN_SUCCESS.equals(result)) {
                if (!map.containsKey(LoginConstants.APP_IDENTITY)) {
                    log.info("登录异常，角色标识丢失！登录账号：{}", phoneNumbers);
                    return "登录异常，请稍后重试！";
                }
                return null;
            }else if (StringUtils.isNotBlank(result)) {
                return result;
            }
        }
        return null;
    }
}

package com.puree.hospital.app.controller;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.api.model.vo.BusCommunicationMessageConversationVO;
import com.puree.hospital.app.domain.BusCommunicationMessage;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.dto.CommunicationMessageDTO;
import com.puree.hospital.app.service.IBusCommunicationMessageService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.impl.BusPrivateChatService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ImGroupType;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.five.api.RemoteServicePackImService;
import com.puree.hospital.five.api.model.BusDoctorPatientGroupRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * im相关控制器
 */
@RestController
@RequestMapping("im")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusCommunicationMessageController extends BaseController {
    private final IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final IBusCommunicationMessageService busCommunicationMessageService;
    private final RemoteServicePackImService remoteServicePackImService;
    private final BusPrivateChatService privateChatService ;

    /**
     * 查询会话列表
     *
     * @param
     * @return
     */
    @Log(title = "查询会话列表")
    @GetMapping("/conversationList")
    public AjaxResult conversationList(BusDoctorPatientGroup busDoctorPatientGroup) {
        if (ObjectUtil.isNotNull(busDoctorPatientGroup) && StrUtil.isNotEmpty(busDoctorPatientGroup.getFamilyName())) {
            busDoctorPatientGroup.setFamilyName(busDoctorPatientGroup.getFamilyName().trim());
        }
        return AjaxResult.success(busDoctorPatientGroupService.selectConversationList(busDoctorPatientGroup));
    }



    /**
     * 查询本地会话列表
     * @param personnelId
     * @param role
     * @return
     */
    @Log(title = "查询本地会话列表")
    @GetMapping("local/conversationList")
    public AjaxResult localConversationList(@RequestParam Long personnelId,@RequestParam String role,@RequestParam(required = false) Long hospitalId){
        return  AjaxResult.success(busDoctorPatientGroupService.conversationList(personnelId,role,hospitalId));
    }

    /**
     * 网页消息数据存储
     *
     * @param busCommunicationMessage
     * @return
     */
    @PostMapping("/webpage/keepGroupMessage")
    @Log(title = "网页消息数据存储", businessType = BusinessType.INSERT)
    public AjaxResult webpageAddGroupMessage(@RequestBody BusCommunicationMessage busCommunicationMessage) {
        busCommunicationMessage.setCreateTime(DateUtils.getNowDate());
        Long messageId = busCommunicationMessageService.insertBusCommunicationMessage(busCommunicationMessage);
        busCommunicationMessage.setId(messageId);
        busCommunicationMessageService.queueMsg(busCommunicationMessage);
        return AjaxResult.success(messageId);
    }

    /**
     * app消息数据存储
     *
     * @param msgJson
     * @return
     */
    @PostMapping("/app/keepGroupMessage")
    @Log(title = "app消息数据存储", businessType = BusinessType.INSERT)
    public AjaxResult appAddGroupMessage(@RequestBody JSONObject msgJson) {
        BusCommunicationMessage busCommunicationMessage = new BusCommunicationMessage(msgJson.toJSONString());
        Long messageId = busCommunicationMessageService.insertBusCommunicationMessage(busCommunicationMessage);
        busCommunicationMessage.setId(messageId);
        busCommunicationMessageService.queueMsg(busCommunicationMessage);
        return AjaxResult.success();
    }

    /**
     * 校验医生是否更新职称
     *
     * @param groupId
     * @param title
     * @return
     */
    @GetMapping("/checkTitle")
    @Log(title = "校验医生是否更新职称", businessType = BusinessType.OTHER)
    public AjaxResult checkTitle(String groupId, String title) {
        busDoctorPatientGroupService.checkTitle(groupId, title);
        return AjaxResult.success();
    }

    /**
     * 消息记录
     *
     * @param groupId          群组id
     * @param nextReqMessageID 起始id
     * @param count            每页多少个
     * @return
     */
    @Log(title = "消息记录")
    @GetMapping("/webpage/getMessageList")
    public AjaxResult webpageGetMessageList(String groupId, Long nextReqMessageID,
                                            Integer count, Long patientId, Long familyId,
                                            Long hospitalId,@RequestParam(required = false) boolean isAssistant,
                                            @RequestParam(required = false) String conversationId) {
        return AjaxResult.success(busCommunicationMessageService.webpageGetMessageList(groupId, nextReqMessageID, count, patientId, familyId, hospitalId, isAssistant, conversationId));
    }


    /**
     * 撤回消息
     *
     * @param busCommunicationMessage
     * @return
     */
    @PostMapping("/msgWithdraw")
    @Log(title = "撤回消息", businessType = BusinessType.OTHER)
    public AjaxResult msgWithdraw(BusCommunicationMessage busCommunicationMessage) {
        return AjaxResult.success(busCommunicationMessageService.msgWithdraw(busCommunicationMessage));
    }

    /**
     * 获取1v1群组信息
     * @param groupId
     * @return
     */
    @Log(title = "获取1v1群组信息")
    @GetMapping("/getGroupInfo")
    public AjaxResult getGroupInfo(@RequestParam String groupId) {
        if (StringUtils.isEmpty(groupId) || StrUtil.isEmpty(groupId) || "null".equals(groupId)) {
            return AjaxResult.error("操作失败，请稍后再试");
        }
        return AjaxResult.success(busDoctorPatientGroupService.getGroupInfo(Long.parseLong(groupId)));
    }


    /**
     * 查询医生有哪些科室可以开通群组
     * @param busDoctorPatientGroup
     * @return
     */
    @Log(title = "查询医生有哪些科室可以开通群组")
    @PostMapping("/getDoctorDepartment")
    public AjaxResult getDoctorDepartment(@RequestBody BusDoctorPatientGroup busDoctorPatientGroup) {
        return AjaxResult.success(busDoctorPatientGroupService.getDoctorDepartment(busDoctorPatientGroup));
    }

    /**
     * 群组解散时重新创建1v1群组
     * @param busDoctorPatientGroup
     * @return
     */
    @Log(title = "群组解散时重新创建1v1群组")
    @PostMapping("/againCreateGroup")
    public AjaxResult againCreateGroup(@RequestBody BusDoctorPatientGroup busDoctorPatientGroup) {
        logger.info("校验群组信息 group={}", JSON.toJSON(busDoctorPatientGroup));

        if(ImGroupType.INQUIRIES.getCode().equals(busDoctorPatientGroup.getType())){
            BusDoctorPatientGroup patientGroup = busDoctorPatientGroupService.checkGroup(busDoctorPatientGroup);
            if(Objects.isNull(patientGroup)|| CodeEnum.NO.getCode().equals(patientGroup.getDelFlag())) {
                return AjaxResult.success();
            }
            busDoctorPatientGroup.setId(patientGroup.getId());
            busDoctorPatientGroupService.createGroup(busDoctorPatientGroup, patientGroup.getDelFlag());
        }else if(ImGroupType.SERVICE_PACK.getCode().equals(busDoctorPatientGroup.getType())){
            BusDoctorPatientGroupRequest doctorPatientGroupRequest= OrikaUtils.convert(busDoctorPatientGroup,BusDoctorPatientGroupRequest.class);
            R<Boolean> againCreateGroup = remoteServicePackImService.againCreateGroup(doctorPatientGroupRequest);
            if(Constants.FAIL.equals(againCreateGroup.getCode())){
                return AjaxResult.error("操作失败，请稍后重试");
            }
        }
        return AjaxResult.success();
    }

    /**
     * 校验群组科室是否修改
     * @param busDoctorPatientGroup
     * @return
     */
    @Log(title = "校验群组科室是否修改")
    @PostMapping("/checkDoctorDepartment")
    public AjaxResult checkDoctorDepartment(@RequestBody BusDoctorPatientGroup busDoctorPatientGroup) {
        return AjaxResult.success(busDoctorPatientGroupService.checkDoctorDepartment(busDoctorPatientGroup));
    }



    /**
     * 消息记录
     *
     * @param groupId          群组id
     * @param nextReqMessageID 起始id
     * @param count            每页多少个
     * @return
     */
    @Log(title = "消息记录")
    @GetMapping("/app/getMessageList")
    public AjaxResult appGetMessageList(String groupId, Long nextReqMessageID, Integer count) {
        if (StringUtils.isEmpty(groupId) || ObjectUtil.isNull(count)) {
            return AjaxResult.error("参数缺失");
        }
        Map<String, Object> map = new HashMap<>();
        BusCommunicationMessage busCommunicationMessage = new BusCommunicationMessage();
        busCommunicationMessage.setGroupId(groupId);
        busCommunicationMessage.setCount(count);
        busCommunicationMessage.setNextReqMessageID(nextReqMessageID);
        List<BusCommunicationMessage> messageLists =
                busCommunicationMessageService.selectBusCommunicationMessageList(busCommunicationMessage);
        List<JSONObject> json = new ArrayList<>();
        if (messageLists.isEmpty()) {
            map.put("nextReqMessageID", null);
            map.put("messageList", messageLists);
            return AjaxResult.success(map);
        }
        //排序
        List<BusCommunicationMessage> messageList =
                messageLists.stream().sorted(Comparator.comparing(BusCommunicationMessage::getId)).collect(Collectors.toList());
        Long id = messageList.get(0).getId();
        map.put("nextReqMessageID", id);
        //数据格式转换
        for (BusCommunicationMessage s : messageList) {
            JSONObject messageAppJSON = s.messageAppJSON(s);
            json.add(messageAppJSON);
        }
        List<JSONObject> list = appDateGroupBy(json);
        list.forEach(l -> {
            JSONArray jsonArray = l.getJSONArray("list");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                jsonObject.put("isPeerRead", Boolean.valueOf(jsonObject.getString("isPeerRead")));
            }
        });
        map.put("messageList", list);
        return AjaxResult.success(map);
    }

    /**
     * app消息格式数据处理
     *
     * @param json
     * @return
     */
    private List<JSONObject> appDateGroupBy(List<JSONObject> json) {
        List<JSONObject> list = new ArrayList<>();
        //今天
        List<JSONObject> todayList = new ArrayList<>();
        //昨天
        List<JSONObject> yesterdayList = new ArrayList<>();
        //历史
        List<JSONObject> historyList = new ArrayList<>();
        json.forEach(m -> {
            String judgeDate = DateUtils.judgeDate(m.getDate("createTime"));
            switch (judgeDate) {
                case "0":
                    todayList.add(m);
                    break;
                case "1":
                    yesterdayList.add(m);
                    break;
                case "2":
                    historyList.add(m);
                    break;
                default:
                    break;
            }
        });
        //处理历史消息
        Map<String, List<JSONObject>> historyMap =
                historyList.stream().collect(Collectors.groupingBy(item -> new SimpleDateFormat("yyyy-MM-dd").format(item.getDate("createTime"))));
        //排序
        Map<String, List<JSONObject>> historyMapResult = historyMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        for (Map.Entry<String, List<JSONObject>> entry : historyMapResult.entrySet()) {
            JSONObject hjson = new JSONObject();
            List<JSONObject> s = entry.getValue();
            hjson.put("time", entry.getKey() + "\t" + DateUtils.changeHHmmDate(s.get(0).getDate("createTime")));
            hjson.put("list", entry.getValue());
            list.add(hjson);
        }
        //处理昨天消息
        Map<String, List<JSONObject>> yesterdayMap =
                yesterdayList.stream().collect(Collectors.groupingBy(item -> new SimpleDateFormat("yyyy-MM-dd HH").format(item.getDate("createTime"))));
        //排序
        Map<String, List<JSONObject>> yesterdayMapResult = yesterdayMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        for (Map.Entry<String, List<JSONObject>> entry : yesterdayMapResult.entrySet()) {
            JSONObject yjson = new JSONObject();
            List<JSONObject> s = entry.getValue();
            yjson.put("time", "昨天" + DateUtils.changeHHmmDate(s.get(0).getDate("createTime")));
            yjson.put("list", entry.getValue());
            list.add(yjson);
        }
        //处理今天消息
        Map<String, List<JSONObject>> todayMap =
                todayList.stream().collect(Collectors.groupingBy(item -> new SimpleDateFormat("yyyy-MM-dd HH").format(item.getDate("createTime"))));
        //排序
        Map<String, List<JSONObject>> todayMapMapResult = todayMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        for (Map.Entry<String, List<JSONObject>> entry : todayMapMapResult.entrySet()) {
            JSONObject tjson = new JSONObject();
            List<JSONObject> s = entry.getValue();
            tjson.put("time", DateUtils.changeHHmmDate(s.get(0).getDate("createTime")));
            tjson.put("list", entry.getValue());
            list.add(tjson);
        }
        return list;
    }


    /**
     * 是否查看复诊
     * @param isFurther (0未查看  1已查询)
     * @param id
     * @return
     */
    @Log(title = "是否查看复诊")
    @GetMapping("/isFurther")
    public AjaxResult updateIsFurther(@RequestParam("isFurther") Integer isFurther,@RequestParam("id") Long id) {
        return AjaxResult.success(busCommunicationMessageService.updateIsFurther(isFurther,id));
    }


    /**
     * 查看就诊人会话列表
     * @param familyId 就诊人ID
     * @return
     */
    @GetMapping("/family-conversation")
    public R<List<BusCommunicationMessageConversationVO>> getFamilyConversationList(@RequestParam("familyId") Integer familyId) {
        return R.ok(busCommunicationMessageService.getFamilyConversationList(familyId));
    }

    /**
     * 生成会话ID
     * @return uuid
     */
    @GetMapping("/generate-conversation")
    public R<String> generateConversationId() {
        return R.ok(UUID.randomUUID().toString());
    }

    /**
     * 个推
     * @param groupId 群组ID
     * @return
     */
    @Log(title = "个推")
    @GetMapping("/pushMsg")
    public R<?> pushMsg(@RequestParam("groupId") String groupId) {
        BusCommunicationMessage busCommunicationMessage = new BusCommunicationMessage();
        busCommunicationMessage.setGroupId(groupId);
        busCommunicationMessageService.pushMsg(busCommunicationMessage);
        return R.ok();
    }

    /**
     * 修改群组消息为已读
     * @param groupId
     * @return
     */
    @GetMapping("/msg/read/{groupId}")
    public AjaxResult updateMsgRead(@PathVariable("groupId") String groupId) {
        return toAjax(busCommunicationMessageService.updateMsgRead(groupId));
    }

    /**
     * 医生发送消息提醒患者
     * @param groupId
     * @return
     */
    @GetMapping("/feign/push/pushAdvisoryNotice")
    public R pushAdvisoryNotice(@RequestParam("groupId") Long groupId) {
        BusDoctorPatientGroup doctorPatientGroup = busDoctorPatientGroupService.selectGroupInfo(groupId);
        busCommunicationMessageService.pushTemplateMsg(doctorPatientGroup);
        return R.ok();
    }


    @PostMapping("/send/imMsg")
    public AjaxResult sendImMsg(CommunicationMessageDTO dto) {
        return AjaxResult.success(busCommunicationMessageService.sendImMsg(dto));
    }

    /**
     * @Param message
     * @Return com.puree.hospital.common.core.web.domain.AjaxResult
     * @Description 新增本地消息
     * <AUTHOR>
     * @Date 2024/3/13 10:43
     **/
    @PostMapping()
    public AjaxResult insert(@RequestBody BusCommunicationMessage message) {
        return AjaxResult.success(busCommunicationMessageService.insert(message));
    }

    @PostMapping("/save-private-chat-msg")
    public AjaxResult savePrivateChatMsg(@RequestBody BusCommunicationMessage message) {
        privateChatService.savePrivateChatMsg(message) ;
        return AjaxResult.success();
    }

    /**
     * 修改1v1消息为已读
     * @param patientId
     * @param familyId
     * @return
     */
    @PostMapping("/read-private-chat-msg")
    public AjaxResult updatePrivateChatMsgRead(Long patientId, Long familyId, Long hospitalId) {
        busCommunicationMessageService.updatePrivateChatMsgRead(patientId, familyId, hospitalId) ;
        return AjaxResult.success();
    }

    /**
     * 校验群组静默状态 (远程调用)
     * @param groupId 群组id
     * @param hospitalId 医院id
     * @return
     */
    @PostMapping("/silence-group-check")
    public AjaxResult checkSilenceGroup(@RequestParam("groupId") Long groupId, @RequestParam("hospitalId") Long hospitalId) {
        logger.debug("校验群组静默状态 groupId={}， hospitalId={}", groupId, hospitalId);
        return busCommunicationMessageService.checkSilenceGroup(groupId, hospitalId);
    }

    /**
     * 随访消息的数据格式化：文本和图片消息格式化为json
     *
     * @param familyId 就诊人id
     * @return
     */
    @Log(title = "随访消息的数据格式化")
    @GetMapping("/msg/data-format")
    public AjaxResult dataFormat(@RequestParam(value = "familyId", required = false) Long familyId) {
        busCommunicationMessageService.dataFormat(familyId);
        return AjaxResult.success();
    }

}

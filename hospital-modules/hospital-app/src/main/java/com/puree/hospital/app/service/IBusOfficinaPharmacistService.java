package com.puree.hospital.app.service;

import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.dto.DispensingPharmacistDTO;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.BusOfficinaPharmacistVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBusOfficinaPharmacistService {

    /**
     * 根据手机号查询药师信息
     *
     * @param phoneNumber 手机号码
     * @return 药师信息
     */
    BusOfficinaPharmacist selectPharmacistByPhone(String phoneNumber);
    BusOfficinaPharmacist selectPharmacistByIdNumber(String idNo);
    int changePassWord(BusSignature busSignature);
    int updatePhoneNumber(String phoneNumber, String oldPhoneNumber, Long pharmacistId);
    List<BusOfficinaPharmacist> selectList(BusOfficinaPharmacist busOfficinaPharmacist);

    /**
     * 复核药师设置登录密码
     *
     * @param dto 登录信息
     * @return 设置结果
     */
    Integer modifyPassword(DispensingPharmacistDTO dto);

    /**
     * 获取复核药师信息
     *
     * @param id 复核药师id
     * @return 复核药师信息
     */
    BusDoctorVo getReviewPharmacistInfoById(Long id);

    /**
     * 获取当前登录的药师信息
     * @param userid id
     * @return 药师信息
     */
    BusOfficinaPharmacistVo getInfo(Long userid);
}

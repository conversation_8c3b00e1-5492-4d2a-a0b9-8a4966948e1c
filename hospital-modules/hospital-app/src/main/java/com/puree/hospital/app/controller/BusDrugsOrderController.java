package com.puree.hospital.app.controller;

import cn.hutool.core.util.ObjectUtil;
import com.puree.hospital.app.api.model.dto.BusFreightResultDTO;
import com.puree.hospital.app.api.model.event.order.BaseOrderEvent;
import com.puree.hospital.app.api.model.event.order.OrderCancelEvent;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusOtcDrugs;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.BusDoctorOrderDTO;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.domain.vo.BusPrescriptionVo;
import com.puree.hospital.app.domain.vo.OrderVo;
import com.puree.hospital.app.queue.producer.event.order.OrderCancelEventProducer;
import com.puree.hospital.app.helper.FreightCalculator;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.app.service.IBusPrescriptionDrugsService;
import com.puree.hospital.app.api.model.dto.BusFreightQueryDTO;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 药品订单相关控制器
 */
@RequestMapping("drugsOrder")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDrugsOrderController extends BaseController {
    private final IBusDrugsOrderService busDrugsOrderService;
    private final IBusPrescriptionDrugsService busPrescriptionDrugsService;
    private final IBusPatientService busPatientService;
    private final IBusConsultationOrderService busConsultationOrderService;
    private final FreightCalculator freightCalculator;

    @Resource @Lazy
    private OrderCancelEventProducer orderCancelEventProducer;

    @PostMapping("create")
    @Log(title = "创建订单", businessType = BusinessType.INSERT)
    public AjaxResult create(@RequestBody BusDrugsOrder drugsOrder) {
        String partnersCode = SecurityUtils.getPartnerscode();
        logger.info("机构code=" + partnersCode);
        if (StringUtils.isNotEmpty(partnersCode)) {
            drugsOrder.setPartnersCode(partnersCode);
        }
        Long hospitalId = drugsOrder.getHospitalId();
        Long prescriptionId = drugsOrder.getPrescriptionId();
        // 校验是否是非处方生成订单
        if (StringUtils.isNotNull(prescriptionId)) {
            BusPrescriptionVo prescription = busPrescriptionDrugsService.selectPrescription(hospitalId, prescriptionId);
            List<BusOtcDrugs> otcList = OrikaUtils.converts(prescription.getList(), BusOtcDrugs.class);
            List<Long> enterpriseIds = otcList.stream().map(BusOtcDrugs::getEnterpriseId).distinct().collect(Collectors.toList());
            if (enterpriseIds.size() > 1) {
                drugsOrder.setOrderType("0,1");
            } else if (enterpriseIds.size() == 1) {
                if (enterpriseIds.get(0) == null) {
                    drugsOrder.setOrderType("0");
                } else {
                    drugsOrder.setOrderType("1");
                }
            }
            drugsOrder.setList(otcList);
            if (PrescriptionStatus.CANCELLATION.equals(prescription.getStatus())) {
                return AjaxResult.error("处方已作废无法购药");
            }
            if (CodeEnum.YES.getCode().equals(prescription.getIdentity())) {
                drugsOrder.setOrderClassify(CodeEnum.YES.getCode());
            }
            if (StringUtils.isNotEmpty(prescription.getPartnersCode())) {
                drugsOrder.setPartnersCode(prescription.getPartnersCode());
            } else if (StringUtils.isNotNull(prescription.getConsultationOrderId()) && CodeEnum.YES.getCode().equals(prescription.getType())) {
                // 查询问诊订单信息
                BusConsultationOrder busConsultationOrder = busConsultationOrderService.queryOrderInfo(prescription.getConsultationOrderId());
                if (StringUtils.isNotNull(busConsultationOrder)) {
                    if (StringUtils.isNotEmpty(busConsultationOrder.getPartnersCode())) {
                        drugsOrder.setPartnersCode(busConsultationOrder.getPartnersCode());
                    } else {
                        drugsOrder.setPartnersCode(null);
                    }
                }
            }
        }
        Map<String, Object> insert = busDrugsOrderService.insert(drugsOrder);
        Long drugsOrderId = (Long) insert.get("drugsOrderId");
        if (Objects.nonNull(drugsOrderId)) {
            return AjaxResult.success(drugsOrderId);
        } else {
            return AjaxResult.success(insert);
        }
    }
    /**
     * 远程调用查询医院设置信息（废弃，发版兼容）
     *
     * @param query 请求参数
     * @return 运费信息
     */
    @Deprecated
    @Log(title = "药品配送查询配送费", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    @GetMapping("/queryFreght")
    public AjaxResult<BusFreightResultDTO> queryFreght(@ModelAttribute BusFreightQueryDTO query) {
        return AjaxResult.success(freightCalculator.calculate(query));
    }

    /**
     * 药品配送查询配送费(调整了http的method,使用新接口)
     *
     * @param query 查询参数
     * @return 运费信息
     */
    @Log(title = "药品配送查询配送费V1", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    @PostMapping("/queryFreightV1")
    public AjaxResult<BusFreightResultDTO> queryFreight(@RequestBody BusFreightQueryDTO query) {
        return AjaxResult.success(freightCalculator.calculate(query));
    }


    /**
     * 查询患者端订单详情信息
     *
     * @return
     */
    @GetMapping("query")
    @Log(title = "查询患者端订单详情信息", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public AjaxResult query(DrugsOrderDto dto) {
        return AjaxResult.success(busDrugsOrderService.select(dto));
    }

    /**
     * 查询患者端订单列表
     *
     * @return
     */
    @GetMapping("queryList4Patient")
    @Log(title = "查询患者端订单列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public TableDataInfo queryList4Patient(DrugsOrderDto dto) {
        String partnersCode = SecurityUtils.getPartnerscode();
        logger.info("药品订单机构code=" + partnersCode);
        if (StringUtils.isNotEmpty(partnersCode)) {
            dto.setPartnersCode(partnersCode);
        }
        if (ObjectUtil.isNull(dto.getHospitalId()) || ObjectUtil.isNull(dto.getPatientId())) {
            SMSLoginUser loginUser = busPatientService.queryPatientInfo();
            dto.setHospitalId(loginUser.getHospitalId());
            dto.setPatientId(loginUser.getUserid());
        }
        startPage();
        List<OrderVo> orderVos = busDrugsOrderService.queryList4Patient(dto);
        return getDataTable(orderVos);
    }

    /**
     * 查询医生药品订单列表
     * drType 0 医生 1特聘
     * @return
     */
    @Log(title = "查询医生药品订单列表")
    @GetMapping("queryList4Doctor")
    public TableDataInfo queryList4Doctor(@RequestParam("doctorId") Long doctorId,
                                          @RequestParam("hospitalId") Long hospitalId,
                                          @RequestParam("drType") Integer drType) {
        DrugsOrderDto dto = new DrugsOrderDto();
        dto.setDoctorId(doctorId);
        dto.setHospitalId(hospitalId);
        dto.setDrType(drType);
        startPage();
        return getDataTable(busDrugsOrderService.selectList(dto));
    }

    /**
     * 查询患者端订单详情商品信息
     *
     * @param dto
     * @return
     */
    @GetMapping("queryDrugsOrderInfo")
    @Log(title = "查询患者端订单详情商品信息", businessType = BusinessType.OTHER, operatorType = OperatorType.MOBILE)
    public AjaxResult queryDrugsOrderInfo(DrugsOrderDto dto) {
        return AjaxResult.success(busDrugsOrderService.queryOrderDrugsAndGoodsDetail(dto));
    }

    /**
     * 取消订单
     *
     * @param dto
     * @return
     */
    @PostMapping("cancel")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult cancel(DrugsOrderDto dto) {
        int cancel = busDrugsOrderService.cancel(dto);
        if (cancel > 0) {
            // 根据token，从redis中获取患者信息
            SMSLoginUser loginUser = busPatientService.queryPatientInfo();
            // 发生用户手动取消事件
            OrderCancelEvent orderCancelEvent = new OrderCancelEvent();
            orderCancelEvent.setEventType(BaseOrderEvent.EventType.USER_PROACTIVELY_CANCEL);
            orderCancelEvent.setTotalOrderNo(dto.getOrderNo());
            orderCancelEvent.setHospitalId(loginUser.getHospitalId());
            orderCancelEventProducer.send(orderCancelEvent);
        }
        return toAjax(cancel);
    }


    /**
     * 待支付的订单加入队列
     *
     * @param id
     * @param orderNo
     * @return
     */
    @PostMapping("delayQueue")
    @Log(title = "待支付的订单加入队列", businessType = BusinessType.OTHER, operatorType = OperatorType.MOBILE)
    public AjaxResult delayQueue(String id, String orderNo) {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        Long hospitalId = loginUser.getHospitalId();
        boolean ret = busDrugsOrderService.delayQueue(hospitalId, id, orderNo);
        return ret ? AjaxResult.success() : AjaxResult.error("加入队列失败！");
    }

    /**
     * 申请退款/换货
     *
     * @param afterSale
     * @return
     */
    @PostMapping("apply/replacement")
    @Log(title = "申请退款/换货", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    public AjaxResult applyReplacement(BusAfterSale afterSale) {
        return toAjax(busDrugsOrderService.applyReplacement(afterSale));
    }

    /**
     * 确认收货/自提
     *
     * @param orderNo 总订单编号
     * @return 操作结果
     */
    @PutMapping("confirm")
    @Log(title = "确认收货/自提", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult confirm(@RequestParam("orderNo") String orderNo) {
        return toAjax(busDrugsOrderService.confirm(orderNo));
    }

    /**
     * 删除订单
     *
     * @param orderNo
     * @return
     */
    @DeleteMapping("remove")
    @Log(title = "删除药品订单", businessType = BusinessType.DELETE, operatorType = OperatorType.MOBILE)
    public AjaxResult remove(@RequestParam("orderNo") String orderNo) {
        return toAjax(busDrugsOrderService.remove(orderNo));
    }

    /**
     * 查询医生药品订单列表
     * busDoctorOrderDTO
     * @return
     */
    @Log(title = "查询医生药品订单列表")
    @GetMapping("doctor/list")
    public TableDataInfo listShopAndDrugsOrder(BusDoctorOrderDTO busDoctorOrderDTO) {
        startPage();
        return getDataTable(busDrugsOrderService.listShopAndDrugsOrder(busDoctorOrderDTO));
    }

    /**
     * 查询订单物流信息
     * @param orderNo
     * @return
     */
    @GetMapping("/logistics/list")
    public AjaxResult queryLogisticsList(@RequestParam("orderNo") String orderNo) {
        return AjaxResult.success(busDrugsOrderService.queryLogisticsList(orderNo));
    }






}

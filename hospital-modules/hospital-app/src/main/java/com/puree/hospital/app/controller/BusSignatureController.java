package com.puree.hospital.app.controller;

import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.dto.CAPersonInfoDto;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusOfficinaPharmacistService;
import com.puree.hospital.app.service.IBusSignatureService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.PharmacistCategoryEnum;
import com.puree.hospital.common.core.enums.SignatureRole;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.security.annotation.InnerAuth;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 电子签名相关控制器
 */
@RequestMapping("signature")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusSignatureController {
    private final IBusSignatureService busSignatureService;
    private final IBusOfficinaPharmacistService busOfficinaPharmacistService;
    private final IBusDoctorService busDoctorService;

    private final static String SENSITIVE_PASSWORD = "******";

    /**
     * 获取申请证书时，需要的用户信息
     *
     * @param phoneNumber
     * @param identity
     * @return
     */
    @GetMapping("personInfo")
    @Log(title = "获取申请证书时，需要的用户信息", businessType = BusinessType.OTHER)
    public AjaxResult getCAPersonInfo(@RequestParam("phoneNumber") String phoneNumber,
                                      @RequestParam("identity") String identity) {
        CAPersonInfoDto result = null;
        if (identity.equals(AppRoleEnum.DOCTOR.getCode())||identity.equals(AppRoleEnum.PHARMACIST.getCode())) {
            List<CAPersonInfoDto> dtos = busSignatureService.getDoctorInfo(phoneNumber);
            if (CollectionUtils.isNotEmpty(dtos)) {
                result = dtos.get(0);
            }
            return AjaxResult.success(result);
        }
        return AjaxResult.error("信息不存在");
    }

    /**
     * 查询已生成的证书ID和签名文件信息
     *
     * @param
     * @return
     */
    @GetMapping("query")
    @Log(title = "查询已生成的证书ID和签名文件信息", businessType = BusinessType.OTHER)
    public AjaxResult query(@RequestParam("objectId") Long objectId,
                            @RequestParam("objectType") String objectType) {
        BusSignature busSignature = new BusSignature();
        busSignature.setObjectId(objectId);
        busSignature.setObjectType(objectType);
        BusSignature signature = busSignatureService.select(busSignature);
        if (null == signature) {
            signature = new BusSignature();
        }
        if (StringUtils.isNotEmpty(signature.getPassword())) {
            signature.setPassword(SENSITIVE_PASSWORD);
        }
        return AjaxResult.success(signature);
    }

    /**
     * 添加一条记录（记录证书ID和签名文件）
     *
     * @param
     * @param certId
     * @return
     */
    @PostMapping("insert")
    @Log(title = "添加一条记录证书ID和签名文件", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    public AjaxResult insert(@RequestParam("objectId") Long objectId,
                             @RequestParam("objectType") String objectType,
                             @RequestParam("certId") String certId,
                             @RequestParam("endDate") String endDate) {
        BusSignature busSignature = new BusSignature();
        busSignature.setObjectId(objectId);
        busSignature.setObjectType(objectType);
        busSignature.setCertId(certId);
        busSignature.setCreateBy(SecurityUtils.getUsername());
        busSignature.setCreateTime(DateUtils.getNowDate());
        busSignature.setEndDate(DateUtils.parseDate(endDate));
        BusSignature result = busSignatureService.select(busSignature);
        if (result != null) {
            return AjaxResult.success(busSignatureService.updateSignature(busSignature));
        }
        return AjaxResult.success(busSignatureService.insert(busSignature));
    }

    /**
     * 更新一条记录（更新证书ID和签名文件）
     *
     * @param
     * @param certSignature
     * @return
     */
    @PostMapping("update")
    @Log(title = "更新证书ID和签名文件", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public AjaxResult update(@RequestParam("objectId") Long objectId,
                             @RequestParam("objectType") String objectType,
                             @RequestParam("certSignature") String certSignature) {
        BusSignature busSignature = new BusSignature();
        busSignature.setObjectId(objectId);
        busSignature.setObjectType(objectType);
        busSignature.setCertSignature(certSignature);
        busSignature.setUpdateBy(SecurityUtils.getUsername());
        busSignature.setUpdateTime(DateUtils.getNowDate());
        return AjaxResult.success(busSignatureService.updateSignature(busSignature));
    }

    /**
     * 开启/关闭生物识别
     *
     * @return
     */
    @PostMapping("enableBiometrics")
    @Log(title = "开启/关闭生物识别", businessType = BusinessType.OTHER)
    public AjaxResult enableBiometrics(@RequestParam("objectId") Long objectId,
                                       @RequestParam("objectType") String objectType,
                                       @RequestParam("biometrics") String biometrics) {
        if (StringUtils.isEmpty(biometrics)) {
            return AjaxResult.error("生物识别标识不能为空");
        }
        BusSignature busSignature = new BusSignature();
        busSignature.setObjectId(objectId);
        busSignature.setObjectType(objectType);
        busSignature.setBiometrics(biometrics);
        return AjaxResult.success(busSignatureService.updateSignature(busSignature));
    }

    /**
     * 设置密码
     *
     * @return
     */
    @PostMapping("setPassword")
    @Log(title = "设置密码", businessType = BusinessType.OTHER)
    public AjaxResult setPassword(@RequestParam("objectId") Long objectId,
                                  @RequestParam("objectType") String objectType,
                                  @RequestParam("password") String password) {
        if (StringUtils.isEmpty(password)) {
            return AjaxResult.error("密码不能为空");
        }
        BusSignature busSignature = new BusSignature();
        busSignature.setObjectId(objectId);
        busSignature.setObjectType(objectType);
        busSignature.setPassword(SecurityUtils.encryptPassword(password));
        BusSignature result = busSignatureService.select(busSignature);
        if (result == null) {
            busSignature.setCreateBy(SecurityUtils.getUsername());
            busSignature.setCreateTime(DateUtils.getNowDate());
            return AjaxResult.success(busSignatureService.insert(busSignature));
        }
        return AjaxResult.success(busSignatureService.setPassword(busSignature));
    }

    /**
     * 校验密码
     *
     * @return
     */
    @PostMapping("checkPassword")
    @Log(title = "校验密码", businessType = BusinessType.OTHER)
    public AjaxResult checkPassword(@RequestParam("objectId") Long objectId,
                                    @RequestParam("objectType") String objectType,
                                    @RequestParam("password") String password) {
        if (StringUtils.isEmpty(password)) {
            return AjaxResult.error("密码不能为空");
        }
        BusSignature busSignature = new BusSignature();
        busSignature.setObjectId(objectId);
        busSignature.setObjectType(objectType);
        busSignature = busSignatureService.select(busSignature);
        return AjaxResult.success(SecurityUtils.matchesPassword(password, busSignature.getPassword()));
    }

    @InnerAuth
    @PostMapping("cert/insert")
    public AjaxResult insert(@RequestParam("idNo") String idNo, @RequestParam("certId") String certId,
                             @RequestParam("certExpire") String certExpire) {
        BusOfficinaPharmacist busOfficinaPharmacist =
                busOfficinaPharmacistService.selectPharmacistByIdNumber(idNo);
        if (busOfficinaPharmacist == null) {
            return AjaxResult.error("药师信息不存在");
        }
        BusSignature busSignature = new BusSignature();
        busSignature.setObjectId(busOfficinaPharmacist.getId());
        String objectType = "";
        if (busOfficinaPharmacist.getPharmacistCategory().equals(PharmacistCategoryEnum.DELIVERY_PHARMACIST.getValue())){
            objectType = SignatureRole.DELIVERY_PHARMACIST.getValue();
        }
        if (busOfficinaPharmacist.getPharmacistCategory().equals(PharmacistCategoryEnum.PHARMACIST.getValue())){
            objectType = SignatureRole.PHARMACIST.getValue();
        }
        busSignature.setObjectType(objectType);
        busSignature.setCertId(certId);
        busSignature.setCreateBy(SecurityUtils.getUsername());
        busSignature.setCreateTime(DateUtils.getNowDate());
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        instance.add(Calendar.YEAR, Integer.valueOf(certExpire));
        busSignature.setEndDate(instance.getTime());
        BusSignature result = busSignatureService.select(busSignature);
        if (result != null) {
            busSignature.setUpdateTime(DateUtils.getNowDate());
            busSignature.setUpdateBy(SecurityUtils.getUsername());
            return AjaxResult.success(busSignatureService.updateSignature(busSignature));
        }
        return AjaxResult.success(busSignatureService.insert(busSignature));
    }

    /**
     * 校验签名是否失效
     *
     * @param
     * @return
     */
    @GetMapping("/check/signature")
    @Log(title = "校验签名是否失效", businessType = BusinessType.OTHER)
    public AjaxResult check(@RequestParam("objectId") Long objectId,
                            @RequestParam("objectType") String objectType) {
        BusSignature signature = busSignatureService.checkSignature(objectId, objectType);
        return AjaxResult.success(signature);
    }
}

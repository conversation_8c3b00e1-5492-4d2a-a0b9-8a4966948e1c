package com.puree.hospital.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 患者就诊记录 VO
 *
 * <AUTHOR>
 * @date 2025/2/26 18:27
 */
@Data
@NoArgsConstructor
public class BusPatientRegRecordVO {

    /**
     * 挂号流水号
     */
    private String tranSerialNo;

    /**
     * 就诊日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date regDate;

    /**
     * 科室医生
     */
    private String doctor;

    /**
     * 确诊疾病
     */
    private String disease;

}

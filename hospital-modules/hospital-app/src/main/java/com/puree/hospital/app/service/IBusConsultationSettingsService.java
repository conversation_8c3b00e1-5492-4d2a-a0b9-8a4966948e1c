package com.puree.hospital.app.service;

import com.puree.hospital.app.api.model.dto.BusFreightBaseSettingDTO;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.api.model.dto.BusFreightQueryDTO;

public interface IBusConsultationSettingsService {
    BusConsultationSettings selectOne(BusConsultationSettings consultationSettings);

    /**
     * 查询运费
     * @param query 查询参数
     * @return 返回西悉尼
     */
    BusFreightBaseSettingDTO queryFreight(BusFreightQueryDTO query);
}

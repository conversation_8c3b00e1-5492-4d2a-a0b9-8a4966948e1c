package com.puree.hospital.app.login.doctor;

import java.util.Map;

/**
 * 医生登录策略接口
 *
 * <AUTHOR>
 * @date 2025/7/25 12:02:10
 */
public interface LoginStrategy {

    String SUFFIX = "LoginStrategy";

    /**
     * 登录
     * @param map 登录参数
     * @param token 登录token
     * @param phoneNumber 手机号
     * @param passWord 密码
     * @param ipAddr  ip
     * @return 登录结果
     */
    String loginCheck(Map<String, Object> map, String token, String phoneNumber, String passWord, String ipAddr);

    /**
     * 排序
     */
    int order();

}

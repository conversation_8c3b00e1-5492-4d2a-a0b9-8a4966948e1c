package com.puree.hospital.app.domain.dto;

import com.puree.hospital.common.core.enums.AuthenticationEnum;
import lombok.Data;

import java.util.List;

/**
 * 医生信息请求类
 */
@Data
public class BusDoctorDto {

    /** id */
    private Long id;
    /** 医生姓名 */
    private String fullName;
    /** 状态 0停用 1启用 */
    private Integer status;
    /** 医生职称(字典表关联数据) */
    private Long title;
    /** 医生职称证 */
    private String titleCertificate;

    /**一级科室*/
    private  Long firstLevel;
    /**二级科室*/
    private  Long secondLevel;

    private Integer isThisCourt;

    private  Long hospitalId;

    private  Integer auditStatus;

    private String phoneNumber;

    /** 一级科室*/
    private List<Long> firstLevelDepartment;

    /**
     * 二级科室
     */
    private List<Long> secondaryDepartment;

    private String hot;

    private Long patientId;
    /**
     * 搜索值
     */
    private String searchValue;

    private String beGoodAt;

    private Integer paging;

    private String type;

    private String appendSql;

    private String partnersCode;
    private Long partnerId;

    private String departmentName;
    /**
     * 0 热门医生
     * 1 特聘专家
     */
    private Integer doctorType;
    /**
     * 每页几条
     */
    private Long pageSize;
    /**
     * 第几页
     */
    private Long pageNum;
    /**
     * 起始位置
     */
    private Long offset;
    /**
     * 是否开启了 startPage
     */
    private Boolean whetherStartPage;

    /**
     * 医生角色  0:医生 1:审方药师 2:护士 4:健康管理师  5:心理咨询师 9康复师 10营养师
     */
    private String role;

    /**
     * 审核内容（0西药 1中药 多选逗号分隔）审方药师独有
     */
    private String reviewContent;

    /** 身份证号码 */
    private String idCardNo;

    /**
     * 认证状态 0未认证 1实名认证 2医师认证 3认证成功 4认证失败
     * @see AuthenticationEnum
     */
    private Integer isAuthentication;

}

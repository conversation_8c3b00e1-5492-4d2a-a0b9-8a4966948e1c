package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.app.domain.BusChannelPatientAgentRelation;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusHospitalWechatConfig;
import com.puree.hospital.app.domain.BusPartners;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.BusPatientApplet;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPatientHospital;
import com.puree.hospital.app.domain.BusPatientPartners;
import com.puree.hospital.app.domain.LoginLog;
import com.puree.hospital.app.domain.LoginMessage;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.PatientDTO;
import com.puree.hospital.app.domain.dto.PatientLoginDTO;
import com.puree.hospital.app.domain.model.UniAppUserinfo;
import com.puree.hospital.app.domain.vo.BusPatientFamilyVo;
import com.puree.hospital.app.domain.vo.BusPatientVo;
import com.puree.hospital.app.domain.vo.LoginResultVO;
import com.puree.hospital.app.infrastructure.wechat.util.WechatUtil;
import com.puree.hospital.app.login.LoginFactory;
import com.puree.hospital.app.login.processor.IPatientLoginProcessor;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusPatientAppletMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPatientHospitalMapper;
import com.puree.hospital.app.mapper.BusPatientMapper;
import com.puree.hospital.app.mapper.BusPatientPartnersMapper;
import com.puree.hospital.app.mapper.LoginLogMapper;
import com.puree.hospital.app.mapper.LoginMessageMapper;
import com.puree.hospital.app.service.IBusChannelPatientAgentRelationService;
import com.puree.hospital.app.service.IBusHospitalWechatConfigService;
import com.puree.hospital.app.service.IBusPartnersService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.service.IBusPatientPartnersService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.LoginExceptionEnums;
import com.puree.hospital.common.core.enums.LoginTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.CheckedException;
import com.puree.hospital.common.core.exception.LoginException;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.common.security.token.TokenService;
import com.puree.hospital.im.api.RemoteImService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
public class BusPatientServiceImpl extends ServiceImpl<BusPatientMapper, BusPatient> implements IBusPatientService {
    private final BusPatientMapper busPatientMapper;
    private final BusPatientHospitalMapper patientHospitalMapper;
    private final RemoteImService remoteImService;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final IBusPatientPartnersService busPatientPartnersService;
    private final IBusPartnersService busPartnersService;
    private final BusPatientPartnersMapper busPatientPartnersMapper;
    private final TokenService tokenService;
    private final BusHospitalMapper busHospitalMapper;
    private final BusPatientAppletMapper busPatientAppletMapper;
    private final RedisService redisService;

    private final LoginMessageMapper loginMessageMapper;
    private final LoginLogMapper loginLogMapper;
    private final IBusPatientHospitalService busPatientHospitalService;
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());


    private final IBusHospitalWechatConfigService busHospitalWechatConfigService;
    private final IBusChannelPatientAgentRelationService busChannelPatientAgentRelationService;


    @Override
    public String getOpenIdByPatientId(Long patientId, Long hospitalId) {
        BusPatientHospital busPatientHospital = new LambdaQueryChainWrapper<>(patientHospitalMapper)
                .eq(BusPatientHospital::getPatientId, patientId)
                .eq(BusPatientHospital::getHospitalId, hospitalId)
                .one();
        return busPatientHospital == null ? null : busPatientHospital.getOpenid();
    }

    @Override
    public BusPatient selectPatientByPhone(String mobile) {
        return patientHospitalMapper.selectPatientInfo(mobile);
    }

    @Override
    public BusPatientVo selectPatientByOpenid(String openid) {
        QueryWrapper<BusPatient> queryWrapper = new QueryWrapper<>();
        QueryWrapper<BusPatientHospital> wrapper = new QueryWrapper<>();
        wrapper.eq("openid", openid);
        BusPatientHospital busPatientHospital = patientHospitalMapper.selectOne(wrapper);
        if (busPatientHospital == null) {
            return null;
        }
        queryWrapper.eq("id", busPatientHospital.getPatientId());
        BusPatient busPatient = busPatientMapper.selectOne(queryWrapper);
        if (null == busPatient) {
            return null;
        }
        BusPatientVo patientVo = OrikaUtils.convert(busPatient, BusPatientVo.class);
        patientVo.setBusPatientHospital(busPatientHospital);
        return patientVo;
    }

    @Override
    public BusPatientVo selectPartnersPatientByOpenid(String openid) {
        LambdaQueryWrapper<BusPatientPartners> queryWrapper =
                new LambdaQueryWrapper<BusPatientPartners>().eq(BusPatientPartners::getOpenid, openid);
        BusPatientPartners busPatientPartners = busPatientPartnersMapper.selectOne(queryWrapper);
        if (busPatientPartners == null) {
            return null;
        }
        LambdaQueryWrapper<BusPatient> wrapper = new LambdaQueryWrapper<BusPatient>().eq(BusPatient::getId,
                busPatientPartners.getPatientId());
        BusPatient busPatient = busPatientMapper.selectOne(wrapper);
        if (null == busPatient) {
            return null;
        }
        BusPatientVo patientVo = OrikaUtils.convert(busPatient, BusPatientVo.class);
        patientVo.setBusPatientPartners(busPatientPartners);
        return patientVo;
    }

    @Override
    public BusPatientHospital selectPatientHospitalByOpenid(String openid) {
        QueryWrapper<BusPatientHospital> wrapper = new QueryWrapper<>();
        wrapper.eq("openid", openid);
        return patientHospitalMapper.selectOne(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public long insertBusPatient(BusPatient patient, BusPatientHospital busPatientHospital) {
        Long hospitalId = busPatientHospital.getHospitalId();
        String openid = busPatientHospital.getOpenid();
        patient.setCreateTime(DateUtils.getNowDate());
        int insert = busPatientMapper.insert(patient);
        if (YesNoEnum.YES.getCode() == insert) {
            // 默认为该账号添加一个就诊人
            BusPatientFamily patientFamily = getBusPatientFamily(patient, hospitalId);
            LambdaQueryWrapper<BusChannelPatientAgentRelation> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusChannelPatientAgentRelation::getHospitalId, hospitalId)
                   .eq(BusChannelPatientAgentRelation::getPatientId, patient.getId());
            BusChannelPatientAgentRelation busChannelPatientAgentRelation = busChannelPatientAgentRelationService.getOne(lambdaQuery);
            if (!Objects.isNull(busChannelPatientAgentRelation)) {
                // 2 - agent
                patientFamily.setSource("2");
            } else {
                patientFamily.setSource(patient.getSource());
            }
            busPatientFamilyMapper.insert(patientFamily);
        }
        String partnersCode = patient.getPartnersCode();
        if (StringUtils.isNotEmpty(partnersCode)) {
            // 查询机构信息
            BusPartners busPartners = busPartnersService.queryPartners(hospitalId, partnersCode);
            if (ObjectUtil.isNull(busPartners)) {
                throw new ServiceException("该医院还未绑定合作机构");
            }
            BusPatientPartners busPatientPartners = busPatientPartnersMapper.selectOne(new LambdaQueryWrapper<BusPatientPartners>()
                    .eq(BusPatientPartners::getOpenid, openid));
            if (ObjectUtil.isNotNull(busPatientPartners)) {
                return 0L;
                //throw new ServiceException("该微信号已绑定其它手机号");
            }
            BusPatientPartners patientPartners = new BusPatientPartners();
            patientPartners.setHospitalId(hospitalId);
            patientPartners.setPartnersId(busPartners.getId());
            patientPartners.setPatientId(patient.getId());
            patientPartners.setOpenid(openid);
            busPatientPartnersService.addInfo(patientPartners);
        } else {
            QueryWrapper<BusPatientHospital> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("openid", openid);
            BusPatientHospital patientHospital = patientHospitalMapper.selectOne(queryWrapper);
            if (ObjectUtil.isNull(patientHospital)) {
                busPatientHospital.setPatientId(patient.getId());
                busPatientHospital.setCreateTime(DateUtils.getNowDate());
                patientHospitalMapper.insert(busPatientHospital);
            } else if (ObjectUtil.isNotNull(patientHospital.getPatientId())) {
                return 0L;
                //throw new ServiceException("该微信号已绑定其它手机号");
            } else {

                busPatientHospital.setId(patientHospital.getId());
                busPatientHospital.setPatientId(patient.getId());
                busPatientHospital.setUpdateTime(DateUtils.getNowDate());
                patientHospitalMapper.updateById(busPatientHospital);
            }
        }
        R<Boolean> returnData = remoteImService.accountCheck(TencentyunImConstants.PATIENT_IM_ACCOUNT + patient.getId());
        if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) returnData.getData()) {
            R<Boolean> r = remoteImService.accountImport(TencentyunImConstants.PATIENT_IM_ACCOUNT + patient.getId(),
                    "", null);
            if (Constants.FAIL.equals(r.getCode())) {
                throw new CheckedException("账号导入患者信息失败");
            }
        }
        return patient.getId();
    }

    private static @NotNull BusPatientFamily getBusPatientFamily(BusPatient patient, Long hospitalId) {
        BusPatientFamily patientFamily = new BusPatientFamily();
        patientFamily.setHospitalId(hospitalId);
        patientFamily.setName(patient.getPhoneNumber());
        patientFamily.setCellPhoneNumber(patient.getPhoneNumber());
        patientFamily.setPatientId(patient.getId());
        patientFamily.setCreateTime(DateUtils.getNowDate());
        patientFamily.setDoctorId(patient.getDoctorId());
        return patientFamily;
    }

    @Override
    public BusPatientVo selectPatientById(SMSLoginUser smsLoginUser, String openid) {
        String loginType = smsLoginUser.getLoginType();
        Long patientId = smsLoginUser.getUserid();
        Long hospitalId = smsLoginUser.getHospitalId();
        //查询患者
        BusPatient busPatient = busPatientMapper.selectById(patientId);
        BusPatientVo patientVo = OrikaUtils.convert(busPatient, BusPatientVo.class);
        BusPatientHospital busPatientHospital = null;
        if (LoginTypeEnum.OTHER_APPLET.getCode().equals(loginType)) {
            LambdaQueryWrapper<BusPatientApplet> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery
                    .eq(BusPatientApplet::getHospitalId, hospitalId)
                    .eq(BusPatientApplet::getOpenid, openid)
                    .eq(BusPatientApplet::getPatientId, patientId);
            BusPatientApplet patientApplet = busPatientAppletMapper.selectOne(lambdaQuery);
            if (ObjectUtil.isNotNull(patientApplet)) {
                busPatientHospital = OrikaUtils.convert(patientApplet, BusPatientHospital.class);
            }
        } else if (LoginTypeEnum.AIDMED_APPLET.getCode().equals(loginType)) {
            LambdaQueryWrapper<BusPatientHospital> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BusPatientHospital::getHospitalId, hospitalId);
            wrapper.eq(BusPatientHospital::getPatientId, patientId);
            busPatientHospital = patientHospitalMapper.selectOne(wrapper);
        } else if (LoginTypeEnum.UNI_APP_APPLET.getCode().equals(loginType)) {
            if (StringUtils.isNotEmpty(SecurityUtils.getPartnerscode())) {
                BusPatientPartners busPatientPartners = busPatientPartnersMapper.selectHospitalInfoByUniAppOpenid(openid);
                if (ObjectUtil.isNotNull(busPatientPartners)) {
                    busPatientHospital = OrikaUtils.convert(busPatientPartners, BusPatientHospital.class);
                    busPatientHospital.setPartnersId(busPatientPartners.getPartnersId());
                }
            } else {
                busPatientHospital = patientHospitalMapper.selectHospitalInfoByUniAppId(openid);
            }
            // 医院公众号患者

        } else {
            if (StringUtils.isNotEmpty(SecurityUtils.getPartnerscode())) {
                BusPatientPartners busPatientPartners = busPatientPartnersMapper.selectHospitalInfo(openid);
                if (ObjectUtil.isNotNull(busPatientPartners)) {
                    busPatientHospital = OrikaUtils.convert(busPatientPartners, BusPatientHospital.class);
                    busPatientHospital.setPartnersId(busPatientPartners.getPartnersId());
                }
            } else {
                busPatientHospital = patientHospitalMapper.selectHospitalInfo(openid);
            }

        }
        patientVo.setBusPatientHospital(busPatientHospital);
        BusHospital busHospital = busHospitalMapper.selectById(hospitalId);
        patientVo.setBusHospital(busHospital);
        if (ObjectUtil.isNotNull(patientVo.getBusPatientHospital())) {
            patientVo.getBusPatientHospital().setPayWay(busHospital.getPayWay());
        }
        return patientVo;
    }

    @Override
    public Long perfectInfo(BusPatientFamily busPatientFamily) {
        busPatientFamily.setCreateTime(DateUtils.getNowDate());
        busPatientFamilyMapper.insert(busPatientFamily);
        return busPatientFamily.getId();
    }

    @Override
    public boolean checkIsPerfect(Long patientId) {
        QueryWrapper<BusPatientFamily> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("patient_id", patientId);
        List<BusPatientFamily> busPatientFamilys = busPatientFamilyMapper.selectList(queryWrapper);
        return CollectionUtil.isNotEmpty(busPatientFamilys);
    }

    @Override
    public int update(BusPatient patient) {
        return busPatientMapper.updateById(patient);
    }

    @Override
    public String getPartnersCode(Long patientId) {
        BusPatient busPatient = busPatientMapper.selectById(patientId);
        if (StringUtils.isNotEmpty(busPatient.getPartnersCode())) {
            return busPatient.getPartnersCode();
        } else {
            return "0";
        }
    }

    @Override
    public int insertPatientHospital(BusPatientHospital busPatientHospital) {
        busPatientHospital.setCreateTime(DateUtils.getNowDate());
        return patientHospitalMapper.insert(busPatientHospital);
    }

    @Override
    public SMSLoginUser queryPatientInfo() {
        //根据请求头中token，查询到用户信息
        Object userInfo = tokenService.getLoginPatientInfo();
        SMSLoginUser loginUser = OrikaUtils.convert(userInfo, SMSLoginUser.class);
        if (ObjectUtil.isNull(loginUser)) {
            throw new ServiceException("登录失效", 401);
        }
        return loginUser;
    }

    /**
     * 查询患者信息
     *
     * @param patientId
     * @return
     */
    @Override
    public BusPatient selectPatientInfo(Long patientId) {
        return busPatientMapper.selectById(patientId);
    }

    /**
     * 查询就诊人信息
     *
     * @param familyId
     * @return
     */
    @Override
    public BusPatientFamilyVo queryFamilyInfo(Long familyId) {
        return busPatientFamilyMapper.queryFamilyInfo(familyId);
    }


    /**
     * 验证患者id是否是当前登录用户
     *
     * @param patientId - 患者id
     * @return - true: 是当前登录用户 false: 不是当前登录用户
     */
    @Override
    public boolean verify(Long patientId) {
        SMSLoginUser smsLoginUser = queryPatientInfo();
        return smsLoginUser.getUserid().equals(patientId);
    }

    /**
     * 获取当前登录用户id
     *
     * @return 返回当前登录用户id
     */
    @Override
    public Long getUserId() {
        SMSLoginUser smsLoginUser = queryPatientInfo();
        return smsLoginUser.getUserid();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public <Req extends PatientLoginDTO> R<LoginResultVO> patientLogin(Req dto) {
        IPatientLoginProcessor<Req> loginProcessor = LoginFactory.getLoginProcessor(dto);
        LoginResultVO loginResultVO;
        try {
            loginResultVO = loginProcessor.login(dto);
        } catch (LoginException loginException) {
            loginResultVO = new LoginResultVO();
            LoginExceptionEnums loginExceptionEnums = LoginExceptionEnums.value(loginException.getMessage());
            if (loginExceptionEnums != null) {
                loginResultVO.setIdentification(loginExceptionEnums.getCode());
            }
            loginResultVO.setPhoneNumber(loginException.getPhoneNumber());
            loginResultVO.setOpenid(loginException.getOpenid());
            int code = 200;
            //兼容小程序
            if (LoginTypeEnum.UNI_APP_APPLET.equals(dto.getLoginType())) {
                code = 601;
            }
            return R.restResult(loginResultVO, code, loginException.getMessage());
        }
        return R.ok(loginResultVO);
    }


    /**
     * 保存 MSG
     *
     * @param hospitalId 医院 ID
     * @param id 身份证
     * @param token 令 牌
     * @param ipAddr IP 地址
     * @param roleType 角色类型
     * @param loginType 登录类型 {@link LoginTypeEnum}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMsg(Long hospitalId, Long id, String token, String ipAddr, String roleType, String loginType) {
        logger.info("登录日志入参hospitalId={}，id={}，token={}，ipAddr={}，roleType={}", hospitalId, id, token, ipAddr, roleType);
        LambdaQueryWrapper<LoginMessage> queryWrapper = new LambdaQueryWrapper<LoginMessage>()
                .eq(hospitalId != null, LoginMessage::getHospitalId, hospitalId)
                .eq(LoginMessage::getUserId, id)
                .eq(LoginMessage::getLoginType, loginType)
                .eq(LoginMessage::getUserType, roleType)
                .orderByDesc(LoginMessage::getCreateTime)
                .last("limit 1");
        LoginMessage loginMessage = loginMessageMapper.selectOne(queryWrapper);
        logger.info("登录信息={}", loginMessage);
        Long loginId;
        LoginMessage message = new LoginMessage();
        if (loginMessage == null) {
            // 保存登录信息
            message.setHospitalId(hospitalId);
            message.setToken(token);
            message.setUserId(id);
            message.setUserType(roleType);
            message.setLoginType(loginType);
            message.setCreateTime(DateUtils.getNowDate());
            loginMessageMapper.insert(message);
            loginId = message.getId();
        } else {
            // 修改登录信息
            message.setId(loginMessage.getId());
            message.setToken(token);
            loginMessageMapper.updateById(message);
            loginId = loginMessage.getId();
        }
        // 保存登录日志
        LoginLog loginLog = new LoginLog();
        loginLog.setHospitalId(hospitalId);
        loginLog.setLoginId(loginId);
        loginLog.setToken(token);
        loginLog.setLoginIp(ipAddr);
        loginLog.setLoginType(YesNoEnum.NO.getCode());
        Date nowDate = DateUtils.getNowDate();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(nowDate);
        // 把日期往后增加一天,整数  往后推,负数往前移动
        calendar.add(Calendar.DATE, 30);
        nowDate = calendar.getTime();
        loginLog.setOverdueTime(nowDate);
        loginLog.setCreateTime(DateUtils.getNowDate());
        loginLogMapper.insert(loginLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean modifyOpenid(PatientDTO dto) {
        BusPatient newPatient = selectPatientByPhone(dto.getNewPhoneNumber());
        if (ObjectUtil.isNull(newPatient)) {
            throw new ServiceException("该手机号未注册！");
        }
        //删除原有openid关联关系
        patientHospitalMapper.removeOpenid(dto.getOpenid(), dto.getUniAppOpenid());

        LambdaUpdateWrapper<BusPatientHospital> lambdaUpdate = Wrappers.lambdaUpdate();
        lambdaUpdate.eq(BusPatientHospital::getHospitalId, dto.getHospitalId())
                .eq(BusPatientHospital::getPatientId, newPatient.getId());
        if (ClientTypeEnum.WX_UNI_APP.getType().equals(dto.getAppType())) {
            lambdaUpdate.set(StringUtils.isNotEmpty(dto.getUniAppOpenid()), BusPatientHospital::getUniAppOpenid, dto.getUniAppOpenid());
        } else {
            //公众号
            lambdaUpdate.set(StringUtils.isNotEmpty(dto.getOpenid()), BusPatientHospital::getOpenid, dto.getOpenid());
        }
        return busPatientHospitalService.update(lambdaUpdate);
    }

    @Override
    public boolean uniAppCheckBind(String jsCode, Long hospitalId,String partnersCode) {
        BusHospitalWechatConfig config = busHospitalWechatConfigService.selectByHospitalId(hospitalId, ClientTypeEnum.WX_UNI_APP.getType());
        UniAppUserinfo unitAppUserinfo = WechatUtil.getUnitAppUserinfo(jsCode, config.getAppid(), config.getAppSecret());
        // 判断是否绑定了合作机构
        if (StringUtils.isNotEmpty(partnersCode)){
            BusPatientPartners busPatientPartners = busPatientPartnersMapper.queryUnitAppOpenid(unitAppUserinfo.getOpenid());
            BusPartners busPartners = busPartnersService.queryPartners(hospitalId, partnersCode);
            return busPartners != null &&
                    busPatientPartners != null &&
                    busPatientPartners.getHospitalId().equals(hospitalId) &&
                    busPatientPartners.getPartnersId().equals(busPartners.getId());
        }else {
            // 判断是否绑定了医院
            BusPatientHospital busPatientHospital = busPatientHospitalService.queryPatientHospitalByUnitAppOpenid(unitAppUserinfo.getOpenid());
            return ObjectUtil.isNotNull(busPatientHospital) && hospitalId.equals(busPatientHospital.getHospitalId());
        }
    }


    /**
     * 缓存短信登录信息
     *
     * @param token token
     * @param data  登录信息
     */
    @Override
    public void setTokenCache(String token, SMSLoginUser data) {
        String userSmsKey = CacheConstants.LOGIN_TOKEN_KEY + token;
        redisService.setCacheObject(userSmsKey, data, CacheConstants.LOGIN_TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
    }

}

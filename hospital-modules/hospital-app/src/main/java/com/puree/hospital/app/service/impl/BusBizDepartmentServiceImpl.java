package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.app.domain.BusAssociateDepartment;
import com.puree.hospital.app.domain.BusBizDepartment;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.vo.BizDepartmentTreeSelect;
import com.puree.hospital.app.domain.vo.BusDoctorDepartmentVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.mapper.BusAssociateDepartmentMapper;
import com.puree.hospital.app.mapper.BusBizDepartmentMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.service.IBusBizDepartmentService;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class BusBizDepartmentServiceImpl implements IBusBizDepartmentService {
    private final static String TCM_DOCTOR_PRACTISING_TYPE = "4" ;

    private final BusBizDepartmentMapper busBizDepartmentMapper;
    private final BusAssociateDepartmentMapper busAssociateDepartmentMapper;
    private final IBusDoctorService busDoctorService;
    private final BusDoctorMapper doctorMapper ;

    /**
     * 查询业务科室关联的数据列表
     *
     * @param busDepartment 查询参数
     * @return List<BusBizDepartment>
     */
    @Override
    public List<BusBizDepartment> selectBusBizDepartmentList(BusBizDepartment busDepartment) {
        LambdaQueryWrapper<BusBizDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(BusBizDepartment::getHospitalId, busDepartment.getHospitalId())
                .eq(BusBizDepartment::getStatus, EnableStatusEnum.ENABLED.getStatus())
                // 是否开放极速问诊
                .eq(Objects.nonNull(busDepartment.getOpenConsultation()), BusBizDepartment::getOpenConsultation, busDepartment.getOpenConsultation())
                .orderByAsc(BusBizDepartment::getSort)
                .orderByAsc(BusBizDepartment::getCreateTime);
        return busBizDepartmentMapper.selectList(queryWrapper);
    }

    /**
     * 构建业务科室下拉列表
     *
     * @param busDepartments 科室列表
     * @return List<BizDepartmentTreeSelect>
     */
    @Override
    public List<BizDepartmentTreeSelect> buildDepartmentsTreeSelect(List<BusBizDepartment> busDepartments) {
        List<BusBizDepartment> departmentTrees = buildBizDepartmentTree(busDepartments);
        return departmentTrees.stream().map(BizDepartmentTreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 查询热门科室列表
     *
     * @param busDepartment 查询科室参数
     * @return List<BusBizDepartment>
     */
    @Override
    public List<BusBizDepartment> queryHotList(BusBizDepartment busDepartment) {
        QueryWrapper<BusBizDepartment> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(busDepartment.getHospitalId()), "hospital_id", busDepartment.getHospitalId())
                .eq("hot", CodeEnum.YES.getCode())
                .eq("status", CodeEnum.YES.getCode())
                .orderByDesc("create_time").last("limit 3");
        return busBizDepartmentMapper.selectList(queryWrapper);
    }

    @Override
    public boolean checkTcmDepartment(Long hospitalId, Long departmentId) {
        QueryWrapper<BusAssociateDepartment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_department_id", departmentId);
        queryWrapper.eq("hospital_id", hospitalId);
        List<String> news = Arrays.asList(Constants.TCM_DEPARTMENT_ID.split(","));
        queryWrapper.in("department_id", news);
        List<BusAssociateDepartment> departments = busAssociateDepartmentMapper.selectList(queryWrapper);
        return CollectionUtil.isNotEmpty(departments);
    }

    @Override
    public BusBizDepartment selectById(Long id) {
        return busBizDepartmentMapper.selectById(id);
    }

    /**
     * 校验是否开放问诊
     *
     * @param hospitalId
     * @param deptId
     * @return
     */
    @Override
    public Boolean checkOpenConsultation(Long hospitalId, Long deptId) {
        LambdaQueryWrapper<BusBizDepartment> queryWrapper = new LambdaQueryWrapper<BusBizDepartment>()
                .eq(BusBizDepartment::getHospitalId, hospitalId).eq(BusBizDepartment::getId, deptId);
        BusBizDepartment busBizDepartment = busBizDepartmentMapper.selectOne(queryWrapper);
        boolean isTrue = YesNoEnum.NO.getCode().equals(busBizDepartment.getOpenConsultation());
        if (isTrue) {
            return false;
        }else{
            return true;
        }
    }

    /**
     * @Param hospitalId
     * @Param doctorId
     * @Return Boolean
     * @Description 校验是否存在中医资质
     * <AUTHOR>
     * @Date 2024/6/20 15:22
     **/
    @Override
    public Boolean checkTcmQualification(Long hospitalId, Long doctorId) {
        QueryWrapper<BusAssociateDepartment> queryWrapper = new QueryWrapper<>();
        List<Long> doctorDepartmentId = getDoctorDepartmentId(hospitalId, doctorId);
        queryWrapper.in("biz_department_id", doctorDepartmentId);
        queryWrapper.eq("hospital_id", hospitalId);
        List<String> news = Arrays.asList(Constants.TCM_DEPARTMENT_ID.split(","));
        queryWrapper.in("department_id", news);
        List<BusAssociateDepartment> departments = busAssociateDepartmentMapper.selectList(queryWrapper);
        if (StringUtils.isNotEmpty(departments)) {
            return true;
        }
        return false;
    }

    /**
     * @Param hospitalId
     * @Param doctorId
     * @Return List<Long>
     * @Description 查询医生科室信息
     * <AUTHOR>
     * @Date 2024/6/20 15:45
     **/
    @Override
    public List<Long> getDoctorDepartmentId(Long hospitalId, Long doctorId){
        BusDoctorVo busDoctorVo = busDoctorService.selectDoctorDetailInfoById(doctorId);
        List<BusDoctorDepartmentVo> list = busDoctorVo.getBusDoctorDepartmentVoList();
        return list.stream().filter(item -> item.getHospitalId().equals(hospitalId))
                .map(item -> item.getDepartmentId()).collect(Collectors.toList());
    }

    private List<BusBizDepartment> buildBizDepartmentTree(List<BusBizDepartment> busDepartments) {
        List<BusBizDepartment> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<Long>();
        for (BusBizDepartment depart : busDepartments) {
            tempList.add(depart.getId());
        }
        for (Iterator<BusBizDepartment> iterator = busDepartments.iterator(); iterator.hasNext(); ) {
            BusBizDepartment depart = (BusBizDepartment) iterator.next();
            if (!tempList.contains(depart.getParentId())) {
                recursionFn(busDepartments, depart);
                returnList.add(depart);
            }
        }
        if (returnList.isEmpty()) {
            returnList = busDepartments;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<BusBizDepartment> list, BusBizDepartment t) {
        List<BusBizDepartment> childList = getChildList(list, t);
        t.setChildren(childList);
        for (BusBizDepartment tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<BusBizDepartment> list, BusBizDepartment t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * 得到子节点列表
     */
    private List<BusBizDepartment> getChildList(List<BusBizDepartment> list, BusBizDepartment t) {
        List<BusBizDepartment> tlist = new ArrayList<>();
        Iterator<BusBizDepartment> it = list.iterator();
        while (it.hasNext()) {
            BusBizDepartment n = (BusBizDepartment) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * @Param doctorId
     * @Return Boolean
     * @Description 校验是否存在中医资质
     **/
    public Boolean checkTcmQualificationV2(Long doctorId) {
        BusDoctor doctor = doctorMapper.selectById(doctorId);
        if (null==doctor) {
            // 101-医生不存在
            throw new ServiceException("101") ;
        }
        if (StrUtil.isBlank(doctor.getPractisingTypeCode())) {
            // 102-请先设置医生的执业资质
            throw new ServiceException("102") ;
        }
        if ( TCM_DOCTOR_PRACTISING_TYPE.equals(doctor.getPractisingTypeCode()) ) {
            return true;
        }
        return false ;
    }

}

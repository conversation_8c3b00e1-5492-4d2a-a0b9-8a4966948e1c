package com.puree.hospital.app.login.doctor;

import com.puree.hospital.app.constant.PhysicianLoginCheckConstant;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.puree.hospital.common.api.constant.Constants.LOGIN_SUCCESS;

/**
 * 审方医师登录策略
 *
 * <AUTHOR>
 * @date 2025/8/12 15:02:13
 */
@Slf4j
@Order(1)
@Component(PhysicianLoginCheckConstant.REVIEW_PHARMACIST + LoginStrategy.SUFFIX)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReviewPharmacistLoginStrategy extends AbstractLoginStrategy implements LoginStrategy {

    private final IBusDoctorService busDoctorService;

    @Override
    public String loginCheck(Map<String, Object> map, String token, String phoneNumber, String passWord, String ipAddr) {
        //药师信息
        String encryptedPhone = DESUtil.encrypt(phoneNumber);
        BusDoctorHospital busDoctorHospital = busDoctorService.selectPhysicianByPhone(AppRoleEnum.PHARMACIST.getCode(), encryptedPhone);
        log.info("药师查询信息 a={}", busDoctorHospital);
        if (null != busDoctorHospital) {
            if (YesNoEnum.NO.getCode().equals(busDoctorHospital.getStatus())) {
                return "用户已被禁用,请联系管理员";
            }
            //密码登录校验
            String res = checkPassword(passWord, busDoctorHospital.getDoctorId(), AppRoleEnum.PHARMACIST.getCode());
            if (StringUtils.isNotEmpty(res)) return res;
            // 刷新token
            refreshToken(token, busDoctorHospital.getId());
            // 保存登录信息 设置身份标识
            saveMsgAndSetIdentity(map, token, encryptedPhone, ipAddr, busDoctorHospital.getDoctorId(), AppRoleEnum.PHARMACIST.getCode());
            return LOGIN_SUCCESS;
        }
        return null;
    }

    /**
     * 排序
     */
    @Override
    public int order() {
        return 1;
    }
}

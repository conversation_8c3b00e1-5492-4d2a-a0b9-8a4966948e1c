package com.puree.hospital.app.service;

import com.puree.hospital.app.domain.BusOrderAfterSales;
import com.puree.hospital.app.domain.dto.BusOrderAfterSalesDTO;
import com.puree.hospital.app.domain.dto.OrderAfterSalesDTO;
import com.puree.hospital.app.domain.dto.RefundAmountDTO;
import com.puree.hospital.app.domain.vo.BusAfterSaleDetailVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单售后信息表
 * <AUTHOR>
 * @since 2023-03-06
 */

public interface IBusOrderAfterSalesService {
    /**
     * 提交售后信息
     * @param dto
     * @return
     */
    Long submit(OrderAfterSalesDTO dto);

    /**
     * 查询售后方式
     * @param dto
     * @return
     */
    List<String> way(OrderAfterSalesDTO dto);

    /**
     * 患者退货填写物流
     * @param orderAfterSalesDTO
     * @return
     */
    int returnGoods(BusOrderAfterSalesDTO orderAfterSalesDTO);

    /**
     * 患者更改物流信息
     * @param orderAfterSalesDTO
     * @return
     */
    int updateReturnGoods(BusOrderAfterSalesDTO orderAfterSalesDTO);

    /**
     * 售后撤销
     * @param orderAfterSalesDTO
     * @return
     */
    int revocation(BusOrderAfterSalesDTO orderAfterSalesDTO);

    /**
     * 修改申请
     * @param orderAfterSalesDTO
     * @return
     */
    int modifyApplyFor(BusOrderAfterSalesDTO orderAfterSalesDTO);

    /**
     * 计算退款金额
     * @param dto
     * @return
     */
    BigDecimal calculateRefundAmount(RefundAmountDTO dto);

    /**
     * 查询售后订单详情
     * @param afterSaleId 售后单详情
     * @return 售后详情
     */
    BusAfterSaleDetailVO getAfterSaleDetail(Long afterSaleId);

    /**
     * 查询售后单信息
     * @param orderAfterSaleId
     * @return
     */
    BusOrderAfterSales selectAfterSales(Long orderAfterSaleId);

    /**
     * 修改售后单状态
     * @param busOrderAfterSales
     * @return
     */
    int updateAfterSaleStatus(BusOrderAfterSales busOrderAfterSales);

    /**
     * 查询售后单列表
     * @param orderAfterSales
     * @return
     */
    List<BusOrderAfterSales> selectAfterSaleList(BusOrderAfterSales orderAfterSales);


    /**
     * 批量将售后订单状态改为退款成功
     * @param orderAfterSaleIds - 售后单id列表
     */
    void updateAfterSaleStatusRefundSuccessByIds(List<Long> orderAfterSaleIds);

    /**
     * 根据订单号查询售后单列表
     * @param orderNo - 订单号
     * @return - 售后单列表
     */
    List<BusOrderAfterSales> selectAfterSalesListByOrderNo(String orderNo);
}
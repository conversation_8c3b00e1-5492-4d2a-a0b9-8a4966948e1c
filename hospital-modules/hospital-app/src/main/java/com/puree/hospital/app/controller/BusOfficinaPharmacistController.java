package com.puree.hospital.app.controller;

import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.vo.BusOfficinaPharmacistVo;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusOfficinaPharmacistService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.tool.api.RemoteSmsNotificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 药师相关控制器
 */
@RestController
@RequestMapping("/pharmacist")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOfficinaPharmacistController extends BaseController {
    private final IBusOfficinaPharmacistService busOfficinaPharmacistService;
    private final IBusDoctorService busDoctorService;
    private final RemoteSmsNotificationService remoteSmsNotificationService;

    /**
     * 修改药师手机号码
     *
     * @param phoneNumber
     * @param oldPhoneNumber
     * @param code
     * @return
     */
    @GetMapping("updatePhoneNumber")
    @Log(title = "修改药师手机号码", businessType = BusinessType.OTHER)
    public AjaxResult updatePhoneNumber(@RequestParam("phoneNumber") String phoneNumber,
                                        @RequestParam("oldPhoneNumber") String oldPhoneNumber,
                                        @RequestParam("code") String code) {
        SMSLoginUser loginUser = busDoctorService.queryDrInfo();
        if (oldPhoneNumber.equals(phoneNumber)) {
            return AjaxResult.error("修改手机号相同");
        }
        AjaxResult result = remoteSmsNotificationService.checkVerifyCode(phoneNumber, code);
        if (!result.isSuccess()) {
            return result;
        }
        return AjaxResult.success(busOfficinaPharmacistService.updatePhoneNumber(phoneNumber, oldPhoneNumber,
                loginUser.getUserid()));
    }

    /**
     * 校验修改手机号验证码
     *
     * @param phoneNumber
     * @param code
     * @return
     */
    @GetMapping("checkCode")
    @Log(title = "校验修改手机号验证码", businessType = BusinessType.OTHER)
    public AjaxResult checkCode(@RequestParam("phoneNumber") String phoneNumber,
                                @RequestParam("code") String code) {
        return remoteSmsNotificationService.checkVerifyCode(phoneNumber, code);
    }

    /**
     * 修改药师密码
     *
     * @param busSignature
     * @return
     */
    @PostMapping("changePassWord")
    @Log(title = "修改药师密码", businessType = BusinessType.OTHER)
    public AjaxResult changePassWord(@RequestBody BusSignature busSignature) {
        SMSLoginUser loginUser = busDoctorService.queryDrInfo();
        busSignature.setObjectId(loginUser.getUserid());
        busSignature.setObjectType(loginUser.getIdentity());
        return AjaxResult.success(busOfficinaPharmacistService.changePassWord(busSignature));
    }

    /**
     * 获取药师信息
     *
     * @return 药师信息
     */
    @GetMapping("/info")
    public R<BusOfficinaPharmacistVo> info() {
        SMSLoginUser loginUser = busDoctorService.queryDrInfo();
        return R.ok(busOfficinaPharmacistService.getInfo(loginUser.getUserid()));
    }
}

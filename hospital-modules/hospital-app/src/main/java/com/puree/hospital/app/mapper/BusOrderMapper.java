package com.puree.hospital.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusPatientOrderInfo;
import com.puree.hospital.app.domain.BusShopOrder;
import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.domain.vo.BusOrderVo;
import com.puree.hospital.app.domain.vo.OrderVo;
import com.puree.hospital.app.domain.vo.BusShopOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface BusOrderMapper extends BaseMapper<BusOrder> {
     /**
      * 查询全部订单
      * @param busOrderDto
      * @return
      */
     List<BusOrderVo> selectAllOrderList(BusOrderDto busOrderDto);

    /**
     * 批量新增订单
     * @param busOrders
     * @return
     */
    int batchInsert(@Param("busOrders") List<BusOrder> busOrders);

    /**
     * 查询药品/商品订单列表
     * @param dto
     * @return
     */
    List<OrderVo> selectOrderList(DrugsOrderDto dto);

    /**
     * 查询药品/商品订单详细
     * @param orderNo
     * @return
     */
    List<OrderVo> selectOrder(String orderNo);

    /**
     * 查询商品订单详细
     * @param subOrderId
     * @return
     */
    BusShopOrderVO selectGoodsDetail(Long subOrderId);

    /**
     * 查询商品订单及商品信息
     * @param orderNo
     * @return
     */
    BusShopOrderVO selectOrderAndGoodsDetail(String orderNo);

    /**
     * 查询总订单信息
     * @param orderNo
     * @return
     */
    List<BusOrder> selectDrugsAndGoodsList(String orderNo);

    BusDrugsOrder selectDrugsOrderInfo(Long subOrderId);

    BusShopOrder selectGoodsOrderInfo(Long subOrderId);

    /**
     * 查询待支付的交易订单
     * @return
     */
    List<OrderVo> selectWaitPayOrderList();

    /**
     * 根据总订单编号查询药品订单关联的处方ID
     * @param orderNo
     * @return
     */
    List<Long> selectPrescriptionId(String orderNo);

    int updateBusOrderInvoice(BusOrder req) ;

    List<BusPatientOrderInfo> getGoodsIdsByOrderId(@Param("id") Long id, @Param("hospitalId") Long hospitalId);

}
package com.puree.hospital.app.queue.consumer;

import com.puree.hospital.app.api.model.event.order.DrugsOrderCompleteEvent;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.mapper.BusDrugsOrderMapper;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.event.order.DrugsOrderCompleteEventProducer;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;

/**
 * @date 2024/2/26 18:49
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class DrugsOrderAutoCompleteConsumer extends RedisStreamConsumer<Message> {

    @Autowired
    private IBusDrugsOrderService busDrugsOrderService;

    @Autowired
    private DrugsOrderCompleteEventProducer drugsOrderCompleteEventProducer;

    @Override
    public void onMessage(RedisMessage<Message> message) {
        log.error("开始处理自动完成订单:{}", message);
        BusDrugsOrder order = busDrugsOrderService.selectById(message.getBody().getId());
        log.info("药品订单自动完成查询订单 data={}", order);
        //订单状态为待收货，改为已完成
        if (order != null && order.getStatus().equals(DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getCode())) {
            BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
            busDrugsOrder.setOrderNo(order.getOrderNo());
            busDrugsOrder.setHospitalId(message.getBody().getHospitalId());
            busDrugsOrder.setStatus(DrugsOrderEnum.COMPLETED.getCode());
            busDrugsOrder.setCompleteTime(new Date());
            busDrugsOrderService.updateStatus(busDrugsOrder);
            log.info("药品订单自动完成成功!:{}", busDrugsOrder.getOrderNo());
            // 发送药品订单完成事件通知
            sendDrugsOrderCompleteEventNotification(order);
        }
    }

    /**
     *  发送药品订单完成事件通知
     * @param order 药品订单信息
     */
    private void sendDrugsOrderCompleteEventNotification(BusDrugsOrder order) {
        log.info("发送药品订单已完成事件 orderNo: {}", order.getOrderNo());
        // 组装订单完成事件通知
        DrugsOrderCompleteEvent orderCompleteEvent = new DrugsOrderCompleteEvent();
        orderCompleteEvent.setSubOrderId(order.getId());
        HashMap<String, Object> attachment = new HashMap<>(3);
        attachment.put("status", "6");
        attachment.put("orderId", order.getId());
        attachment.put("presId", order.getPrescriptionId());
        orderCompleteEvent.setAttachment(attachment);
        drugsOrderCompleteEventProducer.send(orderCompleteEvent);
    }

}

package com.puree.hospital.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.app.api.model.vo.BusSignatureVO;
import com.puree.hospital.app.domain.BusBizDepartment;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDoctorAudit;
import com.puree.hospital.app.domain.BusDoctorAuditRecord;
import com.puree.hospital.app.domain.BusDoctorConsultation;
import com.puree.hospital.app.domain.BusDoctorDepartment;
import com.puree.hospital.app.domain.BusDoctorDepartmentAudit;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusDoctorPushSetup;
import com.puree.hospital.app.domain.BusDoctorTdl;
import com.puree.hospital.app.domain.BusHospitalPreorderDoctor;
import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.BusPatientMyAttention;
import com.puree.hospital.app.domain.BusPreorderDoctor;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionCd;
import com.puree.hospital.app.domain.BusSearchRecord;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.BusDoctorDto;
import com.puree.hospital.app.domain.dto.DoctorConsultationDto;
import com.puree.hospital.app.domain.vo.BizDepartmentTreeSelect;
import com.puree.hospital.app.domain.vo.BusBizDepartmentVo;
import com.puree.hospital.app.domain.vo.BusDoctorBasicInfoVo;
import com.puree.hospital.app.domain.vo.BusDoctorDepartmentVo;
import com.puree.hospital.app.domain.vo.BusDoctorHospitalVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.mapper.BusBizDepartmentMapper;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusConsultationSettingsMapper;
import com.puree.hospital.app.mapper.BusDoctorAuditMapper;
import com.puree.hospital.app.mapper.BusDoctorAuditRecordMapper;
import com.puree.hospital.app.mapper.BusDoctorConsultationMapper;
import com.puree.hospital.app.mapper.BusDoctorDepartmentAuditMapper;
import com.puree.hospital.app.mapper.BusDoctorDepartmentMapper;
import com.puree.hospital.app.mapper.BusDoctorHospitalMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDoctorPushSetupMapper;
import com.puree.hospital.app.mapper.BusDoctorTdlMapper;
import com.puree.hospital.app.mapper.BusHospitalPreorderDoctorMapper;
import com.puree.hospital.app.mapper.BusOfficinaPharmacistMapper;
import com.puree.hospital.app.mapper.BusPartnersMapper;
import com.puree.hospital.app.mapper.BusPatientMyAttentionMapper;
import com.puree.hospital.app.mapper.BusPreorderDoctorMapper;
import com.puree.hospital.app.mapper.BusPrescriptionCdMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusSignatureMapper;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDoctorSchedulingService;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusSearchRecordService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.enums.AuditStatus;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DoctorPracticeEnum;
import com.puree.hospital.common.core.enums.DoctorRoleEnum;
import com.puree.hospital.common.core.enums.EhrSourceEnum;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.core.enums.FiveRoleEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.security.token.TokenService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorServiceImpl implements IBusDoctorService {
    private final BusPrescriptionDrugsServiceImpl busPrescriptionDrugsServiceImpl;
    private Logger log = LoggerFactory.getLogger(BusDoctorServiceImpl.class);

    private final BusDoctorMapper busDoctorMapper;
    private final BusDoctorAuditMapper busDoctorAuditMapper;
    private final BusDoctorDepartmentAuditMapper busDoctorDepartmentAuditMapper;
    private final BusDoctorHospitalMapper busDoctorHospitalMapper;
    private final BusDoctorDepartmentMapper busDoctorDepartmentMapper;
    private final BusDoctorConsultationMapper busDoctorConsultationMapper;
    private final IBusSearchRecordService busSearchRecordService;
    private final BusBizDepartmentMapper busBizDepartmentMapper;
    private final BusDoctorPushSetupMapper busDoctorPushSetupMapper;
    private final BusOfficinaPharmacistMapper busOfficinaPharmacistMapper;
    private final BusSignatureMapper busSignatureMapper;
    private final BusPrescriptionCdMapper busPrescriptionCdMapper;
    private final BusDoctorAuditRecordMapper busDoctorAuditRecordMapper;
    private final BusDoctorTdlMapper busDoctorTdlMapper;
    private final BusConsultationSettingsMapper busConsultationSettingsMapper;
    private final IBusDoctorSchedulingService busDoctorSchedulingService;
    private final BusPreorderDoctorMapper busPreorderDoctorMapper;
    private final TokenService tokenService;
    private final IBusConsultationOrderService busConsultationOrderService;
    private final BusConsultationOrderMapper consultationOrderMapper;
    private final BusHospitalPreorderDoctorMapper hospitalPreorderDoctorMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPatientMyAttentionMapper busPatientMyAttentionMapper;
    private final BusPartnersMapper busPartnersMapper;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;

    @Override
    public BusDoctor selectDoctorByPhone(String phoneNumber) {
        QueryWrapper<BusDoctor> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("phone_number", phoneNumber);
        return busDoctorMapper.selectOne(queryWrapper);
    }

    @Override
    public BusDoctorVo selectDoctorByPhoneAndHospitalId(String phoneNumber, Long hospitalId) {
        BusDoctorDto doctor = new BusDoctorDto();
        doctor.setHospitalId(hospitalId);
        doctor.setPhoneNumber(DESUtil.encrypt(phoneNumber));
        return busDoctorMapper.selectDoctorByPhoneAndHospitalId(doctor);
    }

    @Override
    public BusDoctorVo selectDoctorDetailInfoById(Long id) {
        BusDoctorVo doctorVo = busDoctorMapper.selectDoctorDetailInfoById(id);
        if (ObjectUtil.isNotNull(doctorVo)) {
            doctorVo.setPhoneNumber(DESUtil.decrypt(doctorVo.getPhoneNumber()));
            if (ObjectUtil.isNotEmpty(doctorVo.getIdCardNo())) {
                doctorVo.setIdCardNo(DESUtil.decrypt(doctorVo.getIdCardNo()));
            }
        }
        return doctorVo;
    }

    @Override
    public BusDoctorHospitalVo firstPractice(Long id) {
        return busDoctorHospitalMapper.firstPractice(id);
    }

    /**
     * 查询医生信息
     *
     * @param id
     * @return
     */
    @Override
    public BusDoctorVo selectDoctorInfoById(Long id) {
        return busDoctorMapper.selectDoctorInfoById(id);
    }

    @Override
    public List<BusBizDepartmentVo> selectDoctorLeftDepartmentList(BusDoctorDepartment busDoctorDepartment) {
        return busDoctorMapper.selectDoctorLeftDepartmentList(busDoctorDepartment);
    }

    @Override
    public List<BizDepartmentTreeSelect> buildDepartmentsTreeSelect(List<BusBizDepartmentVo> busDepartments) {
        List<BusBizDepartmentVo> departmentTrees = buildBizDepartmentTree(busDepartments);
        return departmentTrees.stream().map(BizDepartmentTreeSelect::new).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBusDoctor(BusDoctor busDoctor, Long hospitalId) {
        busDoctor.setCreateTime(DateUtils.getNowDate());
        String phoneNumber = DESUtil.encrypt(busDoctor.getPhoneNumber());
        BusDoctor doctor = this.selectDoctorByPhone(phoneNumber);
        if (ObjectUtil.isNull(doctor)) {
            busDoctor.setPhoneNumber(phoneNumber);
            busDoctor.setIdCardNo(DESUtil.encrypt(busDoctor.getIdCardNo()));
            busDoctorMapper.insert(busDoctor);
            BusDoctorHospital busDoctorHospital = new BusDoctorHospital();
            busDoctorHospital.setDoctorId(busDoctor.getId());
            busDoctorHospital.setHospitalId(hospitalId);
            busDoctorHospital.setCreateTime(DateUtils.getNowDate());
            busDoctorHospital.setJobNumber(String.valueOf(System.currentTimeMillis()));
            busDoctorHospitalMapper.insert(busDoctorHospital);
            //插入推送设置
            BusDoctorPushSetup busDoctorPushSetup = new BusDoctorPushSetup();
            busDoctorPushSetup.setPusherId(busDoctor.getId());
            busDoctorPushSetup.setIsPush(YesNoEnum.YES.getCode());
            busDoctorPushSetup.setPushType(2);//全部推送
            busDoctorPushSetup.setType("0");
            busDoctorPushSetup.setCreateTime(DateUtils.getNowDate());
            busDoctorPushSetupMapper.insert(busDoctorPushSetup);
            BusDoctorAudit audit = new BusDoctorAudit();
            audit.setDoctorId(busDoctor.getId());
            audit.setPhoneNumber(phoneNumber);
            audit.setHospitalId(hospitalId);
            audit.setCreateTime(DateUtils.getNowDate());
            busDoctorAuditMapper.insert(audit);
        }
        //绑定多家医院
        if (ObjectUtil.isNotNull(doctor)) {
            log.info("已绑定医生信息: info={}", doctor);
            QueryWrapper<BusDoctorAudit> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("doctor_id", doctor.getId());
            queryWrapper.eq("hospital_id", hospitalId);
            queryWrapper.orderByDesc("create_time");
            BusDoctorAudit doctorAudit = busDoctorAuditMapper.selectOne(queryWrapper.last("limit 1"));
            QueryWrapper<BusDoctorHospital> wrapper = new QueryWrapper<>();
            wrapper.eq("doctor_id", doctor.getId());
            wrapper.eq("hospital_id", hospitalId);
            wrapper.ne("is_authentication", "3");
            List<BusDoctorHospital> doctorHospitals = busDoctorHospitalMapper.selectList(wrapper);
            if (CollUtil.isNotEmpty(doctorHospitals)) {
                throw new ServiceException("当前医院已有待认证或待审核的审批单，绑定失败");
            } else {
                if (ObjectUtil.isNotNull(doctorAudit) && doctorAudit.getAuditStatus().equals(0)) {
                    throw new ServiceException("当前医院已有待认证或待审核的审批单，绑定失败");
                }
            }

            QueryWrapper<BusDoctorHospital> listQueryWrapper = new QueryWrapper<>();
            listQueryWrapper.eq("doctor_id", doctor.getId());
            listQueryWrapper.orderByAsc("create_time");
            listQueryWrapper.eq("is_authentication", "3");
            List<BusDoctorHospital> hospitals = busDoctorHospitalMapper.selectList(listQueryWrapper);
            boolean isThisCourt = false;
            for (BusDoctorHospital h : hospitals) {
                if (YesNoEnum.YES.getCode().equals(h.getIsThisCourt())) {
                    isThisCourt = true;
                }
            }
            QueryWrapper<BusDoctorHospital> oneQueryWrapper = new QueryWrapper<>();
            oneQueryWrapper.eq("doctor_id", doctor.getId());
            oneQueryWrapper.eq("hospital_id", hospitalId);
            BusDoctorHospital hospital = busDoctorHospitalMapper.selectOne(oneQueryWrapper);
            if (ObjectUtil.isNull(hospital)) {
                BusDoctorHospital doctorHospital = new BusDoctorHospital();
                BusDoctorAudit audit = new BusDoctorAudit();
                if (isThisCourt) {
                    doctorHospital.setIsThisCourt(YesNoEnum.NO.getCode());
                    audit.setIsThisCourt(YesNoEnum.NO.getCode());
                }
                doctorHospital.setDoctorId(doctor.getId());
                doctorHospital.setHospitalId(hospitalId);
                doctorHospital.setIsAuthentication(0);
                doctorHospital.setCreateTime(DateUtils.getNowDate());
                busDoctorHospitalMapper.insert(doctorHospital);
                audit.setDoctorId(doctor.getId());
                audit.setPhoneNumber(phoneNumber);
                audit.setHospitalId(hospitalId);
                audit.setCreateTime(DateUtils.getNowDate());
                busDoctorAuditMapper.insert(audit);
            }
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int realNameAuthentication(BusDoctorAudit busDoctorAudit) {
        busDoctorAudit.setCreateTime(null);
        busDoctorAudit.setPhoneNumber(null);
        busDoctorAudit.setUpdateTime(DateUtils.getNowDate());
        busDoctorAudit.setIdCardNo(DESUtil.encrypt(busDoctorAudit.getIdCardNo()));
        UpdateWrapper<BusDoctorAudit> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
        updateWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
        busDoctorAuditMapper.update(busDoctorAudit, updateWrapper);

        //更新绑定医院状态
        BusDoctorHospital doctorHospital = new BusDoctorHospital();
        doctorHospital.setIsAuthentication(1);
        UpdateWrapper<BusDoctorHospital> dhWrapper = new UpdateWrapper<>();
        dhWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
        dhWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
        busDoctorHospitalMapper.update(doctorHospital, dhWrapper);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int physicianAuthentication(BusDoctorAudit busDoctorAudit) {
        List<BusDoctorDepartmentAudit> auditList = busDoctorAudit.getBusDoctorDepartmentAuditList();
        if (CollUtil.isEmpty(auditList)) {
            throw new ServiceException("请选择科室");
        }
        busDoctorAudit.setUpdateTime(DateUtils.getNowDate());
        if (ObjectUtil.isNotNull(busDoctorAudit.getIdCardNo())) {
            busDoctorAudit.setIdCardNo(DESUtil.encrypt(busDoctorAudit.getIdCardNo()));
        }
        //判断是否注册
        BusDoctor doctor = busDoctorMapper.selectById(busDoctorAudit.getDoctorId());
        if (ObjectUtil.isNotNull(doctor) && StringUtils.isEmpty(doctor.getIdCardNo())) {
            busDoctorAudit.setPhoneNumber(DESUtil.encrypt(busDoctorAudit.getPhoneNumber()));
            busDoctorAudit.setDoctorNumber(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "");
            UpdateWrapper<BusDoctorAudit> updateWrapper = new UpdateWrapper<>();
            busDoctorAudit.setAffiliatedHospital(busDoctorAudit.getHospitalId());
            busDoctorAudit.setAuditStatus(YesNoEnum.NO.getCode());
            busDoctorAudit.setCreateTime(null);
            updateWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
            updateWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
            updateWrapper.orderByDesc("create_time");
            busDoctorAuditMapper.update(busDoctorAudit, updateWrapper);
            //插入审核医生科室
            QueryWrapper<BusDoctorAudit> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
            queryWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
            BusDoctorAudit audit = busDoctorAuditMapper.selectOne(queryWrapper);
            //删除科室
            QueryWrapper<BusDoctorDepartmentAudit> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("audit_id", audit.getId());
            busDoctorDepartmentAuditMapper.delete(deleteWrapper);
            List<BusDoctorDepartmentAudit> departmentAuditList = busDoctorAudit.getBusDoctorDepartmentAuditList();
            for (BusDoctorDepartmentAudit departmentAudit : departmentAuditList) {
                departmentAudit.setAuditId(audit.getId());
                departmentAudit.setCreateTime(DateUtils.getNowDate());
            }
            busDoctorDepartmentAuditMapper.batchInsert(departmentAuditList);
            //更新绑定医院状态
            BusDoctorHospital dh = new BusDoctorHospital();
            dh.setIsAuthentication(2);
            UpdateWrapper<BusDoctorHospital> dhWrapper = new UpdateWrapper<>();
            dhWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
            dhWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
            busDoctorHospitalMapper.update(dh, dhWrapper);
        } else {
            if (YesNoEnum.NO.getCode().equals(busDoctorAudit.getIsThisCourt()) ||
                    DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(busDoctorAudit.getIsThisCourt())) {
                //更新绑定医院状态
                BusDoctorHospital dh = new BusDoctorHospital();
                dh.setIsAuthentication(3);
                dh.setIsThisCourt(busDoctorAudit.getIsThisCourt());
                UpdateWrapper<BusDoctorHospital> dhWrapper = new UpdateWrapper<>();
                dhWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
                dhWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
                busDoctorHospitalMapper.update(dh, dhWrapper);
                busDoctorAudit.setAuditStatus(2);//审核通过
                QueryWrapper<BusDoctorAudit> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
                queryWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
                queryWrapper.eq("audit_status", YesNoEnum.NO.getCode());
                queryWrapper.orderByDesc("create_time");
                busDoctorAuditMapper.delete(queryWrapper);
                List<BusDoctorDepartment> departmentList = new ArrayList<>();
                auditList.forEach(a -> {
                    BusDoctorDepartment department = new BusDoctorDepartment();
                    department.setDoctorId(busDoctorAudit.getDoctorId());
                    department.setHospitalId(busDoctorAudit.getHospitalId());
                    department.setDepartmentId(a.getDepartmentId());
                    departmentList.add(department);
                });
                busDoctorDepartmentMapper.batchInsert(departmentList);
                //插入问诊设置
                QueryWrapper<BusConsultationSettings> settingsQueryWrapper = new QueryWrapper<>();
                settingsQueryWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
                settingsQueryWrapper.orderByDesc("create_time");
                BusConsultationSettings settings = busConsultationSettingsMapper.selectOne(settingsQueryWrapper.last(
                        "limit 1"));
                BusDoctorConsultation consultation = new BusDoctorConsultation();
                List<BusDoctorConsultation> consultationDefaultSetUp =
                        consultation.getDefaultSetUp(busDoctorAudit.getDoctorId(), settings);
                consultationDefaultSetUp.forEach(c -> {
                    c.setStatus(YesNoEnum.YES.getCode());
                    c.setCreateTime(DateUtils.getNowDate());
                    busDoctorConsultationMapper.insert(c);
                });
                //判断是否更新第一任职医院
                if (StringUtils.isNotEmpty(busDoctorAudit.getFirstHospital())) {
                    BusDoctor busDoctor = new BusDoctor();
                    busDoctor.setId(busDoctorAudit.getDoctorId());
                    busDoctor.setFirstHospital(busDoctorAudit.getFirstHospital());
                    busDoctor.setFirstHospitalUnifiedCreditCode(StringUtils.isNotEmpty(busDoctorAudit.getFirstHospitalUnifiedCreditCode()) ?
                                    busDoctorAudit.getFirstHospitalUnifiedCreditCode() : null);
                    busDoctor.setUpdateTime(DateUtils.getNowDate());
                    busDoctorMapper.updateById(busDoctor);
                }
            } else {
                busDoctorAudit.setPhoneNumber(DESUtil.encrypt(busDoctorAudit.getPhoneNumber()));
                busDoctorAudit.setCreateTime(null);
                busDoctorAudit.setDoctorNumber(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "");
                busDoctorAudit.setAffiliatedHospital(busDoctorAudit.getHospitalId());
                UpdateWrapper<BusDoctorAudit> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
                updateWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
                busDoctorAuditMapper.update(busDoctorAudit, updateWrapper);
                //删除原有科室插入审核医生科室
                QueryWrapper<BusDoctorAudit> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
                queryWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
                BusDoctorAudit audit = busDoctorAuditMapper.selectOne(queryWrapper);
                QueryWrapper<BusDoctorDepartmentAudit> deleteWrapper = new QueryWrapper<>();
                deleteWrapper.eq("audit_id", audit.getId());
                busDoctorDepartmentAuditMapper.delete(deleteWrapper);
                List<BusDoctorDepartmentAudit> departmentAuditList = busDoctorAudit.getBusDoctorDepartmentAuditList();
                for (BusDoctorDepartmentAudit departmentAudit : departmentAuditList) {
                    departmentAudit.setAuditId(audit.getId());
                    departmentAudit.setCreateTime(DateUtils.getNowDate());
                }
                busDoctorDepartmentAuditMapper.batchInsert(departmentAuditList);

                //更新绑定医院状态
                BusDoctorHospital dh = new BusDoctorHospital();
                dh.setIsAuthentication(2);
                UpdateWrapper<BusDoctorHospital> dhWrapper = new UpdateWrapper<>();
                dhWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
                dhWrapper.eq("hospital_id", busDoctorAudit.getHospitalId());
                busDoctorHospitalMapper.update(dh, dhWrapper);
            }
        }
        BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
        busDoctorTdl.setStatus("2");
        busDoctorTdl.setUpdateTime(DateUtils.getNowDate());
        UpdateWrapper<BusDoctorTdl> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("type", "4");
        updateWrapper.eq("doctor_id", busDoctorAudit.getDoctorId());
        updateWrapper.eq("business_id", busDoctorAudit.getDoctorId());
        updateWrapper.orderByDesc("create_time");
        busDoctorTdlMapper.update(busDoctorTdl, updateWrapper.last("limit 1"));
        return 1;
    }

    @Override
    public BusDoctorHospital selectBusDoctorHospital(DoctorConsultationDto dto) {
        QueryWrapper<BusDoctorHospital> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", dto.getHospitalId())
                .eq("doctor_id", dto.getDoctorId());
        return busDoctorHospitalMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manyTimesAuthentication(BusDoctorAudit busDoctorAudit) {
        try {
            Integer isThisCourt = busDoctorAudit.getIsThisCourt();
            BusDoctorHospital dh = new BusDoctorHospital();
            List<BusDoctorDepartmentAudit> departmentAuditList = busDoctorAudit.getBusDoctorDepartmentAuditList();
            //如果为本院时
            if (YesNoEnum.YES.getCode().equals(isThisCourt)) {
                busDoctorAuditMapper.insert(busDoctorAudit);
                //插入审核医生科室
                for (BusDoctorDepartmentAudit departmentAudit : departmentAuditList) {
                    departmentAudit.setAuditId(busDoctorAudit.getId());
                }
                busDoctorDepartmentAuditMapper.batchInsert(departmentAuditList);
                //插入绑定医院
                dh.setIsAuthentication(2);
                dh.setDoctorId(busDoctorAudit.getDoctorId());
                dh.setHospitalId(busDoctorAudit.getHospitalId());
                busDoctorHospitalMapper.insert(dh);
            } else {
                //插入绑定医院状态
                dh.setIsAuthentication(4);
                dh.setDoctorId(busDoctorAudit.getDoctorId());
                dh.setHospitalId(busDoctorAudit.getHospitalId());
                busDoctorHospitalMapper.insert(dh);
                //插入医生绑定医院科室
                List<BusDoctorDepartment> departmentList = new ArrayList<>();
                for (BusDoctorDepartmentAudit departmentAudit : departmentAuditList) {
                    BusDoctorDepartment doctorDepartment = new BusDoctorDepartment();
                    doctorDepartment.setDoctorId(busDoctorAudit.getDoctorId());
                    doctorDepartment.setHospitalId(busDoctorAudit.getHospitalId());
                    doctorDepartment.setDepartmentId(busDoctorAudit.getId());
                    doctorDepartment.setCreateTime(DateUtils.getNowDate());
                    departmentList.add(doctorDepartment);
                }
                busDoctorDepartmentMapper.batchInsert(departmentList);
                //设置问诊
                //TODO:后期添加医院设置问诊金额
                BusDoctorConsultation consultation = new BusDoctorConsultation();
                List<BusDoctorConsultation> consultationDefaultSetUp =
                        consultation.getDefaultSetUp(busDoctorAudit.getId(), busDoctorAudit.getHospitalId());
                consultationDefaultSetUp.forEach(c -> {
                    if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(isThisCourt)) {
                        c.setStatus(YesNoEnum.NO.getCode());
                    }
                    busDoctorConsultationMapper.insert(c);
                });
            }
        } catch (Exception e) {
            throw new ServiceException("二次认证失败");
        }
    }

    @Override
    public BusDoctorAudit authenticationInfo(Long doctorId, Long hospitalId, String phoneNumber) {
        QueryWrapper<BusDoctorAudit> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hospital_id", hospitalId);
        queryWrapper.eq("doctor_id", doctorId);
        queryWrapper.eq("phone_number", DESUtil.encrypt(phoneNumber));
        queryWrapper.orderByDesc("create_time");
        BusDoctorAudit audit = busDoctorAuditMapper.selectOne(queryWrapper.last("limit 1"));
        if (ObjectUtil.isNotNull(audit)) {
            audit.setIdCardNo(DESUtil.decrypt(audit.getIdCardNo()));
            audit.setPhoneNumber(DESUtil.decrypt(audit.getPhoneNumber()));
            QueryWrapper<BusDoctorAuditRecord> recordQueryWrapper = new QueryWrapper<>();
            recordQueryWrapper.eq("audit_id", audit.getId());
            recordQueryWrapper.orderByDesc("create_time");
            BusDoctorAuditRecord auditRecord = busDoctorAuditRecordMapper.selectOne(recordQueryWrapper.last("limit 1"));
            audit.setBusDoctorAuditRecord(auditRecord);
            BusDoctorDepartmentAudit busDoctorDepartmentAudit = new BusDoctorDepartmentAudit();
            busDoctorDepartmentAudit.setAuditId(audit.getId());
            List<BusDoctorDepartmentAudit> departmentAudits =
                    busDoctorDepartmentAuditMapper.selectList(busDoctorDepartmentAudit);
            audit.setBusDoctorDepartmentAuditList(departmentAudits);
        }
        return audit;
    }

    @Override
    public List<BusDoctorVo> doctorList(BusDoctorDto busDoctorDto) {
        if (Boolean.FALSE.equals(busDoctorDto.getWhetherStartPage())){
            if (ObjectUtil.isNotNull(busDoctorDto.getPageNum()) && ObjectUtil.isNotNull(busDoctorDto.getPageSize())) {
                //分页
                busDoctorDto.setOffset((busDoctorDto.getPageNum() - 1) * busDoctorDto.getPageSize());
            }
        }
        List<BusDoctorVo> doctorVos = busDoctorMapper.selectDoctorList(busDoctorDto);
        for (int i = 0; i < doctorVos.size(); i++) {
            BusDoctorVo d = doctorVos.get(i);
            d.setRole(FiveRoleEnum.getInfoByValue(d.getRole()));
            d.setIdCardNo(DESUtil.decrypt(d.getIdCardNo()));
            d.setPhoneNumber(DESUtil.decrypt(d.getPhoneNumber()));
            BusDoctorDepartment departmentQueryWrapper = new BusDoctorDepartment();
            departmentQueryWrapper.setHospitalId(busDoctorDto.getHospitalId());
            departmentQueryWrapper.setDoctorId(d.getId());
            List<BusDoctorDepartmentVo> departmentList = busDoctorDepartmentMapper.selectList(departmentQueryWrapper);
            d.setBusDoctorDepartmentVoList(departmentList);
            QueryWrapper<BusDoctorConsultation> consultationQueryWrapper = new QueryWrapper<>();
            consultationQueryWrapper.eq("hospital_id", busDoctorDto.getHospitalId());
            consultationQueryWrapper.eq("doctor_id", d.getId());
            List<BusDoctorConsultation> consultationList =
                    busDoctorConsultationMapper.selectList(consultationQueryWrapper);
            d.setBusDoctorConsultationList(consultationList);
            List<BusDoctorHospitalVo> hospitalVos = busDoctorHospitalMapper.selectDoctorHospitalList(d.getId(),
                    busDoctorDto.getHospitalId());
            d.setDoctorHospitalList(hospitalVos);
            // 校验医生是否有排班
            Boolean b = busDoctorSchedulingService.queryDrSchedule(busDoctorDto.getHospitalId(), d.getId(),
                    busDoctorDto.getSecondLevel());
            d.setDisable(b);
            // 为近期看过的医生打标签
            if (ObjectUtil.isNotNull(busDoctorDto.getPatientId())) {
                // 查询近期看过的医生id
                Long doctorId = busConsultationOrderService.selectDoctor(busDoctorDto);
                if (ObjectUtil.isNotNull(doctorId) && d.getId().equals(doctorId)) {
                    d.setYes(CodeEnum.YES.getCode());
                    doctorVos.add(0, doctorVos.remove(i));
                }
            }
        }
        return doctorVos;
    }

    @Override
    public List<BusDoctorBasicInfoVo> doctorBasicInfoList(BusDoctorDto busDoctorDto) {
        if (Boolean.FALSE.equals(busDoctorDto.getWhetherStartPage())){
            if (ObjectUtil.isNotNull(busDoctorDto.getPageNum()) && ObjectUtil.isNotNull(busDoctorDto.getPageSize())) {
                //分页
                busDoctorDto.setOffset((busDoctorDto.getPageNum() - 1) * busDoctorDto.getPageSize());
            }
        }
        List<Long> departmentIds = busBizDepartmentMapper.selectByDepartmentName(busDoctorDto);
        List<BusDoctorBasicInfoVo> busDoctorBasicInfoVo = busDoctorMapper.selectHotDoctorListWithNotPartnersCode(busDoctorDto, departmentIds);
        busDoctorBasicInfoVo.parallelStream().forEach(d -> setDoctorInfo(busDoctorDto, d));
        return busDoctorBasicInfoVo;
    }

    /**
     * 设置医生信息- 部门信息，排盘信息，问诊设置信息
     * @param busDoctorDto - 医生dto查询条件
     * @param d - 医生vo
     */
    private void setDoctorInfo(BusDoctorDto busDoctorDto, BusDoctorBasicInfoVo d) {
        d.setRole(FiveRoleEnum.getInfoByValue(d.getRole()));
        BusDoctorDepartment departmentQueryWrapper = new BusDoctorDepartment();
        departmentQueryWrapper.setHospitalId(busDoctorDto.getHospitalId());
        departmentQueryWrapper.setDoctorId(d.getId());
        List<BusDoctorDepartmentVo> departmentList = busDoctorDepartmentMapper.selectList(departmentQueryWrapper);
        d.setBusDoctorDepartmentVoList(departmentList);
        QueryWrapper<BusDoctorConsultation> consultationQueryWrapper = new QueryWrapper<>();
        consultationQueryWrapper.eq("hospital_id", busDoctorDto.getHospitalId());
        consultationQueryWrapper.eq("doctor_id", d.getId());
        List<BusDoctorConsultation> consultationList =
                busDoctorConsultationMapper.selectList(consultationQueryWrapper);
        d.setBusDoctorConsultationList(consultationList);
        List<BusDoctorHospitalVo> hospitalVos = busDoctorHospitalMapper.selectDoctorHospitalList(d.getId(),
                busDoctorDto.getHospitalId());
        d.setDoctorHospitalList(hospitalVos);
        // 校验医生是否有排班
        Boolean b = busDoctorSchedulingService.queryDrSchedule(busDoctorDto.getHospitalId(), d.getId(),
                busDoctorDto.getSecondLevel());
        d.setDisable(b);
        //职称
        String titleValue = busDoctorMapper.selectDoctorTitleByDoctorId(d.getId());
        d.setTitleValue(titleValue);
    }

    @Override
    public BusDoctorBasicInfoVo getHospitalDoctorInfo(Long id, Long hospitalId, Long patientId) {
        BusDoctorVo doctorInfoVo = busDoctorMapper.getHospitalDoctorInfo(id, hospitalId);
        if (Objects.isNull(doctorInfoVo)) {
            return null;
        }
        BusDoctorBasicInfoVo doctorInfo = new BusDoctorBasicInfoVo();
        BeanUtils.copyProperties(doctorInfoVo, doctorInfo);
        doctorInfo.setRoleId(doctorInfo.getRole());
        doctorInfo.setRole(FiveRoleEnum.getInfoByValue(doctorInfo.getRole()));
        if (ObjectUtil.isNotNull(patientId)) {
            String partnersCode = SecurityUtils.getPartnerscode();
            LambdaQueryWrapper<BusPatientMyAttention> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusPatientMyAttention::getHospitalId, hospitalId)
                    .eq(BusPatientMyAttention::getDoctorId, id)
                    .eq(BusPatientMyAttention::getPatientId, patientId)
                    .eq(BusPatientMyAttention::getIsAttention, YesNoEnum.YES.getCode());
            if (StringUtils.isNotEmpty(partnersCode)) {
                lambdaQuery.eq(BusPatientMyAttention::getPartnersCode, partnersCode);
            } else {
                lambdaQuery.isNull(BusPatientMyAttention::getPartnersCode);
            }
            BusPatientMyAttention busPatientMyAttention = busPatientMyAttentionMapper.selectOne(lambdaQuery);
            if (ObjectUtil.isNotNull(busPatientMyAttention)) {
                doctorInfo.setIsAttention(YesNoEnum.YES.getCode());
            } else {
                doctorInfo.setIsAttention(YesNoEnum.NO.getCode());
            }
        }
        return doctorInfo;
    }

    @Override
    public BusDoctorVo selectHospitalDoctorInfo(Long id, Long hospitalId) {
        BusDoctorVo doctirInfo = busDoctorMapper.selectHospitalDoctorInfo(id, hospitalId);
        if (ObjectUtil.isNull(doctirInfo)) {
            throw new ServiceException("查询失败");
        }
        return doctirInfo;
    }

    @Override
    public List<BusDoctorVo> globalSearch(BusDoctorDto busDoctorDto, boolean isLogin) {
        List<BusDoctorVo> doctorVoList = busDoctorMapper.selectDoctorList(busDoctorDto);
        doctorVoList.forEach(d -> {
            BusDoctorDepartment departmentQueryWrapper = new BusDoctorDepartment();
            departmentQueryWrapper.setHospitalId(busDoctorDto.getHospitalId());
            departmentQueryWrapper.setDoctorId(d.getId());
            List<BusDoctorDepartmentVo> departmentList = busDoctorDepartmentMapper.selectList(departmentQueryWrapper);
            d.setBusDoctorDepartmentVoList(departmentList);
            QueryWrapper<BusDoctorConsultation> consultationQueryWrapper = new QueryWrapper<>();
            consultationQueryWrapper.eq("hospital_id", busDoctorDto.getHospitalId());
            consultationQueryWrapper.eq("doctor_id", d.getId());
            List<BusDoctorConsultation> consultationList =
                    busDoctorConsultationMapper.selectList(consultationQueryWrapper);
            d.setBusDoctorConsultationList(consultationList);
            List<BusDoctorHospitalVo> hospitalVos = busDoctorHospitalMapper.selectDoctorHospitalList(d.getId(),
                    busDoctorDto.getHospitalId());
            d.setDoctorHospitalList(hospitalVos);
            // 校验医生是否有排班
            Boolean b = busDoctorSchedulingService.queryDrSchedule(busDoctorDto.getHospitalId(), d.getId(),
                    busDoctorDto.getSecondLevel());
            d.setDisable(b);
        });
        //插入消息记录
        if (isLogin) {
            insertRecord(busDoctorDto);
        }
        return doctorVoList;
    }

    @Override
    public List<Map<String, Object>> fillSearch(String searchValue, Long hospitalId) {
        Integer paging = 3;
        List<Map<String, Object>> valueList = new ArrayList<>();
        //名称
        BusDoctorDto doctorQueryWrapper = new BusDoctorDto();
        doctorQueryWrapper.setFullName(searchValue);
        doctorQueryWrapper.setHospitalId(hospitalId);
        doctorQueryWrapper.setDoctorType(YesNoEnum.NO.getCode());
        doctorQueryWrapper.setPaging(paging);
        List<BusDoctor> busDoctors = busDoctorMapper.selectDoctorLeftHospitaList(doctorQueryWrapper);
        //擅长
        if (busDoctors.isEmpty()) {
            paging = 5;
        } else {
            if (busDoctors.size() < 2) {
                paging = 5;
            } else {
                paging = 4;
            }
        }
        BusDoctorDto doctorWrapper = new BusDoctorDto();
        doctorWrapper.setBeGoodAt(searchValue);
        doctorWrapper.setHospitalId(hospitalId);
        doctorWrapper.setPaging(paging);
        List<BusDoctor> busDoctorList = busDoctorMapper.selectDoctorLeftHospitaList(doctorWrapper);
        //科室
        if (busDoctors.isEmpty()) {
            paging = 10;
        } else {
            if (busDoctorList.size() < 2) {
                paging = 6;
            } else {
                paging = 3;
            }
        }
        QueryWrapper<BusBizDepartment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hospital_id", hospitalId);
        queryWrapper.like("department_name", searchValue);
        List<BusBizDepartment> list = busBizDepartmentMapper.selectList(queryWrapper.last("limit 3"));
        busDoctors.stream().forEach(d -> {
            boolean s = true;
            for (Map m : valueList) {
                boolean b = m.containsValue(d.getFullName());
                if (b) {
                    s = false;
                    continue;
                }
            }
            if (s) {
                Map<String, Object> map = new HashMap<>();
                map.put("type", "1");
                map.put("value", d.getFullName());
                valueList.add(map);
            }
        });
        busDoctorList.stream().forEach(d -> {
            boolean s = true;
            for (Map m : valueList) {
                boolean b = m.containsValue(d.getBeGoodAt());
                if (b) {
                    s = false;
                    continue;
                }
            }
            if (s) {
                Map<String, Object> map = new HashMap<>();
                map.put("type", "2");
                map.put("value", d.getBeGoodAt());
                valueList.add(map);
            }
        });
        list.stream().forEach(d -> {
            boolean s = true;
            for (Map m : valueList) {
                boolean b = m.containsValue(d.getDepartmentName());
                if (b) {
                    s = false;
                    continue;
                }
            }
            if (s) {
                Map<String, Object> map = new HashMap<>();
                map.put("type", "3");
                map.put("value", d.getDepartmentName());
                valueList.add(map);
            }
        });
        return valueList;
    }

    @Override
    public BusDoctorVo personalData(Long hospitalId, Long doctorId) {
        BusDoctorVo audit = busDoctorMapper.selectBusDoctorAuditInfo(hospitalId, doctorId);
        if (ObjectUtil.isNotNull(audit)) {
            audit.setIdCardNo(DESUtil.decrypt(audit.getIdCardNo()));
            audit.setPhoneNumber(DESUtil.decrypt(audit.getPhoneNumber()));
            return audit;
        }
        BusDoctorVo doctirInfo = busDoctorMapper.getHospitalDoctorInfo(doctorId, hospitalId);

        //查询是否开方医生
        LambdaQueryWrapper<BusPreorderDoctor> wrapper = Wrappers.lambdaQuery(BusPreorderDoctor.class);
        wrapper.eq(BusPreorderDoctor::getDoctorId, doctorId)
                .eq(BusPreorderDoctor::getHospitalId, hospitalId)
                .eq(BusPreorderDoctor::getDoctorRole, DoctorRoleEnum.DOCTOR.getCode());
        List<BusPreorderDoctor> busPreorderDoctors = busPreorderDoctorMapper.selectList(wrapper);

        if (CollUtil.isNotEmpty(busPreorderDoctors)) {
            doctirInfo.setIsPreorderDoctor(true);
        }

        if (doctirInfo != null) {
            doctirInfo.setIdCardNo(DESUtil.decrypt(doctirInfo.getIdCardNo()));
            doctirInfo.setPhoneNumber(DESUtil.decrypt(doctirInfo.getPhoneNumber()));
            QueryWrapper<BusDoctorAudit> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("hospital_id", hospitalId);
            queryWrapper.eq("doctor_id", doctorId);
            queryWrapper.eq("audit_status", "1");
            queryWrapper.orderByDesc("create_time");
            BusDoctorAudit doctorAudit = busDoctorAuditMapper.selectOne(queryWrapper.last("limit 1"));
            if (ObjectUtil.isNotNull(doctorAudit)) {
                QueryWrapper<BusDoctorAuditRecord> recordQueryWrapper = new QueryWrapper<>();
                recordQueryWrapper.eq("audit_id", doctorAudit.getId());
                recordQueryWrapper.orderByDesc("create_time");
                BusDoctorAuditRecord auditRecord = busDoctorAuditRecordMapper.selectOne(recordQueryWrapper.last("limit 1"));
                doctirInfo.setBusDoctorAuditRecord(auditRecord);
            }

            if (CharSequenceUtil.isBlank(doctirInfo.getDepartmentName())) {
                List<BusDoctorDepartmentVo> doctorDepartmentVoList = doctirInfo.getBusDoctorDepartmentVoList();
                StringBuilder sb = new StringBuilder();
                if (null!=doctorDepartmentVoList) {
                    doctorDepartmentVoList.forEach(e->{
                        if (CharSequenceUtil.isNotBlank(e.getDepartmentName())) {
                            sb.append(e.getDepartmentName()).append(",") ;
                        }
                    });
                }
                sb.deleteCharAt(sb.lastIndexOf(",")) ;
                doctirInfo.setDepartmentName(sb.toString());
            }

            return doctirInfo;
        } else {
            throw new ServiceException("请完善医生个人资料");
        }
    }


    @Override
    public List<BusDoctorHospital> getDoctorHospitalInfo(Long doctorId) {
        return busDoctorHospitalMapper.selectList(new LambdaQueryWrapper<BusDoctorHospital>().eq(BusDoctorHospital::getDoctorId, doctorId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePersonal(BusDoctorAudit busDoctorAudit) {
        Long hospitalId = busDoctorAudit.getHospitalId();
        Long doctorId = busDoctorAudit.getDoctorId();
        BusDoctor busDoctor = busDoctorMapper.selectOne(new LambdaQueryWrapper<BusDoctor>().eq(BusDoctor::getId, doctorId));
        List<BusDoctorDepartmentAudit> departmentAuditList = busDoctorAudit.getBusDoctorDepartmentAuditList();
        if (CollUtil.isEmpty(departmentAuditList)) {
            throw new ServiceException("科室参数缺失");
        }
        BusDoctorHospital doctorHospital = busDoctorHospitalMapper.selectOne(new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getDoctorId, busDoctorAudit.getDoctorId())
                .eq(BusDoctorHospital::getHospitalId, busDoctorAudit.getHospitalId()));
        //判断是否有(审核拒绝或审核中的处方) 0:未审核；1：审核未通过；2：审核通过; 3:作废 4 已使用 5已失效 6未签名
        List<BusPrescription> prescriptionList = busPrescriptionMapper.selectList(new LambdaQueryWrapper<BusPrescription>()
                .inSql(BusPrescription::getStatus, "0,1")
                .eq(BusPrescription::getHospitalId, busDoctorAudit.getHospitalId())
                .eq(BusPrescription::getDoctorId, busDoctorAudit.getDoctorId()));
        if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(busDoctorAudit.getIsThisCourt())) {
            BusPreorderDoctor busPreorderDoctor = busPreorderDoctorMapper.selectOne(new LambdaQueryWrapper<BusPreorderDoctor>()
                    .eq(BusPreorderDoctor::getHospitalId, hospitalId)
                    .eq(BusPreorderDoctor::getDoctorId, doctorId));
            if (ObjectUtil.isNotNull(busPreorderDoctor)) {
                throw new ServiceException("您已经是用药医生，无法成为特邀专家医生");
            }
            // 新增代码 2023-02-22 转特聘时有问诊中订单不能转
            BusHospitalPreorderDoctor preorderDoctor = hospitalPreorderDoctorMapper.selectOne(new LambdaQueryWrapper<BusHospitalPreorderDoctor>()
                    .eq(BusHospitalPreorderDoctor::getDoctorId, doctorId)
                    .eq(BusHospitalPreorderDoctor::getHospitalId, hospitalId));
            if (!Objects.isNull(preorderDoctor)) {
                throw new ServiceException("该医生已是开方医生，无法成为特聘专家");
            }
            if (!DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(doctorHospital.getIsThisCourt())
                    && CollUtil.isNotEmpty(prescriptionList)) {
                throw new ServiceException("您有处方未处理，无法切换执业方式");
            }
        } else {
            if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(doctorHospital.getIsThisCourt())
                    && CollUtil.isNotEmpty(prescriptionList)) {
                //判断是否有(审核拒绝或审核中的处方) 0:未审核；1：审核未通过；2：审核通过; 3:作废 4 已使用 5已失效 6未签名
                throw new ServiceException("您有处方未处理，无法切换执业方式");

            }
        }
        //完善三证
        busDoctorAudit.setPracticeCertificateNumber(busDoctor.getPracticeCertificateNumber());
        busDoctorAudit.setQualificationCertificateNumber(busDoctor.getQualificationCertificateNumber());
        busDoctorAudit.setTitleCertificateNumber(busDoctor.getTitleCertificateNumber());
        busDoctorAudit.setPracticeCertificateTime(busDoctor.getPracticeCertificateTime());
        busDoctorAudit.setQualificationCertificateTime(busDoctor.getQualificationCertificateTime());
        busDoctorAudit.setTitleCertificateTime(busDoctor.getTitleCertificateTime());
        busDoctorAudit.setWorkTime(busDoctor.getWorkTime());
        busDoctorAudit.setQualificationsCategory(busDoctor.getQualificationsCategory());
        if (YesNoEnum.NO.getCode().equals(busDoctorAudit.getIsThisCourt())) {
            QueryWrapper<BusDoctorHospital> listQueryWrapper = new QueryWrapper<>();
            listQueryWrapper.eq("doctor_id", doctorId);
            listQueryWrapper.orderByAsc("create_time");
            listQueryWrapper.eq("is_authentication", "3");
            List<BusDoctorHospital> hospitals = busDoctorHospitalMapper.selectList(listQueryWrapper);
            Long hospitalId1 = null;
            boolean isThisCourt = false;
            for (BusDoctorHospital h : hospitals) {
                if (YesNoEnum.YES.getCode().equals(h.getIsThisCourt()) && !h.getHospitalId().equals(hospitalId)) {
                    isThisCourt = true;
                    hospitalId1 = h.getHospitalId();
                }
            }
            if (ObjectUtil.isNotNull(hospitals)) {
                if (isThisCourt) {
                    busDoctorAudit.setAffiliatedHospital(hospitalId1);
                } else {
                    busDoctorAudit.setAffiliatedHospital(hospitals.get(0).getHospitalId());
                }
                busDoctorAudit.setCreateTime(DateUtils.getNowDate());
                busDoctorAudit.setIdCardNo(DESUtil.encrypt(busDoctorAudit.getIdCardNo()));
                busDoctorAudit.setPhoneNumber(DESUtil.encrypt(busDoctorAudit.getPhoneNumber()));
                busDoctorAudit.setAuditStatus(AuditStatus.NOTAPPROVED.getCode());
                busDoctorAudit.setUpdateTime(null);
                busDoctorAuditMapper.insert(busDoctorAudit);
            }
        } else {
            busDoctorAudit.setCreateTime(DateUtils.getNowDate());
            busDoctorAudit.setUpdateTime(null);
            busDoctorAudit.setIdCardNo(DESUtil.encrypt(busDoctorAudit.getIdCardNo()));
            busDoctorAudit.setPhoneNumber(DESUtil.encrypt(busDoctorAudit.getPhoneNumber()));
            busDoctorAudit.setAuditStatus(AuditStatus.NOTAPPROVED.getCode());
            busDoctorAudit.setAffiliatedHospital(hospitalId);//所属医院
            busDoctorAuditMapper.insert(busDoctorAudit);
        }

        //删除科室
        QueryWrapper<BusDoctorDepartmentAudit> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("audit_id", busDoctorAudit.getId());
        departmentAuditList.forEach(d -> {
            d.setCreateTime(DateUtils.getNowDate());
            d.setAuditId(busDoctorAudit.getId());
            busDoctorDepartmentAuditMapper.insert(d);
        });
        BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
        busDoctorTdl.setStatus("2");
        busDoctorTdl.setUpdateTime(DateUtils.getNowDate());
        UpdateWrapper<BusDoctorTdl> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("type", "4");
        updateWrapper.eq("doctor_id", doctorId);
        updateWrapper.eq("business_id", doctorId);
        updateWrapper.orderByDesc("create_time");
        busDoctorTdlMapper.update(busDoctorTdl, updateWrapper.last("limit 1"));
        return 1;
    }

    /**
     * 转特聘专家检查是否有在问诊中的订单
     *
     * @param hospitalId 医院id
     * @param doctorId   医生id
     * @return true-存在 false-不存在
     */
    private boolean checkHaveConsultationOrder(Long hospitalId, Long doctorId) {
        ArrayList<Integer> statusList = Lists.newArrayList(2, 3);
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusConsultationOrder::getHospitalId, hospitalId)
                .eq(BusConsultationOrder::getDoctorId, doctorId).and(
                        i -> i.in(BusConsultationOrder::getStatus, statusList)
                                .or().in(BusConsultationOrder::getVideoStatus, statusList));
        List<BusConsultationOrder> consultationOrders = consultationOrderMapper.selectList(queryWrapper);
        log.info("医院id:{},医生id:{}查询到的问诊中订单为:{}条", hospitalId, doctorId, consultationOrders.size());
        return !consultationOrders.isEmpty();
    }


    @Override
    public int updateBasePersonal(BusDoctor busDoctor) {
        log.info("修改医生资料入参：{}", busDoctor);
        busDoctor.setIdCardNo(DESUtil.encrypt(busDoctor.getIdCardNo()));
        busDoctor.setPhoneNumber(DESUtil.encrypt(busDoctor.getPhoneNumber()));
        busDoctor.setUpdateTime(DateUtils.getNowDate());
        return busDoctorMapper.updateById(busDoctor);
    }

    @Override
    public int changePassWord(BusSignature busSignature) {
        QueryWrapper<BusSignature> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("object_id", busSignature.getObjectId());
        queryWrapper.eq("object_type", busSignature.getObjectType());
        BusSignature signature = busSignatureMapper.selectOne(queryWrapper);
        String passWord = busSignature.getPassword();
        if (SecurityUtils.matchesPassword(passWord, signature.getPassword())) {
            throw new ServiceException("密码相同,无法修改");
        }
        busSignature.setPassword(SecurityUtils.encryptPassword(passWord));
        busSignature.setUpdateTime(DateUtils.getNowDate());
        UpdateWrapper<BusSignature> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("object_id", busSignature.getObjectId());
        updateWrapper.eq("object_type", busSignature.getObjectType());
        return busSignatureMapper.update(busSignature, updateWrapper);
    }

    @Override
    public int updatePhoneNumber(String phoneNumber, String oldPhoneNumber, Long doctorId) {
        phoneNumber = DESUtil.encrypt(phoneNumber);
        BusDoctor doctor = this.selectDoctorByPhone(phoneNumber);
        if (ObjectUtil.isNotNull(doctor)) {
            throw new ServiceException("手机号已被注册,无法修改");
        }
        QueryWrapper<BusOfficinaPharmacist> pharmacistQueryWrapper = new QueryWrapper<>();
        pharmacistQueryWrapper.eq("phone_number", phoneNumber);
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectOne(pharmacistQueryWrapper.last("limit " +
                "1"));
        if (ObjectUtil.isNotNull(pharmacist)) {
            throw new ServiceException("手机号已被注册,无法修改");
        }
        BusDoctor busDoctor = new BusDoctor();
        busDoctor.setPhoneNumber(DESUtil.encrypt(phoneNumber));
        busDoctor.setId(doctorId);
        busDoctor.setUpdateTime(DateUtils.getNowDate());
        return busDoctorMapper.updateById(busDoctor);
    }

    @Override
    public List<BusPrescriptionCd> diagnosis(Long hospitalId, Long doctorId) {
        List<BusPrescriptionCd> prescriptionCds = busPrescriptionCdMapper.selectDorctorPrescriptionCdList(hospitalId, doctorId);
        prescriptionCds = prescriptionCds.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(o -> o.getDiseaseName()))), ArrayList::new));
        return prescriptionCds;
    }

    @Override
    public boolean checkIdCardNo(String idCardNo, Long id) {
        QueryWrapper<BusDoctor> queryWrapper = new QueryWrapper<>();
        String encrypt = DESUtil.encrypt(idCardNo);
        queryWrapper.eq("id_card_no", encrypt);
        BusDoctor doctor = busDoctorMapper.selectOne(queryWrapper.last("limit 1"));
        if (ObjectUtil.isNotNull(doctor) && !(ObjectUtil.isNotNull(id) && id.equals(doctor.getId()))) {
            return true;
        }
        QueryWrapper<BusOfficinaPharmacist> pharmacistQueryWrapper = new QueryWrapper<>();
        pharmacistQueryWrapper.eq("id_number", encrypt);
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectOne(pharmacistQueryWrapper.last("limit " +
                "1"));
        return ObjectUtil.isNotNull(pharmacist) && !(ObjectUtil.isNotNull(id) && id.equals(pharmacist.getId()));
    }

    @Override
    public boolean checkDoctorAudit(Long doctorId, Long hospiatalId) {
        QueryWrapper<BusDoctorAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("doctor_id", doctorId);
        wrapper.orderByDesc("create_time");
        BusDoctorAudit busDoctorAudit = busDoctorAuditMapper.selectOne(wrapper.last("limit 1"));
        if (ObjectUtil.isNotNull(busDoctorAudit) && YesNoEnum.NO.getCode().equals(busDoctorAudit.getAuditStatus())) {
            return true;
        }
        QueryWrapper<BusDoctorHospital> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("doctor_id", doctorId);
        queryWrapper.ne("is_authentication", "3");
        List<BusDoctorHospital> doctorHospitals = busDoctorHospitalMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(doctorHospitals)) {
            for (BusDoctorHospital hospital : doctorHospitals) {
                return !hospiatalId.equals(hospital.getHospitalId());
            }
        }
        return false;
    }

    private void insertRecord(BusDoctorDto busDoctorDto) {
        try {
            BusSearchRecord searchRecord = new BusSearchRecord();
            searchRecord.setSearchValue(busDoctorDto.getSearchValue());
            searchRecord.setHospitalId(busDoctorDto.getHospitalId());
            searchRecord.setPatientId(busDoctorDto.getPatientId());
            searchRecord.setCreateTime(DateUtils.getNowDate());
            // 0为全局类型
            searchRecord.setSearchType(Integer.valueOf(busDoctorDto.getType()));
            //校验是否重复搜索
            BusSearchRecord latestRecord = busSearchRecordService.selectLatestRecord(searchRecord);
            if (ObjectUtil.isNull(latestRecord)) {
                busSearchRecordService.insert(searchRecord);
            }
        } catch (Exception e) {
            log.error("插入搜索记录失败 e={}", e);
        }
    }


    private List<BusBizDepartmentVo> buildBizDepartmentTree(List<BusBizDepartmentVo> busDepartments) {
        List<BusBizDepartmentVo> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<Long>();
        for (BusBizDepartmentVo depart : busDepartments) {
            tempList.add(depart.getId());
        }
        for (Iterator<BusBizDepartmentVo> iterator = busDepartments.iterator(); iterator.hasNext(); ) {
            BusBizDepartmentVo depart = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(depart.getParentId())) {
                recursionFn(busDepartments, depart);
                returnList.add(depart);
            }
        }
        if (returnList.isEmpty()) {
            returnList = busDepartments;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<BusBizDepartmentVo> list, BusBizDepartmentVo t) {
        // 得到子节点列表
        List<BusBizDepartmentVo> childList = getChildList(list, t);
        t.setChildren(childList);
        for (BusBizDepartmentVo tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<BusBizDepartmentVo> list, BusBizDepartmentVo t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * 得到子节点列表
     */
    private List<BusBizDepartmentVo> getChildList(List<BusBizDepartmentVo> list, BusBizDepartmentVo t) {
        List<BusBizDepartmentVo> tlist = new ArrayList<>();
        Iterator<BusBizDepartmentVo> it = list.iterator();
        while (it.hasNext()) {
            BusBizDepartmentVo n = it.next();
            if (ObjectUtil.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 查询热门医生下所有科室
     *
     * @param hospitalId
     * @return
     */
    @Override
    public List<BusBizDepartmentVo> queryDept(Long hospitalId) {
        String partnersCode = SecurityUtils.getPartnerscode();
        if (StringUtils.isNotEmpty(partnersCode)) {
            return busPartnersMapper.selectDept(hospitalId, partnersCode);
        } else {
            return busDoctorMapper.selectDept(hospitalId);
        }
    }

    @Override
    public boolean checkDisable(Long hospitalId, Long doctorId) {
        BusDoctorHospital doctirInfo = busDoctorHospitalMapper.selectOne(new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, hospitalId)
                .eq(BusDoctorHospital::getDoctorId, doctorId)
                .eq(BusDoctorHospital::getStatus, YesNoEnum.NO.getCode()));
        return ObjectUtil.isNotNull(doctirInfo);
    }

    /**
     * 注销医生
     *
     * @param doctorId
     * @param hospitalIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int logOut(Long doctorId, List<Long> hospitalIds) {
        BusDoctorHospital busDoctorHospital = new BusDoctorHospital();
        busDoctorHospital.setStatus(YesNoEnum.NO.getCode());
        QueryWrapper<BusDoctorHospital> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("doctor_id", doctorId)
                .in("hospital_id", hospitalIds);
        int update = busDoctorHospitalMapper.update(busDoctorHospital, queryWrapper);
        hospitalIds.forEach(h -> {
            QueryWrapper<BusDoctorHospital> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1
                    .eq("hospital_id", h)
                    .eq("doctor_id", doctorId);
            BusDoctorHospital doctorHospital = busDoctorHospitalMapper.selectOne(queryWrapper1);
            // 判断医生是否已认证，未认证删除相关数据
            if (ObjectUtil.isNotNull(doctorHospital) && !doctorHospital.getIsAuthentication().equals(3)) {
                // 删除医生关联医院表
                busDoctorHospitalMapper.deleteById(doctorHospital.getId());
                // 查询审核信息
                QueryWrapper<BusDoctorAudit> queryWrapper2 = new QueryWrapper<>();
                queryWrapper2
                        .eq("hospital_id", h)
                        .eq("doctor_id", doctorId);
                BusDoctorAudit busDoctorAudit = busDoctorAuditMapper.selectOne(queryWrapper2.last(" limit 1"));
                if (ObjectUtil.isNotNull(busDoctorAudit)) {
                    // 删除医生审核表
                    busDoctorAuditMapper.delete(queryWrapper2);
                    // 删除医生审核记录表
                    QueryWrapper<BusDoctorAuditRecord> doctorAuditRecordQueryWrapper = new QueryWrapper<>();
                    doctorAuditRecordQueryWrapper.eq("audit_id", busDoctorAudit.getId());
                    busDoctorAuditRecordMapper.delete(doctorAuditRecordQueryWrapper);
                }
                // 删除医生科室表
                QueryWrapper<BusDoctorDepartment> queryWrapper3 = new QueryWrapper<>();
                queryWrapper3
                        .eq("hospital_id", h)
                        .eq("doctor_id", doctorId);
                busDoctorDepartmentMapper.delete(queryWrapper3);
                // 删除医生问诊表
                QueryWrapper<BusDoctorConsultation> queryWrapper4 = new QueryWrapper<>();
                queryWrapper4
                        .eq("hospital_id", h)
                        .eq("doctor_id", doctorId);
                busDoctorConsultationMapper.delete(queryWrapper4);
            }
        });
        return update;
    }

    /**
     * 查询医助管理医生列表
     *
     * @param hospitalId
     * @param assistantId
     * @return
     */
    @Override
    public List<BusDoctorVo> queryManageDrList(Long hospitalId, Long assistantId) {
        List<BusDoctorVo> busDoctorVos = busDoctorMapper.queryManageDrList(hospitalId, assistantId);
        busDoctorVos.forEach(d -> {
            // 查询医生关联科室信息
            BusDoctorDepartment departmentQueryWrapper = new BusDoctorDepartment();
            departmentQueryWrapper.setHospitalId(hospitalId);
            departmentQueryWrapper.setDoctorId(d.getDoctorId());
            List<BusDoctorDepartmentVo> departmentList = busDoctorDepartmentMapper.selectList(departmentQueryWrapper);
            d.setBusDoctorDepartmentVoList(departmentList);
            // 查询医生问诊信息
            QueryWrapper<BusDoctorConsultation> consultationQueryWrapper = new QueryWrapper<>();
            consultationQueryWrapper.eq("hospital_id", hospitalId);
            consultationQueryWrapper.eq("doctor_id", d.getDoctorId());
            List<BusDoctorConsultation> consultationList =
                    busDoctorConsultationMapper.selectList(consultationQueryWrapper);
            d.setBusDoctorConsultationList(consultationList);
            // 查询医生医院信息
            List<BusDoctorHospitalVo> hospitalVos = busDoctorHospitalMapper.selectDoctorHospitalList(d.getDoctorId(), hospitalId);
            d.setDoctorHospitalList(hospitalVos);
        });
        return busDoctorVos;
    }

    /**
     * 查询特聘专家列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<BusDoctorVo> queryExpertList(BusDoctorDto dto) {
        List<BusDoctorVo> doctorVos = busDoctorMapper.selectDoctorList(dto);
        doctorVos.forEach(d -> {
            d.setIdCardNo(DESUtil.decrypt(d.getIdCardNo()));
            d.setPhoneNumber(DESUtil.decrypt(d.getPhoneNumber()));
            BusDoctorDepartment departmentQueryWrapper = new BusDoctorDepartment();
            departmentQueryWrapper.setHospitalId(dto.getHospitalId());
            departmentQueryWrapper.setDoctorId(d.getId());
            List<BusDoctorDepartmentVo> departmentList = busDoctorDepartmentMapper.selectList(departmentQueryWrapper);
            d.setBusDoctorDepartmentVoList(departmentList);
            List<BusDoctorHospitalVo> hospitalVos = busDoctorHospitalMapper.selectDoctorHospitalList(d.getId(),
                    dto.getHospitalId());
            d.setDoctorHospitalList(hospitalVos);
        });
        return doctorVos;
    }

    /**
     * 从缓存中获取医生信息
     *
     * @return
     */
    @Override
    public SMSLoginUser queryDrInfo() {
        // 获取医生信息
        Object userInfo = tokenService.getLoginUserInfo();
        SMSLoginUser loginUser = OrikaUtils.convert(userInfo, SMSLoginUser.class);
        if (ObjectUtil.isNull(loginUser)) {
            throw new ServiceException("登录失效", 401);
        }
        return loginUser;
    }

    @Override
    public BusDoctorHospital getDoctorHospital(Long hospitalId, Long doctorId) {
        return busDoctorHospitalMapper.selectOne(new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, hospitalId)
                .eq(BusDoctorHospital::getDoctorId, doctorId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDoctorNumber(String phoneNumber) {
        return busDoctorMapper.update(null, new LambdaUpdateWrapper<BusDoctor>()
                .set(BusDoctor::getPhoneNumber, phoneNumber)
                .eq(BusDoctor::getId, SecurityUtils.getUserId()));
    }

    /**
     * 查询药师信息
     *
     * @param role
     * @param phoneNumbers
     * @return
     */
    @Override
    public BusDoctorHospital selectPhysicianByPhone(String role, String phoneNumbers) {
        return busDoctorMapper.selectPhysicianByPhone(role, phoneNumbers);
    }

    /**
     * 修改登录token
     *
     * @param busDoctorHospital
     * @return
     */
    @Override
    public int updateToken(BusDoctorHospital busDoctorHospital) {
        return busDoctorHospitalMapper.updateById(busDoctorHospital);
    }

    /**
     * 查询药师信息
     *
     * @param officinaPharmacist
     * @return
     */
    @Override
    public List<BusDoctor> selectPhysician(BusOfficinaPharmacist officinaPharmacist) {
        return busDoctorMapper.selectPhysician(officinaPharmacist);
    }

    /**
     * 查询医师信息
     *
     * @param id
     * @return
     */
    @Override
    public BusDoctor selectPhysicianById(Long id) {
        return busDoctorMapper.selectById(id);
    }

    /**
     * 查询医助关联医生所在的科室ID
     *
     * @param assistantId 医助ID
     * @return
     */
    @Override
    public List<Long> selectDrDeptList(Long assistantId) {
        return busDoctorMapper.selectDrDeptList(assistantId);
    }

    /**
     * 查询该订单医助关联医生信息
     *
     * @param id
     * @param assistantId
     * @return
     */
    @Override
    public List<BusDoctor> selectOrderDrList(Long id, Long assistantId) {
        return busDoctorMapper.selectOrderDrList(id, assistantId);
    }

    /**
     * 查找热门医生
     * @param busDoctorDto - 查询条件
     * @return - 医生热门列表
     */
    @Override
    public List<BusDoctorBasicInfoVo> queryHostList(BusDoctorDto busDoctorDto) {
        List<BusDoctorBasicInfoVo> doctorList = new ArrayList<>();
        if (ObjectUtil.isEmpty(busDoctorDto.getPartnersCode())) {
            // 1.1查询热门医生列表-非合作机构--// 根据医院，科室名称筛选热门
            doctorList = doctorBasicInfoList(busDoctorDto);
        }else {
            // 1.2查询热门医生列表-合作机构---根据医院科室名称筛选热门
            doctorList = doctorBasicInfoPartnerList(busDoctorDto);
        }

        // 为最近一次发生过问诊的医生打标签
        // 判断是不是第一页
        if (ObjectUtil.isNull(busDoctorDto.getPageNum()) ||
                busDoctorDto.getPageNum() != 1 ){
            // 非第一页，且没有patientId，直接返回
            if (ObjectUtil.isNull(busDoctorDto.getPatientId())){
                return doctorList;
            }
            //非第一页，但是有patientId，则不显示近期看过的医生
            Long doctorId = busConsultationOrderService.selectDoctor(busDoctorDto);
            if (ObjectUtil.isNull(doctorId) || ObjectUtil.isEmpty(doctorList)) {
                // 近期没有看过的医生，直接返回
                return doctorList;
            }
            //判断list中是否存在近期看过的医生
            List<BusDoctorBasicInfoVo> finalDoctorList = doctorList;
            int index = IntStream.range(0, doctorList.size())
                    .filter(i -> doctorId.equals(finalDoctorList.get(i).getId()))
                    .findFirst()
                    .orElse(-1);
            if (index >= 0){
                // 移除该元素-近期看过的医生要去除（）
                doctorList.remove(index);
            }
            return doctorList;
        }
        // 第一页，如果没有传患者就不需要处理对应的近期看过的医生
        if (ObjectUtil.isNull(busDoctorDto.getPatientId())){
            return doctorList;
        }
        Long doctorId = busConsultationOrderService.selectDoctor(busDoctorDto);
        // 查找近期看过的医生
        // 为空就不用重新调整顺序了
        if (ObjectUtil.isNull(doctorId)) {
            return doctorList;
        }
        if (ObjectUtil.isEmpty(doctorList)){
            return showRecentlyDoctorWithPageOne(doctorId, busDoctorDto, new ArrayList<>());
        }

        //判断list中是否存在热门医生
        List<BusDoctorBasicInfoVo> finalDoctorList = doctorList;
        int index = IntStream.range(0, doctorList.size())
                .filter(i -> doctorId.equals(finalDoctorList.get(i).getId()))
                .findFirst()
                .orElse(-1);
        // 不存在-查出来-放入第一个直接返回
        if (index<0) {
            return showRecentlyDoctorWithPageOne(doctorId, busDoctorDto,doctorList);
        }
        // 存在-获取，然后放第一个返回
        if (StringUtils.isEmpty(busDoctorDto.getDepartmentName())) {
            BusDoctorBasicInfoVo busDoctorBasicInfoVo = doctorList.get(index);
            busDoctorBasicInfoVo.setYes(CodeEnum.YES.getCode());
            busDoctorBasicInfoVo.setOrderNum(0);
            doctorList = moveDoctorToFirst(doctorList, index);
        } else {
            List<String> departmentNames =
                    doctorList.get(index).getBusDoctorDepartmentVoList().stream().map(BusDoctorDepartmentVo::getDepartmentName).collect(Collectors.toList());
            if (departmentNames.contains(busDoctorDto.getDepartmentName())) {
                BusDoctorBasicInfoVo busDoctorBasicInfoVo = doctorList.get(index);
                busDoctorBasicInfoVo.setYes(CodeEnum.YES.getCode());
                busDoctorBasicInfoVo.setOrderNum(0);
                doctorList = moveDoctorToFirst(doctorList, index);
            }
        }

        return doctorList;
    }

    /**
     * 将list中的第i个医生放入第一个
     * @param doctorList - 医生列表
     * @param i - 需要放第一个的位置
     * @return - 调整顺序后的list
     */
    private List<BusDoctorBasicInfoVo> moveDoctorToFirst(List<BusDoctorBasicInfoVo> doctorList, int i) {
        // 将第i个放在第一个
        if (i == 0) {
            return doctorList;
        }
        // 移除后
        // 将该元素放到第一个
        doctorList.add(0, doctorList.remove(i));
        // 说明，移除和放到第一个会比sublist快
        return doctorList;
    }

    /**
     * 查询合作机构的医生列表
     * @param busDoctorDto - 查询条件
     * @return - 医生列表
     */
    private List<BusDoctorBasicInfoVo> doctorBasicInfoPartnerList(BusDoctorDto busDoctorDto) {
        if (Boolean.FALSE.equals(busDoctorDto.getWhetherStartPage())
                && ObjectUtil.isNotNull(busDoctorDto.getPageNum())
                && ObjectUtil.isNotNull(busDoctorDto.getPageSize())) {
            //分页
            busDoctorDto.setOffset((busDoctorDto.getPageNum() - 1) * busDoctorDto.getPageSize());

        }
        Long partnerId = busPartnersMapper.selectPartnerIdByPartnerCode(busDoctorDto.getPartnersCode());
        if (partnerId == null){
            log.warn("未查询到合作机构，合作机构编码为：{}",busDoctorDto.getPartnersCode());
            return null;
        }
        busDoctorDto.setPartnerId(partnerId);
        //科室
        List<Long> departments = busBizDepartmentMapper.selectByDepartmentName(busDoctorDto);
        List<BusDoctorBasicInfoVo> busDoctorBasicInfoVo = busDoctorMapper.selectHotDoctorListWithPartnersCode(busDoctorDto, departments);
        busDoctorBasicInfoVo.parallelStream().forEach(d -> setDoctorInfo(busDoctorDto, d));
        return busDoctorBasicInfoVo;
    }

    /**
     * 查询最近看过的医生信息
     * @param busDoctorDto- 前端入参查询条件
     * @param doctorId - 最近看过的医生的id
     * @param doctorList - 最后返回的医生列表
     */
    private List<BusDoctorBasicInfoVo> showRecentlyDoctorWithPageOne(Long doctorId, BusDoctorDto busDoctorDto,List<BusDoctorBasicInfoVo> doctorList) {
        //只有当为第一页的时候，数据为空才会显示近期看过的医生
        BusDoctorBasicInfoVo busDoctorVo = getHospitalDoctorInfo(doctorId,
                busDoctorDto.getHospitalId(), null);
        if (ObjectUtil.isNull(busDoctorVo)) {
            return doctorList;
        }
        List<String> departmentNames =
                busDoctorVo.getBusDoctorDepartmentVoList().stream().map(BusDoctorDepartmentVo::getDepartmentName).collect(Collectors.toList());
        if (StringUtils.isEmpty(busDoctorDto.getDepartmentName())
        ||departmentNames.contains(busDoctorDto.getDepartmentName())) {
            busDoctorVo.setYes(CodeEnum.YES.getCode());
            busDoctorVo.setOrderNum(0);
            doctorList.add(0, busDoctorVo);
        }
        return doctorList;
    }

    /**
     * 查询单个医生
     * @param query 查询参数
     * @return 医生信息
     */
    @Override
    public BusDoctorVo getOneDoctor(BusDoctorDto query) {
        return busDoctorMapper.getOne(query);
    }

    /**
     * 根据医生id，获取医生详情
     *
     * @param id 医生id
     * @return 医生信息详情
     */
    @Override
    public BusDoctorVo getById(Long id) {
        BusDoctor doctor = busDoctorMapper.selectById(id);
        return BeanUtil.copyProperties(doctor,BusDoctorVo.class );
    }

    @Override
    public BusDoctor queryByDTO(BusDoctorDto busDoctorDto) {
        if (Objects.isNull(busDoctorDto)) {
            return null;
        }
        BusDoctor doctor = null;
        if (Objects.nonNull(busDoctorDto.getId())) {
            doctor = busDoctorMapper.selectById(busDoctorDto.getId());
        } else if (StringUtils.isNotBlank(busDoctorDto.getPhoneNumber())) {
            LambdaQueryWrapper<BusDoctor> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusDoctor::getPhoneNumber, DESUtil.encrypt(busDoctorDto.getPhoneNumber()));
            queryWrapper.last(" limit 1");
            doctor = busDoctorMapper.selectOne(queryWrapper);
        } else if (StringUtils.isNotBlank(busDoctorDto.getIdCardNo())) {
            LambdaQueryWrapper<BusDoctor> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusDoctor::getIdCardNo, DESUtil.encrypt(busDoctorDto.getIdCardNo()));
            queryWrapper.last(" limit 1");
            doctor = busDoctorMapper.selectOne(queryWrapper);
        }
        if (Objects.nonNull(doctor) && Objects.nonNull(busDoctorDto.getHospitalId())) {
            LambdaQueryWrapper<BusDoctorHospital> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusDoctorHospital::getDoctorId, doctor.getId());
            queryWrapper.eq(BusDoctorHospital::getHospitalId, busDoctorDto.getHospitalId());
            queryWrapper.eq(StringUtils.isNotBlank(busDoctorDto.getRole()), BusDoctorHospital::getRole, busDoctorDto.getRole());
            queryWrapper.last(" limit 1");
            BusDoctorHospital doctorHospital = busDoctorHospitalMapper.selectOne(queryWrapper);
            if (Objects.isNull(doctorHospital) || !EnableStatusEnum.isEnabled(doctorHospital.getStatus())) {
                return null;
            }

        }
        return doctor;
    }

    /**
     *  获取医院医生签名
     * @param hospitalId    医院ID
     * @param doctorId      医生ID
     * @return  医生签名，如果是特聘专家，会返回设置的开方医生签名
     */
    @Override
    public BusSignatureVO getDoctorSignature(Long hospitalId, Long doctorId) {
        BusSignatureVO busSignatureVO = new BusSignatureVO();
        // 查询医院关联的医生信息
        LambdaQueryWrapper<BusDoctorHospital> queryWrapper = new LambdaQueryWrapper<BusDoctorHospital>().eq(BusDoctorHospital::getHospitalId, hospitalId).eq(BusDoctorHospital::getDoctorId, doctorId);
        BusDoctorHospital busDoctorHospital = busDoctorHospitalMapper.selectOne(queryWrapper, false);
        if (Objects.isNull(busDoctorHospital)) {
            log.warn("医生不存在 ！hospitalId={},doctorId={}", hospitalId, doctorId);
            return busSignatureVO;
        }
        // 判断是否是特邀医生
        if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(busDoctorHospital.getIsThisCourt())) {
            LambdaQueryWrapper<BusHospitalPreorderDoctor> preOrderDoctorQueryWrapper = new LambdaQueryWrapper<BusHospitalPreorderDoctor>().eq(BusHospitalPreorderDoctor::getHospitalId, hospitalId);
            BusHospitalPreorderDoctor busHospitalPreorderDoctor = hospitalPreorderDoctorMapper.selectOne(preOrderDoctorQueryWrapper, false);
            if (Objects.isNull(busHospitalPreorderDoctor)) {
                log.warn("医院未设置默认开方医生 ！hospitalId={}", hospitalId);
                return busSignatureVO;
            }
            doctorId = busHospitalPreorderDoctor.getDoctorId();
        }
        // 查询医生签名信息
        LambdaQueryWrapper<BusSignature> signatureQueryWrapper = new LambdaQueryWrapper<BusSignature>().eq(BusSignature::getObjectId, doctorId).eq(BusSignature::getObjectType, EhrSourceEnum.DOCTOR.getCode());
        BusSignature busSignature = busSignatureMapper.selectOne(signatureQueryWrapper, false);
        if (Objects.nonNull(busSignature)) {
            BeanUtil.copyProperties(busSignature, busSignatureVO);
        }
        return busSignatureVO;
    }

}

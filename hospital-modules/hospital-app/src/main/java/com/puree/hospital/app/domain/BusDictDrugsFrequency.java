package com.puree.hospital.app.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import com.puree.hospital.common.api.enums.YesNoEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 用药频次信息表
 */
@Data
public class BusDictDrugsFrequency extends Entity {

    private static final long serialVersionUID = 2610936449037561100L;

    /** 编码 */
    private String code;
    /** 名称 */
    private String name;
    /** 备注 */
    private String remark;

    /**
     * 次/天
     */
    @TableField(exist = false)
    private BigDecimal severalTimesDay;

    /**
     * 几次
     */
    private Integer severalTimes;

    /**
     *  天数
     */
    private Integer days;

    /**
     *  是否参与计算【0.否 1.是】
     */
    private Integer calculate;

    /**
     *  计算用药频率
     * @return  次/天
     */
    public BigDecimal getSeveralTimesDay() {
        if (YesNoEnum.YES.getCode().equals(this.getCalculate())) {
            return new BigDecimal(this.getSeveralTimes()).divide(new BigDecimal(this.getDays()), 2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ONE;
    }

    /**
     *  处理配置项和实体名称对不上问题
     */
    @JSONField(name = "miMappingCode")
    public void setMiMappingCode(Integer miMappingCode) {
        this.code = String.valueOf(miMappingCode);
    }

    /**
     *  处理配置项和实体名称对不上问题
     */
    @JSONField(name = "dictCode")
    public void setDictCode(String dictCode) {
        this.name = dictCode;
    }

    /**
     *  处理配置项和实体名称对不上问题
     */
    @JSONField(name = "dictLabel")
    public void setDictLabel(String dictLabel) {
        this.remark = dictLabel;
    }
}

package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.annotation.Excel;
import com.puree.hospital.common.api.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 药品对象 bus_drugs
 * 
 * <AUTHOR>
 * @date 2021-10-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BusDrugs extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    /** 药品编号 */
    @Excel(name = "药品编号")
    private String drugsNumber;

    /** 标准通用名 */
    @Excel(name = "标准通用名")
    private String standardCommonName;

    /** 药品制造商 */
    @Excel(name = "药品制造商")
    private String drugsManufacturer;

    /** 药品名称 */
    @Excel(name = "药品名称")
    private String drugsName;

    /** 参考售价 */
    @Excel(name = "参考售价")
    private BigDecimal referenceSellingPrice;

    /** 药品本位码 */
    @Excel(name = "药品本位码")
    private String drugsStandardCode;

    /** 参考进价 */
    @Excel(name = "参考进价")
    private BigDecimal referencePurchasePrice;

    /** 国药准字(英文简写) */
    @Excel(name = "国药准字(英文简写)")
    private String nmpn;

    /** 拼音码 */
    @Excel(name = "拼音码")
    private String pinyinCode;

    /** 药品图片 */
    @Excel(name = "药品图片")
    private String drugsImg;

    /** 药品详情 */
    @Excel(name = "药品详情")
    private String drugsDetails;

    /** 药理/功效分类(字典表关联) */
    @Excel(name = "药理/功效分类(字典表关联)")
    private Long efficacyClassification;

    /** 处方标识(字典表关联) */
    @Excel(name = "处方标识(字典表关联)")
    private Long prescriptionIdentification;

    /** 商品类型*/
    @Excel(name = "商品类型")
    private Long drugsType;

    /** 药品剂型(字典表关联) */
    @Excel(name = "药品剂型(字典表关联)")
    private Long drugsDosageForm;

    /** 药品规格 */
    @Excel(name = "药品规格")
    private String drugsSpecification;

    /** 药品包装单位(字典表关联) */
    @Excel(name = "药品包装单位(字典表关联)")
    private Long drugsPackagingUnit;

    /** 药品用法(字典表关联) */
    @Excel(name = "药品用法 0 口服 1外服")
    private Long drugsUsage;

    /** 建议用量 */
    @Excel(name = "建议用量")
    private String recommendedDosage;

    /** 医保类型(字典表关联) */
    @Excel(name = "医保类型(字典表关联)")
    private Long medicalInsuranceType;

    /** 状态（0停用 1启用） */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private Integer status;

    /** 是否删除（0否 1是） */
    private Integer delFlag;

    /**
     * 商品分类
     */
    @Excel(name = "药品分类")
    private Long classifyId;

    /**药品类型 0西药 1中药*/
    private Integer type;

    /**运营药品编号*/
    private String yySeq;

    /**煎煮方法*/
    private Long decoctingMethod;
    private String ypid;
    /**
     * 国标药品代码
     */
    private String nationalDrugCode;
    /**
     * 详细规格
     */
    private String detailedSpecifications;
    /**
     * 药品详情图*/
    private String drugsImgDetail;
    /**
     * 药品主图
     * */
    private String mainImg;
    /**
     * 最小包装数量
     * */
    private Integer minPackNum;
    /**
     * 最小制剂单位
     * */
    private String minMakeUnit;
    /**
     * 最小包装单位
     * */
    private String minPackUnit;
    /**
     * icd-10编码
     */
    private String icdCode;

    /** 重量（克） */
    @TableField(exist = false)
    private String weight;

    /**
     * 基本剂量
     */
    private BigDecimal baseDose;

    /**
     * 基本剂量单位
     */
    private String baseDoseUnit;

    /**
     * HIS药品ID
     */
    private String hisDrugsId;

    /**
     * 每次用量
     */
    private BigDecimal singleDose;

    /**
     * 默认频次，参考频次字典表
     */
    private String defaultFrequency;

    /**
     * 推荐使用天数
     */
    private Integer recommendUseDays;

}

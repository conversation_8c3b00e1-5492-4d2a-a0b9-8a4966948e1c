package com.puree.hospital.app.third.controller;

import com.puree.hospital.app.api.model.dto.third.ThirdPartyCancelDTO;
import com.puree.hospital.app.third.service.ThirdPartyOrderService;
import com.puree.hospital.common.api.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName ThirdPartyOrderController
 * <AUTHOR>
 * @Description 第三方 订单相关接口
 * @Date 2024/11/18 15:16
 * @Version 1.0
 */
@RequestMapping("/3rd/order")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ThirdPartyOrderController {


    private final ThirdPartyOrderService thirdPartyOrderService;

    /**
     * @Param
     * @Return AjaxResult<Boolean>
     * @Description 第三方取消订单
     * <AUTHOR>
     * @Date 2024/11/18 15:10
     **/
    @PutMapping("/cancel")
    //todo 第三方鉴权
    public AjaxResult<Boolean> thirdPartyCancel(@RequestBody @Validated ThirdPartyCancelDTO dto) {
        return AjaxResult.success(thirdPartyOrderService.thirdPartyCancel(dto));
    }


}

package com.puree.hospital.app.service;

import com.puree.hospital.app.api.model.vo.BusSignatureVO;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDoctorAudit;
import com.puree.hospital.app.domain.BusDoctorDepartment;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.BusPrescriptionCd;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.BusDoctorDto;
import com.puree.hospital.app.domain.dto.DoctorConsultationDto;
import com.puree.hospital.app.domain.vo.BizDepartmentTreeSelect;
import com.puree.hospital.app.domain.vo.BusBizDepartmentVo;
import com.puree.hospital.app.domain.vo.BusDoctorBasicInfoVo;
import com.puree.hospital.app.domain.vo.BusDoctorHospitalVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;

import java.util.List;
import java.util.Map;

public interface IBusDoctorService {
    /**
     * 根据手机号码查询id
     * @param phoneNumber
     * @return
     */
    BusDoctor selectDoctorByPhone(String phoneNumber);

    /**
     * 根据手机号码和id
     * @param phoneNumber
     * @return
     */
    BusDoctorVo selectDoctorByPhoneAndHospitalId(String phoneNumber,Long hospitalId);

    /**
     * 根据id获取医生信息
     * @param id
     * @return
     */
    BusDoctorVo selectDoctorDetailInfoById(Long id);

    /**
     * 第一执业地点
     * @param id
     * @return
     */
    BusDoctorHospitalVo firstPractice(Long id);
    /**
     * 查询医生信息
     * @param id
     * @return
     */
    BusDoctorVo selectDoctorInfoById(Long id);
    List<BusBizDepartmentVo> selectDoctorLeftDepartmentList(BusDoctorDepartment busDoctorDepartment);
    List<BizDepartmentTreeSelect> buildDepartmentsTreeSelect(List<BusBizDepartmentVo> busDepartments);

    /**
     * 插入医生信息
     * @param busDoctor
     * @return
     */
    int insertBusDoctor(BusDoctor busDoctor,Long hospitalId);

    /**
     * 实名认证
     * @param busDoctorAudit
     * @return
     */
    int realNameAuthentication(BusDoctorAudit busDoctorAudit);

    /**
     * 医生认证
     * @param busDoctorAudit
     * @return
     */
    int physicianAuthentication(BusDoctorAudit busDoctorAudit);

    /**
     * 获取医生绑定医院
     * @param dto
     * @return
     */
    BusDoctorHospital selectBusDoctorHospital(DoctorConsultationDto dto);

    /**
     * 医生多次认证操作
     * @param busDoctorAudit
     */
    void manyTimesAuthentication(BusDoctorAudit busDoctorAudit);

    /**
     * 获取医生认证信息
     * @param doctorId
     * @param hospitalId
     * @param phoneNumber
     * @return
     */
    BusDoctorAudit authenticationInfo(Long doctorId,Long hospitalId,String phoneNumber);

    /**
     * 查询医生列表
     * @param busDoctorDto
     * @return
     */
    List<BusDoctorVo> doctorList(BusDoctorDto busDoctorDto);

    List<BusDoctorBasicInfoVo> doctorBasicInfoList(BusDoctorDto busDoctorDto);

    /**
     * 获取医院医生信息
     * @param id
     * @param hospitalId
     * @return
     */
    BusDoctorBasicInfoVo getHospitalDoctorInfo(Long id, Long hospitalId, Long patientId);
    BusDoctorVo selectHospitalDoctorInfo(Long id,Long hospitalId);

    /**
     * 全局搜索
     * @param busDoctorDto
     * @return
     */
    List<BusDoctorVo> globalSearch(BusDoctorDto busDoctorDto,boolean isLogin);

    /**
     * 搜索填充
     * @param searchValue
     * @return
     */
    List<Map<String,Object>> fillSearch(String searchValue, Long hospitalId);

    /**
     * 查询医生个人资料
     * @param hospitalId
     * @param doctorId
     * @return
     */
    BusDoctorVo personalData(Long hospitalId, Long doctorId);

    /**
     * 查询医生个人资料
     * @param doctorId
     * @return
     */
    List<BusDoctorHospital> getDoctorHospitalInfo(Long doctorId);

    /**
     * 修改证件资料
     * @param busDoctorAudit
     * @return
     */
    int updatePersonal(BusDoctorAudit busDoctorAudit);

    /**
     * 修改基本资料
     * @param busDoctor
     * @return
     */
    int updateBasePersonal(BusDoctor busDoctor);

    /**
     * 修改密码
     * @param busSignature
     * @return
     */
    int changePassWord(BusSignature busSignature);

    /**
     * 修改手机号码
     * @return
     */
    int updatePhoneNumber(String phoneNumber,String oldPhoneNumber,Long doctorId);
    /**
     * 查询医生所在医院诊断
     * @param hospitalId
     * @param doctorId
     * @return
     */
    List<BusPrescriptionCd> diagnosis(Long hospitalId, Long doctorId);

    /**
     * 校验身份证
     * @param idCardNo
     * @return
     */
    boolean checkIdCardNo(String idCardNo,Long id);
    boolean checkDoctorAudit(Long doctorId,Long hospiatalId);

    /**
     * 查询热门医生下所有科室
     * @param hospitalId
     * @return
     */
    List<BusBizDepartmentVo> queryDept(Long hospitalId);

    /**
     * 校验医生是否禁用
     * @param hospitalId
     * @param doctorId
     * @return
     */
    boolean checkDisable(Long hospitalId, Long doctorId);

    /**
     * 注销医生
     * @param doctorId
     * @param hospitalIds
     * @return
     */
    int logOut(Long doctorId, List<Long> hospitalIds);

    /**
     * 查询医助管理医生列表
     * @param hospitalId
     * @param assistantId
     * @return
     */
    List<BusDoctorVo> queryManageDrList(Long hospitalId, Long assistantId);

    /**
     * 查询特聘专家列表
     * @param dto
     * @return
     */
    List<BusDoctorVo> queryExpertList(BusDoctorDto dto);

    /**
     * 从缓存中获取医生信息
     * @return
     */
    SMSLoginUser queryDrInfo();

    /**
     * 查询医生执业方式(isThisCourt)
     * <AUTHOR>
     * @param doctorId 医生id
     * @param hospitalId 医院id
     * @return 医院医生信息
     */
    BusDoctorHospital getDoctorHospital(Long hospitalId, Long doctorId);

    /**
     * 更新医师手机号
     * <AUTHOR>
     * @param phoneNumber 医生id
     * @return 医院医生信息
     */
    int updateDoctorNumber(String phoneNumber);

    /**
     * 查询医师信息
     * @param role
     * @param phoneNumbers
     * @return
     */
    BusDoctorHospital selectPhysicianByPhone(String role, String phoneNumbers);

    /**
     * 修改登录token
     * @param busDoctorHospital
     * @return
     */
    int updateToken(BusDoctorHospital busDoctorHospital);

    /**
     * 查询药师信息
     * @param officinaPharmacist
     * @return
     */
    List<BusDoctor> selectPhysician(BusOfficinaPharmacist officinaPharmacist);

    /**
     * 查询医师信息
     * @param id
     * @return
     */
    BusDoctor selectPhysicianById(Long id);

    /**
     * 查询医助关联医生所在的科室ID
     * @param assistantId 医助ID
     * @return
     */
    List<Long> selectDrDeptList(Long assistantId);

    /**
     * 查询该订单医助关联医生信息
     * @param id
     * @param assistantId
     * @return
     */
    List<BusDoctor> selectOrderDrList(Long id, Long assistantId);

    /**
     * 查找热门医生
     * @param busDoctorDto - 医生dto
     */
    List<BusDoctorBasicInfoVo> queryHostList(BusDoctorDto busDoctorDto);

    /**
     * 查询最近看过的医生信息
     * @param query- 前端入参查询条件
     * @return 医生信息
     */
    BusDoctorVo getOneDoctor(BusDoctorDto query);

    BusDoctorVo getById(Long id);

    /**
     * 根据查询参数查询医生信息
     *
     * @param busDoctorDto 查询参数
     * @return 医生信息
     */
    BusDoctor queryByDTO(BusDoctorDto busDoctorDto);

    /**
     *  获取医院医生签名
     * @param hospitalId    医院ID
     * @param doctorId      医生ID
     * @return  医生签名，如果是特聘专家，会返回设置的开方医生签名
     */
    BusSignatureVO getDoctorSignature(Long hospitalId, Long doctorId);

}

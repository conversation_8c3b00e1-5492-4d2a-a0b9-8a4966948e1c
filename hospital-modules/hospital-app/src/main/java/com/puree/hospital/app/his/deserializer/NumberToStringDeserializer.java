package com.puree.hospital.app.his.deserializer;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;

/**
 * <p>
 * 数字序列化字符串类型，去掉后面“.0”
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/30 15:13
 */
public class NumberToStringDeserializer implements ObjectDeserializer {

    @Override
    public String deserialze(DefaultJSONParser parser, Type type, Object o) {
        Object value = parser.parse();
        if (value instanceof Number) {
            Number num = (Number) value;
            // 如果是整数，去掉.0
            if (num.doubleValue() == num.longValue()) {
                return String.valueOf(num.longValue());
            }
            return String.valueOf(num.doubleValue());
        }
        return value != null ? value.toString() : null;
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}

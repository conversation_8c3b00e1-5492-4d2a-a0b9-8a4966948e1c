package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.puree.hospital.app.api.model.event.consultation.BaseConsultationEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationBookedEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationReturnNumberEvent;
import com.puree.hospital.app.constant.ConsultationOrderStatus;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDoctorScheduling;
import com.puree.hospital.app.domain.BusDoctorSchedulingTime;
import com.puree.hospital.app.domain.BusDoctorTdl;
import com.puree.hospital.app.domain.dto.BusDoctorSchedulingDto;
import com.puree.hospital.app.domain.dto.ReAppointmentDto;
import com.puree.hospital.app.domain.vo.BusDoctorSchedulingVo;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusConsultationSettingsMapper;
import com.puree.hospital.app.mapper.BusDoctorSchedulingMapper;
import com.puree.hospital.app.mapper.BusDoctorSchedulingTimeMapper;
import com.puree.hospital.app.mapper.BusDoctorTdlMapper;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.VideoConsultationGuohaoProducer;
import com.puree.hospital.app.queue.producer.VideoConsultationNoticeProducer;
import com.puree.hospital.app.queue.producer.event.consultation.ConsultationBookedEventProducer;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDoctorSchedulingService;
import com.puree.hospital.app.service.ITongLianPayService;
import com.puree.hospital.app.service.IWxPayService;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ImMemberEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorSchedulingServiceImpl implements IBusDoctorSchedulingService {
    private final BusDoctorSchedulingMapper busDoctorSchedulingMapper;
    private final BusDoctorSchedulingTimeMapper busDoctorSchedulingTimeMapper;
    private final BusConsultationSettingsMapper busConsultationSettingsMapper;
    private final BusConsultationOrderMapper busConsultationOrderMapper;
    private final IWxPayService wxPayService;
    private final BusDoctorTdlMapper busDoctorTdlMapper;
    private final RedisTemplate<String, String> redisTemplate;
    @Autowired
    private ITongLianPayService iTongLianPayService;
    @Autowired
    @Lazy
    private VideoConsultationGuohaoProducer videoConsultationGuohaoProducer;
    @Autowired
    @Lazy
    private VideoConsultationNoticeProducer videoConsultationNoticeProducer;

    @Resource
    private ApplicationEventPublisher publisher;

    @Lazy
    @Resource
    private ConsultationBookedEventProducer consultationBookedEventProducer;

    @Override
    public List<BusDoctorSchedulingVo> selectList(BusDoctorSchedulingDto doctorScheduling) {
        return busDoctorSchedulingMapper.selectDoctorSchedulingList(doctorScheduling);
    }

    @Override
    public Map<String, List<BusDoctorSchedulingVo>> selectDepartmentList(BusDoctorSchedulingDto doctorScheduling) {
        List<BusDoctorSchedulingVo> doctorSchedulingList =
                busDoctorSchedulingMapper.selectDoctorSchedulingList(doctorScheduling);
        if (!doctorSchedulingList.isEmpty()) {
            //时间格式转换
            doctorSchedulingList.stream().forEach(s -> {
                LocalDateTime localDate = DateUtils.parseLocalDate(s.getSchedulingDate());
                s.setGroupDate(localDate);
            });
            //根据天分组
            Map<String, List<BusDoctorSchedulingVo>> groupByDay = doctorSchedulingList.stream().collect(
                    Collectors.groupingBy(d -> DateUtils.parse_yyyyMMdd(d.getGroupDate()))
            );
            return groupByDay;
        }
        return new HashMap<>();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(BusDoctorScheduling doctorScheduling) {
        BusDoctorSchedulingTime time = doctorScheduling.getSchedulingTime();
        // 当前时间
        Date nowDate = DateUtils.getNowDate();
        Date startTime = DateUtils.pjTime(doctorScheduling.getSchedulingDate(), time.getStartTime());
        int hour = DateUtils.computationTime(startTime, nowDate);
        if (hour < 1) {
            throw new ServiceException("排班时间必须设置为当前时间一小时后");
        }
        if (StringUtils.isNull(doctorScheduling.getId())) {
            busDoctorSchedulingMapper.insert(doctorScheduling);
        }

        QueryWrapper<BusConsultationSettings> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hospital_id", doctorScheduling.getHospitalId());
        BusConsultationSettings settings = busConsultationSettingsMapper.selectOne(queryWrapper.last("limit 1"));
        String videoPriceTimes = settings.getVideoPriceTimes();
        String[] split = videoPriceTimes.split(",");
        String min = split[1];
        //插入排班时间
        time.setMinute(min);
        time.setSchedulingId(doctorScheduling.getId());
        time.setCreateTime(DateUtils.getNowDate());
        //查询分段时间
        List<BusDoctorSchedulingTime> schedulingTimes = splitDate(time, doctorScheduling.getSchedulingDate());
        List<BusDoctorSchedulingTime> reorder = reorder(schedulingTimes, doctorScheduling.getId(), min);
        for (BusDoctorSchedulingTime t : reorder) {
            busDoctorSchedulingTimeMapper.insert(t);
        }
        return 1;
    }

    private List<BusDoctorSchedulingTime> reorder(List<BusDoctorSchedulingTime> times, Long id, String min) {
        List<BusDoctorSchedulingTime> schedulingTimes = new ArrayList<>();
        //早
        List<BusDoctorSchedulingTime> morningList = new ArrayList<>();
        //中
        List<BusDoctorSchedulingTime> middleList = new ArrayList<>();
        //晚
        List<BusDoctorSchedulingTime> nightList = new ArrayList<>();
        for (BusDoctorSchedulingTime s : times) {
            int time = DateUtils.pdTime(s.getStartTime());
            switch (time) {
                case 1:
                    morningList.add(s);
                    break;
                case 2:
                    middleList.add(s);
                    break;
                case 3:
                    nightList.add(s);
                    break;
                default:
            }
        }
        if (StringUtils.isNotEmpty(morningList)) {
            BusDoctorSchedulingTime one = morningList.get(0);
            BusDoctorSchedulingTime last = morningList.get(morningList.size() - 1);
            Date startTime = one.getStartTime();
            Date endTime = last.getEndTime();
            String minute = one.getMinute();
            BusDoctorSchedulingTime schedulingTime = new BusDoctorSchedulingTime();
            schedulingTime.setStartTime(startTime);
            schedulingTime.setEndTime(endTime);
            schedulingTime.setMinute(minute);
            schedulingTime.setSchedulingId(id);
            schedulingTime.setMinute(min);
            schedulingTime.setCreateTime(DateUtils.getNowDate());
            schedulingTimes.add(schedulingTime);
        }

        if (StringUtils.isNotEmpty(middleList)) {
            BusDoctorSchedulingTime one = middleList.get(0);
            BusDoctorSchedulingTime last = middleList.get(middleList.size() - 1);
            Date startTime = one.getStartTime();
            Date endTime = last.getEndTime();
            String minute = one.getMinute();
            BusDoctorSchedulingTime schedulingTime = new BusDoctorSchedulingTime();
            schedulingTime.setStartTime(startTime);
            schedulingTime.setEndTime(endTime);
            schedulingTime.setMinute(minute);
            schedulingTime.setSchedulingId(id);
            schedulingTime.setMinute(min);
            schedulingTime.setCreateTime(DateUtils.getNowDate());
            schedulingTimes.add(schedulingTime);
        }

        if (StringUtils.isNotEmpty(nightList)) {
            BusDoctorSchedulingTime one = nightList.get(0);
            BusDoctorSchedulingTime last = nightList.get(nightList.size() - 1);
            Date startTime = one.getStartTime();
            Date endTime = last.getEndTime();
            String minute = one.getMinute();
            BusDoctorSchedulingTime schedulingTime = new BusDoctorSchedulingTime();
            schedulingTime.setStartTime(startTime);
            schedulingTime.setEndTime(endTime);
            schedulingTime.setMinute(minute);
            schedulingTime.setSchedulingId(id);
            schedulingTime.setMinute(min);
            schedulingTime.setCreateTime(DateUtils.getNowDate());
            schedulingTimes.add(schedulingTime);
        }
        return schedulingTimes;
    }

    private List<BusDoctorSchedulingTime> splitDate(BusDoctorSchedulingTime time, Date schedulingDate) {
        if (CodeEnum.NO.getCode().equals(time.getMinute())) {
            log.error("分段时间", time.getMinute());
            throw new ServiceException("医生排班异常");
        }
        List<BusDoctorSchedulingTime> list = new ArrayList<>();
        Date startTime = DateUtils.pjTime(schedulingDate, time.getStartTime());
        Date endTime = null;
        if ("00:00".equals(DateUtils.changeHHmmDate(time.getEndTime()))) {
            endTime = DateUtils.pjTimeTow(schedulingDate, time.getEndTime());
        } else {
            endTime = DateUtils.pjTime(schedulingDate, time.getEndTime());
        }

        Calendar dd = Calendar.getInstance();// 定义日期实例
        dd.setTime(startTime);// 设置日期起始时间
        Calendar cale = Calendar.getInstance();
        //结束日期
        Calendar c = Calendar.getInstance();
        c.setTime(endTime);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        BusDoctorSchedulingTime schedulingTime = null;
        BusDoctorSchedulingTime fireschedulingTime = new BusDoctorSchedulingTime();
        fireschedulingTime.setStartTime(startTime);
        dd.setTime(dd.getTime());
        dd.add(Calendar.MINUTE, Integer.valueOf(time.getMinute()));
        fireschedulingTime.setEndTime(dd.getTime());
        fireschedulingTime.setSchedulingId(time.getSchedulingId());
        fireschedulingTime.setCreateTime(DateUtils.getNowDate());
        list.add(fireschedulingTime);
        while (dd.before(c) || dd.compareTo(c) == 0) {// 判断是否到结束日期
            schedulingTime = new BusDoctorSchedulingTime();
            Date date = dd.getTime();
            cale.setTime(date);
            cale.add(Calendar.MINUTE, Integer.valueOf(time.getMinute()));
            schedulingTime.setStartTime(date);
            schedulingTime.setEndTime(cale.getTime());
            schedulingTime.setSchedulingId(time.getSchedulingId());
            schedulingTime.setCreateTime(DateUtils.getNowDate());
            list.add(schedulingTime);
            dd.add(Calendar.MINUTE, Integer.valueOf(time.getMinute()));// 加上时间区间的分钟
        }
        if (!list.isEmpty()) {
            BusDoctorSchedulingTime doctorSchedulingTime = list.get(list.size() - 1);
            Date s = doctorSchedulingTime.getStartTime();
            Calendar cc = Calendar.getInstance();// 定义日期实例
            cc.setTime(s);
            long l = DateUtils.jsDate(s, time.getEndTime());
            Long aLong = Long.valueOf(time.getMinute());
            if (l < aLong) {
                list.remove(list.size() - 1);
            }
        }
        return list;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BusDoctorSchedulingTime doctorScheduling) {
        BusDoctorScheduling scheduling = busDoctorSchedulingMapper.selectById(doctorScheduling.getSchedulingId());
        if (Objects.isNull(scheduling)) {
            throw new ServiceException("排班已被删除,无法操作");
        }
        Date startTime = DateUtils.pjTime(scheduling.getSchedulingDate(), doctorScheduling.getStartTime());
        Date endTime = DateUtils.pjTime(scheduling.getSchedulingDate(), doctorScheduling.getEndTime());
        if (startTime.compareTo(new Date()) < 0) {
            throw new ServiceException("排班时间已过,无法删除");
        }
        busDoctorSchedulingTimeMapper.deleteById(doctorScheduling.getId());
        //为空删除整个排班
        QueryWrapper<BusDoctorSchedulingTime> wrapper = new QueryWrapper<>();
        wrapper.eq("scheduling_id", doctorScheduling.getSchedulingId());
        List<BusDoctorSchedulingTime> schedulingTimes = busDoctorSchedulingTimeMapper.selectList(wrapper);
        if (schedulingTimes.isEmpty()) {
            busDoctorSchedulingMapper.deleteById(doctorScheduling.getSchedulingId());
        }
        QueryWrapper<BusConsultationOrder> orderQueryWrapper = new QueryWrapper<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String videoStatus = "0,10,11";
        List<String> newsTypes = Arrays.asList(videoStatus.split(","));
        orderQueryWrapper.lambda()
                .eq(BusConsultationOrder::getDoctorId, doctorScheduling.getDoctorId())
                .eq(BusConsultationOrder::getDepartmentId, doctorScheduling.getDepartmentId())
                .eq(BusConsultationOrder::getHospitalId, doctorScheduling.getHospitalId())
                .ge(BusConsultationOrder::getStartTime, sdf.format(startTime))
                .le(BusConsultationOrder::getStartTime, sdf.format(endTime))
                .in(BusConsultationOrder::getVideoStatus, newsTypes);
        List<BusConsultationOrder> consultationOrders = busConsultationOrderMapper.selectList(orderQueryWrapper);
        if (CollectionUtil.isNotEmpty(consultationOrders)) {
            if (StringUtils.isEmpty(doctorScheduling.getReason())) {
                throw new ServiceException("退号原因不可为空");
            }
            for (BusConsultationOrder o : consultationOrders) {
                UpdateWrapper<BusConsultationOrder> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", o.getId());
                // 视频问诊
                updateWrapper.eq("order_type", "1");
                BusConsultationOrder order = new BusConsultationOrder();
                order.setCancelTime(DateUtils.getNowDate());
                //已取消
                order.setVideoStatus("8");
                order.setReason(doctorScheduling.getReason());
                order.setUpdateTime(DateUtils.getNowDate());
                //退款逻辑处理 未支付订单 不退款 已支付订单退款
//                BusConsultationOrder busConsultationOrder = busConsultationOrderMapper.selectById(o);
                if ("7".equals(o.getVideoStatus())) {
                    continue;
                }
                if ("8".equals(o.getVideoStatus())) {
                    continue;
                }
                if ("9".equals(o.getVideoStatus())) {
                    continue;
                }
                order.setVideoStatus("0".equals(o.getVideoStatus()) ? "8" : "9");
                busConsultationOrderMapper.update(order, updateWrapper);
                //结束待办任务
                BusDoctorTdl tdl = new BusDoctorTdl();
                //已办
                tdl.setStatus("2");
                UpdateWrapper<BusDoctorTdl> doctorTdlUpdateWrapper = new UpdateWrapper<>();
                doctorTdlUpdateWrapper.eq("business_id", o.getId());
                doctorTdlUpdateWrapper.eq("doctor_id", o.getDoctorId());
                doctorTdlUpdateWrapper.eq("type", CodeEnum.YES.getCode());
                busDoctorTdlMapper.update(tdl, doctorTdlUpdateWrapper);
                //发送退号事件
                this.publisherReturnEvent(o);

                if (!ConsultationOrderStatus.UNPAID.equals(o.getVideoStatus())) {
                    if (new BigDecimal(o.getAmount()).compareTo(new BigDecimal(0)) == 0) {
                        BusConsultationOrder consultationOrder = new BusConsultationOrder();
                        consultationOrder.setId(o.getId());
                        consultationOrder.setVideoStatus(ConsultationOrderStatus.CANCEL);
                        busConsultationOrderMapper.updateStatus(consultationOrder);
                    } else {
                        Map<String, Object> map = new HashMap<>();
                        map.put("outTradeNo", o.getOrderNo());
                        String amount = o.getAmount();
                        BigDecimal orderAmount = new BigDecimal(amount);
                        map.put("refundAmount", orderAmount);
                        map.put("orderAmount", orderAmount);
                        // 默认从众爱互联网医院商户退钱
                        map.put("mchType", "2");
                        map.put("consultationOrder", o);
                        map.put("appType", doctorScheduling.getAppType());
                        // 自动退款标识
                        map.put("automaticRefund", "1");
                        map.put("hospitalId", o.getHospitalId());
                        if (StringUtils.isNotEmpty(o.getPartnersCode())) {
                            map.put("partnersCode", o.getPartnersCode());
                        }
                        JSONObject refund = new JSONObject();
                        try {
                            // 设置preStatus=1，标识自动退款
                            if (PaysTypeEnum.WECHAT_PAY.getCode().equals(o.getPayWay())) {
                                BusConsultationOrder busConsultationOrder1 = new BusConsultationOrder();
                                busConsultationOrder1.setId(o.getId());
                                busConsultationOrder1.setOrderNo(o.getOrderNo());
                                busConsultationOrder1.setPreStatus("1");
                                // 退款
                                busConsultationOrderMapper.updateStatus(busConsultationOrder1);
                                refund = wxPayService.refund(map);
                            } else {
                                BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
                                busRefundPayDTO.setOrderId(o.getId());
                                busRefundPayDTO.setOrderType(OrderTypeEnum.CONSULTATION.getCode());
                                refund = iTongLianPayService.tongLianRefund(busRefundPayDTO).getData();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(),e);
                        }
                        log.info("退款是否成功={}", refund.toJSONString());
                    }
                }
            }
        }
        return 1;
    }

    @Override
    public void check(BusDoctorScheduling doctorScheduling) {
        //当前提交数据时间段
        BusDoctorSchedulingTime time = doctorScheduling.getSchedulingTime();
        //查询当前医生存储时间段
        BusDoctorSchedulingDto doctorSchedulingDto = new BusDoctorSchedulingDto();
        doctorSchedulingDto.setDoctorId(doctorScheduling.getDoctorId());
        doctorSchedulingDto.setSpecificDate(doctorScheduling.getSchedulingDate());
//        doctorSchedulingDto.setHospitalId(doctorScheduling.getHospitalId());
        List<BusDoctorSchedulingVo> busDoctorScheduling =
                busDoctorSchedulingMapper.selectDoctorSchedulingList(doctorSchedulingDto);
        if (!busDoctorScheduling.isEmpty()) {
            //校验时间是否冲突
            busDoctorScheduling.stream().forEach(d -> {
                List<BusDoctorSchedulingTime> schedulingTimeList = d.getSchedulingTimeList();
                boolean check = checkList(schedulingTimeList, time);
                if (check) {
                    throw new ServiceException("当前时间排班冲突,请重新选择");
                }
            });

        }
    }

    @Override
    public List<BusDoctorSchedulingVo> count(BusDoctorSchedulingDto doctorScheduling) {

        return busDoctorSchedulingMapper.count(doctorScheduling);
    }


    private boolean checkList(List<BusDoctorSchedulingTime> schedulingTimeList, BusDoctorSchedulingTime time) {
        boolean check = false;
        for (BusDoctorSchedulingTime st : schedulingTimeList) {
            boolean overlap = DateUtils.isOverlap(st.getStartTime(), st.getEndTime(), time.getStartTime(),
                    time.getEndTime());
            if (overlap) {
                check = overlap;
                break;
            }
        }
        return check;
    }

    @Override
    public List<BusDoctorSchedulingTime> getDoctorSchedulingList(BusDoctorScheduling doctorScheduling) {
        // 查询医生排版日期
        BusDoctorScheduling busDoctorScheduling = busDoctorSchedulingMapper.selectInfo(doctorScheduling);
        if (StringUtils.isNull(busDoctorScheduling)) {
            throw new ServiceException("医生排班发生变化，请重新预约！");
        }
        // 查询医生排版时间
        List<BusDoctorSchedulingTime> busDoctorSchedulingTimes =
                busDoctorSchedulingTimeMapper.selectDoctorSchedulingList(busDoctorScheduling.getId());
        List<BusDoctorSchedulingTime> list = new ArrayList<>();
        for (BusDoctorSchedulingTime busDoctorSchedulingTime : busDoctorSchedulingTimes) {
            List<BusDoctorSchedulingTime> list1 = splitDate(busDoctorSchedulingTime,
                    busDoctorScheduling.getSchedulingDate());
            list.addAll(list1);
        }
        List<BusDoctorSchedulingTime> resultList = new ArrayList<>();
        // 查询问诊订单
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", doctorScheduling.getHospitalId())
                .eq("doctor_id", doctorScheduling.getDoctorId())
                .eq("department_id", doctorScheduling.getDepartmentId())
                .eq("order_type", "1")
                .inSql("video_status", "0,10,11");
        String dateTime = DateUtils.dateTime(doctorScheduling.getSchedulingDate());
        switch (doctorScheduling.getStatus()) {
            case 0:
                queryWrapper.ge("start_time", dateTime + " 00:00");
                queryWrapper.le("start_time", dateTime + " 11:59");
                break;
            case 1:
                queryWrapper.ge("start_time", dateTime + " 12:00");
                queryWrapper.le("start_time", dateTime + " 17:59");
                break;
            case 2:
                queryWrapper.ge("start_time", dateTime + " 18:00");
                queryWrapper.le("start_time", dateTime + " 23:59");
                break;
            default:
        }
        List<BusConsultationOrder> busConsultationOrders = busConsultationOrderMapper.selectList(queryWrapper);
        for (BusDoctorSchedulingTime busDoctorSchedulingTime : list) {
            if (StringUtils.isNotEmpty(busConsultationOrders)) {
                busConsultationOrders.stream().forEach(c -> {
                    Date startTime = c.getStartTime();
                    Date endTime = c.getEndTime();
                    Date date = DateUtils.pjTime(doctorScheduling.getSchedulingDate(),
                            busDoctorSchedulingTime.getStartTime());
                    boolean b = DateUtils.isAvailableDate(date, startTime, endTime);
                    if (b) {
                        busDoctorSchedulingTime.setSubscribe(1);
                    }
                });
            }
            int time = DateUtils.pdTime(busDoctorSchedulingTime.getStartTime());
            switch (doctorScheduling.getStatus()) {
                case 0:
                    if (time == 1) {
                        resultList.add(busDoctorSchedulingTime);
                    }
                    break;
                case 1:
                    if (time == 2) {
                        resultList.add(busDoctorSchedulingTime);
                    }
                    break;
                case 2:
                    if (time == 3) {
                        resultList.add(busDoctorSchedulingTime);
                    }
                    break;
                default:
            }
        }
        // 获取当前时间
        Date nowDate = DateUtils.getNowDate();
        Calendar instance = Calendar.getInstance();
        instance.setTime(nowDate);
        instance.add(Calendar.HOUR, 1);
        Date time = instance.getTime();
        Iterator<BusDoctorSchedulingTime> iterator = resultList.iterator();
        while (iterator.hasNext()) {
            BusDoctorSchedulingTime next = iterator.next();
            Date startTime = next.getStartTime();
            if (startTime.before(time)) {
                iterator.remove();
            }
        }
        return resultList;
    }

    /**
     * 重新预约
     *
     * @param dto
     * @return
     */
    @Override
    public int reAppointment(ReAppointmentDto dto) {
        log.info("重新预约入参={}", dto.toString());
        // 获取预约时间
        Date startTime = DateUtils.parseDate(dto.getSubscribeTime());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(startTime);
        // 查询医生排版时间
        QueryWrapper<BusDoctorScheduling> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1
                .eq("hospital_id", dto.getHospitalId())
                .eq("doctor_id", dto.getDoctorId())
                .eq("department_id", dto.getDepartmentId())
                .eq("scheduling_date", format);
        BusDoctorScheduling busDoctorScheduling = busDoctorSchedulingMapper.selectOne(queryWrapper1);
        if (StringUtils.isNull(busDoctorScheduling)) {
            throw new ServiceException("医生排班发生变化，请重新预约！");
        }
        // 查询医生排版时间段
        QueryWrapper<BusDoctorSchedulingTime> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2
                .eq("scheduling_id", busDoctorScheduling.getId());
        List<BusDoctorSchedulingTime> list = busDoctorSchedulingTimeMapper.selectList(queryWrapper2);
        String minute = "";
        for (BusDoctorSchedulingTime schedulingTime : list) {
            Date start = DateUtils.pjTime(DateUtils.parseDate(format), schedulingTime.getStartTime());
            Date end = DateUtils.pjTime(DateUtils.parseDate(format), schedulingTime.getEndTime());
            boolean effectiveDate = DateUtils.isEffectiveDate(startTime, start, end);
            if (effectiveDate) {
                minute = schedulingTime.getMinute();
                break;
            }
        }
        // 查询订单信息
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", dto.getOrderNo());
        BusConsultationOrder consultationOrder = busConsultationOrderMapper.selectOne(queryWrapper);
        if (!ConsultationOrderStatus.NUMBER_PASSED.equals(consultationOrder.getVideoStatus())) {
            throw new ServiceException("不能重新预约！");
        }

        // 修改预约时间和状态
        BusConsultationOrder busConsultationOrder = new BusConsultationOrder();
        busConsultationOrder.setStartTime(startTime);
        Calendar nowTime = Calendar.getInstance();
        nowTime.setTime(startTime);
        nowTime.add(Calendar.MINUTE, Integer.parseInt(minute));
        busConsultationOrder.setEndTime(nowTime.getTime());
        busConsultationOrder.setVideoStatus(ConsultationOrderStatus.RESERVED_NOT_ARRIVED);
        int ret = busConsultationOrderMapper.update(busConsultationOrder, queryWrapper);

        if (ret == 1) {
            //添加待办任务
            BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
            busDoctorTdl.setDoctorId(consultationOrder.getDoctorId());
            busDoctorTdl.setHospitalId(consultationOrder.getHospitalId());
            busDoctorTdl.setType(consultationOrder.getOrderType());
            busDoctorTdl.setBusinessId(consultationOrder.getId());
            busDoctorTdl.setStatus(CodeEnum.NO.getCode());
            busDoctorTdl.setRoleType(ImMemberEnum.DOCTOR.getCode());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", consultationOrder.getFamilyName());
            jsonObject.put("age", consultationOrder.getFamilyAge());
            busDoctorTdl.setContent(jsonObject.toJSONString());
            busDoctorTdl.setCreateTime(DateUtils.getNowDate());
            busDoctorTdlMapper.insert(busDoctorTdl);
            // 更新队列信息
            changeQueue(consultationOrder, startTime);
            // 患者已约事件
            ConsultationBookedEvent bookedEvent = new ConsultationBookedEvent();
            bookedEvent.setEventType(BaseConsultationEvent.EventType.BOOKED);
            bookedEvent.setConsultationOrderId(consultationOrder.getId());
            bookedEvent.setOrderNo(consultationOrder.getOrderNo());
            consultationBookedEventProducer.send(bookedEvent);


        }
        return ret;
    }

    /**
     * 更新重新预约视频问诊队列信息
     *
     * @param consultationOrder
     */
    private void changeQueue(BusConsultationOrder consultationOrder, Date startTime) {
        // 移除过号队列
//        Message msg = new Message();
//        msg.setId(consultationOrder.getId());
//        msg.setHospitalId(consultationOrder.getHospitalId());
//        msg.setFireTime(startTime.getTime());
//        redisTemplate.opsForZSet().remove(QueueConstant.VIDEO_CONSULTATION_GUO_HAO, msg);

        // 移除自动完成队列
//        msg.setFireTime(startTime.getTime() + QueueConstant.QUEUE_VIDEO_CONSULTATION_AUTO_COMPLETE_TIME);
//        redisTemplate.opsForZSet().remove(QueueConstant.VIDEO_CONSULTATION_AUTO_COMPLETE, msg);

        // 加入过号队列
        String rescheduleNo = UUID.randomUUID().toString(true);
        Message message = new Message();
        message.setId(consultationOrder.getId());
        message.setHospitalId(consultationOrder.getHospitalId());
        message.setFireTime(startTime.getTime());
        message.setExtend(rescheduleNo);
        videoConsultationGuohaoProducer.sendOnTime(message, message.getFireTime());

        // 加入提前15分钟通知患者队列
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.MINUTE, -15);
        message.setFireTime(calendar.getTime().getTime());
        videoConsultationNoticeProducer.sendOnTime(message, message.getFireTime());

        // 重新预约标志
        String key = CacheConstants.VIDEO_ORDER_RESCHEDULE + consultationOrder.getId();
        redisTemplate.opsForValue().set(key, rescheduleNo, Duration.ofDays(30));
    }

    /**
     * 校验医生是否有排班
     *
     * @param hospitalId
     * @param doctorId
     * @return
     */
    @Override
    public Boolean queryDrSchedule(Long hospitalId, Long doctorId, Long departmentId) {
        // 当前日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = DateUtils.parseDate(sdf.format(DateUtils.getNowDate()));
        // 7天后的日期
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.add(Calendar.DATE, 6);
        Date time = instance.getTime();
        LambdaQueryWrapper<BusDoctorScheduling> queryWrapper = new LambdaQueryWrapper<BusDoctorScheduling>()
                .eq(BusDoctorScheduling::getHospitalId, hospitalId)
                .eq(BusDoctorScheduling::getDoctorId, doctorId)
                .eq(ObjectUtil.isNotEmpty(departmentId), BusDoctorScheduling::getDepartmentId, departmentId)
                .between(BusDoctorScheduling::getSchedulingDate, date, time);
        List<BusDoctorScheduling> busDoctorSchedulings = busDoctorSchedulingMapper.selectList(queryWrapper);
        if (StringUtils.isNotEmpty(busDoctorSchedulings)) {
            return true;
        }
        return false;
    }

    /**
     * 发送退号事件
     *
     * @param order 问诊订单
     */
    private void publisherReturnEvent(BusConsultationOrder order) {
        ConsultationReturnNumberEvent returnNumberEvent = new ConsultationReturnNumberEvent();
        returnNumberEvent.setEventType(BaseConsultationEvent.EventType.RETURN_NUMBER);
        returnNumberEvent.setOrderNo(order.getOrderNo());
        returnNumberEvent.setConsultationOrderId(order.getId());
        Map<String, Object> attachment = new HashMap<>();
        //前端约定，status=5 为跳转患者订单详情
        attachment.put("status", "5");
        returnNumberEvent.setAttachment(attachment);
        //设置原始状态
        returnNumberEvent.setOriginalStatus(order.getVideoStatus());
        //发送退号事件
        publisher.publishEvent(returnNumberEvent);
    }
}

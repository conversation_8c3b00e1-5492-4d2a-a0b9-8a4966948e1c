package com.puree.hospital.app.service;

import com.puree.hospital.app.api.model.dto.BusFreightQueryDTO;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderShop;
import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.domain.dto.CartOrderDTO;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.domain.vo.BusOrderVo;
import com.puree.hospital.app.domain.vo.BusShopOrderVO;
import com.puree.hospital.app.domain.vo.OrderVo;
import com.puree.hospital.app.api.model.dto.BusFreightResultDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface IBusOrderService {
    /**
     * 查询全部订单
     * @param busOrderDto
     * @return
     */
    List<BusOrderVo> selectOrederList(BusOrderDto busOrderDto);

    /**
     * 新增购物车订单
     * @param dto
     * @return
     */
    Map<String, Object> addCartOrder(CartOrderDTO dto);

    /**
     * 查询购物车订单
     * @param code 0药品 1商品
     * @param subOrderId
     * @return
     */
    BusOrder selectOreder(String code, Long subOrderId);

    /**
     * 查询总订单信息
     * @param orderNo
     * @return
     */
    List<BusOrder> selectOrederByNo(String orderNo);

    /**
     * 查询订单金额
     * @param busOrders
     * @return
     */
    BigDecimal selectOrderAmount(List<BusOrder> busOrders);

    /**
     * 查询总订单信息
     * @param code 0药品 1商品
     * @param subOrderId 子订单ID
     * @return
     */
    BusOrder selectOrederInfo(String code, Long subOrderId);

    /**
     * 修改总订单状态
     * @param busOrder
     * @return
     */
    int updateOrderStatus(BusOrder busOrder);

    /**
     * 新增订单
     * @param order
     * @return
     */
    int addOrder(BusOrder order);

    /**
     * 查询订单信息
     * @param dto
     * @return
     */
    List<OrderVo> selectOrderList(DrugsOrderDto dto);

    /**
     * 分组查询订单详细
     * @param orderNo
     * @return
     */
    List<OrderVo> selectOrderInfo(String orderNo);

    /**
     * 查询订单商品详细
     * @param subOrderId
     * @return
     */
    BusShopOrderVO selectGoodsDetail(Long subOrderId);

    /**
     * 查询订单药品/商品列表
     * @param orderNo
     * @return
     */
    List<BusOrder> selectDrugsAndGoodsList(String orderNo);

    /**
     * 查询订单药品/商品详细
     * @param orderNo
     * @return
     */
    BusShopOrderVO selectOrderAndGoodsDetail(String orderNo);

    /**
     * 改变商品库存
     * @param hospitalId
     * @param shopGoodsList
     * @param type 更新库存类型（"reduce 扣减"，"increase 释放"）
     */
    void changeHospitalShopStock(Long hospitalId, List<BusOrderShop> shopGoodsList, String type);

    /**
     * 根据药品订单ID查询总订单信息
     * @param id
     * @return
     */
    List<BusOrder> selectOrderById(Long id);

    /**
     * 查询医保支付明细
     * @param orderType 0问诊 1药品
     * @param orderId 订单ID/订单编号
     * @return
     */
    OrderVo queryOrderInfo(String orderType, String orderId);

    /**
     * 查询待支付的交易订单
     * @return
     */
    List<OrderVo> selectWaitPayOrderList();

    /**
     * 是否已修改订单收件信息，仅能修改一次
     * @param order
     * @return
     */
    int modifyOrderAddr(BusOrder order);


}

package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.domain.BusDrugOrderPackage;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.dto.BusDrugOrderPackageDTO;
import com.puree.hospital.app.api.model.dto.GoodsAndDrugsDTO;
import com.puree.hospital.app.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.app.service.IBusDrugOrderPackageService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/2/8 17:57
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDrugOrderPackageServiceImpl implements IBusDrugOrderPackageService {
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final IBusPrescriptionService busPrescriptionService;

    /**
     * 查询包裹信息
     *
     * @param hospitalId
     * @param prescriptionId
     * @return
     */
    @Override
    public List<BusDrugOrderPackage> selectPackageList(Long hospitalId, Long prescriptionId) {
        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, hospitalId);
        lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, prescriptionId);
        lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
        return busDrugOrderPackageMapper.selectList(lambdaQuery);
    }

    @Override
    public List<BusDrugOrderPackage> selectPackageListByOrderNo(long orderId) {
        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, orderId);
        lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
        lambdaQuery.orderByDesc(BusDrugOrderPackage::getDeliveryTime);
        return busDrugOrderPackageMapper.selectList(lambdaQuery);
    }

    /**
     * 修改包裹信息
     *
     * @param orderPackage
     * @return
     */
    @Override
    public int updateOrderPackage(BusDrugOrderPackage orderPackage) {
        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugOrderPackage::getEnterpriseId, orderPackage.getEnterpriseId());
        lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, orderPackage.getPrescriptionId());
        return busDrugOrderPackageMapper.update(orderPackage, lambdaQuery);
    }

    /**
     * 查询药品/商品发货状态
     *
     * @param dto
     * @return 1未发货 3已发货
     */
    @Override
    public String getDeliveryStatus(BusDrugOrderPackageDTO dto) {
        log.info("查询申请售后商品发货状态入参={}", dto);
        if (Objects.nonNull(dto.getPrescriptionId())) {
            // 查询处方信息
            BusPrescription busPrescription = busPrescriptionService.selectPrescriptionDrugList(dto.getPrescriptionId());
            if (CodeEnum.YES.getCode().equals(busPrescription.getPrescriptionType())) {
                List<BusDrugOrderPackage> orderPackages = new ArrayList<>();
                List<BusPrescriptionDrugs> pdDrugsList = busPrescription.getPdDrugsList();
                for (BusPrescriptionDrugs drugs : pdDrugsList) {
                    if (Objects.nonNull(drugs.getEnterpriseId())) {
                        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, dto.getPrescriptionId());
                        lambdaQuery.eq(BusDrugOrderPackage::getOrderNo, dto.getOrderNo());
                        lambdaQuery.and(wrapper -> wrapper.isNotNull(BusDrugOrderPackage::getDeliveryNo).or().isNotNull(BusDrugOrderPackage::getCode));
                        lambdaQuery.eq(BusDrugOrderPackage::getEnterpriseId, drugs.getEnterpriseId());
                        lambdaQuery.last("limit 1");
                        BusDrugOrderPackage drugOrderPackage = busDrugOrderPackageMapper.selectOne(lambdaQuery);
                        if (Objects.nonNull(drugOrderPackage)) {
                            orderPackages.add(drugOrderPackage);
                        }
                    } else {
                        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, dto.getPrescriptionId());
                        lambdaQuery.eq(BusDrugOrderPackage::getOrderNo, dto.getOrderNo());
                        lambdaQuery.and(wrapper -> wrapper.isNotNull(BusDrugOrderPackage::getDeliveryNo).or().isNotNull(BusDrugOrderPackage::getCode));
                        lambdaQuery.eq(BusDrugOrderPackage::getDrugsGoodsId, drugs.getDrugsId());
                        lambdaQuery.last("limit 1");
                        BusDrugOrderPackage drugOrderPackage = busDrugOrderPackageMapper.selectOne(lambdaQuery);
                        if (Objects.nonNull(drugOrderPackage)) {
                            orderPackages.add(drugOrderPackage);
                        }
                    }
                }
                if (CollUtil.isEmpty(orderPackages)) {
                    return "1";
                } else if (orderPackages.size() == pdDrugsList.size()) {
                    return "3";
                } else {
                    throw new ServiceException("该处方未全部发货，暂不支持售后");
                }
            } else {
                LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, dto.getPrescriptionId());
                lambdaQuery.eq(BusDrugOrderPackage::getOrderNo, dto.getOrderNo());
                lambdaQuery.and(wrapper -> wrapper.isNotNull(BusDrugOrderPackage::getDeliveryNo).or().isNotNull(BusDrugOrderPackage::getCode));
                lambdaQuery.last(" limit 1");
                BusDrugOrderPackage drugOrderPackage = busDrugOrderPackageMapper.selectOne(lambdaQuery);
                if (Objects.nonNull(drugOrderPackage)) {
                    return "3";
                } else {
                    return "1";
                }
            }
        } else {
            if (CollUtil.isEmpty(dto.getIdList())) {
                throw new ServiceException("参数缺失");
            } else {
                for (GoodsAndDrugsDTO drugsDTO : dto.getIdList()) {
                    LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                    lambdaQuery.eq(BusDrugOrderPackage::getOrderNo, dto.getOrderNo());
                    lambdaQuery.and(wrapper -> wrapper.isNotNull(BusDrugOrderPackage::getDeliveryNo).or().isNotNull(BusDrugOrderPackage::getCode));
                    lambdaQuery.eq(BusDrugOrderPackage::getPackageType, drugsDTO.getBusinessType());
                    lambdaQuery.eq(BusDrugOrderPackage::getDrugsGoodsId, drugsDTO.getBusinessId());
                    lambdaQuery.last("limit 1");
                    BusDrugOrderPackage packages = busDrugOrderPackageMapper.selectOne(lambdaQuery);
                    if (Objects.nonNull(packages)) {
                        return "3";
                    } else {
                        return "1";
                    }
                }
                return null;
            }
        }
    }
}

package com.puree.hospital.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusInvoiceHeader;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.dto.InvoiceHeaderDTO;
import com.puree.hospital.app.mapper.BusInvoiceHeaderMapper;
import com.puree.hospital.app.mapper.BusPatientMapper;
import com.puree.hospital.common.core.constant.InvoiceConstants;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: BusInvoiceHeaderService
 * @Date 2023/10/27 15:08
 * <AUTHOR> jian
 * @Description: invoice header service
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusInvoiceHeaderService {

    private Logger logger = LoggerFactory.getLogger(BusInvoiceHeaderService.class);

    private final BusInvoiceHeaderMapper invoiceHeaderMapper ;
    private final BusPatientMapper patientMapper ;



    @Transactional(rollbackFor = Exception.class)
    public Long addInvoiceHeader(InvoiceHeaderDTO req){
        if (!Arrays.asList(0,1).contains(req.getHeaderType())) {
            throw new ServiceException(InvoiceConstants.WRONG_INVOICE_HEADER_TYPE);
        }
        if (!Arrays.asList(0,1).contains(req.getIsDefault())) {
            throw new ServiceException(InvoiceConstants.WRONG_INVOICE_HEADER_DEFAULT);
        }
        if (!Arrays.asList(0,1).contains(req.getIsShow())) {
            throw new ServiceException(InvoiceConstants.WRONG_INVOICE_HEADER_SHOW);
        }
        if (1==req.getIsShow()) {
            BusInvoiceHeader tmpHeader = new BusInvoiceHeader();
            tmpHeader.setIsShow(1);
            tmpHeader.setHospitalId(req.getHospitalId());
            tmpHeader.setPatientId(req.getPatientId());
            tmpHeader.setHeaderType(req.getHeaderType());
            tmpHeader.setInvoiceHeader(req.getInvoiceHeader());
            int i = invoiceHeaderMapper.countDuplicateHeader(tmpHeader);
            if (i>0) {
                throw new ServiceException(InvoiceConstants.DUPLICATE_INVOICE_HEADER);
            }
        }

        BusInvoiceHeader invoiceHeader = new BusInvoiceHeader();
        BeanUtils.copyProperties(req, invoiceHeader);
        Date now = new Date();
        invoiceHeader.setCreateTime(now);
        invoiceHeader.setUpdateTime(now);
        invoiceHeader.setIsDeleted(0);
        if (1==req.getIsDefault()) {
            BusInvoiceHeader tmpHeader = new BusInvoiceHeader();
            tmpHeader.setIsDefault(0);
            tmpHeader.setHospitalId(req.getHospitalId());
            tmpHeader.setPatientId(req.getPatientId());
            invoiceHeaderMapper.updateDefaultInvoiceHeader(tmpHeader) ;
        }
        if (StringUtils.isBlank(invoiceHeader.getPhone())) {
            BusPatient busPatient = patientMapper.selectById(invoiceHeader.getPatientId());
            invoiceHeader.setPhone(busPatient.getPhoneNumber());
        }
        invoiceHeaderMapper.insertInvoiceHeader(invoiceHeader) ;
        return invoiceHeader.getId() ;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteInvoiceHeader(Long id){
        BusInvoiceHeader invoiceHeader = invoiceHeaderMapper.selectById(id);
        invoiceHeader.setIsDeleted(1);
        invoiceHeaderMapper.updateById(invoiceHeader) ;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateInvoiceHeader(InvoiceHeaderDTO req){
        if (null==req.getId()) {
            throw new ServiceException("no id");
        }
        if (!Arrays.asList(0,1).contains(req.getHeaderType())) {
            throw new ServiceException(InvoiceConstants.WRONG_INVOICE_HEADER_TYPE);
        }
        if (!Arrays.asList(0,1).contains(req.getIsDefault())) {
            throw new ServiceException(InvoiceConstants.WRONG_INVOICE_HEADER_DEFAULT);
        }
        if (!Arrays.asList(0,1).contains(req.getIsShow())) {
            throw new ServiceException(InvoiceConstants.WRONG_INVOICE_HEADER_SHOW);
        }
        if (1==req.getIsShow()) {
            BusInvoiceHeader tmpHeader = new BusInvoiceHeader();
            tmpHeader.setIsShow(1);
            tmpHeader.setHospitalId(req.getHospitalId());
            tmpHeader.setPatientId(req.getPatientId());
            tmpHeader.setHeaderType(req.getHeaderType());
            tmpHeader.setInvoiceHeader(req.getInvoiceHeader());
            tmpHeader.setId(req.getId()) ;
            int i = invoiceHeaderMapper.countDuplicateHeader(tmpHeader);
            if (i>0) {
                throw new ServiceException(InvoiceConstants.DUPLICATE_INVOICE_HEADER);
            }
        }
        BusInvoiceHeader invoiceHeader = new BusInvoiceHeader();
        BeanUtils.copyProperties(req, invoiceHeader);
        invoiceHeader.setUpdateTime(new Date());
        if (1==req.getIsDefault()) {
            BusInvoiceHeader tmpHeader = new BusInvoiceHeader();
            tmpHeader.setId(req.getId()) ;
            tmpHeader.setIsDefault(0);
            tmpHeader.setHospitalId(req.getHospitalId());
            tmpHeader.setPatientId(req.getPatientId());
            invoiceHeaderMapper.updateDefaultInvoiceHeader(tmpHeader) ;
        }
        if (StringUtils.isBlank(invoiceHeader.getPhone())) {
            BusPatient busPatient = patientMapper.selectById(invoiceHeader.getPatientId());
            invoiceHeader.setPhone(busPatient.getPhoneNumber());
        }
        invoiceHeaderMapper.updateById(invoiceHeader) ;
    }

    public List<BusInvoiceHeader> listInvoiceHeader(Long hospitalId, Long patientId){
        return invoiceHeaderMapper.selectList(new LambdaQueryWrapper<BusInvoiceHeader>()
                .eq(BusInvoiceHeader::getHospitalId, hospitalId)
                .eq(BusInvoiceHeader::getPatientId, patientId)
                .eq(BusInvoiceHeader::getIsDeleted, 0)
                .eq(BusInvoiceHeader::getIsShow, 1)
        );
    }

    public BusInvoiceHeader findInvoiceHeaderDetail(Long id){
        return invoiceHeaderMapper.selectById(id) ;
    }







}

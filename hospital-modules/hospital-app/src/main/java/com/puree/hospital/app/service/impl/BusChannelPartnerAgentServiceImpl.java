package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.domain.BusBankcard;
import com.puree.hospital.app.domain.BusChannelPartner;
import com.puree.hospital.app.domain.BusChannelPartnerAgent;
import com.puree.hospital.app.domain.BusChannelPartnerAgentBankcard;
import com.puree.hospital.app.domain.BusChannelPatientAgentRelation;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusFiveServicePackOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusPatientHospital;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.vo.BusChannelOrderVO;
import com.puree.hospital.app.domain.vo.BusChannelPartnerAgentVO;
import com.puree.hospital.app.domain.vo.BusChannelPartnerVO;
import com.puree.hospital.app.domain.vo.BusHomeCount;
import com.puree.hospital.app.helper.ChannelPartnerHelper;
import com.puree.hospital.app.mapper.BusBankcardMapper;
import com.puree.hospital.app.mapper.BusChannelOrderMapper;
import com.puree.hospital.app.mapper.BusChannelPartnerAgentBankcardMapper;
import com.puree.hospital.app.mapper.BusChannelPartnerAgentMapper;
import com.puree.hospital.app.mapper.BusChannelPatientAgentRelationMapper;
import com.puree.hospital.app.mapper.BusFiveServicePackOrderMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.service.IBusChannelPartnerAgentService;
import com.puree.hospital.app.service.IBusChannelPartnerService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.common.api.enums.DelFlagEnum;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.LoginTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.IdUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.tool.api.RemoteSmsNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 经纪人接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusChannelPartnerAgentServiceImpl implements IBusChannelPartnerAgentService {

    private final BusChannelPartnerAgentMapper busChannelPartnerAgentMapper;
    private final BusChannelOrderMapper busChannelOrderMapper;
    private final IBusChannelPartnerService busChannelPartnerService;
    private final IBusPatientHospitalService busPatientHospitalService;
    private final IBusConsultationOrderService busConsultationOrderService;
    private final BusFiveServicePackOrderMapper busFiveServicePackOrderMapper;
    private final BusOrderMapper busOrderMapper;
    private final BusChannelPartnerAgentBankcardMapper busChannelPartnerAgentBankcardMapper;
    private final BusBankcardMapper busBankcardMapper;

    @Lazy
    @Resource
    private ChannelPartnerHelper channelPartnerHelper;

    @Resource
    private BusChannelPatientAgentRelationMapper busChannelPatientAgentRelationMapper;

    /**
     * 过期时间30天
     */
    private static final long EXPIRE_TIME = 30 * 24 * 60 * 60L;
    private final RedisService redisService;
    private final RemoteSmsNotificationService remoteSmsNotificationService;
    private final IBusPatientService busPatientService;

    @Override
    public BusChannelPartnerAgentVO getById(Long id) {
        return busChannelPartnerAgentMapper.getById(id);
    }


    @Override
    public List<BusChannelPartnerAgentVO> listByChannelPartner(Long channelPartnerId) {
        return busChannelPartnerAgentMapper.listByChannelPartner(channelPartnerId);
    }


    @Override
    @Deprecated
    public BusChannelPartnerAgentVO getByPhoneNumber(String phoneNumber, Long hospitalId) {
        LambdaQueryWrapper<BusChannelPartnerAgent> query = new LambdaQueryWrapper<>();
        query.eq(BusChannelPartnerAgent::getPhonenumber, phoneNumber)
                .eq(BusChannelPartnerAgent::getHospitalId, hospitalId)
                .eq(BusChannelPartnerAgent::getDelFlag, YesNoEnum.NO.getCode());
        BusChannelPartnerAgent agent = busChannelPartnerAgentMapper.selectOne(query);
        if (Objects.isNull(agent)) {
            return null;
        }
        BusChannelPartnerAgentVO vo = new BusChannelPartnerAgentVO();
        BeanUtils.copyProperties(agent, vo);
        BusChannelPartner channelPartner = busChannelPartnerService.getById(agent.getChannelPartnerId());
        if (Objects.nonNull(channelPartner)) {
            vo.setChannelPartnerName(channelPartner.getFullName());
            vo.setHospitalId(channelPartner.getHospitalId());
            vo.setIdentity(AppRoleEnum.AGENT.getCode());
        }
        return vo;
    }

    @Override
    public int updateChannelPartnerAgent(BusChannelPartnerAgent busChannelPartnerAgent) {
        // TODO - 建议走领域模型 - 现在不确定来源多处，需要修改不
        int result = busChannelPartnerAgentMapper.updateById(busChannelPartnerAgent);
        channelPartnerHelper.deletePatientRelationCache(busChannelPartnerAgent.getPhonenumber(), busChannelPartnerAgent.getHospitalId());
        return result;
    }

    @Override
    public int modifyStatus(BusChannelPartnerAgent agent) {
        if (Objects.isNull(agent)) {
            return 0;
        }
        BusChannelPartnerAgent info = busChannelPartnerAgentMapper.selectById(agent.getId());
        if (Objects.nonNull(info)) {
            redisService.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + info.getToken());
            channelPartnerHelper.deletePatientRelationCache(info.getPhonenumber(), info.getHospitalId());
        }
//        return busChannelPartnerAgentMapper.updateById(agent);
        return 0;
    }

    @Override
    public int unbind(BusChannelPartnerAgent busChannelPartnerAgent) {
        BusChannelPartnerAgent info = busChannelPartnerAgentMapper.selectById(busChannelPartnerAgent.getId());
        if (Objects.nonNull(info)) {
            redisService.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + info.getToken());
            channelPartnerHelper.deletePatientRelationCache(info.getPhonenumber(), info.getHospitalId());
        }
//        return busChannelPartnerAgentMapper.updateById(busChannelPartnerAgent);
        return 0;
    }

    @Override
    public List<BusChannelOrderVO> queryOrderList(Long agentId) {
        return busChannelOrderMapper.queryOrderList(agentId);
    }

    /**
     * 查询经纪人订单列表
     *
     * @param busChannelOrderVOList 经纪人订单列表
     * @return 订单列表
     */
    @Override
    public List<List<BusChannelOrderVO>> queryOrderListByGroup(List<BusChannelOrderVO> busChannelOrderVOList) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        busChannelOrderVOList.forEach(l -> {
            Date date = l.getOrderTime();
            String format = simpleDateFormat.format(date);
            String[] split = format.split("-");
            l.setDateStr(split[0] + "年" + split[1] + "月");
            l.setOrderTime2(l.getOrderTime());
        });
        Map<String, List<BusChannelOrderVO>> collect = busChannelOrderVOList.stream()
                .collect(Collectors.groupingBy(BusChannelOrderVO::getDateStr, () -> new TreeMap<>((Comparator.reverseOrder())),
                        Collectors.toList()));
        Set<String> strings = collect.keySet();
        List<List<BusChannelOrderVO>> result = new ArrayList<>();
        //开始根据键找值
        for (String key : strings) {
            List<BusChannelOrderVO> list = collect.get(key);
            list.sort(Comparator.comparing(BusChannelOrderVO::getOrderTime).reversed());
            result.add(list);
        }
        return result;
    }

    @Override
    public BusChannelPartnerVO agentLoginCheck(String phoneNumber, Long channelPartnerId, Long hospitalId) {
        log.info("经纪人登录日志phoneNumber = {}，channelPartnerId = {}, hospitalId = {}", phoneNumber, channelPartnerId, hospitalId);
        BusChannelPartnerVO result;
        /*查询合作渠道信息*/
        BusChannelPartner busChannelPartner = busChannelPartnerService.getByPhoneNumber(phoneNumber, hospitalId);
        /*根据手机号查询经纪人信息*/
        BusChannelPartnerAgentVO busChannelPartnerAgent = this.getByPhoneNumber(phoneNumber, hospitalId);
        /*如果是通过邀请进行登录,则必然是经纪人*/
        if (Objects.nonNull(channelPartnerId)) {
            /*如果手机号在合作渠道表和经纪人表中不存在*/
            if (Objects.isNull(busChannelPartner) && Objects.isNull(busChannelPartnerAgent)) {
                log.info("经纪人登录phoneNumber = {}，channelPartnerId = {}, hospitalId = {}", phoneNumber, channelPartnerId, hospitalId);
                result = new BusChannelPartnerVO();
                BusChannelPartnerAgent bcp = new BusChannelPartnerAgent();
                bcp.setPhonenumber(phoneNumber);
                bcp.setChannelPartnerId(channelPartnerId);
                bcp.setCreateTime(new Date());
                bcp.setHospitalId(hospitalId);
                // TODO 这里插入数据会造成脏数据的出现，医伴经纪人下线后需要移除
//                this.insertChannelPartnerAgent(bcp);
                result.setIdentity(AppRoleEnum.AGENT.getCode());
                result.setAgent(this.getByPhoneNumber(phoneNumber, hospitalId));
            } else {
                log.info("合作渠道登录日志phoneNumber = {}，channelPartnerId = {}, hospitalId = {}", phoneNumber, channelPartnerId, hospitalId);
                result = loginCheck(busChannelPartner, busChannelPartnerAgent);
            }
            /*如果是自行登录*/
        } else {
            log.info("合作渠道或合伙人登录日志phoneNumber = {}，hospitalId = {}", phoneNumber, hospitalId);
            result = loginCheck(busChannelPartner, busChannelPartnerAgent);
        }
        return result;
    }

    @Override
    public SMSLoginUser getSetTokenInfo(BusChannelPartnerVO busChannelPartnerVO) {
        SMSLoginUser smsLoginUser = new SMSLoginUser();
        smsLoginUser.setHospitalId(busChannelPartnerVO.getHospitalId());
        smsLoginUser.setLoginType(LoginTypeEnum.AIDMED_APPLET.getCode());
        log.info("当前医伴经纪人登录信息:{}", busChannelPartnerVO);
        if (AppRoleEnum.AGENT.getCode().equals(busChannelPartnerVO.getIdentity())) {
            smsLoginUser.setUserid(busChannelPartnerVO.getAgent().getId());
            smsLoginUser.setUsername(busChannelPartnerVO.getAgent().getFullName());
            smsLoginUser.setIdentity(busChannelPartnerVO.getIdentity());
            String token = IdUtils.fastSimpleUUID();
            if (ObjectUtil.isNotNull(busChannelPartnerVO.getAgent().getToken()) && StringUtils.isNotEmpty(busChannelPartnerVO.getAgent().getToken())) {
                redisService.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + busChannelPartnerVO.getAgent().getToken());
            }
            BusChannelPartnerAgent busChannelPartnerAgent = new BusChannelPartnerAgent();
            busChannelPartnerAgent.setToken(token);
            busChannelPartnerAgent.setId(busChannelPartnerVO.getAgent().getId());
            this.updateChannelPartnerAgent(busChannelPartnerAgent);

            redisService.setCacheObject(CacheConstants.LOGIN_TOKEN_KEY + token, smsLoginUser, EXPIRE_TIME,
                    TimeUnit.SECONDS);
            smsLoginUser.setToken(token);
            log.info("经纪人登录信息：{}", busChannelPartnerVO);
        } else if (AppRoleEnum.CHANNEL.getCode().equals(busChannelPartnerVO.getIdentity())) {
            smsLoginUser.setUserid(busChannelPartnerVO.getId());
            smsLoginUser.setUsername(busChannelPartnerVO.getFullName());
            smsLoginUser.setIdentity(busChannelPartnerVO.getIdentity());
            String token = IdUtils.fastSimpleUUID();
            if (StrUtil.isNotEmpty(busChannelPartnerVO.getToken())) {
                redisService.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + busChannelPartnerVO.getToken());
            }
            BusChannelPartner busChannelPartner = new BusChannelPartner();
            busChannelPartner.setToken(token);
            busChannelPartner.setId(busChannelPartnerVO.getId());
            busChannelPartnerService.updateChannelPartner(busChannelPartner);
            redisService.setCacheObject(CacheConstants.LOGIN_TOKEN_KEY + token, smsLoginUser, EXPIRE_TIME,
                    TimeUnit.SECONDS);
            smsLoginUser.setToken(token);
            log.info("合作渠道登录信息：{}", busChannelPartnerVO);
        }
        return smsLoginUser;
    }

    @Override
    public void checkPhoneSms(String phoneNumbers, String code) {
        AjaxResult result = remoteSmsNotificationService.checkVerifyCode(phoneNumbers, code);
        if (!result.isSuccess()) {
            throw new ServiceException(result.getMsg());
        }
    }

    /**
     * 正常登录逻辑判断
     */
    @Override
    public BusChannelPartnerVO loginCheck(BusChannelPartner busChannelPartner, BusChannelPartnerAgentVO busChannelPartnerAgent) {
        BusChannelPartnerVO result;
        /*如果手机号在合作渠表不存在*/
        if (ObjectUtil.isNull(busChannelPartner)) {
            result = new BusChannelPartnerVO();
            /*如果数据库中没有患者信息就进行插入操作*/
            if (ObjectUtil.isNotNull(busChannelPartnerAgent)) {
                result.setIdentity(AppRoleEnum.AGENT.getCode());
                result.setAgent(busChannelPartnerAgent);
            } else {
                throw new ServiceException("该手机号未注册！");
            }
            if (YesNoEnum.NO.getCode().equals(busChannelPartnerAgent.getStatus())) {
                throw new ServiceException("该账号已经被禁用！");
            }
        } else {
            if (YesNoEnum.NO.getCode().equals(busChannelPartner.getStatus())) {
                throw new ServiceException("该账号已经被禁用！");
            }
            result = OrikaUtils.convert(busChannelPartner, BusChannelPartnerVO.class);
            result.setIdentity(AppRoleEnum.CHANNEL.getCode());
        }
        return result;
    }

    @Override
    public BusHomeCount homeCount(Long agentId) {
        //统计当日经纪人邀请人数？实际是统计昨日一整天
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startOfYesterday = yesterday.atStartOfDay(); // 昨天 00:00:00
        LocalDateTime startOfToday = yesterday.atTime(23, 59, 59); // 昨天 23:59:59（即昨天结束）
        Long inviteCount = getInviteCount(agentId,
                Date.from(startOfYesterday.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(startOfToday.atZone(ZoneId.systemDefault()).toInstant()));

        //经纪人邀请的所有人
        List<BusChannelPatientAgentRelation> list = getBusPatientHospitals(agentId);

        if (CollUtil.isEmpty(list)) {
            //经纪人无邀请人
            BusHomeCount busHomeCount = new BusHomeCount();
            busHomeCount.setInviteCount(inviteCount);
            return busHomeCount;
        }
        Long hospitalId = list.get(0).getHospitalId();

        //获取患者 ID
        List<Long> patientIdList = list.stream().map(BusChannelPatientAgentRelation::getPatientId).collect(Collectors.toList());

        //图文和视频订单
        List<BusConsultationOrder> consultationOrderList = getBusConsultationOrders(patientIdList, hospitalId);

        //订单数量
        int consultationCount = 0;
        //累加
        BigDecimal consultationBigDecimal = BigDecimal.ZERO;
        if (!CollUtil.isEmpty(consultationOrderList)) {
            consultationCount = consultationOrderList.size();
            //累加
            for (BusConsultationOrder item : consultationOrderList) {
                consultationBigDecimal = consultationBigDecimal.add(new BigDecimal(item.getAmount()));
            }
        }

        //服务包订单
        List<BusFiveServicePackOrder> busFiveServicePackOrders = getBusFiveServicePackOrders(patientIdList, hospitalId);

        //订单数量
        int busFiveCount = 0;
        //累加
        BigDecimal busFiveBigDecimal = new BigDecimal("0");
        if (!CollUtil.isEmpty(busFiveServicePackOrders)) {
            busFiveCount = busFiveServicePackOrders.size();
            //累加
            for (BusFiveServicePackOrder item : busFiveServicePackOrders) {
                busFiveBigDecimal = busFiveBigDecimal.add(new BigDecimal(item.getAmount()));
            }
        }

        //交易订单
        List<BusOrder> busOrders = getBusOrders(patientIdList, hospitalId);


        //订单数量
        int busOrderCount = 0;
        //累加
        BigDecimal busOrderBigDecimal = new BigDecimal("0");
        if (!CollUtil.isEmpty(busFiveServicePackOrders)) {
            busOrderCount = busOrders.size();
            //累加
            for (BusOrder item : busOrders) {
                busOrderBigDecimal = busOrderBigDecimal.add(new BigDecimal(item.getRelPrice().toString()));
            }
        }


        //当日订单总金额
        BigDecimal orderBigDecimal = busOrderBigDecimal.add(consultationBigDecimal).add(busFiveBigDecimal);
        if (orderBigDecimal.compareTo(BigDecimal.ZERO) == 0) {
            orderBigDecimal = new BigDecimal("0");
        }

        //当日订单数量
        Integer orderCount = busOrderCount + busFiveCount + consultationCount;

        BusHomeCount busHomeCount = new BusHomeCount();
        busHomeCount.setConsultationCount(consultationCount);
        busHomeCount.setBusFiveCount(busFiveCount);
        busHomeCount.setBusOrderCount(busOrderCount);
        busHomeCount.setBusOrderBigDecimal(busOrderBigDecimal.toString());
        busHomeCount.setConsultationBigDecimal(consultationBigDecimal.toString());
        busHomeCount.setBusFiveBigDecimal(busFiveBigDecimal.toString());
        busHomeCount.setInviteCount(inviteCount);
        busHomeCount.setOrderBigDecimal(orderBigDecimal.toString());
        busHomeCount.setOrderCount(orderCount);
        busHomeCount.setOrderBigDecimal(orderBigDecimal.toString());

        return busHomeCount;
    }

    @Override
    public Long getInviteCount(Long agentId, Date startTime, Date endTime) {
        LambdaQueryWrapper<BusChannelPatientAgentRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BusChannelPatientAgentRelation::getAgentId, agentId)
                .isNotNull(BusChannelPatientAgentRelation::getHospitalId)
                .isNotNull(BusChannelPatientAgentRelation::getPatientId)
                .ge(Objects.nonNull(startTime), BusChannelPatientAgentRelation::getCreateTime, startTime)
                .le(Objects.nonNull(endTime), BusChannelPatientAgentRelation::getCreateTime, endTime);
        //当日邀请人数
        return busChannelPatientAgentRelationMapper.selectCount(queryWrapper);

    }

    @Override
    public BusChannelPartnerAgentVO getUserAgentDetail() {
        SMSLoginUser user;
        try {
            user = busPatientService.queryPatientInfo();
        } catch (ServiceException e) {
            log.warn("获取患者信息失败");
            return null;
        }
        if (ObjectUtil.isNull(user)) {
            return null;
        }
        LambdaQueryWrapper<BusChannelPatientAgentRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BusChannelPatientAgentRelation::getPatientId, user.getUserid())
                        .eq(BusChannelPatientAgentRelation::getHospitalId, user.getHospitalId())
                        .orderByDesc(BusChannelPatientAgentRelation::getCreateTime)
                        .last("limit 1");
        BusChannelPatientAgentRelation busChannelPatientAgentRelation = busChannelPatientAgentRelationMapper.selectOne(queryWrapper);
        if (Objects.isNull(busChannelPatientAgentRelation)) {
            return null;
        }
        BusChannelPartnerAgentVO busChannelPartnerAgentVO = new BusChannelPartnerAgentVO();
        BusChannelPartnerAgent busChannelPartnerAgent = busChannelPartnerAgentMapper.selectById(busChannelPatientAgentRelation.getAgentId());
        if (busChannelPartnerAgent == null) {
            return null;
        }
        BeanUtils.copyProperties(busChannelPartnerAgent, busChannelPartnerAgentVO);
        return busChannelPartnerAgentVO;
    }

    @Override
    public Long getAgentByOpenId(String openid) {
        BusPatientHospital ph = busPatientHospitalService.getOne(new LambdaQueryWrapper<BusPatientHospital>()
                        .eq(BusPatientHospital::getOpenid, openid)
                        .select(BusPatientHospital::getAgentId));
        if (null == ph || null == ph.getAgentId()){
            return null;
        }
        return ph.getAgentId();
    }

    @Override
    public Boolean saveBankCard(BusChannelPartnerAgentBankcard bankcard) {
        if (Objects.isNull(bankcard) || Objects.isNull(bankcard.getPartnerAgentId())) {
            return false;
        }
        BusChannelPartnerAgentBankcard dbBankCard = this.getBankCard(bankcard.getPartnerAgentId());
        if (dbBankCard == null) {
            bankcard.setCreateTime(new Date());
            busChannelPartnerAgentBankcardMapper.insert(bankcard);
        } else {
            bankcard.setId(dbBankCard.getId());
            bankcard.setUpdateTime(new Date());
            busChannelPartnerAgentBankcardMapper.updateById(bankcard);
        }
        return true;
    }

    @Override
    public BusChannelPartnerAgentBankcard getBankCard(Long partnerAgentId) {
        LambdaQueryWrapper<BusChannelPartnerAgentBankcard> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BusChannelPartnerAgentBankcard::getPartnerAgentId, partnerAgentId);
        queryWrapper.last("limit 1");
        return busChannelPartnerAgentBankcardMapper.selectOne(queryWrapper);
    }

    @Override
    public List<BusBankcard> getSupportBankCardList() {
        return busBankcardMapper.selectList(Wrappers.emptyWrapper());
    }


    @Override
    public BusChannelPartnerAgentVO getByIdentityId(Long channelIdentityId, Long hospitalId) {
        LambdaQueryWrapper<BusChannelPartnerAgent> query = new LambdaQueryWrapper<>();
        query.eq(BusChannelPartnerAgent::getChannelIdentityId, channelIdentityId)
                .eq(BusChannelPartnerAgent::getHospitalId, hospitalId)
                .eq(BusChannelPartnerAgent::getDelFlag, DelFlagEnum.NORMAL.getCode())
                .orderByDesc(BusChannelPartnerAgent::getCreateTime)
                .last("limit 1");
        BusChannelPartnerAgent agent = busChannelPartnerAgentMapper.selectOne(query);
        if (Objects.isNull(agent)) {
            return null;
        }
        BusChannelPartnerAgentVO vo = new BusChannelPartnerAgentVO();
        BeanUtils.copyProperties(agent, vo);
        BusChannelPartner channelPartner = busChannelPartnerService.getById(agent.getChannelPartnerId());
        if (Objects.nonNull(channelPartner)) {
            vo.setChannelPartnerName(channelPartner.getFullName());
            vo.setHospitalId(channelPartner.getHospitalId());
            vo.setIdentity(AppRoleEnum.AGENT.getCode());
        }
        return vo;
    }



    private List<BusChannelPatientAgentRelation> getBusPatientHospitals(Long agentId) {
        LambdaQueryWrapper<BusChannelPatientAgentRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(BusChannelPatientAgentRelation::getPatientId, BusChannelPatientAgentRelation::getHospitalId)
                .isNotNull(BusChannelPatientAgentRelation::getHospitalId)
                .isNotNull(BusChannelPatientAgentRelation::getPatientId)
                .eq(BusChannelPatientAgentRelation::getAgentId, agentId);
        return busChannelPatientAgentRelationMapper.selectList(queryWrapper);
    }

    private List<BusFiveServicePackOrder> getBusFiveServicePackOrders(List<Long> patienIdList, Long hospitalId) {
        LambdaQueryWrapper<BusFiveServicePackOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(BusFiveServicePackOrder::getAmount)
                .in(BusFiveServicePackOrder::getPatientId, patienIdList)
                .eq(BusFiveServicePackOrder::getHospitalId, hospitalId)
                .ge(BusFiveServicePackOrder::getPaymentTime, DateUtil.beginOfDay(DateUtil.yesterday()))
                .lt(BusFiveServicePackOrder::getPaymentTime, DateUtil.beginOfDay(DateUtil.date()));
        return busFiveServicePackOrderMapper.selectList(queryWrapper);
    }

    private List<BusConsultationOrder> getBusConsultationOrders(List<Long> patienIdList, Long hospitalId) {
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(BusConsultationOrder::getAmount)
                .in(BusConsultationOrder::getPatientId, patienIdList)
                .eq(BusConsultationOrder::getHospitalId, hospitalId)
                .ge(BusConsultationOrder::getCompleteTime, DateUtil.beginOfDay(DateUtil.yesterday()))
                .lt(BusConsultationOrder::getCompleteTime, DateUtil.beginOfDay(DateUtil.date()));
        return busConsultationOrderService.list(queryWrapper);
    }

    private List<BusOrder> getBusOrders(List<Long> patientIdList, Long hospitalId) {
        LambdaQueryWrapper<BusOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .select(BusOrder::getRelPrice)
                .in(BusOrder::getPatientId, patientIdList)
                .eq(BusOrder::getHospitalId, hospitalId)
                .ge(BusOrder::getCompleteTime, DateUtil.beginOfDay(DateUtil.yesterday()))
                .lt(BusOrder::getCompleteTime, DateUtil.beginOfDay(DateUtil.date()))
                .groupBy(BusOrder::getOrderNo);
        return busOrderMapper.selectList(queryWrapper);
    }
}

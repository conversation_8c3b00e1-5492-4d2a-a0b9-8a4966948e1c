package com.puree.hospital.app.controller;

import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationPackage;
import com.puree.hospital.app.service.IBusConsultationPackageService;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 问诊包相关控制器
 */
@RestController
@RequestMapping("/consultation/package")
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class BusConsultationPackageController extends BaseController {
    private final IBusConsultationPackageService consultationPackageService;

    private final PureeRabbitProducer pureeRabbitProducer;

    /**
     * 赠送问诊包
     *
     * @param consultationOrder
     * @return
     */
    @PostMapping("/give")
    @Log(title = "赠送问诊包", businessType = BusinessType.INSERT)
    public AjaxResult give(@RequestBody BusConsultationOrder consultationOrder) {
        consultationPackageService.give(consultationOrder,true);
        RegulatoryCollectEvent event = new RegulatoryCollectEvent(consultationOrder.getHospitalId(), RegulatoryEventTypeEnum.CONSULTATION_ORDER, consultationOrder.getId() + "");
        pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        return toAjax(1);
    }

    /**
     * 查询赠送问诊包
     *
     * @param consultationPackage
     * @return
     */
    @Log(title = "查询赠送问诊包")
    @GetMapping("/list")
    public AjaxResult list(BusConsultationPackage consultationPackage) {
        return AjaxResult.success(consultationPackageService.selectList(consultationPackage));
    }

    /**
     * 剩余问诊次数
     *
     * @param consultationPackage
     * @return
     */
    @GetMapping("/remainingTimes")
    @Log(title = "剩余问诊次数", businessType = BusinessType.OTHER)
    public AjaxResult remainingTimes(BusConsultationPackage consultationPackage) {
        int remainingTimes = consultationPackageService.remainingTimes(consultationPackage);
        return AjaxResult.success(remainingTimes);
    }

    /**
     * 购买问诊包
     *
     * @param consultationOrder
     * @return
     */
    @PostMapping("purchase")
    @Log(title = "购买问诊包", businessType = BusinessType.INSERT)
    public AjaxResult purchase(@RequestBody BusConsultationOrder consultationOrder, HttpServletRequest request) {
        String partnersCode = request.getHeader("partnersCode");
        if (StringUtils.isNotEmpty(partnersCode)) {
            consultationOrder.setPartnersCode(partnersCode);
        }
        long consultationOrderId = consultationPackageService.insert(consultationOrder);
        RegulatoryCollectEvent event = new RegulatoryCollectEvent(consultationOrder.getHospitalId(), RegulatoryEventTypeEnum.CONSULTATION_ORDER, consultationOrderId + "");
        pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        return AjaxResult.success(consultationOrderId);
    }

    /**
     * 问诊包复诊
     *
     * @param consultationOrder
     * @return
     */
    @PostMapping("furtherConsultation/insert")
    @Log(title = "问诊包复诊", businessType = BusinessType.INSERT)
    public AjaxResult insertFurtherConsultation(@RequestBody BusConsultationOrder consultationOrder) {
        String partnersCode = SecurityUtils.getPartnerscode();
        if (StringUtils.isNotEmpty(partnersCode)) {
            consultationOrder.setPartnersCode(partnersCode);
        }
        long consultationOrderId = consultationPackageService.insert(consultationOrder);
        RegulatoryCollectEvent event = new RegulatoryCollectEvent(consultationOrder.getHospitalId(), RegulatoryEventTypeEnum.CONSULTATION_ORDER,  consultationOrderId+ "");
        pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        return AjaxResult.success(consultationOrderId);
    }
}

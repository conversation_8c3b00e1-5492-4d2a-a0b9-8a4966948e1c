package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.constant.ConsultationOrderStatus;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusBizDepartment;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationPackage;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusQuickConsultation;
import com.puree.hospital.app.domain.GuardianConfig;
import com.puree.hospital.app.domain.QuickConsultationPrice;
import com.puree.hospital.app.domain.dto.QuickConsultationDto;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.BusGuardianVO;
import com.puree.hospital.app.domain.vo.BusQuickConsultationVo;
import com.puree.hospital.app.domain.vo.QuickConsultationOrderPoolVo;
import com.puree.hospital.app.helper.GuardianHelper;
import com.puree.hospital.app.mapper.BusAfterSaleMapper;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusConsultationPackageMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusQuickConsultationMapper;
import com.puree.hospital.app.mapper.QuickConsultationPriceMapper;
import com.puree.hospital.app.service.IBusBizDepartmentService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusQuickConsultationService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.ehr.api.RemoteEhrCaseDiagnosisHistoryService;
import com.puree.hospital.ehr.api.model.BusEhrCaseDiagnosisHistoryRequest;
import com.puree.hospital.ehr.api.model.enums.DiagnosisCreateModeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusQuickConsultationServiceImpl implements IBusQuickConsultationService {
    private final BusQuickConsultationMapper quickConsultationMapper;
    private final BusAfterSaleMapper busAfterSaleMapper;
    @Autowired
    private IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final BusConsultationOrderMapper busConsultationOrderMapper;
    private final BusQuickConsultationMapper busQuickConsultationMapper;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final RemoteEhrCaseDiagnosisHistoryService remoteEhrCaseDiagnosisHistoryService;
    private final BusDoctorMapper doctorMapper;
    private final BusConsultationPackageMapper consultationPackageMapper;
    private final BusHandlePrescriptionService handlePrescriptionService ;
    private final QuickConsultationPriceMapper quickConsultationPriceMapper ;
    private final GuardianHelper guardianHelper;
    private final IBusBizDepartmentService busBizDepartmentService;

    @Override
    public int addQuickConsultation(BusQuickConsultation quickConsultation) {
        quickConsultation.setCreateTime(DateUtils.getNowDate());
        return quickConsultationMapper.insert(quickConsultation);
    }

    @Override
    public List<BusQuickConsultationVo> graphicConsultationList(BusQuickConsultation quickConsultation) {
        return quickConsultationMapper.selectGraphicConsultationList(quickConsultation);
    }

    @Override
    public BusQuickConsultationVo selectGraphicConsultation(QuickConsultationDto dto) {
        log.info("查询极速问诊请求参数:dto={}", dto);
        BusQuickConsultationVo busQuickConsultationVo = quickConsultationMapper.selectQuickConsultation(dto);
        log.info("查询极速问诊信息:info={}", busQuickConsultationVo);
        if (ObjectUtil.isNotNull(busQuickConsultationVo) && ObjectUtil.isNotNull(busQuickConsultationVo.getDoctorId())) {
            BusDoctorPatientGroup patientGroup = new BusDoctorPatientGroup();
            patientGroup.setDoctorId(busQuickConsultationVo.getDoctorId());
            patientGroup.setHospitalId(busQuickConsultationVo.getHospitalId());
            patientGroup.setPatientId(busQuickConsultationVo.getPatientId());
            patientGroup.setFamilyId(busQuickConsultationVo.getFamilyId());
            patientGroup.setDepartmentId(busQuickConsultationVo.getDepartmentId());
            patientGroup.setType(CodeEnum.NO.getCode());
            BusDoctorPatientGroup group = busDoctorPatientGroupService.selectOne(patientGroup);
            if (ObjectUtil.isNotNull(group)) {
                busQuickConsultationVo.setGroupId(group.getId());
            }
            // 查询科室名称
            if (Objects.nonNull(busQuickConsultationVo.getDepartmentId())) {
                BusBizDepartment busBizDepartment = busBizDepartmentService.selectById(busQuickConsultationVo.getDepartmentId());
                if (Objects.nonNull(busBizDepartment)) {
                    busQuickConsultationVo.setDepartmentName(busBizDepartment.getDepartmentName());
                }
            }
        }
        if (ObjectUtil.isNotNull(busQuickConsultationVo) && ObjectUtil.isNotNull(busQuickConsultationVo.getHospitalId())) {
            // 儿童返回监护人信息
            GuardianConfig guardianConfig = guardianHelper.getGuardianConfig(busQuickConsultationVo.getHospitalId());
            if (guardianConfig != null) {
                BusPatientFamily family = busPatientFamilyMapper.selectById(busQuickConsultationVo.getFamilyId());
                if (guardianConfig.isEnableChangeAge() && guardianHelper.isChild(family.getDateOfBirth(), guardianConfig)) {
                    BusGuardianVO busGuardianInfo = guardianHelper.getBusGuardianInfo(busQuickConsultationVo.getFamilyId(), busQuickConsultationVo.getPatientId());
                    if (Objects.isNull(busGuardianInfo)) {
                        return busQuickConsultationVo;
                    }
                    busQuickConsultationVo.setBusGuardianInfo(busGuardianInfo);
                }
            }
        }
        return busQuickConsultationVo;
    }

    @Override
    public int delete(Long consultationId) {
        return quickConsultationMapper.deleteById(consultationId);
    }

    @Override
    public List<QuickConsultationOrderPoolVo> selectOrderPool(QuickConsultationDto dto) {
        List<QuickConsultationOrderPoolVo> poolVos = new ArrayList<>();
        // 医生未设置科室或未开启接单池
        if (StringUtils.isEmpty(dto.getDepartmentIds()) || CodeEnum.NO.getCode().equals(dto.getPool())) {
            return poolVos;
        } else {
            String[] ids = dto.getDepartmentIds().split(",");
            List<Long> dIds = Arrays.asList(ids).stream().map(Long::valueOf).collect(Collectors.toList());
            dto.setDepartmentIdList(dIds);
            poolVos = quickConsultationMapper.selectOrderPool(dto);
        }
        return poolVos;
    }

    @Override
    public BusQuickConsultation selectById(Long id) {
        return quickConsultationMapper.selectById(id);
    }

    @Override
    public List<BusQuickConsultationVo> videoConsultationList(BusQuickConsultation videoConsultation) {
        if (ObjectUtil.isNull(videoConsultation) || CollUtil.isEmpty(videoConsultation.getDoctorIds())) {
            return new ArrayList<>();
        }
        calDate(videoConsultation);
        List<BusQuickConsultationVo> busQuickConsultationVos =
                quickConsultationMapper.selectVideoConsultationList(videoConsultation);
        // 过滤不符合条件的数据
        filterConsultaion(busQuickConsultationVos);
        return busQuickConsultationVos;
    }

    @Override
    public List<BusQuickConsultationVo> subscribeVideoList(BusQuickConsultation videoConsultation) {
        if (ObjectUtil.isNull(videoConsultation) || CollUtil.isEmpty(videoConsultation.getDoctorIds())) {
            return new ArrayList<>();
        }
        List<BusQuickConsultationVo> busQuickConsultationVos =
                quickConsultationMapper.selectSubscribeVideoList(videoConsultation);
        // 过滤不符合条件的数据
        filterConsultaion(busQuickConsultationVos);
        return busQuickConsultationVos;
    }

    @Override
    public int passPreliminary(String orderNo, Integer examine) {
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<BusConsultationOrder>()
                .eq(BusConsultationOrder::getOrderNo, orderNo);
        BusConsultationOrder busConsultationOrder = new BusConsultationOrder();
        if (ObjectUtil.isNotNull(examine) && YesNoEnum.YES.getCode().equals(examine)) {
            busConsultationOrder.setPass(3);
        } else {
            busConsultationOrder.setPass(2);
        }
        return busConsultationOrderMapper.update(busConsultationOrder, queryWrapper);
    }

    @Override
    public Long insertBusQuickConsulation(BusQuickConsultation consultation) {
        BusConsultationOrder busConsultationOrder;
        BusQuickConsultation busQuickConsultation = null;
        BusPatientFamily busPatientFamily;

        BusEhrCaseDiagnosisHistoryRequest request = new BusEhrCaseDiagnosisHistoryRequest();
        /*手动结束*/
        if (ObjectUtil.isNotNull(consultation) &&
                ObjectUtil.isNull(consultation.getConsultationOrderId())) {
            LambdaQueryWrapper<BusConsultationOrder> queryWrapper3 = new LambdaQueryWrapper<BusConsultationOrder>()
                    .eq(BusConsultationOrder::getDoctorId, consultation.getDoctorId())
                    .eq(BusConsultationOrder::getFamilyId, consultation.getFamilyId())
                    .eq(BusConsultationOrder::getHospitalId, consultation.getHospitalId())
                    .eq(BusConsultationOrder::getPatientId, consultation.getPatientId())
                    .eq(BusConsultationOrder::getDepartmentId, consultation.getDepartmentId())
                    .orderByDesc(BusConsultationOrder::getCreateTime)
                    .last("limit 1");
            /*查询最新的订单信息*/
            busConsultationOrder = busConsultationOrderMapper.selectOne(queryWrapper3);
        } else {
            /*自动结束*/
            busConsultationOrder = busConsultationOrderMapper.selectById(consultation.getConsultationOrderId());
            consultation.setFamilyId(busConsultationOrder.getFamilyId());
            consultation.setHospitalId(busConsultationOrder.getHospitalId());
        }
        if (ObjectUtil.isNotNull(busConsultationOrder)) {
            if (ObjectUtil.isNotNull(busConsultationOrder.getConsultationId())) {
                /*查询急速问诊表的就诊人的问诊信息*/
                busQuickConsultation = busQuickConsultationMapper.selectById(busConsultationOrder.getConsultationId());
            }

            /*查询就诊人的信息*/
            busPatientFamily = busPatientFamilyMapper.selectById(consultation.getFamilyId());
            request.setFullName(busPatientFamily.getName());
            request.setSex(busPatientFamily.getSex());
            Date dateOfBirth = busPatientFamily.getDateOfBirth();
            request.setAge(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd", dateOfBirth)));
            request.setDateOfBirth(dateOfBirth);
            request.setFamilyPhone(busPatientFamily.getCellPhoneNumber());
            request.setCreateTime(DateUtils.getNowDate());
            request.setIdCardNo(busPatientFamily.getIdNumber());
            request.setFamilyId(busPatientFamily.getId());
            request.setSource(YesNoEnum.NO.getCode());
            request.setDoctorId(busConsultationOrder.getDoctorId());
            request.setConsultationTime(null == busConsultationOrder.getCreateTime() ? DateUtils.getNowDate() : busConsultationOrder.getCreateTime());
            request.setHospitalId(consultation.getHospitalId());
            if (null != busQuickConsultation) {
                request.setChiefComplaint(busQuickConsultation.getSymptomDescription());
                request.setHistoryOfPresentIllness(busQuickConsultation.getIllness());
                request.setPastHistory(busQuickConsultation.getAnamneses());
                request.setAllergicDrugs(busQuickConsultation.getAllergicDrugs());
            } else {
                request.setChiefComplaint(consultation.getSymptomDescription());
                request.setHistoryOfPresentIllness(consultation.getIllness());
                request.setPastHistory(consultation.getAnamneses());
                request.setAllergicDrugs(consultation.getAllergicDrugs());
            }

            request.setConsultationId(busConsultationOrder.getId());

            /*查询处方信息*/
            LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusPrescription::getConsultationOrderId, busConsultationOrder.getId());
            queryWrapper.in(BusPrescription::getStatus, PrescriptionStatus.PASS, PrescriptionStatus.USED);
            List<BusPrescription> prescriptionList = busPrescriptionMapper.selectList(queryWrapper);
            handlePrescriptionService.handlePrescriptionInfo(consultation.getHospitalId(), prescriptionList, request);

        }
        // 关联了处方的问诊单为手动创建
        handleConsultationWithPrescriptionForManuallyCreateMode(request);
        return remoteEhrCaseDiagnosisHistoryService.insertBusEhrCaseDiagnosisHistory(request).getData();
    }

    /**
     *  处理关联了处方的问诊单为手动创建
     * @param request   参数
     */
    private void handleConsultationWithPrescriptionForManuallyCreateMode(BusEhrCaseDiagnosisHistoryRequest request) {
        // 问诊单如果关联了处方，标记创建来源为手动添加
        if (Objects.isNull(request.getCreateMode()) && Objects.nonNull(request.getConsultationId())) {
            LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusPrescription::getConsultationOrderId, request.getConsultationId());
            queryWrapper.isNotNull(BusPrescription::getPatientId);
            Long count = busPrescriptionMapper.selectCount(queryWrapper);
            if (count > 0) {
                request.setCreateMode(DiagnosisCreateModeEnum.MANUALLY.getCode());
            }
        }
    }

    @Override
    public List<QuickConsultationOrderPoolVo> assistanceOrderPool(QuickConsultationDto dto) {
        return quickConsultationMapper.selectOrderPool(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int preDiagnosis(Long orderId, Long doctorId) {
        // 查询医生信息
        BusDoctorVo doctor = doctorMapper.selectDrInfo(doctorId);
        // 补充问诊包信息
        BusConsultationPackage consultationPackage = new BusConsultationPackage();
        consultationPackage.setDoctorId(doctorId);
        LambdaQueryWrapper<BusConsultationPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusConsultationPackage::getOrderId, orderId);
        consultationPackageMapper.update(consultationPackage, lambdaQuery);
        // 补充问诊订单医生信息
        BusConsultationOrder consultationOrder = new BusConsultationOrder();
        consultationOrder.setId(orderId);
        consultationOrder.setDoctorId(doctorId);
        consultationOrder.setDoctorName(doctor.getFullName());
        consultationOrder.setPhoto(doctor.getPhoto());
        consultationOrder.setTitle(doctor.getTitleValue());
        consultationOrder.setConsultationType(CodeEnum.YES.getCode());
        consultationOrder.setPass(2);
        return busConsultationOrderMapper.updateById(consultationOrder);
    }

    private void filterConsultaion(List<BusQuickConsultationVo> busQuickConsultationVos) {
        Iterator<BusQuickConsultationVo> iterator = busQuickConsultationVos.iterator();
        while (iterator.hasNext()) {
            BusQuickConsultationVo next = iterator.next();
            if (ConsultationOrderStatus.REFUNDING.equals(next.getVideoStatus())) {
                // 查询售后信息
                QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("order_no", next.getOrderNo());
                BusAfterSale busAfterSale = busAfterSaleMapper.selectOne(queryWrapper);
                if (ObjectUtil.isNotNull(busAfterSale)) {
                    if (!ConsultationOrderStatus.RESERVED_ALREADY_ARRIVED.equals(busAfterSale.getOrderStatus())) {
                        iterator.remove();
                    } else if ("4".equals(busAfterSale.getStatus())) {
                        iterator.remove();
                    }
                }
            }
        }
    }

    private void calDate(BusQuickConsultation videoConsultation) {
        Date nowDate = DateUtils.getNowDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format1 = sdf.format(nowDate);
        Calendar instance = Calendar.getInstance();
        instance.setTime(nowDate);
        instance.add(Calendar.DATE, 1);
        Date tomorrowDate = instance.getTime();
        String format2 = sdf.format(tomorrowDate);
        videoConsultation.setNowDate(format1);
        videoConsultation.setTomorrowDate(format2);
    }

    @Override
    public BigDecimal findQuickConsultationPriceByDepartment(Long hospitalId, Long departmentId){

        QuickConsultationPrice req = new QuickConsultationPrice() ;
        req.setHospitalId(hospitalId);
        req.setDepartmentId(departmentId);
        req.setDelFlag(YesNoEnum.NO.getCode());
        req.setCount(1);
        List<QuickConsultationPrice> quickConsultationPriceList = quickConsultationPriceMapper.selectByCondition(req);

        if ( null==quickConsultationPriceList || quickConsultationPriceList.isEmpty() ) {
            return null ;
        }

        QuickConsultationPrice quickConsultationPrice = quickConsultationPriceList.get(0) ;
        if ( null==quickConsultationPrice.getPrice() ) {
            return BigDecimal.ZERO ;
        }
        return  quickConsultationPrice.getPrice() ;
    }

}

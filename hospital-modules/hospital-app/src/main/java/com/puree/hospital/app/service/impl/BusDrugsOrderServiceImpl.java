package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.app.api.model.event.order.DrugsOrderAgreeRefundEvent;
import com.puree.hospital.app.constant.AfterSaleStatus;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusDrugOrderPackage;
import com.puree.hospital.app.domain.BusDrugs;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusEnterpriseDrugsPrice;
import com.puree.hospital.app.domain.BusEnterpriseProductOrder;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusHospitalOfficina;
import com.puree.hospital.app.domain.BusHospitalPa;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderAfterSales;
import com.puree.hospital.app.domain.BusOrderShop;
import com.puree.hospital.app.domain.BusOtcDrugs;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusShopCart;
import com.puree.hospital.app.domain.dto.BusDoctorOrderDTO;
import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.domain.dto.HospitalOfficinaDrugsDTO;
import com.puree.hospital.app.domain.vo.BusOrderShopVO;
import com.puree.hospital.app.domain.vo.BusPrescriptionVo;
import com.puree.hospital.app.domain.vo.BusShopOrderVO;
import com.puree.hospital.app.domain.vo.DrugsAndGoodsDetailVO;
import com.puree.hospital.app.domain.vo.DrugsAndGoodsOrderVO;
import com.puree.hospital.app.domain.vo.DrugsAndGoodsVO;
import com.puree.hospital.app.domain.vo.DrugsOrderVo;
import com.puree.hospital.app.domain.vo.HospitalVo;
import com.puree.hospital.app.domain.vo.OrderDetailVO;
import com.puree.hospital.app.domain.vo.OrderPrescriptionVo;
import com.puree.hospital.app.domain.vo.OrderVo;
import com.puree.hospital.app.domain.vo.SubOrderVO;
import com.puree.hospital.app.helper.FreightCalculator;
import com.puree.hospital.app.infrastructure.supplier.SupplierFactory;
import com.puree.hospital.app.mapper.BusAfterSaleMapper;
import com.puree.hospital.app.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.app.mapper.BusDrugsMapper;
import com.puree.hospital.app.mapper.BusDrugsOrderMapper;
import com.puree.hospital.app.mapper.BusEnterpriseDrugsMapper;
import com.puree.hospital.app.mapper.BusEnterpriseDrugsPriceMapper;
import com.puree.hospital.app.mapper.BusEnterpriseProductOrderMapper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusHospitalPaMapper;
import com.puree.hospital.app.mapper.BusInvoiceMapper;
import com.puree.hospital.app.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.mapper.BusOrderShopMapper;
import com.puree.hospital.app.mapper.BusOtcDrugsMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPatientMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.queue.QueueConstant;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.DrugsOrderCancelProducer;
import com.puree.hospital.app.queue.producer.PrescriptionInvalidProducer;
import com.puree.hospital.app.queue.producer.PrescriptionLastValidProducer;
import com.puree.hospital.app.queue.producer.PrescriptionValidProducer;
import com.puree.hospital.app.service.IBusChannelOrderService;
import com.puree.hospital.app.service.IBusConsultationSettingsService;
import com.puree.hospital.app.service.IBusDoctorHospitalService;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusHospitalOfficinaService;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.app.service.IBusShopCartService;
import com.puree.hospital.app.api.model.dto.BusFreightResultDTO;
import com.puree.hospital.app.api.model.dto.BusFreightQueryDTO;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.domain.BusInvoice;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DoctorPracticeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.EntOrderStatusEnum;
import com.puree.hospital.common.core.enums.EnterpriseEnum;
import com.puree.hospital.common.core.enums.InvoiceStatusEnum;
import com.puree.hospital.common.core.enums.OrderStatusEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.ShopCartTypeEnum;
import com.puree.hospital.common.core.enums.StockEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.insurance.api.RemotePaymentService;
import com.puree.hospital.insurance.api.RemotePreprocessorService;
import com.puree.hospital.insurance.api.model.MiPayResult;
import com.puree.hospital.insurance.api.model.UniformRequest;
import com.puree.hospital.order.api.RemoteBusShopOrderService;
import com.puree.hospital.order.api.model.BusShopOrder;
import com.puree.hospital.supplier.api.RemoteDFHService;
import com.puree.hospital.supplier.api.model.dfh.DFHOrder;
import com.puree.hospital.supplier.api.model.dfh.OrderQueryDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDrugsOrderServiceImpl extends ServiceImpl<BusDrugsOrderMapper, BusDrugsOrder> implements IBusDrugsOrderService {

    private final BusDrugsOrderMapper busDrugsOrderMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final BusHospitalMapper busHospitalMapper;
    private final BusAfterSaleMapper afterSaleMapper;
    private final IBusShopCartService busShopCartService;
    private final IBusHospitalOfficinaService busHospitalOfficinaService;
    private final IBusDoctorHospitalService iBusDoctorHospitalService;
    private final RemoteDFHService remoteDFHService;
    private final BusEnterpriseDrugsMapper busEnterpriseDrugsMapper;
    private final BusEnterpriseDrugsPriceMapper busEnterpriseDrugsPriceMapper;
    private final BusOtcDrugsMapper busOtcDrugsMapper;
    private final BusHospitalPaMapper busHospitalPaMapper;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final SupplierFactory supplierFactory;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    private final RemoteBusShopOrderService remoteBusShopOrderService;
    private final IBusShopCartService shopCartService;
    private final BusOrderAfterSalesMapper busOrderAfterSalesMapper;
    private final BusOrderShopMapper busOrderShopMapper;
    @Lazy
    @Resource
    private IBusOrderService busOrderService;
    private final BusOrderMapper orderMapper;
    private final BusEnterpriseProductOrderMapper entOrderMapper;
    private final IBusPatientService busPatientService;
    private final BusDrugsMapper busDrugsMapper;
    private final RemotePaymentService remotePaymentService;
    private final BusHospitalInvoiceService hospitalInvoiceService;
    private final BusInvoiceMapper invoiceMapper;
    private final BusPatientFamilyMapper patientFamilyMapper;
    private final BusPatientMapper patientMapper;
    private final RemotePreprocessorService remotePreprocessorService;
    @Autowired
    @Lazy
    private DrugsOrderCancelProducer drugsOrderCancelProducer;
    @Autowired
    @Lazy
    private PrescriptionInvalidProducer prescriptionInvalidProducer;
    @Autowired
    @Lazy
    private PrescriptionLastValidProducer prescriptionLastValidProducer;
    @Autowired
    @Lazy
    private PrescriptionValidProducer prescriptionValidProducer;

    @Autowired @Lazy
    private ApplicationEventPublisher publisher;

    @Resource
    private IBusChannelOrderService busChannelOrderService;



    @Resource
    private FreightCalculator freightCalculator;

    @Override
    public List<DrugsOrderVo> selectList(DrugsOrderDto dto) {
        List<DrugsOrderVo> orderVos = busDrugsOrderMapper.selectDetailList(dto);
        if (CollectionUtil.isNotEmpty(orderVos)) {
            orderVos.forEach(o -> {
                if (Objects.nonNull(o.getPaId())) {
                    BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(o.getPaId());
                    o.setZyfjType(busHospitalPa.getType());
                }
                if (Objects.nonNull(o.getPrescriptionId())) {
                    // 查询处方药品信息
                    List<BusPrescriptionDrugs> prescriptionDrugs =
                            busPrescriptionDrugsMapper.selectPrescriptonDrugsList(o.getPrescriptionId());
                    o.setList(prescriptionDrugs);
                } else { // 非处方药品
                    List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectOtcList(o.getId());
                    o.setEnterpriseId(busOtcDrugs.get(0).getEnterpriseId());
                    List<BusPrescriptionDrugs> drugsList = OrikaUtils.converts(busOtcDrugs, BusPrescriptionDrugs.class);
                    drugsList.forEach(p -> p.setPreType(CodeEnum.YES.getCode()));
                    o.setList(drugsList);
                }
                // 查看是否存在售后信息
                QueryWrapper<BusAfterSale> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.eq("order_no", o.getOrderNo());
                BusAfterSale busAfterSale = afterSaleMapper.selectOne(queryWrapper1);
                if (Objects.nonNull(busAfterSale)) {
                    o.setExitAfterSale(1);
                }
                // 查询包裹信息
                LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, o.getHospitalId());
                lambdaQuery.isNotNull(BusDrugOrderPackage::getDeliveryNo);
                if (Objects.nonNull(o.getPrescriptionId())) {
                    lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, o.getPrescriptionId());
                } else {
                    lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, o.getId());
                }
                // 判断是否是换货单
                if (o.getAmount().compareTo(new BigDecimal(0)) == 0) {
                    lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
                } else {
                    lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
                }
                lambdaQuery.orderByAsc(BusDrugOrderPackage::getDeliveryTime);
                List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectList(lambdaQuery);
                if (CollectionUtil.isNotEmpty(busDrugOrderPackages)) {
                    o.setDeliveryTime(busDrugOrderPackages.get(0).getDeliveryTime());
                }
            });
        }
        return orderVos;
    }

    private void queryDrugsAssociationInfo(DrugsOrderVo o) {
        if (Objects.nonNull(o.getPrescriptionId())) {
            List<BusPrescriptionDrugs> prescriptionDrugs;
            if (PrescriptionTypeEnum.isZyxdf(o.getPrescriptionType())) {
                BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(o.getPaId());
                o.setZyfjType(busHospitalPa.getType());
                if (YesNoEnum.NO.getCode().equals(busHospitalPa.getType())) {
                    prescriptionDrugs = new ArrayList<>();
                    BusPrescriptionDrugs drugs = new BusPrescriptionDrugs();
                    drugs.setDrugsName(busHospitalPa.getPaName());
                    prescriptionDrugs.add(drugs);
                } else {
                    // 查询处方药品信息
                    prescriptionDrugs = busPrescriptionDrugsMapper.selectPrescriptonDrugsList(o.getPrescriptionId());
                }
            } else {
                // 查询处方药品信息
                prescriptionDrugs = busPrescriptionDrugsMapper.selectPrescriptonDrugsList(o.getPrescriptionId());
            }
            o.setList(prescriptionDrugs);
        } else { // 非处方药品
            List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectOtcList(o.getId());
            if (CollectionUtil.isNotEmpty(busOtcDrugs)) {
                BusOtcDrugs busOtcDrugs1 = busOtcDrugs.get(0);
                if (Objects.nonNull(busOtcDrugs1)) {
                    o.setEnterpriseId(busOtcDrugs1.getEnterpriseId());
                }
                List<BusPrescriptionDrugs> drugsList = OrikaUtils.converts(busOtcDrugs, BusPrescriptionDrugs.class);
                drugsList.forEach(p -> {
                    p.setPreType(CodeEnum.YES.getCode());
                    p.setDrugsType("1");
                });
                o.setList(drugsList);
            }
        }
    }

    @Override
    public BusDrugsOrder select(DrugsOrderDto dto) {
        log.info("参数值={}", JSONObject.toJSONString(dto));
        BusDrugsOrder busDrugsOrder = null;
        String orderNo = dto.getOrderNo();
        // 查询订单信息
        List<BusOrder> orderList = busOrderService.selectOrederByNo(orderNo);
        if (CollectionUtils.isNotEmpty(orderList)) {
            BusOrder order = orderList.get(0);
            busDrugsOrder = OrikaUtils.convert(order, BusDrugsOrder.class);
            busDrugsOrder.setModifyAddress(order.getModifyAddress());
            // 查询是否医保支付
            R<MiPayResult> payResult = remotePaymentService.queryPayResult(orderNo);
            if (Constants.SUCCESS == payResult.getCode()) {
                MiPayResult miPayResult = payResult.getData();
                if (null != miPayResult) {
                    busDrugsOrder.setOwnPayAmt(miPayResult.getOwnPayAmt());
                    busDrugsOrder.setFundPay(miPayResult.getFundPay());
                    busDrugsOrder.setPsnAcctPay(miPayResult.getPsnAcctPay());
                    //订单详情增加其他现金支付金额
                    busDrugsOrder.setOtherCashAmount(miPayResult.getOtherCashAmount());
                    //订单详情增加费用类型
                    busDrugsOrder.setMiMedType(miPayResult.getMedType());
                }
            }
            busDrugsOrder.setStatus(order.getOrderStatus());
            busDrugsOrder.setReceivingUser(order.getReceiver());
            busDrugsOrder.setReceivingTel(order.getReceivePhone());
            busDrugsOrder.setReceivingAddress(order.getReceiveAddress());
            BigDecimal payAmount = order.getRelPrice();
            BigDecimal freight = BigDecimal.valueOf(order.getFreight());
            BigDecimal processPrice = new BigDecimal(0);
            busDrugsOrder.setFreight(freight.doubleValue());
            for (int i = 0; i < orderList.size(); i++) {
                BusOrder busOrder = orderList.get(i);
                if (CodeEnum.NO.getCode().equals(busOrder.getSubOrderType())) {
                    BusDrugsOrder drugsOrder = busDrugsOrderMapper.selectById(busOrder.getSubOrderId());
                    if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
                        BusPrescription prescription = busPrescriptionMapper.selectById(drugsOrder.getPrescriptionId());
                        if (!CodeEnum.YES.getCode().equals(prescription.getPrescriptionType()) && prescription.getProcessPrice() != null) {
                            processPrice = processPrice.add(prescription.getProcessPrice());
                        }
                    }
                } else {
                    R<BusShopOrder> r = remoteBusShopOrderService.selectShopOrderById(busOrder.getSubOrderId());
                    if (Constants.SUCCESS.equals(r.getCode())) {
                        //BusShopOrder shopOrder = JSONObject.parseObject(JSONObject.toJSONString(r.getData()), BusShopOrder.class);
                        BusShopOrder shopOrder = r.getData();
                        busDrugsOrder.setFamilyId(shopOrder.getFamilyId());
                        busDrugsOrder.setFamilyName(shopOrder.getFamilyName());
                        busDrugsOrder.setFamilySex(shopOrder.getFamilySex());
                        busDrugsOrder.setFamilyAge(shopOrder.getFamilyAge());
                    } else {
                        log.error("远程调用查询商品订单失败={}", r.getMsg());
                    }
                }
            }
            busDrugsOrder.setProcessPrice(processPrice);
            busDrugsOrder.setAmount(payAmount.subtract(freight).subtract(processPrice).toString());
            // 查询物流信息
            List<BusDrugOrderPackage> packageList = this.queryLogisticsList(order.getOrderNo());
            if (!packageList.isEmpty()) {
                BusDrugOrderPackage drugOrderPackage = packageList.get(0);
                log.info("患者详情包裹信息={}", drugOrderPackage);
                busDrugsOrder.setDeliveryTime(drugOrderPackage.getDeliveryTime());
                Calendar instance = Calendar.getInstance();
                instance.setTime(drugOrderPackage.getDeliveryTime());
                instance.add(Calendar.DATE, 7);
                long timeInMillis = instance.getTimeInMillis();
                long currentTime = DateUtils.getNowDate().getTime();
                busDrugsOrder.setExpirationTime(timeInMillis - currentTime);
                busDrugsOrder.setPickupCode(drugOrderPackage.getOrderPackageList().get(0).getCode());
            }else if (order.getDeliveryTime() != null){
                // 无物流信息的订单 设置自动收货倒计时
                Calendar instance = Calendar.getInstance();
                instance.setTime(order.getDeliveryTime());
                instance.add(Calendar.DATE, 3);
                long timeInMillis = instance.getTimeInMillis();
                long currentTime = DateUtils.getNowDate().getTime();
                busDrugsOrder.setExpirationTime(timeInMillis - currentTime);
            }
            busDrugsOrder.setPackageList(packageList);
            boolean flag = hospitalInvoiceService.getHospitalInvoiceStatus(order.getHospitalId());
            if (flag) {
                BusInvoice busInvoice = invoiceMapper.findLastInvoice(orderNo);
                if (null != busInvoice) {
                    order.setPicUrl(busInvoice.getPicUrl());
                    order.setDownloadUrl(busInvoice.getDownloadUrl());
                }
                busDrugsOrder.setBusOrder(order);
            }
        }
        return busDrugsOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> insert(BusDrugsOrder busDrugsOrder) {
        Map<String, Object> map = new HashMap<>();
        Date nowDate = DateUtils.getNowDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        busDrugsOrder.setId(null);
        busDrugsOrder.setOrderNo(OrderTypeConstant.DRUGS_ORDER + simpleDateFormat.format(nowDate));
        busDrugsOrder.setCreateTime(nowDate);
        //fix-缺少订单时间（2024-6-25）
        busDrugsOrder.setOrderTime(nowDate);
        String amount = busDrugsOrder.getAmount();
        BusFreightResultDTO freightDTO = freightCalculator.calculate(convertFreightQuery(busDrugsOrder));
        busDrugsOrder.setFreight(freightDTO.getFreight() == null ? 0L : freightDTO.getFreight().doubleValue());
        Double doubleAmount = StringUtils.isBlank(amount) ? 0D : Double.parseDouble(amount);
        busDrugsOrder.setAmount(String.valueOf(busDrugsOrder.getFreight() + doubleAmount));
        busDrugsOrderMapper.insert(busDrugsOrder);

        // 非处方药订单
        if (Objects.isNull(busDrugsOrder.getPrescriptionId())) {
            // 校验/扣减库存
            Map<String, Object> noStockMap = this.checkOtcDrugsStock(busDrugsOrder);
            if (MapUtil.isNotEmpty(noStockMap)) {
                return noStockMap;
            }
            // otc药直接购买，删除购物车
            for (BusOtcDrugs otcDrug : busDrugsOrder.getList()) {
                BusShopCart cart = new BusShopCart();
                cart.setBusinessId(otcDrug.getDrugsId());
                cart.setType(ShopCartTypeEnum.DRUGS.getCode());
                cart.setHospitalId(otcDrug.getHospitalId());
                cart.setPatientId(busDrugsOrder.getPatientId());
                shopCartService.deleteBusinessById(cart);
            }
        } else {
            // 设置处方状态为已使用
            QueryWrapper<BusPrescription> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .eq("hospital_id", busDrugsOrder.getHospitalId())
                    .eq("id", busDrugsOrder.getPrescriptionId());
            BusPrescription prescription = new BusPrescription();
            prescription.setStatus(PrescriptionStatus.USED);
            busPrescriptionMapper.update(prescription, queryWrapper);

            List<BusOtcDrugs> pdList = busDrugsOrder.getList();
            for (BusOtcDrugs drugs : pdList) {
                drugs.setDrugsOrderId(busDrugsOrder.getId());
                drugs.setOtcOrNot(YesNoEnum.NO.getCode());
                if (Objects.nonNull(drugs.getEnterpriseId())) {
                    // 查询配送企业成本价
                    BusEnterpriseDrugsPrice drugsPrice = busEnterpriseDrugsPriceMapper.getDrugsInfoByDrugsId(busDrugsOrder.getHospitalId(), drugs.getEnterpriseId(), drugs.getDrugsId());
                    if (!Objects.isNull(drugsPrice)) {
                        drugs.setReferencePurchasePrice(drugsPrice.getReferencePurchasePrice().doubleValue());
                    }
                    // 保存订单ID到包裹表
                    LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                    lambdaQuery.eq(BusDrugOrderPackage::getEnterpriseId, drugs.getEnterpriseId());
                    lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrder.getPrescriptionId());
                    lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
                    BusDrugOrderPackage drugOrderPackage = new BusDrugOrderPackage();
                    drugOrderPackage.setDrugsOrderId(busDrugsOrder.getId());
                    busDrugOrderPackageMapper.update(drugOrderPackage, lambdaQuery);
                }
            }
            busOtcDrugsMapper.batchInsert(pdList);
            // 从处方列表购买，删除购物车
            shopCartService.deletePrescriptionOrOrder(busDrugsOrder.getPrescriptionId(), false);
        }

        BusOrder order = new BusOrder();
        order.setOrderNo(OrderTypeConstant.TOTAL_ORDER + simpleDateFormat.format(nowDate));
        order.setOrderTime(DateUtils.getNowDate());
        order.setCreateTime(DateUtils.getNowDate());
        order.setSubOrderId(busDrugsOrder.getId());
        order.setSubOrderType("0");
        order.setHospitalId(busDrugsOrder.getHospitalId());
        order.setPatientId(busDrugsOrder.getPatientId());
        if (StringUtils.isNotEmpty(busDrugsOrder.getPartnersCode())) {
            order.setPartnersCode(busDrugsOrder.getPartnersCode());
        }
        order.setFreight(freightDTO.getFreight() == null ? 0L : freightDTO.getFreight().doubleValue());
        order.setFreightType(freightDTO.getFreightType());
        order.setRelPrice(new BigDecimal(amount).add(BigDecimal.valueOf(order.getFreight())));
        order.setDeliveryType(busDrugsOrder.getDeliveryType());
        order.setPickUpTime(busDrugsOrder.getPickUpTime());
        if (CodeEnum.NO.getCode().equals(busDrugsOrder.getDeliveryType())) {
            // 查询患者手机号
            BusPatient busPatient = busPatientService.selectPatientInfo(busDrugsOrder.getPatientId());
            order.setReceiver(null);
            order.setReceivePhone(busPatient.getPhoneNumber());
        } else {
            order.setReceiver(busDrugsOrder.getReceivingUser());
            order.setReceivePhone(busDrugsOrder.getReceivingTel());
        }
        order.setReceiveAddress(busDrugsOrder.getReceivingAddress());
        // 查询支付方式
        BusHospital busHospital = busHospitalMapper.selectById(busDrugsOrder.getHospitalId());
        order.setPayWay(busHospital.getPayWay());
        order.setOrderStatus("0");
        order.setHospitalName(busDrugsOrder.getHospitalName());
        order.setProvince(busDrugsOrder.getProvince());
        order.setCity(busDrugsOrder.getCity());
        order.setArea(busDrugsOrder.getArea());
        order.setDetailedAddress(busDrugsOrder.getDetailedAddress());
        order.setRemarks(busDrugsOrder.getRemarks());
        //  invoiceStatus 初始状态 不可开具
        order.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());
        busOrderService.addOrder(order);

        // 校验患者是否是经纪人邀请
        BusOrderDto busOrderDto = new BusOrderDto();
        busOrderDto.setHospitalId(busDrugsOrder.getHospitalId());
        busOrderDto.setPatientId(busDrugsOrder.getPatientId());
        busOrderDto.setId(order.getId());
        busOrderDto.setOrderType(1);
        busOrderDto.setOrderNo(order.getOrderNo());
        busChannelOrderService.createBusChannelOrderByOrderDTO(busOrderDto);

        map.put("drugsOrderId", busDrugsOrder.getId());
        return map;
    }

    private BusFreightQueryDTO convertFreightQuery(BusDrugsOrder busDrugsOrder) {
        if (StringUtils.isEmpty(busDrugsOrder.getAmount())) {
            return new BusFreightQueryDTO();
        }
        BusFreightQueryDTO query = new BusFreightQueryDTO();
        query.setHospitalId(busDrugsOrder.getHospitalId());
        query.setProvinceName(busDrugsOrder.getProvince());
        query.setCityName(busDrugsOrder.getCity());
        query.setAreaName(busDrugsOrder.getArea());
        query.setDeliveryType(busDrugsOrder.getDeliveryType());
        query.setAmount(new BigDecimal(busDrugsOrder.getAmount()));
        return query;
    }

    @Override
    public String getHospitalPayWay(Long hospitalId) {
        BusHospital busHospital = busHospitalMapper.selectById(hospitalId);
        return busHospital.getPayWay();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int cancel(DrugsOrderDto dto) {
        // 查询总订单信息
        List<BusOrder> busOrders = busOrderService.selectOrederByNo(dto.getOrderNo());
        if (ObjectUtil.isEmpty(busOrders)) {
            throw new ServiceException("订单不存在！");
        }
        // 医保退费
        insuranceRefund(busOrders.get(0), dto);
        for (BusOrder order : busOrders) {
            // 药品订单
            if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                BusDrugsOrder drugsOrder = busDrugsOrderMapper.selectById(order.getSubOrderId());
                // 订单是处方订单重新放回
                putRxCart(drugsOrder);
                // 释放药品库存
                //increaseDrugStock(drugsOrder);
            } else {
                LambdaQueryWrapper<BusOrderShop> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusOrderShop::getOrderId, order.getSubOrderId());
                List<BusOrderShop> shopGoodsList = busOrderShopMapper.selectList(lambdaQuery);
                // 释放商品库存
                busOrderService.changeHospitalShopStock(order.getHospitalId(), shopGoodsList, StockEnum.INCREASE.getName());
            }
        }

        BusOrder busOrder = new BusOrder();
        busOrder.setOrderNo(dto.getOrderNo());
        busOrder.setCancelTime(DateUtils.getNowDate());
        busOrder.setOrderStatus(OrderStatusEnum.CANCEL.getCode());
        return busOrderService.updateOrderStatus(busOrder);
    }

    /**
     * 药品订单重新放回购物车
     *
     * @param drugsOrder 药品订单
     */
    @Override
    public void putRxCart(BusDrugsOrder drugsOrder) {
        if (null != drugsOrder.getPrescriptionId()) {
            BusPrescription prescription = busPrescriptionMapper.selectById(drugsOrder.getPrescriptionId());

            // 处方三种失效时间查询
            BusConsultationSettings settings = new BusConsultationSettings();
            settings.setHospitalId(drugsOrder.getHospitalId());
            settings = busConsultationSettingsService.selectOne(settings);

            long currTime = System.currentTimeMillis();
            long reviewTime = prescription.getReviewTime().getTime();
            long invalidTime;
            long processTime;
            long lastInvalidTime;

            if (settings == null || settings.getPrescrptionOrderExpire() == null) {
                invalidTime = QueueConstant.QUEUE_PRESCRIPTION_INVALID_TIME + reviewTime;
            } else {
                invalidTime = settings.getPrescrptionOrderExpire() * 60 * 60 * 1000L + reviewTime;
            }

            // 处方还在有效期内
            if (currTime < invalidTime) {
                // 处方失效queue
                Message message = new Message();
                message.setId(drugsOrder.getPrescriptionId());
                message.setHospitalId(drugsOrder.getHospitalId());
                message.setFireTime(invalidTime);
                prescriptionInvalidProducer.sendOnTime(message, message.getFireTime());

                // 进行中queue
                if (settings == null || settings.getPrescrptionInValid() == null) {
                    processTime = QueueConstant.QUEUE_PRESCRIPTION_IN_INVALID_TIME + reviewTime;
                } else {
                    processTime = settings.getPrescrptionInValid() * 60 * 1000 + reviewTime;
                }
                if (currTime < processTime) {
                    message.setFireTime(processTime);
                    prescriptionValidProducer.sendOnTime(message, message.getFireTime());
                }

                // 最后一次提醒queue
                if (settings == null || settings.getPrescrptionLastValid() == null) {
                    lastInvalidTime = invalidTime - QueueConstant.QUEUE_PRESCRIPTION_LAST_INVALID_TIME;
                    message.setHoursRemaining("1小时0分钟");
                } else {
                    lastInvalidTime = invalidTime - settings.getPrescrptionLastValid() * 60 * 1000;
                    message.setHoursRemaining(DateUtils.minZHour(settings.getPrescrptionLastValid()));
                }
                if (currTime < lastInvalidTime) {
                    message.setFireTime(lastInvalidTime);
                    prescriptionLastValidProducer.sendOnTime(message, message.getFireTime());
                }

                //加入患者购物车
                BusShopCart shopCart = new BusShopCart();
                if (StringUtils.isNotNull(prescription.getPreorderId())) {
                    shopCart.setType(ShopCartTypeEnum.ADVANCE_ORDER.getCode());
                    shopCart.setBusinessId(prescription.getPreorderId());
                } else {
                    shopCart.setType(ShopCartTypeEnum.PRESCRIPTION.getCode());
                    shopCart.setBusinessId(prescription.getId());
                }
                shopCart.setPatientId(prescription.getPatientId());
                shopCart.setHospitalId(prescription.getHospitalId());
                busShopCartService.insert(shopCart);

                prescription.setStatus(PrescriptionStatus.PASS);
                busPrescriptionMapper.updateById(prescription);
            }
        }
    }

    /**
     * 取消订单释放药品库存
     *
     * @param drugsOrder
     */
    @Override
    public void increaseDrugStock(BusDrugsOrder drugsOrder) {
        if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
            // 查询处方信息
            BusPrescription prescription = busPrescriptionMapper.selectById(drugsOrder.getPrescriptionId());
            // 药房处方药品
            List<BusPrescriptionDrugs> drugstorePdList = new ArrayList<>();
            // 配送企业处方药品
            List<BusPrescriptionDrugs> enterprisePdList = new ArrayList<>();
            // 查询处方药品信息
            LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusPrescriptionDrugs::getPrescriptionId, drugsOrder.getPrescriptionId());
            List<BusPrescriptionDrugs> prescriptionDrugs = busPrescriptionDrugsMapper.selectList(lambdaQuery);
            for (BusPrescriptionDrugs drug : prescriptionDrugs) {
                if (Objects.nonNull(drug.getEnterpriseId())) {
                    enterprisePdList.add(drug);
                } else {
                    drugstorePdList.add(drug);
                }
            }
            if (CollectionUtil.isNotEmpty(drugstorePdList)) {
                // 修改药房处方药库存
                changeDrugstoreStock(drugstorePdList, drugsOrder);
            }
            if (CollectionUtil.isNotEmpty(enterprisePdList)) {
                // 修改西药配送企业处方药库存
                if (CodeEnum.YES.getCode().equals(prescription.getPrescriptionType())) {
                    changeDeliveryEnterpriseStock(enterprisePdList, drugsOrder.getHospitalId());
                }
            }
        } else {
            // 药房otc药品
            List<BusPrescriptionDrugs> drugstoreOtcList = new ArrayList<>();
            // 配送企业otc药品
            List<BusPrescriptionDrugs> enterpriseOtcList = new ArrayList<>();
            // 查询otc药品信息
            LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusOtcDrugs::getDrugsOrderId, drugsOrder.getId());
            List<BusOtcDrugs> list = busOtcDrugsMapper.selectList(lambdaQuery);
            List<BusPrescriptionDrugs> otcDrugsList = OrikaUtils.converts(list, BusPrescriptionDrugs.class);
            for (BusPrescriptionDrugs drug : otcDrugsList) {
                if (Objects.nonNull(drug.getEnterpriseId())) {
                    enterpriseOtcList.add(drug);
                } else {
                    drugstoreOtcList.add(drug);
                }
            }
            if (CollectionUtil.isNotEmpty(drugstoreOtcList)) {
                // 修改药房otc药库存
                changeDrugstoreStock(drugstoreOtcList, drugsOrder);
            }
            if (CollectionUtil.isNotEmpty(enterpriseOtcList)) {
                // 修改配送企业otc药库存
                changeDeliveryEnterpriseStock(enterpriseOtcList, drugsOrder.getHospitalId());
            }
        }
    }

    private void changeDeliveryEnterpriseStock(List<BusPrescriptionDrugs> enterpriseDrugsList, Long hospitalId) {
        //根据处方里面药品ID查询对应药品的库存
        Long enterpriseId = enterpriseDrugsList.get(0).getEnterpriseId();
        HospitalOfficinaDrugsDTO query = new HospitalOfficinaDrugsDTO();
        query.setHospitalId(hospitalId);
        query.setDrugsIds(enterpriseDrugsList.stream().map(BusPrescriptionDrugs::getDrugsId).collect(Collectors.toList()));
        query.setEnterpriseId(enterpriseId);
        List<BusEnterpriseDrugsPrice> enterpriseStocks = busEnterpriseDrugsMapper.selectEnterpriseDrugsListV2(query);

        if (CollectionUtils.isEmpty(enterpriseStocks)) {
            throw new ServiceException("配送方药品缺失");
        }
        for (BusPrescriptionDrugs item : enterpriseDrugsList) {
            for (BusEnterpriseDrugsPrice item1 : enterpriseStocks) {
                if (!item.getDrugsId().equals(item1.getDrugsId()) || item.getQuantity() == 0) {
                    continue;
                }
                log.info("释放库存， 药品信息：{}，释放的库存数量：{}", item1, item.getQuantity());
                busEnterpriseDrugsPriceMapper.updateStockById(item1.getId(), item.getQuantity());
            }
        }
    }

    private void changeDrugstoreStock(List<BusPrescriptionDrugs> drugstoreDrugsList, BusDrugsOrder drugsOrder) {
        BusPrescription prescription = null;
        if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
            prescription = busPrescriptionMapper.selectById(drugsOrder.getPrescriptionId());
        }
        //根据处方里面药品ID查询药房里面的对应药品的库存
        HospitalOfficinaDrugsDTO drugsIdDTO = new HospitalOfficinaDrugsDTO();
        drugsIdDTO.setHospitalId(drugsOrder.getHospitalId());
        drugsIdDTO.setDrugsIds(drugstoreDrugsList.stream().map(BusPrescriptionDrugs::getDrugsId).collect(Collectors.toList()));
        List<BusHospitalOfficina> drugstoreStocks = busHospitalOfficinaService.selectOfficinaList(drugsIdDTO);
        if (CollectionUtils.isEmpty(drugstoreStocks)) {
            throw new ServiceException("药房药品缺失！");
        }
        for (BusPrescriptionDrugs item : drugstoreDrugsList) {
            for (BusHospitalOfficina item1 : drugstoreStocks) {
                BusHospitalOfficina bho = new BusHospitalOfficina();
                bho.setDrugsId(item1.getDrugsId());
                bho.setHospitalId(item1.getHospitalId());
                if (null != prescription) {
                    // 西药释放方式
                    if (CodeEnum.YES.getCode().equals(prescription.getPrescriptionType())) {
                        bho.setStock(item1.getStock() + item.getQuantity());
                        log.info("原始库存+释放数量[" + item1.getStock() + "+" + item.getQuantity() + "]");
                    } else {
                        int js = Integer.parseInt(prescription.getUsages().split(",")[1]);
                        bho.setStock(item1.getStock() + (Integer.parseInt(item.getWeight()) * js));
                        log.info("原始库存+释放数量[" + item1.getStock() + (Integer.parseInt(item.getWeight()) * js) + "]");
                    }
                } else {
                    // 西药释放方式
                    bho.setStock(item1.getStock() + item.getQuantity());
                    log.info("释放库存={}", item1.getStock() + item.getQuantity());
                }
                busHospitalOfficinaService.updateStock(bho);
            }
        }
    }

    @Override
    public int update(BusDrugsOrder busDrugsOrder) {
        return busDrugsOrderMapper.updateById(busDrugsOrder);
    }

    @Override
    public int updateStatus(BusDrugsOrder busDrugsOrder) {
        busDrugsOrder.setUpdateTime(DateUtils.getNowDate());
        return busDrugsOrderMapper.updateStatus(busDrugsOrder);
    }

    @Override
    public BusPrescriptionVo queryDrugsOrderInfo(DrugsOrderDto dto) {
        BusPrescriptionVo vo = new BusPrescriptionVo();
        if (Objects.nonNull(dto.getPrescriptionId())) {
            BusPrescription busPrescription = new BusPrescription();
            busPrescription.setId(dto.getPrescriptionId());
            busPrescription.setHospitalId(dto.getHospitalId());
            //处方信息
            QueryWrapper<BusPrescription> queryWrapper = new QueryWrapper<BusPrescription>()
                    .eq("hospital_id", busPrescription.getHospitalId())
                    .eq("id", busPrescription.getId());
            BusPrescription bp = busPrescriptionMapper.selectOne(queryWrapper);
            if (null == bp) {
                return null;
            }
            vo = OrikaUtils.convert(bp, BusPrescriptionVo.class);
            if (Objects.nonNull(bp.getPaId())) {
                BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(bp.getPaId());
                vo.setZyfjType(busHospitalPa.getType());
                vo.setZyfjAmount(busHospitalPa.getAmount());
            }
            //处方药品列表
            List<BusPrescriptionDrugs> busPrescriptionDrugsList =
                    busPrescriptionDrugsMapper.selectPrescriptonDrugsList(busPrescription.getId());
            /*根据处方id查询订单表的物流信息*/
            vo.setList(busPrescriptionDrugsList);
        } else { // 非处方药
            List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectOtcList(dto.getId());
            List<BusPrescriptionDrugs> drugsList = OrikaUtils.converts(busOtcDrugs, BusPrescriptionDrugs.class);
            drugsList.forEach(p -> p.setPreType(CodeEnum.YES.getCode()));
            vo.setList(drugsList);
        }
        //查询医院信息
        BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectById(dto.getId());
        BusHospital hospital = busHospitalMapper.selectById(dto.getHospitalId());
        if (Objects.nonNull(busDrugsOrder)) {
            vo.setDeliveryNo(busDrugsOrder.getDeliveryNo());
            vo.setLogisticsCompany(busDrugsOrder.getLogisticsCompany());
        }
        vo.setHospitalAddress(hospital.getDetailAddress());
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int applyReplacement(BusAfterSale afterSale) {
        log.info("申请退款信息：afterSake={}", afterSale);
        BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
        busDrugsOrder.setOrderNo(afterSale.getOrderNo());
        busDrugsOrder.setStatus(DrugsOrderEnum.AFTERSALES.getCode());
        this.updateStatus(busDrugsOrder);

        // 查询订单信息
        QueryWrapper<BusDrugsOrder> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("order_no", afterSale.getOrderNo());
        BusDrugsOrder drugsOrder = busDrugsOrderMapper.selectOne(queryWrapper1);

        if (DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getCode().equals(drugsOrder.getStatus())) {
            throw new ServiceException("药品已发货！");
        }

        // 西药配送企业订单
        if (CodeEnum.YES.getCode().equals(drugsOrder.getOrderDrugsType()) && CodeEnum.YES.getCode().equals(drugsOrder.getOrderType())) {
            // 查询药品所属配送企业
            List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectPdList(drugsOrder);
            if (StringUtils.isNotEmpty(busDrugOrderPackages)) {
                busDrugOrderPackages.forEach(dop -> {
                    log.info("配送企业标识=" + dop.getIdentifying());
                    if (EnterpriseEnum.SST.getInfo().equals(dop.getIdentifying())) {
                        // 调用东方红订单查询接口查询子订单号
                        selectDfhSubOrderNo(drugsOrder);
                    }
                });
            }
        }

        afterSale.setApplyTime(DateUtils.getNowDate());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        afterSale.setNumber(simpleDateFormat.format(new Date()));
        afterSale.setStatus(AfterSaleStatus.UNDER_REVIEW);
        afterSale.setExamineTime(DateUtils.getNowDate());
        QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", afterSale.getOrderNo());
        BusAfterSale busAfterSale = afterSaleMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(busAfterSale)) {
            afterSale.setUpdateTime(DateUtils.getNowDate());
            afterSale.setId(busAfterSale.getId());
            return afterSaleMapper.updateById(afterSale);
        } else {
            afterSale.setCreateTime(DateUtils.getNowDate());
            return afterSaleMapper.insert(afterSale);
        }
    }

    @Override
    public BusDrugsOrder queryDrugsOrder(String outTradeNo) {
        QueryWrapper<BusDrugsOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(outTradeNo), "order_no", outTradeNo);
        return busDrugsOrderMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirm(String orderNo) {
        BusOrder busOrder = new BusOrder();
        busOrder.setOrderNo(orderNo);
        busOrder.setOrderStatus(OrderStatusEnum.FINISH.getCode());
        busOrder.setCompleteTime(DateUtils.getNowDate());
        // 更新配送企业订单状态为已完成
        BusEnterpriseProductOrder entOrder = new BusEnterpriseProductOrder();
        entOrder.setStatus(EntOrderStatusEnum.FINISH.getCode());
        entOrder.setUpdateTime(new Date());
        BusOrder shopOrder = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                .eq(BusOrder::getOrderNo, orderNo)
                .eq(BusOrder::getSubOrderType, YesNoEnum.YES.getCode()));
        if (StringUtils.isNotNull(shopOrder)) {
            entOrderMapper.update(entOrder, new LambdaQueryWrapper<BusEnterpriseProductOrder>()
                    .eq(BusEnterpriseProductOrder::getSubOrderId, shopOrder.getSubOrderId()));
        }
        return busOrderService.updateOrderStatus(busOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateRefundStatus(BusDrugsOrder drugsOrder, String orderStatus) {
        if (!DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getCode().equals(orderStatus)) {
            // 释放库存
            releaseStock(drugsOrder);
        }

        // 修改订单状态
        BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
        busDrugsOrder.setUpdateTime(DateUtils.getNowDate());
        busDrugsOrder.setOrderNo(drugsOrder.getOrderNo());
        busDrugsOrder.setStatus(DrugsOrderEnum.REFUNDED.getCode());
        busDrugsOrder.setRefundTime(DateUtils.getNowDate());
        busDrugsOrderMapper.updateStatus(busDrugsOrder);


        // 修改售后状态
        BusAfterSale busAfterSale = new BusAfterSale();
        busAfterSale.setUpdateTime(DateUtils.getNowDate());
        busAfterSale.setStatus(AfterSaleStatus.REFUND_SUCCESS);
        UpdateWrapper<BusAfterSale> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_no", drugsOrder.getOrderNo());
        int update = afterSaleMapper.update(busAfterSale, updateWrapper);
        // 发送商品订单同意退款事件通知
        sendDrugsOrderAgreeRefundEventNotification(drugsOrder);
        return update;
    }

    @Override
    public int remove(String orderNo) {
        BusOrder busOrder = new BusOrder();
        busOrder.setOrderNo(orderNo);
        busOrder.setDelStatus(CodeEnum.YES.getCode());
        busOrder.setUpdateTime(DateUtils.getNowDate());
        return busOrderService.updateOrderStatus(busOrder);
    }

    @Override
    public HospitalVo selectHospitalTel(Long hospitalId) {
        return busDrugsOrderMapper.selectHospitalTel(hospitalId);
    }

    @Override
    public List<BusDrugsOrder> selectOrderNoList(Long hospitalId) {
        QueryWrapper<BusDrugsOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", hospitalId)
                .eq("order_type", "1")
                .eq("order_drugs_type", "1") // 西药
                .inSql("status", "1,2");
        return busDrugsOrderMapper.selectList(queryWrapper);
    }

    @Override
    public BusOrder checkPayment(Long hospitalId, Long id) {
        QueryWrapper<BusDrugsOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", hospitalId)
                .eq("prescription_id", id)
                .orderByDesc("order_time")
                .last("limit 1");
        BusDrugsOrder drugsOrder = busDrugsOrderMapper.selectOne(queryWrapper);
        if (!Objects.isNull(drugsOrder)) {
            BusOrder order = orderMapper.selectOne(
                    new LambdaQueryWrapper<BusOrder>()
                            .eq(BusOrder::getSubOrderType, CodeEnum.NO.getCode())
                            .eq(BusOrder::getSubOrderId, drugsOrder.getId()));
            return order;
        }
        return null;
    }

    @Override
    public String selectPatientInfo(String orderNo) {
        String openid = busDrugsOrderMapper.selectPatientInfo(orderNo);
        log.info("医院患者openid=" + openid);
        return openid;
    }

    @Override
    public List<OrderPrescriptionVo> selectPrescriptionList() {
        return busDrugsOrderMapper.selectPrescriptionList();
    }

    @Override
    public List<BusDrugsOrder> queryXXDeliverStatus(DrugsOrderDto dto) {
        return busDrugsOrderMapper.queryXXDeliverStatus(dto);
    }

    @Override
    public List<BusDrugsOrder> queryZXDeliverStatus(DrugsOrderDto dto) {
        return busDrugsOrderMapper.queryZXDeliverStatus(dto);
    }

    @Override
    public BusDrugsOrder selectOrderInfo(Long subOrderId) {
        return busDrugsOrderMapper.selectById(subOrderId);
    }

    /**
     * 新增药品订单
     *
     * @param busDrugsOrder
     * @return
     */
    @Override
    public int addDrugsOrder(BusDrugsOrder busDrugsOrder) {
        Date nowDate = DateUtils.getNowDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        busDrugsOrder.setOrderNo(OrderTypeConstant.DRUGS_ORDER + simpleDateFormat.format(nowDate));
        busDrugsOrder.setOrderTime(nowDate);
        busDrugsOrder.setCreateTime(nowDate);
        return busDrugsOrderMapper.insert(busDrugsOrder);
    }

    /**
     * 修改订单状态
     *
     * @param drugsOrder
     */
    @Override
    public int updateOrderStatus(BusDrugsOrder drugsOrder) {
        return busDrugsOrderMapper.updateById(drugsOrder);
    }

    /**
     * 调用东方红查询子订单号
     *
     * @param drugsOrder
     */
    private void selectDfhSubOrderNo(BusDrugsOrder drugsOrder) {
        OrderQueryDto orderQueryDto = new OrderQueryDto();
        orderQueryDto.setPageNo(1);
        orderQueryDto.setPageSize(10);
        orderQueryDto.setPlatformCode(drugsOrder.getOrderNo());
        R<JSONObject> objectR = remoteDFHService.getOrderInfo(orderQueryDto);
        JSONObject orderInfo = objectR.getData();
        Boolean success = orderInfo.getBoolean("success");
        log.info("调用东方红查询订单是否成功={}", success);
        String oid = "";
        if (!success) {
            throw new ServiceException("调用东方红查询订单失败！");
        }
        JSONArray orders = orderInfo.getJSONArray("orders");
        if (orders.size() > 0) {
            JSONObject o1 = orders.getJSONObject(orders.size() - 1);
            JSONArray details = o1.getJSONArray("details");
            if (details.size() > 0) {
                JSONObject jsonObject = details.getJSONObject(details.size() - 1);
                oid = jsonObject.getString("oid");
            }
        }
        // 调用东方红订单退款状态修改接口
        if (StringUtils.isNotEmpty(oid)) {
            DFHOrder dfhOrder = new DFHOrder();
            dfhOrder.setPlatformCode(drugsOrder.getOrderNo());
            dfhOrder.setOid(oid);
            dfhOrder.setRefundState(2); // 退款中
            R<JSONObject> refundUpdate = remoteDFHService.refundUpdate(dfhOrder);
            JSONObject updateData = refundUpdate.getData();
            Boolean success1 = updateData.getBoolean("success");
            if (!success1) {
                throw new ServiceException("调用东方红订单退款状态修改失败！");
            }
        } else {
            throw new ServiceException("子订单号为空！");
        }
    }

    /**
     * 处方药释放库存
     * 非处方药扣减/释放库存
     *
     * @param prescriptionId
     * @param drugsOrderId
     * @param hospitalId
     * @param type
     */
    private void changeStock(Long prescriptionId, Long drugsOrderId, Long hospitalId, String type) {
        List<BusPrescriptionDrugs> drugsList;
        BusPrescription prescription = null;
        if (StringUtils.isNotNull(prescriptionId)) {
            // 查询处方药品信息
            LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusPrescriptionDrugs::getHospitalId, hospitalId);
            lambdaQuery.eq(BusPrescriptionDrugs::getPrescriptionId, prescriptionId);
            lambdaQuery.isNull(BusPrescriptionDrugs::getEnterpriseId);
            drugsList = busPrescriptionDrugsMapper.selectList(lambdaQuery);
            if (StringUtils.isEmpty(drugsList)) {
                throw new ServiceException("处方药品缺失！");
            }
            prescription = busPrescriptionMapper.selectById(prescriptionId);
        } else {
            // 查询非处方药品信息
            LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusOtcDrugs::getHospitalId, hospitalId);
            lambdaQuery.eq(BusOtcDrugs::getDrugsOrderId, drugsOrderId);
            lambdaQuery.isNull(BusOtcDrugs::getEnterpriseId);
            List<BusOtcDrugs> otcDrugsList = busOtcDrugsMapper.selectList(lambdaQuery);
            if (StringUtils.isEmpty(otcDrugsList)) {
                throw new ServiceException("非处方药品缺失！");
            }
            drugsList = OrikaUtils.converts(otcDrugsList, BusPrescriptionDrugs.class);
        }
        if (StringUtils.isNotEmpty(drugsList)) {
            //根据处方里面药品ID查询药房里面的对应药品的库存
            HospitalOfficinaDrugsDTO drugsIdDTO = new HospitalOfficinaDrugsDTO();
            drugsIdDTO.setDrugsIds(drugsList.stream().map(BusPrescriptionDrugs::getDrugsId).collect(Collectors.toList()));
            drugsIdDTO.setHospitalId(hospitalId);
            List<BusHospitalOfficina> drugsStocks = busHospitalOfficinaService.selectOfficinaList(drugsIdDTO);
            if (CollectionUtils.isEmpty(drugsStocks)) {
                throw new ServiceException("药房药品缺失！");
            }
            //变更库存
            for (BusPrescriptionDrugs item : drugsList) {
                for (BusHospitalOfficina item1 : drugsStocks) {
                    BusHospitalOfficina bho = new BusHospitalOfficina();
                    bho.setDrugsId(item1.getDrugsId());
                    bho.setHospitalId(item1.getHospitalId());
                    if (null != prescription) {
                        // 西药释放方式
                        if (CodeEnum.YES.getCode().equals(prescription.getPrescriptionType())) {
                            bho.setStock(item1.getStock() + item.getQuantity());
                            log.info("原始库存+释放数量[" + item1.getStock() + "+" + item.getQuantity() + "]");
                        } else {
                            int js = Integer.parseInt(prescription.getUsages().split(",")[1]);
                            bho.setStock(item1.getStock() + (Integer.parseInt(item.getWeight()) * js));
                            log.info("原始库存+释放数量[" + item1.getStock() + (Integer.parseInt(item.getWeight()) * js) + "]");
                        }
                    } else {
                        if (StockEnum.REDUCE.getName().equals(type)) {
                            // 西药扣减方式
                            int stock = item1.getStock() - item.getQuantity();
                            if (stock < 0) {
                                throw new ServiceException("库存不足");
                            }
                            bho.setStock(stock);
                            log.info("扣减库存数={}", item1.getStock() - item.getQuantity());
                        } else {
                            // 西药释放方式
                            bho.setStock(item1.getStock() + item.getQuantity());
                            log.info("释放库存={}", item1.getStock() + item.getQuantity());
                        }
                    }
                    busHospitalOfficinaService.updateStock(bho);
                }
            }
        }
    }

    /**
     * 配送企业扣减/释放库存
     *
     * @param prescriptionId
     * @param drugsOrderId
     * @param hospitalId
     * @param enterpriseId
     * @param type
     * @return
     */
    private int changeEnterpriseStock(Long prescriptionId, Long drugsOrderId, Long hospitalId, Long enterpriseId, String type) {
        List<BusPrescriptionDrugs> drugsList;
        if (StringUtils.isNotNull(prescriptionId)) {
            // 查询处方药品信息
            LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusPrescriptionDrugs::getHospitalId, hospitalId);
            lambdaQuery.eq(BusPrescriptionDrugs::getPrescriptionId, prescriptionId);
            lambdaQuery.eq(BusPrescriptionDrugs::getEnterpriseId, enterpriseId);
            drugsList = busPrescriptionDrugsMapper.selectList(lambdaQuery);
            if (CollectionUtils.isEmpty(drugsList)) {
                throw new ServiceException("处方药品缺失！");
            }
        } else {
            // 查询非处方药品信息
            LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusOtcDrugs::getHospitalId, hospitalId);
            lambdaQuery.eq(BusOtcDrugs::getDrugsOrderId, drugsOrderId);
            lambdaQuery.eq(BusOtcDrugs::getEnterpriseId, enterpriseId);
            List<BusOtcDrugs> otcDrugsList = busOtcDrugsMapper.selectList(lambdaQuery);
            if (StringUtils.isEmpty(otcDrugsList)) {
                throw new ServiceException("非处方药品缺失！");
            }
            drugsList = OrikaUtils.converts(otcDrugsList, BusPrescriptionDrugs.class);
        }

        if (CollectionUtils.isEmpty(drugsList)) {
            return 1;
        }

        //根据处方里面药品ID查询对应药品的库存
        HospitalOfficinaDrugsDTO query = new HospitalOfficinaDrugsDTO();
        query.setHospitalId(hospitalId);
        query.setDrugsIds(drugsList.stream().map(BusPrescriptionDrugs::getDrugsId).collect(Collectors.toList()));
        query.setEnterpriseId(enterpriseId);
        List<BusEnterpriseDrugsPrice> drugsStocks = busEnterpriseDrugsMapper.selectEnterpriseDrugsListV2(query);

        if (CollectionUtils.isEmpty(drugsStocks)) {
            log.error("配送方药品缺失：{}", query);
            throw new ServiceException("配送方药品缺失");
        }
        //变更库存
        for (BusPrescriptionDrugs item : drugsList) {
            for (BusEnterpriseDrugsPrice item1 : drugsStocks) {
                if (!item.getDrugsId().equals(item1.getDrugsId()) || item.getQuantity() == 0) {
                    continue;
                }
                Integer stock = item.getQuantity();
                //扣减库存
                if (StockEnum.REDUCE.getName().equals(type)) {
                    stock = -item.getQuantity();
                }
                log.info("配送企业库存数={}，购买数量={}", item1.getStock(), stock);
                int count = busEnterpriseDrugsPriceMapper.updateStockById(item1.getId(), stock);
                if (count < 1) {
                    log.error("药品库存不足：{}, 变更的库存数量：{}", item1, stock);
                    throw new ServiceException("药品库存不足");
                }
            }
        }

        return 1;
    }

    /**
     * 校验非处方药库存
     *
     * @param busDrugsOrder
     * @return
     */
    @Override
    public Map<String, Object> checkOtcDrugsStock(BusDrugsOrder busDrugsOrder) {
        Map<String, Object> map = new HashMap<>();
        List<Long> noStockDrugsIds = new ArrayList<>();
        // 提交的药品信息
        List<BusOtcDrugs> otcDrugs = busDrugsOrder.getList();
        List<BusOtcDrugs> newOtc = new ArrayList<>();
        // 查询医院发货设置信息
        BusConsultationSettings consultationSettings = new BusConsultationSettings();
        consultationSettings.setHospitalId(busDrugsOrder.getHospitalId());
        BusConsultationSettings settings = busConsultationSettingsService.selectOne(consultationSettings);
        if (StringUtils.isNull(settings)) {
            throw new ServiceException("医院发货设置缺失！");
        }
        // 查询药房药品库存
        HospitalOfficinaDrugsDTO drugsIdDTO = new HospitalOfficinaDrugsDTO();
        drugsIdDTO.setDrugsIds(busDrugsOrder.getList().stream().map(BusOtcDrugs::getDrugsId).collect(Collectors.toList()));
        drugsIdDTO.setHospitalId(busDrugsOrder.getHospitalId());
        List<BusHospitalOfficina> drugsStocks = busHospitalOfficinaService.selectOfficinaList(drugsIdDTO);
        if (CollectionUtils.isEmpty(drugsStocks)) {
            throw new ServiceException("药房药品缺失");
        }
        // 药房优先发货
        if (CodeEnum.NO.getCode().equals(settings.getSendPrior())) {
            for (BusOtcDrugs buyDrugs : otcDrugs) {
                for (BusHospitalOfficina officinaDrugs : drugsStocks) {
                    if (officinaDrugs.getDrugsId().equals(buyDrugs.getDrugsId())) {
                        Integer officinaQuantity = buyDrugs.getQuantity();
                        if (officinaDrugs.getStock() >= officinaQuantity) {
                            buyDrugs.setEnterpriseId(null);
                        } else {
                            // 判断该药品是否设置过配送企业
                            if (StringUtils.isNull(officinaDrugs.getEnterpriseId())) {
                                noStockDrugsIds.add(officinaDrugs.getDrugsId());
                                break;
                            } else {
                                // 查询配送企业标识
                                String identifying = busEnterpriseDrugsMapper.selectEnterpriseIdentifying(officinaDrugs.getEnterpriseId());
                                BusPrescriptionDrugs drugStock = new BusPrescriptionDrugs();
                                drugStock.setDrugsId(buyDrugs.getDrugsId());
                                drugStock.setQuantity(officinaQuantity);
                                int enterpriseStock = supplierFactory.getProcessor(identifying)
                                        .judgeSingleStock(busDrugsOrder.getHospitalId(), officinaDrugs.getEnterpriseId(), drugStock);
                                // 配送企业库存不足
                                if (enterpriseStock < officinaQuantity) {
                                    // 判断药房库存+配送企业库存是否满足购买数量
                                    int stock = officinaDrugs.getStock() + enterpriseStock;
                                    if (officinaQuantity > stock) {
                                        noStockDrugsIds.add(officinaDrugs.getDrugsId());
                                        break;
                                    } else {
                                        // 先扣减药房库存
                                        buyDrugs.setEnterpriseId(null);
                                        buyDrugs.setQuantity(officinaDrugs.getStock());
                                        // 后扣减配送企业库存
                                        BusOtcDrugs drugs = OrikaUtils.convert(buyDrugs, BusOtcDrugs.class);
                                        drugs.setQuantity(officinaQuantity - officinaDrugs.getStock());
                                        drugs.setEnterpriseId(officinaDrugs.getEnterpriseId());
                                        newOtc.add(drugs);
                                    }
                                } else {
                                    buyDrugs.setEnterpriseId(officinaDrugs.getEnterpriseId());
                                }
                            }
                        }
                        buyDrugs.setDrugsOrderId(busDrugsOrder.getId());
                        buyDrugs.setCreateTime(DateUtils.getNowDate());
                    }
                }
            }
            if (StringUtils.isNotEmpty(noStockDrugsIds)) {
                map.put("drugsOrderId", null);
                map.put("errorDrugsId", noStockDrugsIds);
                return map;
            }
        } else { // 配送企业优先发货
            for (BusHospitalOfficina officinaDrugs : drugsStocks) {
                for (BusOtcDrugs buyDrugs : otcDrugs) {
                    if (officinaDrugs.getDrugsId().equals(buyDrugs.getDrugsId())) {
                        Integer officinaQuantity = buyDrugs.getQuantity();
                        if (StringUtils.isNull(officinaDrugs.getEnterpriseId())) {
                            if (officinaQuantity > officinaDrugs.getStock()) {
                                noStockDrugsIds.add(officinaDrugs.getDrugsId());
                            } else {
                                buyDrugs.setEnterpriseId(null);
                            }
                        } else {
                            BusPrescriptionDrugs pd = new BusPrescriptionDrugs();
                            pd.setDrugsId(buyDrugs.getDrugsId());
                            pd.setQuantity(officinaQuantity);
                            int enterpriseStock = supplierFactory.getProcessor(officinaDrugs.getIdentifying())
                                    .judgeSingleStock(busDrugsOrder.getHospitalId(), officinaDrugs.getEnterpriseId(), pd);
                            // 配送企业库存不足
                            if (enterpriseStock < officinaQuantity) {
                                // 药房库存不足
                                if (officinaQuantity > officinaDrugs.getStock()) {
                                    // 判断药房库存+配送企业库存是否满足购买数量
                                    int stock = officinaDrugs.getStock() + enterpriseStock;
                                    if (officinaQuantity > stock) {
                                        noStockDrugsIds.add(officinaDrugs.getDrugsId());
                                        break;
                                    } else {
                                        // 先扣减配送企业库存
                                        buyDrugs.setEnterpriseId(officinaDrugs.getEnterpriseId());
                                        buyDrugs.setQuantity(enterpriseStock);
                                        // 先扣减药房库存
                                        BusOtcDrugs drugs = OrikaUtils.convert(buyDrugs, BusOtcDrugs.class);
                                        drugs.setQuantity(officinaQuantity - enterpriseStock);
                                        drugs.setEnterpriseId(null);
                                        newOtc.add(drugs);
                                    }
                                } else {
                                    buyDrugs.setEnterpriseId(null);
                                }
                            } else {
                                buyDrugs.setEnterpriseId(officinaDrugs.getEnterpriseId());
                            }
                        }
                        buyDrugs.setDrugsOrderId(busDrugsOrder.getId());
                        buyDrugs.setCreateTime(DateUtils.getNowDate());
                    }
                }
            }
            if (StringUtils.isNotEmpty(noStockDrugsIds)) {
                map.put("drugsOrderId", null);
                map.put("errorDrugsId", noStockDrugsIds);
                return map;
            }
        }
        if (StringUtils.isNotEmpty(newOtc)) {
            newOtc.forEach(otc -> otc.setDrugsOrderId(busDrugsOrder.getId()));
            otcDrugs.addAll(newOtc);
        }
        otcDrugs.forEach(od -> {
            if (StringUtils.isNotNull(od.getEnterpriseId())) {
                // 查询配送企业成本价
                BusEnterpriseDrugsPrice drugsPrice = busEnterpriseDrugsPriceMapper.getDrugsInfoByDrugsId(busDrugsOrder.getHospitalId(), od.getEnterpriseId(), od.getDrugsId());
                if (StringUtils.isNotNull(drugsPrice)) {
                    od.setReferencePurchasePrice(drugsPrice.getReferencePurchasePrice().doubleValue());
                }
            }
            if (StringUtils.isNotEmpty(od.getMainImg())) {
                od.setDrugsImg(od.getMainImg());
            }
            od.setHospitalId(busDrugsOrder.getHospitalId());
        });
        busOtcDrugsMapper.batchInsert(otcDrugs);
        // 扣减库存
        deductionOtcStock(busDrugsOrder);
        return map;
    }

    /**
     * 查询患者端订单列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<OrderVo> queryList4Patient(DrugsOrderDto dto) {
        // 查询订单信息
        List<OrderVo> orders = busOrderService.selectOrderList(dto);
        if (CollectionUtil.isNotEmpty(orders)) {
            for (OrderVo order : orders) {
                List<OrderVo> orderVos = busOrderService.selectOrderInfo(order.getOrderNo());
                OrderVo orderVo = orderVos.get(0);
                List<SubOrderVO> subOrderList = orderVo.getSubOrderList();
                // 未拆单
                if (subOrderList.size() == 1) {
                    if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                        // 查询药品订单详细信息
                        DrugsOrderVo drugsOrderVo = busDrugsOrderMapper.selectDrugsDetail(order.getSubOrderId());
                        drugsOrderVo.setHospitalId(orderVo.getHospitalId());
                        drugsOrderVo.setHospitalName(orderVo.getHospitalName());
                        drugsOrderVo.setOrderNo(order.getOrderNo());
                        drugsOrderVo.setStatus(orderVo.getOrderStatus());
                        drugsOrderVo.setAmount(orderVo.getRelPrice());
                        drugsOrderVo.setOrderTime(orderVo.getOrderTime());
                        drugsOrderVo.setPaymentTime(orderVo.getPaymentTime());
                        drugsOrderVo.setCancelTime(orderVo.getCancelTime());
                        drugsOrderVo.setCompleteTime(orderVo.getCompleteTime());
                        drugsOrderVo.setPartnersCode(orderVo.getPartnersCode());
                        drugsOrderVo.setPayWay(orderVo.getPayWay());
                        // 查询药品订单关联药品信息
                        queryDrugsAssociationInfo(drugsOrderVo);
                        // 查询药品订单关联包裹信息
                        drugsOrderVo.setDeliveryType(orderVo.getDeliveryType());
                        drugsOrderVo.setChangePrice(orderVo.getChangePrice());
                        List<BusDrugOrderPackage> packageList = queryDrugsPackageInfo(drugsOrderVo);
                        if (CollectionUtil.isNotEmpty(packageList)) {
                            drugsOrderVo.setDeliveryTime(packageList.get(0).getDeliveryTime());
                        }
                        order.setDrugsOrderVo(drugsOrderVo);
                        order.setType(0);
                    } else {
                        // 查询商品订单详细信息
                        BusShopOrderVO shopOrderVO = busOrderService.selectGoodsDetail(order.getSubOrderId());
                        shopOrderVO.setHospitalId(orderVo.getHospitalId());
                        shopOrderVO.setHospitalName(orderVo.getHospitalName());
                        shopOrderVO.setOrderNo(order.getOrderNo());
                        shopOrderVO.setStatus(orderVo.getOrderStatus());
                        shopOrderVO.setAmount(orderVo.getRelPrice());
                        shopOrderVO.setOrderTime(orderVo.getOrderTime());
                        shopOrderVO.setPaymentTime(orderVo.getPaymentTime());
                        shopOrderVO.setCancelTime(orderVo.getCancelTime());
                        shopOrderVO.setCompleteTime(orderVo.getCompleteTime());
                        shopOrderVO.setPartnersCode(orderVo.getPartnersCode());
                        shopOrderVO.setPayWay(orderVo.getPayWay());
                        // 查询商品订单关联包裹信息
                        shopOrderVO.setDeliveryType(orderVo.getDeliveryType());
                        shopOrderVO.setChangePrice(orderVo.getChangePrice());
                        List<BusDrugOrderPackage> packageList = queryGoodsPackageInfo(shopOrderVO);
                        if (CollectionUtil.isNotEmpty(packageList)) {
                            shopOrderVO.setDeliveryTime(packageList.get(0).getDeliveryTime());
                        } else {
                            shopOrderVO.setDeliveryTime(order.getDeliveryTime());
                        }
                        order.setShopOrderVO(shopOrderVO);
                        order.setType(1);
                    }
                } else { // 拆单（药品和商品）
                    DrugsAndGoodsOrderVO orderVO = new DrugsAndGoodsOrderVO();
                    orderVO.setHospitalId(orderVo.getHospitalId());
                    orderVO.setHospitalName(orderVo.getHospitalName());
                    orderVO.setOrderNo(order.getOrderNo());
                    orderVO.setStatus(orderVo.getOrderStatus());
                    orderVO.setAmount(orderVo.getRelPrice());
                    orderVO.setOrderTime(orderVo.getOrderTime());
                    orderVO.setPaymentTime(orderVo.getPaymentTime());
                    orderVO.setCancelTime(orderVo.getCancelTime());
                    orderVO.setCompleteTime(orderVo.getCompleteTime());
                    orderVO.setPartnersCode(orderVo.getPartnersCode());
                    orderVO.setPayWay(orderVo.getPayWay());
                    // 定义包裹集合
                    List<BusDrugOrderPackage> orderPackages = new ArrayList<>();
                    // 定义药品商品信息集合
                    List<DrugsAndGoodsVO> list = new ArrayList<>();
                    Long id = null;
                    List<Long> prescriptionIds = new ArrayList<>();
                    for (SubOrderVO subOrder : subOrderList) {
                        if (CodeEnum.NO.getCode().equals(subOrder.getSubOrderType())) {
                            // 查询药品订单详细信息
                            DrugsOrderVo drugsOrderVo = busDrugsOrderMapper.selectDrugsDetail(subOrder.getSubOrderId());
                            prescriptionIds.add(drugsOrderVo.getPrescriptionId());
                            // 查询药品订单关联药品信息
                            queryDrugsAssociationInfo(drugsOrderVo);
                            id = drugsOrderVo.getId();
                            List<BusPrescriptionDrugs> drugsList = drugsOrderVo.getList();
                            for (BusPrescriptionDrugs drugs : drugsList) {
                                DrugsAndGoodsVO goodsVO = new DrugsAndGoodsVO();
                                goodsVO.setDrugsImg(drugs.getDrugsImg());
                                if (StringUtils.isNotEmpty(drugs.getStandardCommonName())) {
                                    goodsVO.setDrugsType("1");
                                } else {
                                    goodsVO.setDrugsType("0");
                                }
                                goodsVO.setPreType(drugs.getPreType());
                                goodsVO.setBusinessId(drugs.getDrugsId());
                                goodsVO.setQuantity(drugs.getQuantity());
                                list.add(goodsVO);
                            }
                            // 查询药品订单关联包裹信息
                            drugsOrderVo.setDeliveryType(orderVo.getDeliveryType());
                            drugsOrderVo.setChangePrice(orderVo.getChangePrice());
                            List<BusDrugOrderPackage> packageList = queryDrugsPackageInfo(drugsOrderVo);
                            orderPackages.addAll(packageList);
                        } else {
                            // 查询商品订单详细信息
                            BusShopOrderVO shopOrderVO = busOrderService.selectGoodsDetail(subOrder.getSubOrderId());
                            List<BusOrderShopVO> goodsList = shopOrderVO.getList();
                            for (BusOrderShopVO goods : goodsList) {
                                DrugsAndGoodsVO goodsVO = new DrugsAndGoodsVO();
                                goodsVO.setDrugsImg(goods.getDrugsImg());
                                goodsVO.setDrugsType(null);
                                goodsVO.setBusinessId(goods.getDrugsId());
                                goodsVO.setQuantity(goods.getQuantity());
                                goodsVO.setShopType(goods.getShopType());
                                list.add(goodsVO);
                            }
                            // 查询商品订单关联包裹信息
                            shopOrderVO.setDeliveryType(orderVo.getDeliveryType());
                            shopOrderVO.setChangePrice(orderVo.getChangePrice());
                            List<BusDrugOrderPackage> packageList = queryGoodsPackageInfo(shopOrderVO);
                            orderPackages.addAll(packageList);
                        }
                    }
                    if (StringUtils.isNotEmpty(orderPackages)) {
                        orderPackages = orderPackages.stream()
                                .sorted(Comparator.comparing(BusDrugOrderPackage::getDeliveryTime)).collect(Collectors.toList());
                        orderVO.setDeliveryTime(orderPackages.get(0).getDeliveryTime());
                    }
                    orderVO.setId(id);
                    orderVO.setList(list);
                    orderVO.setPrescriptionIds(prescriptionIds);
                    order.setOrderVO(orderVO);
                    order.setType(2);
                }
            }
        }
        return orders;
    }

    /**
     * 查询商品包裹信息
     *
     * @param o
     */
    private List<BusDrugOrderPackage> queryGoodsPackageInfo(BusShopOrderVO o) {
        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, o.getHospitalId());
        lambdaQuery.eq(BusDrugOrderPackage::getPackageType, YesNoEnum.YES.getCode());
        lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, o.getId());
        // 判断是自提还是快递
        if (CodeEnum.NO.getCode().equals(o.getDeliveryType())) {
            lambdaQuery.isNotNull(BusDrugOrderPackage::getCode);
        } else {
            lambdaQuery.isNotNull(BusDrugOrderPackage::getDeliveryNo);
        }
        // 判断是否是换货单
        if (o.getAmount().compareTo(new BigDecimal(0)) == 0 && YesNoEnum.NO.getCode().equals(o.getChangePrice())) {
            lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
        } else {
            lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
        }
        lambdaQuery.orderByAsc(BusDrugOrderPackage::getDeliveryTime);
        return busDrugOrderPackageMapper.selectList(lambdaQuery);
    }

    /**
     * 查询药品包裹信息
     *
     * @param o
     */
    private List<BusDrugOrderPackage> queryDrugsPackageInfo(DrugsOrderVo o) {
        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, o.getHospitalId());
        lambdaQuery.eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode());
        // 判断是处方药还是otc药
        if (Objects.nonNull(o.getPrescriptionId())) {
            lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, o.getPrescriptionId());
        } else {
            lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, o.getId());
        }
        // 判断是自提还是快递
        if (CodeEnum.NO.getCode().equals(o.getDirectoryType())) {
            lambdaQuery.isNotNull(BusDrugOrderPackage::getCode);
        } else {
            lambdaQuery.isNotNull(BusDrugOrderPackage::getDeliveryNo);
        }
        // 判断是否是换货单
        if (o.getAmount().compareTo(new BigDecimal(0)) == 0 && YesNoEnum.NO.getCode().equals(o.getChangePrice())) {
            lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
        } else {
            lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
        }
        lambdaQuery.orderByAsc(BusDrugOrderPackage::getDeliveryTime);
        return busDrugOrderPackageMapper.selectList(lambdaQuery);
    }

    /**
     * 查询患者端药品/商品订单详情
     *
     * @param dto
     * @return
     */
    @Override
    public List<OrderDetailVO> queryOrderDrugsAndGoodsDetail(DrugsOrderDto dto) {
        List<OrderDetailVO> orderDetailList = new ArrayList<>();
        String orderNo = dto.getOrderNo();
        // 查询总订单信息
        List<BusOrder> orderList = busOrderService.selectDrugsAndGoodsList(orderNo);
        if (CollectionUtil.isNotEmpty(orderList)) {
            for (BusOrder order : orderList) {
                if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                    handleDrugsOrder(orderDetailList, order.getDrugsOrderNo());
                } else {
                    handleGoodsOrder(orderDetailList, order.getGoodsOrderNo());
                }
            }
        }
        return orderDetailList;
    }

    /**
     * 处理商品订单信息
     *
     * @param orderDetailList
     * @param orderNo
     */
    private void handleGoodsOrder(List<OrderDetailVO> orderDetailList, String orderNo) {
        // 查询订单及商品信息
        BusShopOrderVO shopOrderVO = busOrderService.selectOrderAndGoodsDetail(orderNo);
        List<BusOrderShopVO> orderShopVOS = shopOrderVO.getList();
        if (StringUtils.isEmpty(orderDetailList)) {
            OrderDetailVO orderDetailVO = new OrderDetailVO();
            orderDetailVO.setTitleName(shopOrderVO.getHospitalName() + "药房");
            List<DrugsAndGoodsDetailVO> drugsAndGoodsDetailVOS = new ArrayList<>();
            for (BusOrderShopVO shop : orderShopVOS) {
                DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                drugsAndGoodsDetailVO.setImg(shop.getDrugsImg());
                drugsAndGoodsDetailVO.setName(shop.getDrugsName());
                drugsAndGoodsDetailVO.setSpecification(shop.getDrugsSpecification());
                drugsAndGoodsDetailVO.setQuantity(shop.getQuantity());
                drugsAndGoodsDetailVO.setSellingPrice(shop.getSellingPrice());
                drugsAndGoodsDetailVO.setOrderId(shop.getOrderId());
                drugsAndGoodsDetailVO.setBusinessId(shop.getDrugsId());
                drugsAndGoodsDetailVO.setBusinessType(YesNoEnum.YES.getCode());
                drugsAndGoodsDetailVO.setShopType(shop.getShopType());
                // 查询该商品是否存在售后
                checkGoodsOrOtcAfterInfo(CodeEnum.YES.getCode(), shop.getOrderId(), shop.getDrugsId(), drugsAndGoodsDetailVO);
                drugsAndGoodsDetailVOS.add(drugsAndGoodsDetailVO);
            }
            orderDetailVO.setDetailVOList(drugsAndGoodsDetailVOS);
            orderDetailList.add(orderDetailVO);
        } else {
            for (int i = 0; i < orderDetailList.size(); i++) {
                OrderDetailVO detailVO = orderDetailList.get(i);
                boolean flag = true;
                // 不是处方单
                if (StringUtils.isEmpty(detailVO.getType())) {
                    detailVO.setTitleName(shopOrderVO.getHospitalName() + "药房");
                    List<DrugsAndGoodsDetailVO> detailVOList = detailVO.getDetailVOList();
                    for (BusOrderShopVO shop : orderShopVOS) {
                        DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                        drugsAndGoodsDetailVO.setImg(shop.getDrugsImg());
                        drugsAndGoodsDetailVO.setName(shop.getDrugsName());
                        drugsAndGoodsDetailVO.setSpecification(shop.getDrugsSpecification());
                        drugsAndGoodsDetailVO.setQuantity(shop.getQuantity());
                        drugsAndGoodsDetailVO.setSellingPrice(shop.getSellingPrice());
                        drugsAndGoodsDetailVO.setOrderId(shop.getOrderId());
                        drugsAndGoodsDetailVO.setBusinessId(shop.getDrugsId());
                        drugsAndGoodsDetailVO.setBusinessType(YesNoEnum.YES.getCode());
                        drugsAndGoodsDetailVO.setShopType(shop.getShopType());
                        // 查询该商品是否存在售后
                        checkGoodsOrOtcAfterInfo(CodeEnum.YES.getCode(), shop.getOrderId(), shop.getDrugsId(), drugsAndGoodsDetailVO);
                        detailVOList.add(drugsAndGoodsDetailVO);
                        flag = false;
                    }
                    detailVO.setDetailVOList(detailVOList);
                }
                if (i + 1 == orderDetailList.size() && flag) {
                    OrderDetailVO orderDetailVO = new OrderDetailVO();
                    orderDetailVO.setTitleName(shopOrderVO.getHospitalName() + "药房");
                    List<DrugsAndGoodsDetailVO> drugsAndGoodsDetailVOS = new ArrayList<>();
                    for (BusOrderShopVO shop : orderShopVOS) {
                        DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                        drugsAndGoodsDetailVO.setImg(shop.getDrugsImg());
                        drugsAndGoodsDetailVO.setName(shop.getDrugsName());
                        drugsAndGoodsDetailVO.setSpecification(shop.getDrugsSpecification());
                        drugsAndGoodsDetailVO.setQuantity(shop.getQuantity());
                        drugsAndGoodsDetailVO.setSellingPrice(shop.getSellingPrice());
                        drugsAndGoodsDetailVO.setOrderId(shop.getOrderId());
                        drugsAndGoodsDetailVO.setBusinessId(shop.getDrugsId());
                        drugsAndGoodsDetailVO.setBusinessType(YesNoEnum.YES.getCode());
                        drugsAndGoodsDetailVO.setShopType(shop.getShopType());
                        // 查询该商品是否存在售后
                        checkGoodsOrOtcAfterInfo(CodeEnum.YES.getCode(), shop.getOrderId(), shop.getDrugsId(), drugsAndGoodsDetailVO);
                        drugsAndGoodsDetailVOS.add(drugsAndGoodsDetailVO);
                    }
                    orderDetailVO.setDetailVOList(drugsAndGoodsDetailVOS);
                    orderDetailList.add(orderDetailVO);
                    break;
                }
            }
        }
    }

    /**
     * 处理药品订单信息
     *
     * @param orderDetailList 订单明细
     * @param orderNo         订单编号
     */
    private void handleDrugsOrder(List<OrderDetailVO> orderDetailList, String orderNo) {
        OrderDetailVO orderDetailVO = new OrderDetailVO();
        // 查询订单信息
        LambdaQueryWrapper<BusDrugsOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugsOrder::getOrderNo, orderNo);
        BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectOne(lambdaQuery);
        // 处方药
        if (Objects.nonNull(busDrugsOrder.getPrescriptionId())) {
            orderDetailVO.setPrescriptionId(busDrugsOrder.getPrescriptionId());
            orderDetailVO.setOrderId(busDrugsOrder.getId());
            orderDetailVO.setExaminationFee(busDrugsOrder.getExaminationFee());
            orderDetailVO.setExaminationName(busDrugsOrder.getExaminationName());
            // 查询药品订单详情处方药品信息
            List<BusPrescriptionDrugs> drugsList = busPrescriptionDrugsMapper.queryDrugsList(busDrugsOrder.getPrescriptionId());
            BusPrescriptionDrugs prescriptionDrugs = drugsList.get(0);
            orderDetailVO.setTitleName(prescriptionDrugs.getDoctorName() + "医生开具的处方");
            List<DrugsAndGoodsDetailVO> drugsAndGoodsDetail = new ArrayList<>();
            if (PrescriptionTypeEnum.isTcm(prescriptionDrugs.getPrescriptionType())) {
                String usages = prescriptionDrugs.getUsages();
                String[] split = usages.split(",");
                // 获取中药剂数
                Integer tcmAgents = Integer.valueOf(split[1]);
                for (BusPrescriptionDrugs drugs : drugsList) {
                    DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                    drugsAndGoodsDetailVO.setName(drugs.getDrugsName());
                    drugsAndGoodsDetailVO.setSpecification(drugs.getDrugsSpecification());
                    drugsAndGoodsDetailVO.setQuantity(tcmAgents);
                    drugsAndGoodsDetailVO.setWeight(drugs.getWeight());
                    drugsAndGoodsDetailVO.setSellingPrice(drugs.getSellingPrice());
                    drugsAndGoodsDetailVO.setOrderId(busDrugsOrder.getId());
                    drugsAndGoodsDetailVO.setDoses(tcmAgents);
                    drugsAndGoodsDetail.add(drugsAndGoodsDetailVO);
                }
                orderDetailVO.setProcessPrice(prescriptionDrugs.getProcessPrice());
                orderDetailVO.setType("0");
                // 查询中药处方是否存在售后
                checkPrescriptionAfterInfo(busDrugsOrder.getPrescriptionId(), orderDetailVO);
            } else if (PrescriptionTypeEnum.isMm(prescriptionDrugs.getPrescriptionType())) {
                for (BusPrescriptionDrugs drugs : drugsList) {
                    DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                    drugsAndGoodsDetailVO.setImg(drugs.getDrugsImg());
                    drugsAndGoodsDetailVO.setName(drugs.getDrugsName());
                    drugsAndGoodsDetailVO.setSpecification(drugs.getDrugsSpecification());
                    drugsAndGoodsDetailVO.setQuantity(drugs.getQuantity());
                    drugsAndGoodsDetailVO.setPreType(drugs.getPreType());
                    drugsAndGoodsDetailVO.setSellingPrice(drugs.getSellingPrice());
                    drugsAndGoodsDetailVO.setOrderId(busDrugsOrder.getId());
                    drugsAndGoodsDetailVO.setBusinessId(drugs.getDrugsId());
                    drugsAndGoodsDetailVO.setBusinessType(YesNoEnum.NO.getCode());
                    drugsAndGoodsDetail.add(drugsAndGoodsDetailVO);
                }
                orderDetailVO.setType("1");
                // 查询西药处方是否存在售后
                checkPrescriptionAfterInfo(busDrugsOrder.getPrescriptionId(), orderDetailVO);
            } else {
                //中药方剂

                String usages = prescriptionDrugs.getUsages();
                String[] split = usages.split(",");
                // 获取中药剂数
                Integer tcmAgents = Integer.valueOf(split[1]);
                // 方剂的单价从bus_prescription中计算得，避免药品价格修改后出现单价和总价对不上
                // 处方单价=（处方总价-加工费）/数量
                BusPrescription busPrescription = busPrescriptionMapper.selectById(busDrugsOrder.getPrescriptionId());
                BigDecimal processPrice = ObjectUtil.isNull(busPrescription.getProcessPrice()) ? BigDecimal.ZERO : busPrescription.getProcessPrice();

                BigDecimal amount = busPrescription.getPrescriptionAmount().subtract(processPrice).
                        divide(BigDecimal.valueOf(tcmAgents), RoundingMode.CEILING);

                // 查询方剂类型
                BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(prescriptionDrugs.getPaId());
                if (YesNoEnum.NO.getCode().equals(busHospitalPa.getType())) {
                    DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                    drugsAndGoodsDetailVO.setName(busHospitalPa.getPaName());
                    drugsAndGoodsDetailVO.setQuantity(tcmAgents);
                    drugsAndGoodsDetailVO.setSellingPrice(amount);
                    drugsAndGoodsDetailVO.setOrderId(busDrugsOrder.getId());
                    drugsAndGoodsDetailVO.setDoses(tcmAgents);
                    drugsAndGoodsDetail.add(drugsAndGoodsDetailVO);
                    orderDetailVO.setProcessPrice(prescriptionDrugs.getProcessPrice());
                    orderDetailVO.setType("2");
                } else {
                    for (BusPrescriptionDrugs drugs : drugsList) {
                        DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                        drugsAndGoodsDetailVO.setName(drugs.getDrugsName());
                        drugsAndGoodsDetailVO.setSpecification(drugs.getDrugsSpecification());
                        drugsAndGoodsDetailVO.setQuantity(tcmAgents);
                        drugsAndGoodsDetailVO.setWeight(drugs.getWeight());
                        drugsAndGoodsDetailVO.setSellingPrice(drugs.getSellingPrice());
                        drugsAndGoodsDetailVO.setOrderId(busDrugsOrder.getId());
                        drugsAndGoodsDetailVO.setDoses(tcmAgents);
                        drugsAndGoodsDetail.add(drugsAndGoodsDetailVO);
                    }
                    orderDetailVO.setAmount(amount);
                    orderDetailVO.setProcessPrice(prescriptionDrugs.getProcessPrice());
                    orderDetailVO.setType("0");
                }
                // 查询中药处方是否存在售后
                checkPrescriptionAfterInfo(busDrugsOrder.getPrescriptionId(), orderDetailVO);
            }
            orderDetailVO.setDetailVOList(drugsAndGoodsDetail);
            orderDetailList.add(orderDetailVO);
        } else { // 非处方药
            // 查询药品订单详情非处方药品信息
            List<BusOtcDrugs> drugsList = busOtcDrugsMapper.queryDrugsList(busDrugsOrder.getId());
            BusOtcDrugs otcDrugs = drugsList.get(0);
            if (CollectionUtil.isEmpty(orderDetailList)) {
                orderDetailVO.setTitleName(otcDrugs.getHospitalName() + "药房");
                List<DrugsAndGoodsDetailVO> drugsAndGoodsDetailList = new ArrayList<>();
                for (BusOtcDrugs drugs : drugsList) {
                    DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                    drugsAndGoodsDetailVO.setImg(drugs.getDrugsImg());
                    drugsAndGoodsDetailVO.setName(drugs.getDrugsName());
                    drugsAndGoodsDetailVO.setSpecification(drugs.getDrugsSpecification());
                    drugsAndGoodsDetailVO.setQuantity(drugs.getQuantity());
                    drugsAndGoodsDetailVO.setPreType("1");
                    drugsAndGoodsDetailVO.setSellingPrice(drugs.getSellingPrice());
                    drugsAndGoodsDetailVO.setOrderId(drugs.getDrugsOrderId());
                    drugsAndGoodsDetailVO.setBusinessId(drugs.getDrugsId());
                    drugsAndGoodsDetailVO.setBusinessType(YesNoEnum.NO.getCode());
                    // 查询该otc药品是否存在售后
                    checkGoodsOrOtcAfterInfo(CodeEnum.NO.getCode(), drugs.getDrugsOrderId(), drugs.getDrugsId(), drugsAndGoodsDetailVO);
                    drugsAndGoodsDetailList.add(drugsAndGoodsDetailVO);
                }
                orderDetailVO.setDetailVOList(drugsAndGoodsDetailList);
                orderDetailList.add(orderDetailVO);
            } else {
                for (int i = 0; i < orderDetailList.size(); i++) {
                    OrderDetailVO detailVO = orderDetailList.get(i);
                    boolean flag = true;
                    // 不是处方单
                    if (StringUtils.isEmpty(detailVO.getType())) {
                        detailVO.setTitleName(otcDrugs.getHospitalName() + "药房");
                        List<DrugsAndGoodsDetailVO> detailVOList = detailVO.getDetailVOList();
                        for (BusOtcDrugs drugs : drugsList) {
                            DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                            drugsAndGoodsDetailVO.setImg(drugs.getDrugsImg());
                            drugsAndGoodsDetailVO.setName(drugs.getDrugsName());
                            drugsAndGoodsDetailVO.setSpecification(drugs.getDrugsSpecification());
                            drugsAndGoodsDetailVO.setQuantity(drugs.getQuantity());
                            drugsAndGoodsDetailVO.setPreType("1");
                            drugsAndGoodsDetailVO.setSellingPrice(drugs.getSellingPrice());
                            drugsAndGoodsDetailVO.setOrderId(drugs.getDrugsOrderId());
                            drugsAndGoodsDetailVO.setBusinessId(drugs.getDrugsId());
                            drugsAndGoodsDetailVO.setBusinessType(YesNoEnum.NO.getCode());
                            // 查询该otc药品是否存在售后
                            checkGoodsOrOtcAfterInfo(CodeEnum.NO.getCode(), drugs.getDrugsOrderId(), drugs.getDrugsId(), drugsAndGoodsDetailVO);
                            detailVOList.add(drugsAndGoodsDetailVO);
                            flag = false;
                        }
                        detailVO.setDetailVOList(detailVOList);
                    }
                    if (i + 1 == orderDetailList.size() && flag) {
                        OrderDetailVO orderDetail = new OrderDetailVO();
                        orderDetail.setTitleName(otcDrugs.getHospitalName() + "药房");
                        List<DrugsAndGoodsDetailVO> drugsAndGoodsDetail = new ArrayList<>();
                        for (BusOtcDrugs drugs : drugsList) {
                            DrugsAndGoodsDetailVO drugsAndGoodsDetailVO = new DrugsAndGoodsDetailVO();
                            drugsAndGoodsDetailVO.setImg(drugs.getDrugsImg());
                            drugsAndGoodsDetailVO.setName(drugs.getDrugsName());
                            drugsAndGoodsDetailVO.setSpecification(drugs.getDrugsSpecification());
                            drugsAndGoodsDetailVO.setQuantity(drugs.getQuantity());
                            drugsAndGoodsDetailVO.setPreType("1");
                            drugsAndGoodsDetailVO.setSellingPrice(drugs.getSellingPrice());
                            drugsAndGoodsDetailVO.setOrderId(drugs.getDrugsOrderId());
                            drugsAndGoodsDetailVO.setBusinessId(drugs.getDrugsId());
                            drugsAndGoodsDetailVO.setBusinessType(YesNoEnum.NO.getCode());
                            // 查询该otc药品是否存在售后
                            checkGoodsOrOtcAfterInfo(CodeEnum.NO.getCode(), drugs.getDrugsOrderId(), drugs.getDrugsId(), drugsAndGoodsDetailVO);
                            drugsAndGoodsDetail.add(drugsAndGoodsDetailVO);
                        }
                        orderDetail.setDetailVOList(drugsAndGoodsDetail);
                        orderDetailList.add(orderDetail);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 校验otc药品、商品是否存在售后
     *
     * @param afterSalesType （0药品 1商品）
     * @param orderId        订单ID
     * @param businessId     药品/商品ID
     * @param vo
     */
    private void checkGoodsOrOtcAfterInfo(String afterSalesType, Long orderId, Long businessId, DrugsAndGoodsDetailVO vo) {
        //允许多次售后之后，可能会查询出多个售后订单，选择其中创建时间最晚的
        LambdaQueryWrapper<BusOrderAfterSales> query = Wrappers.lambdaQuery();
        query.eq(BusOrderAfterSales::getAfterSalesType, afterSalesType);
        query.eq(BusOrderAfterSales::getOrderId, orderId);
        query.eq(BusOrderAfterSales::getGoodsId, businessId);
        //根据创建时间降序排序后，选择第一个（创建时间最晚的）
        query.orderByDesc(BusOrderAfterSales::getCreateTime).last("limit 1");
        BusOrderAfterSales orderAfterSales = busOrderAfterSalesMapper.selectOne(query);
        if (Objects.nonNull(orderAfterSales)) {
            vo.setOrderAfterSalesId(orderAfterSales.getId());
            vo.setAfterSalesStatus(orderAfterSales.getAfterSalesStatus());
            vo.setOrderAfterSales(orderAfterSales);
        }
    }

    /**
     * 校验处方是否存在售后
     *
     * @param id            处方ID
     * @param orderDetailVO
     */
    private void checkPrescriptionAfterInfo(Long id, OrderDetailVO orderDetailVO) {
        //允许多次售后之后，可能会查询出多个售后订单，选择其中创建时间最晚的
        LambdaQueryWrapper<BusOrderAfterSales> query = Wrappers.lambdaQuery();
        query.eq(BusOrderAfterSales::getPrescriptionId, id);
        query.eq(BusOrderAfterSales::getOrderId, orderDetailVO.getOrderId());
        //根据创建时间降序排序后，选择第一个（创建时间最晚的）
        query.orderByDesc(BusOrderAfterSales::getCreateTime).last("limit 1");

        BusOrderAfterSales orderAfterSales = busOrderAfterSalesMapper.selectOne(query);
        if (Objects.nonNull(orderAfterSales)) {
            orderDetailVO.setOrderAfterSalesId(orderAfterSales.getId());
            orderDetailVO.setAfterSalesStatus(orderAfterSales.getAfterSalesStatus());
            orderDetailVO.setOrderAfterSales(orderAfterSales);
        }
    }

    /**
     * 扣减otc库存
     *
     * @param busDrugsOrder
     */
    private void deductionOtcStock(BusDrugsOrder busDrugsOrder) {
        // 根据配送企业分组查询非处方药品信息
        List<BusPrescriptionDrugs> otcDrugList = busOtcDrugsMapper.selectOtcDrugList(busDrugsOrder);
        for (BusPrescriptionDrugs pd : otcDrugList) {
            if (StringUtils.isNotNull(pd.getEnterpriseId())) {
                BusDrugOrderPackage orderPackage = new BusDrugOrderPackage();
                orderPackage.setHospitalId(pd.getHospitalId());
                orderPackage.setPackageType(YesNoEnum.NO.getCode());
                orderPackage.setDrugsOrderId(pd.getDrugsOrderId());
                orderPackage.setEnterpriseId(pd.getEnterpriseId());
                orderPackage.setEnterpriseName(pd.getEnterpriseName());
                orderPackage.setCreateTime(DateUtils.getNowDate());
                orderPackage.setDeliveryType(Integer.valueOf(busDrugsOrder.getDeliveryType()));
                orderPackage.setDrugsGoodsId(pd.getDrugsId());
                busDrugOrderPackageMapper.insert(orderPackage);
                // 变更库存
                if (EnterpriseEnum.GYJT.getInfo().equals(pd.getIdentifying())) {
                    changeEnterpriseStock(null, pd.getDrugsOrderId(), busDrugsOrder.getHospitalId(),
                            pd.getEnterpriseId(), StockEnum.REDUCE.getName());
                }
            } else {
                // 变更库存
                changeStock(null, pd.getDrugsOrderId(), pd.getHospitalId(), StockEnum.REDUCE.getName());
            }
        }
        // 设置订单类型，方便后台展示发货按钮
        BusDrugsOrder drugsOrder = new BusDrugsOrder();
        if (otcDrugList.size() > 1) {
            drugsOrder.setOrderType("0,1");
        } else {
            Long enterpriseId = otcDrugList.get(0).getEnterpriseId();
            if (StringUtils.isNotNull(enterpriseId)) {
                drugsOrder.setOrderType("1");
            } else {
                drugsOrder.setOrderType("0");
            }
        }
        drugsOrder.setId(busDrugsOrder.getId());
        busDrugsOrderMapper.updateById(drugsOrder);
    }

    /**
     * 释放药品库存
     *
     * @param drugsOrder
     */
    @Override
    public void releaseStock(BusDrugsOrder drugsOrder) {
        // 查询该订单下所有包裹信息
        List<BusDrugOrderPackage> pdList = busDrugOrderPackageMapper.selectPdList(drugsOrder);
        if (CollectionUtil.isNotEmpty(pdList)) {
            for (BusDrugOrderPackage orderPackage : pdList) {
                if (StringUtils.isEmpty(orderPackage.getIdentifying())) {
                    if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
                        // 药房处方药释放库存
                        changeStock(drugsOrder.getPrescriptionId(), null, drugsOrder.getHospitalId(),
                                StockEnum.INCREASE.getName());
                    } else {
                        // 药房非处方药释放库存
                        changeStock(null, drugsOrder.getId(), drugsOrder.getHospitalId(), StockEnum.INCREASE.getName());
                    }
                } else if (EnterpriseEnum.GYJT.getInfo().equals(orderPackage.getIdentifying())) {
                    if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
                        // 广药处方药释放库存
                        changeEnterpriseStock(drugsOrder.getPrescriptionId(), null, drugsOrder.getHospitalId(),
                                orderPackage.getEnterpriseId(), StockEnum.INCREASE.getName());
                    } else {
                        // 广药非处方药释放库存
                        changeEnterpriseStock(null, drugsOrder.getId(), drugsOrder.getHospitalId(),
                                orderPackage.getEnterpriseId(), StockEnum.INCREASE.getName());
                    }
                }
            }
        }
    }

    @Override
    public List<OrderVo> listShopAndDrugsOrder(BusDoctorOrderDTO busDoctorOrderDTO) {
        // 查询医生是否是特聘专家
        BusDoctorHospital doctorHospital = iBusDoctorHospitalService.selectDoctorHospitalInfo(busDoctorOrderDTO.getHospitalId(), busDoctorOrderDTO.getDoctorId());
        if (doctorHospital != null && DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(doctorHospital.getIsThisCourt())) {
            busDoctorOrderDTO.setExpertId(busDoctorOrderDTO.getDoctorId());
            busDoctorOrderDTO.setDoctorId(null);
        }
        List<OrderVo> orderVos = busDrugsOrderMapper.listShopAndDrugsOrder(busDoctorOrderDTO);
        if (CollectionUtil.isNotEmpty(orderVos)) {
            List<Long> familyIds = orderVos.stream().map(OrderVo::getFamilyId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, BusPatientFamily> familyMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(familyIds)) {
                List<BusPatientFamily> patientFamilies = patientFamilyMapper.selectBatchIds(familyIds);
                if (CollectionUtil.isNotEmpty(patientFamilies)) {
                    familyMap.putAll(patientFamilies.stream().collect(Collectors.toMap(BusPatientFamily::getId, Function.identity())));
                }
            }

            for (OrderVo topItem : orderVos) {
                /*如果是药品*/
                if (CodeEnum.NO.getCode().equals(topItem.getSubOrderType())) {
                    List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectList(
                            new LambdaQueryWrapper<BusPrescriptionDrugs>()
                                    .eq(BusPrescriptionDrugs::getPrescriptionId, topItem.getPrId())
                    );
                    busPrescriptionDrugs.forEach(item -> {
                        BusDrugs busDrugs = busDrugsMapper.selectById(item.getDrugsId());
                        if (ObjectUtil.isNotNull(busDrugs)) {
                            item.setDrugsDosageForm(busDrugs.getDrugsDosageForm());
                            String otcAndPre = busDrugsOrderMapper.isOtcAndPre(busDrugs.getPrescriptionIdentification());
                            item.setPreType(otcAndPre);
                        }
                    });
                    topItem.setBusPrescriptionDrugs(busPrescriptionDrugs);
                } else {
                    /*如果是商品*/
                    List<BusOrderShop> busOrderShops = busOrderShopMapper.selectList(new LambdaQueryWrapper<BusOrderShop>()
                            .eq(BusOrderShop::getOrderId, topItem.getSubOrderId()));
                    topItem.setBusOrderShops(busOrderShops);
                    // 查询手机号
                    String phoneNumber;
                    if (CodeEnum.NO.getCode().equals(topItem.getDeliveryType())) {
                        BusPatient patient = patientMapper.selectById(topItem.getPatientId());
                        phoneNumber = patient.getPhoneNumber();
                    } else {
                        BusPatientFamily family = null;
                        if (Objects.nonNull(topItem.getFamilyId())) {
                            family = familyMap.get(topItem.getFamilyId());
                        }
                        if (Objects.nonNull(family)) {
                            phoneNumber = family.getCellPhoneNumber();
                        } else {
                            phoneNumber = topItem.getReceivePhone();
                        }
                    }
                    topItem.setPhoneNumber(phoneNumber);
                }
            }
        }


        return orderVos;
    }


    /**
     * 交易订单加入待支付队列
     *
     * @param hospitalId
     * @param id
     * @param orderNo
     * @return
     */
    @Override
    public boolean delayQueue(Long hospitalId, String id, String orderNo) {
        BusConsultationSettings bs = new BusConsultationSettings();
        bs.setHospitalId(hospitalId);
        bs = busConsultationSettingsService.selectOne(bs);
        Message message = new Message();
        message.setOrderNo(orderNo);
        if (!"null".equals(id)) {
            message.setId(Long.valueOf(id));
        }
        message.setHospitalId(hospitalId);

        if (bs == null || bs.getConsultationOrderExpire() == null) {
            message.setFireTime(QueueConstant.QUEUE_DRUGS_ORDER_CANCEL_TIME);
        } else {
            long time = bs.getConsultationOrderExpire() * 60 * 60 * 1000L;
            message.setFireTime(time);
        }
        return drugsOrderCancelProducer.delaySend(message, message.getFireTime());
    }

    /**
     * 查询订单物流信息
     *
     * @param orderNo
     * @return
     */
    @Override
    public List<BusDrugOrderPackage> queryLogisticsList(String orderNo) {
        List<BusOrder> busOrders = busOrderService.selectOrederByNo(orderNo);
        BusOrder order = busOrders.get(0);
        int barterOrNot = 0;
        if (new BigDecimal(0).compareTo(order.getRelPrice()) == 0 &&
                YesNoEnum.NO.getCode().equals(order.getChangePrice())) {
            barterOrNot = 1;
        }
        List<BusDrugOrderPackage> packageList = busDrugOrderPackageMapper.selectPackageList(orderNo, barterOrNot);
        if (packageList.isEmpty()) {
            packageList = new ArrayList<>();
        }
        packageList.forEach(p -> p.setReceivingTel(order.getReceivePhone()));
        return packageList;
    }

    /**
     * @Param id
     * @Return BusDrugsOrder
     * @Description 根据ID查询
     * <AUTHOR>
     * @Date 2024/11/18 16:33
     **/
    @Override
    public BusDrugsOrder selectById(Long id) {
        return busDrugsOrderMapper.selectById(id);
    }


    /**
     * 发送商品订单同意退款事件通知
     *
     * @param drugsOrder
     */
    private void sendDrugsOrderAgreeRefundEventNotification(BusDrugsOrder drugsOrder) {
        log.info("发送药品订单同意退款事件 orderNo: {}", drugsOrder.getOrderNo());
        // 组装同意退款消息事件通知
        DrugsOrderAgreeRefundEvent drugsOrderAgreeRefundEvent = new DrugsOrderAgreeRefundEvent();
        drugsOrderAgreeRefundEvent.setSubOrderId(drugsOrder.getId());
        Map<String, Object> attachment = new HashMap<>();
        attachment.put("status", "6");
        attachment.put("orderId", drugsOrder.getId());
        if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
            attachment.put("presId", drugsOrder.getPrescriptionId());
        }
        drugsOrderAgreeRefundEvent.setAttachment(attachment);
        publisher.publishEvent(drugsOrderAgreeRefundEvent);
    }

    /**
     * 医保退费
     *
     * @param order
     * @param dto
     */
    private void insuranceRefund(BusOrder order, DrugsOrderDto dto) {
        log.info("交易订单查询信息={}", order);
        if (OrderStatusEnum.FREIGHT_WAIT_PAY.getCode().equals(order.getOrderStatus())) {
            UniformRequest request = new UniformRequest();
            request.setHospitalId(order.getHospitalId());
            request.setReqKey("6203");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("payAuthNo", dto.getPayAuthNo());
            jsonObject.put("uldLatlnt", "0,0");
            jsonObject.put("totalOrderNo", order.getOrderNo());
            request.setParams(jsonObject);
            AjaxResult ajaxResult = remotePreprocessorService.uniformRequest(request);
            log.info("医保退费结果{}", ajaxResult);
            if (!ajaxResult.isSuccess()) {
                throw new ServiceException(ajaxResult.getMsg());
            }
        }
    }
}

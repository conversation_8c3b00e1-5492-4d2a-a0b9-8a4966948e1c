package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.puree.hospital.common.api.annotation.Excel;
import com.puree.hospital.common.api.domain.entity.BaseEntity;
import lombok.Data;

/**
 * 医院信息对象 bus_hospital
 * 
 * <AUTHOR>
 * @date 2021-10-15
 */
@Data
public class BusHospital extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 医院code */
    @Excel(name = "医院code")
    private String hospitalCode;

    /** 医院编号 */
    @Excel(name = "医院编号")
    private String hospitalNumber;

    /** 医院名称 */
    @Excel(name = "医院名称")
    private String hospitalName;

    /** 医院简称 */
    @Excel(name = "医院简称")
    private String hospitalAbbreviation;

    /** 医院电话 */
    @Excel(name = "医院电话")
    private String hospitalPhone;

    /** 省级code */
    @Excel(name = "省级code")
    private Long provinceCode;

    /** 市级code */
    @Excel(name = "市级code")
    private Long cityCode;

    /** 区级code */
    @Excel(name = "区级code")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long areaCode;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String detailAddress;

    /** 地址坐标 */
    @Excel(name = "地址坐标")
    private String addressCoordinate;

    /** 医院简介 */
    @Excel(name = "医院简介")
    private String hospitalDescription;

    /** 医院照片 */
    @Excel(name = "医院照片")
    private String hospitalPhoto;

    /** 医疗执照 */
    @Excel(name = "医疗执照")
    private String medicalLicense;

    /** 状态（0停用 1启用） */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private Integer status;

    /** 是否删除（0否 1是） */
    private Integer delFlag;
    /**
     * 医院公章
     */
    private String hospitalSeal;
    /**
     * 支付方式(1-微信  2通联)
     */
    private String payWay;
    /**
     * 微信支付验证文件
     */
    private String wxCheckFile;
    /**
     * 是否开通医保（0否 1是）
     */
    private Integer isInsurance;
    /**
     * 是否接入监管（0否 1是）
     */
    private Integer isRegulator;
    /**
     * 医院域名
     */
    private String hospitalDomain;

    /**
     * 是否测试医院（0否 1是）
     */
    private Integer isTest;
}

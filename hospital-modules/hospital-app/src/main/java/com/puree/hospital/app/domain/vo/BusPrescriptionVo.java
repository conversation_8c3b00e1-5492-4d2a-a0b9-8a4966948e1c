package com.puree.hospital.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BusPrescriptionVo extends BusPrescriptionDto {

    private static final long serialVersionUID = 7369267552949116673L;
    /**
     * 发送到药房或配送企业（0：药房 1：配送企业）
     */
    private String sendToDest;
    private Long deliveryPharmacistId;
    private String deliveryPharmacistName;
    /**
     * 发货药师签名
     */
    private String deliveryPharmacistSignature;


    private List<BusSignatureVO> signatureList;

    private String hospitalPhone;
    private String hospitalSeal;
    private String province;
    private String city;
    private String area;
    private Long dispensingPharmacistId;
    private String dispensingPharmacistName;
    /**
     * 调剂药师签名
     */
    private String dispensingPharmacistSignature;
    private Long servicePackId;

    /**
     *物流公司
     */
    private String logisticsCompany;
    /**
     *运单号码
     */
    private String deliveryNo;
    /**
     * 中药方剂类型 0-协定方 1-经典方
     */
    private Integer zyfjType;
    /**
     *方剂价格
     */
    private String zyfjAmount;
    /**
     * 总订单id
     */
    private String orderNo;

    /**
     * 患者详细住址
     */
    private String familyDetailAddress;

    /**
     * 药品目录类型0院内 1院外 3中药
     */
    private String drugDirectoryType;

    /**
     * 处方有效时长
     */
    private Integer effectiveHours;

    /**
     * 处方文件链接地址
     */
    private String path;

    /**
     * 医生医保编码
     */
    private String doctorMiCode;

    /**
     * 复核状态
     */
    private String dispensingStatus;

    /**
     * 复核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dispensingTime;

}

package com.puree.hospital.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusDrugs;
import com.puree.hospital.app.domain.dto.BusDrugsDto;
import com.puree.hospital.app.domain.dto.ShopDrugDTO;
import com.puree.hospital.app.domain.vo.BusDrugsVo;
import com.puree.hospital.app.domain.vo.DrugSearchVO;
import com.puree.hospital.app.domain.vo.ShopDrugVO;
import com.puree.hospital.app.mapper.BusDrugsMapper;
import com.puree.hospital.app.service.IBusDrugsService;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BusDrugsServiceImpl implements IBusDrugsService {
    private final BusDrugsMapper busDrugsMapper;

    @Autowired
    public BusDrugsServiceImpl(BusDrugsMapper busDrugsMapper) {
        this.busDrugsMapper = busDrugsMapper;
    }

    @Override
    public List<BusDrugsVo> searchTcmDrugs(BusDrugsDto dto) {
        return busDrugsMapper.selectTcmList(dto);
    }

    /**
     * 搜索查询商品及药品列表
     * @param dto
     * @return
     */
    @Override
    public List<ShopDrugVO> selectBusDrugsAndShopList(ShopDrugDTO dto) {
        return busDrugsMapper.selectBusDrugsAndShopList(dto);
    }

    @Override
    public List<String> searchTips(ShopDrugDTO shopDrugDTO) {
        return busDrugsMapper.searchTips(shopDrugDTO);
    }

    @Override
    public List<BusDrugs> selectEffectiveByIds(List<Long> drugIdList) {
       return busDrugsMapper.selectList(new LambdaQueryWrapper<BusDrugs>()
                .in(BusDrugs::getId, drugIdList)
                .eq(BusDrugs::getStatus, EnableStatusEnum.ENABLED.getStatus())
                .eq(BusDrugs::getDelFlag, "0"));
    }

    @Override
    public List<DrugSearchVO> searchDrug(ShopDrugDTO shopDrugDTO) {
        return busDrugsMapper.searchDrug(shopDrugDTO);
    }

}

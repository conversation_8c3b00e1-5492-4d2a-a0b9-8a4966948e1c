package com.puree.hospital.app.his;


import com.puree.hospital.app.domain.vo.BusPatientMedicalRecordListVO;
import com.puree.hospital.app.domain.vo.BusPatientRegRecordVO;
import com.puree.hospital.app.domain.vo.BusInspectReportDetail;
import com.puree.hospital.app.domain.vo.BusExamineReportDetailVO;
import com.puree.hospital.app.his.domain.HisPatientInfo;
import com.puree.hospital.app.his.domain.HisQueryContext;
import com.puree.hospital.app.his.domain.query.HisPatientInfoQuery;
import com.puree.hospital.app.his.domain.vo.BusEmrDetailVO;
import com.puree.hospital.app.his.domain.vo.BusPrescriptionInfoVO;

import java.util.List;

/**
 * 患者病例信息
 *
 * <AUTHOR>
 * @date 2025/3/24 13:01
 */
public interface IPatientMedicalRecord {

    /**
     * 获取患者就诊记录列表
     *
     * @param queryContext 查询参数上下文
     * @return 就诊记录列表
     */
    List<BusPatientRegRecordVO> getPatientMedicalRecordList(HisQueryContext queryContext);

    /**
     * 获取患者电子病历子项列表
     *
     * @param queryContext 查询参数上下文
     * @return 医疗记录列表
     */
    List<BusPatientMedicalRecordListVO> getElectronicMedicalRecordItemList(HisQueryContext queryContext);

    /**
     *  获取电子病历详情
     * @param queryContext  查询上下文
     * @return  电子病历详情
     */
    List<BusEmrDetailVO> getEmrDetail(HisQueryContext queryContext);

    /**
     * 获取患者检查记录详情
     *
     * @param queryContext 查询参数上下文
     * @return 检查记录详情
     */
    List<BusInspectReportDetail> getInspectDetail(HisQueryContext queryContext);

    /**
     * 获取患者检验记录详情
     *
     * @param queryContext 查询参数上下文
     * @return 检验记录详情
     */
    BusExamineReportDetailVO getExamineDetail(HisQueryContext queryContext);

    /**
     * 获取患者处方详情
     *
     * @param queryContext 查询参数上下文
     * @return 处方详情
     */
    BusPrescriptionInfoVO getPrescriptionInfo(HisQueryContext queryContext);

    /**
     * 获取渠道名称
     * @return  渠道名称
     */
    String getChannelName();

    /**
     * 获取患者信息
     *
     * @param query 查询参数
     * @return 患者信息
     */
    HisPatientInfo getPatientInfo(HisPatientInfoQuery query);
}

package com.puree.hospital.app.his.helper;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.puree.hospital.app.domain.vo.BusExamineReportDetail;
import com.puree.hospital.app.domain.vo.BusInspectReportDetail;
import com.puree.hospital.app.his.domain.HisPatientInfo;
import com.puree.hospital.app.his.domain.query.HisExamineReportQuery;
import com.puree.hospital.app.his.domain.query.HisInspectReportQuery;
import com.puree.hospital.app.his.domain.query.HisOutPatientRecordQuery;
import com.puree.hospital.app.his.domain.query.HisPatientInfoQuery;
import com.puree.hospital.app.his.domain.query.HisPrescriptionInfoDTO;
import com.puree.hospital.app.his.domain.query.HisRegisteredInfoQuery;
import com.puree.hospital.app.his.domain.vo.HisExamineReport;
import com.puree.hospital.app.his.domain.vo.HisInspectReport;
import com.puree.hospital.app.his.domain.vo.HisOutPatientRecord;
import com.puree.hospital.app.his.domain.vo.HisPrescriptionInfo;
import com.puree.hospital.app.his.domain.vo.HisRegisteredInfo;
import com.puree.hospital.app.his.domain.vo.HisResult;
import com.puree.hospital.app.his.exception.soap.HisActiveDoctorNullException;
import com.puree.hospital.app.his.exception.soap.HisPatientInfoIsNullException;
import com.puree.hospital.app.his.exception.soap.HisSOAPException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.xml.soap.SOAPException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 东软（开发区）获取数据 - 临时方案,代替his remote 服务
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/18 18:03
 */
@Slf4j
@Component
public class DrHisHelper {

    /**
     * SOAP方法常量
     */
    private static final class SoapMethods {

        static final String QUERY_REG_INFO = "QueryRegInfoListHLW";
        static final String OUT_PATIENT_RECORD = "OutpatientRecord";
        static final String EXAMINE = "QueryLisReport";
        static final String EXAMINE_DETAIL = "QueryLisReportDetail";
        static final String INSPECT = "QueryPacsReport";
        static final String INSPECT_DETAIL = "QueryPacsReportDetail";
        static final String PRESCRIPTION_INFO_METHOD = "QueryRecipeDetail";
        static final String QUERY_PATIENT_INFO = "QueryOutPatientInfoHLW";
    }
    /**
     * 执行SOAP查询并转换结果列表的通用方法
     * @param methodName SOAP方法名
     * @param query 查询参数
     * @param resultClass 结果类型
     * @return 查询结果列表
     * @throws HisSOAPException SOAP调用异常
     */
    private <T, Q> List<T> executeSoapQuery(String methodName, Q query, Class<T> resultClass) {
        try {
            return SOAPActuator.executeQueryAndConvertList(methodName, query, resultClass);
        } catch (HisActiveDoctorNullException e) {
            log.debug("查询结果为空 - 方法: {}, 参数: {}", methodName, query);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("SOAP调用失败 - 方法: {}, 参数: {}, 错误: {}", methodName, query, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 执行SOAP通用方法
     * @param methodName SOAP方法名
     * @param query 查询参数
     * @param resultClass 结果类型
     * @param operationName 操作名称
     * @return 查询结果
     * @throws HisSOAPException SOAP调用异常
     */
    private <T, Q> List<T> executeSoap(String methodName, Q query, Class<T> resultClass, String operationName) {
        Assert.notNull(query, "查询条件不能为空");
        try {
            HisResult hisResult = SOAPActuator.execute(methodName, query);
            String result = hisResult.getResult();
            log.debug("{}查询结果：{}", operationName, result);
            JSONArray jsonArray = JSON.parseArray(result);
            return jsonArray.toJavaList(resultClass);
        } catch (Exception e) {
            log.error("HIS{}失败 - 参数: {}, 错误: {} ", operationName, query, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取患者信息
     *
     * @param patientName 患者姓名
     * @param idCardNo 身份证号码
     */
    public HisPatientInfo getPatientInfo(String patientName, String idCardNo) {
        Assert.notEmpty(patientName, "患者姓名不能为空");
        Assert.notEmpty(idCardNo, "身份证号不能为空");
        HisPatientInfoQuery query = new HisPatientInfoQuery();
        query.setPatientName(patientName);
        query.setIDCardNO(idCardNo);
        // "1"卡号 "2"身份证
        query.setCardTypeID("2");
        try {
            HisResult hisResult = SOAPActuator.execute(SoapMethods.QUERY_PATIENT_INFO, query);
            return JSON.parseObject(hisResult.getResult(), HisPatientInfo.class);
        }catch (HisPatientInfoIsNullException e){
            return null;
        } catch (SOAPException e) {
            log.error("查询患者信息失败, 错误: {}", e.getMessage());
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 查询就诊记录
     * @param query 查询条件
     * @return 就诊记录列表
     * @throws HisSOAPException SOAP调用异常
     */
    public List<HisRegisteredInfo> getRegisteredInfo(HisRegisteredInfoQuery query) {
        if (StrUtil.isBlank(query.getPatientID())) {
            return Collections.emptyList();
        }
        return executeSoapQuery(SoapMethods.QUERY_REG_INFO, query, HisRegisteredInfo.class);
    }

    /**
     * 查询门诊患者文本病历
     * @param query 查询条件
     * @return 患者病历
     */
    public List<HisOutPatientRecord> getOutPatientRecord(HisOutPatientRecordQuery query) {
        return executeSoap(SoapMethods.OUT_PATIENT_RECORD, query, HisOutPatientRecord.class, "查询门诊患者文本病历");
    }

    /**
     * 获取检验报告
     * @param query 查询条件
     * @return 患者检验报告
     */
    public List<HisExamineReport> getExamineList(HisExamineReportQuery query) {
        if (StrUtil.isBlank(query.getPatientID())) {
            return Collections.emptyList();
        }
        return executeSoap(SoapMethods.EXAMINE, query, HisExamineReport.class, "查询检验列表");
    }

    /**
     * 获取检验报告明细
     * @param query 查询条件
     * @return 患者检验报告明细
     */
    public List<BusExamineReportDetail> getExamineDetail(HisExamineReportQuery query) {
        return executeSoap(SoapMethods.EXAMINE_DETAIL, query, BusExamineReportDetail.class, "查询检验明细");
    }

    /**
     * 获取检查报告
     * @param query 查询条件
     * @return 患者检查报告
     */
    public List<HisInspectReport> getInspectList(HisInspectReportQuery query) {
        if (StrUtil.isBlank(query.getPatientID())) {
            return Collections.emptyList();
        }
        return executeSoap(SoapMethods.INSPECT, query, HisInspectReport.class, "查询检查列表");
    }

    /**
     * 获取检查报告明细
     * @param query 查询条件
     * @return 患者检查报告明细
     */
    public List<BusInspectReportDetail> getInspectDetail(HisInspectReportQuery query) {
        return executeSoap(SoapMethods.INSPECT_DETAIL, query, BusInspectReportDetail.class, "查询检查明细");
    }

    /**
     * 处方信息查询
     * @param dto dto
     * @return 处方信息
     */
    public List<HisPrescriptionInfo> getPrescriptionInfo(HisPrescriptionInfoDTO dto) {
        Assert.notNull(dto, "处方信息查询参数不能为空");
        if (StrUtil.isBlank(dto.getCardNO())) {
            return Collections.emptyList();
        }
        List<HisPrescriptionInfo> hisPrescriptionInfos = executeSoapQuery(SoapMethods.PRESCRIPTION_INFO_METHOD, dto, HisPrescriptionInfo.class);
        hisPrescriptionInfos.removeIf(info -> "其它".equals(info.getOrderTypeName()) || Objects.isNull(info.getOrderTypeName()));
        return hisPrescriptionInfos;
    }
}

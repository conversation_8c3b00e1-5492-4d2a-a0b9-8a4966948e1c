package com.puree.hospital.app.controller;

import com.google.common.collect.Lists;
import com.puree.hospital.app.api.model.BusPatientHospitalDTO;
import com.puree.hospital.app.api.model.BusPatientPartnersDTO;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPatientHospital;
import com.puree.hospital.app.domain.BusSearchRecord;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.domain.dto.CommunicationMessageDTO;
import com.puree.hospital.app.domain.dto.ImDualChannelAuthDTO;
import com.puree.hospital.app.domain.dto.QrCodeDto;
import com.puree.hospital.app.domain.vo.BusPatientFamilyVo;
import com.puree.hospital.app.service.IBusChannelPatientAgentRelationService;
import com.puree.hospital.app.service.IBusCommunicationMessageService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusDoctorPatientService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.service.IBusPatientPartnersService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.app.service.IBusSearchRecordService;
import com.puree.hospital.app.service.IQrcodeService;
import com.puree.hospital.business.api.RemoteComplaintService;
import com.puree.hospital.business.api.model.BusComplaintVO;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.BizStatusEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.SearchTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.common.security.annotation.InnerAuth;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 患者信息相关控制器
 */
@RestController
@RequestMapping("/patient")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPatientController {
    private final IBusPatientService busPatientService;
    private final IBusDoctorPatientService busDoctorPatientService;
    private final IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final IBusSearchRecordService busSearchRecordService;
    private final IBusConsultationOrderService busConsultationOrderService;
    private final IBusPatientHospitalService busPatientHospitalService;
    private final IQrcodeService qrcodeService;
    private final RemoteComplaintService remoteComplaintService;
    private final IBusCommunicationMessageService busCommunicationMessageService;
    private final IBusPatientPartnersService busPatientPartnersService;
    private final IBusChannelPatientAgentRelationService busChannelPatientAgentRelationService;

    /**
     * 完善信息
     *
     * @param
     * @return
     */
    @PostMapping("/perfect")
    @Log(title = "完善信息", businessType = BusinessType.OTHER)
    public AjaxResult perfect(@RequestBody BusPatientFamily busPatientFamily) {
        return AjaxResult.success(busPatientService.perfectInfo(busPatientFamily));
    }

    /**
     * 校验是否有就诊人
     *
     * @param
     * @return
     */
    @GetMapping("/checkIsPerfect")
    @Log(title = "校验是否有就诊人", businessType = BusinessType.OTHER)
    public AjaxResult checkIsPerfect() {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        return AjaxResult.success(busPatientService.checkIsPerfect(loginUser.getUserid()));
    }

    /**
     * 群组修改就诊人信息
     *
     * @param
     * @return
     */
    @PostMapping("/updateFamily")
    @Log(title = "群组修改就诊人信息", businessType = BusinessType.OTHER)
    public AjaxResult updateFamily(BusDoctorPatientGroup busDoctorPatientGroup) {
        busDoctorPatientGroup.setType(CodeEnum.NO.getCode());
        BusDoctorPatientGroup group = busDoctorPatientGroupService.selectOne(busDoctorPatientGroup);
        if (StringUtils.isNotNull(group)) {
            return AjaxResult.error("更新就诊人已存在群组");
        }

        BusConsultationOrder busConsultationOrder = new BusConsultationOrder();
        busConsultationOrder.setHospitalId(StringUtils.isNotNull(busDoctorPatientGroup.getHospitalId()) ?
                busDoctorPatientGroup.getHospitalId() : null);
        busConsultationOrder.setPatientId(busDoctorPatientGroup.getPatientId());
        busConsultationOrder.setFamilyId(busDoctorPatientGroup.getFamilyId());
        busConsultationOrder.setDepartmentId(busDoctorPatientGroup.getDepartmentId());
        busConsultationOrder.setDoctorId(busDoctorPatientGroup.getDoctorId());
        BusConsultationOrder consultationOrder =
                busConsultationOrderService.selectBusConsultationOrder(busConsultationOrder);
        if (StringUtils.isNotNull(consultationOrder)) {
            return AjaxResult.error("修改的就诊人已存在问诊订单无法修改");
        }
        return AjaxResult.success(busDoctorPatientService.updateFamily(busDoctorPatientGroup));
    }

    /**
     * 校验是否可以修改就诊人信息
     *
     * @param
     * @return
     */
    @Log(title = "校验是否可以修改就诊人信息")
    @PostMapping("/checkUpdateFamily")
    public AjaxResult checkUpdateFamily(BusConsultationOrder busConsultationOrder) {
        int code = busConsultationOrderService.checkUpdateFamily(busConsultationOrder);
        if (HttpStatus.SUCCESS !=  code) {
            return AjaxResult.error(code, null, BizStatusEnum.CAN_NOT_MODIFY.getCode());
        }
        return AjaxResult.success();
    }

    /**
     * 患者搜索记录
     *
     * @param
     * @return
     */
    @Log(title = "患者搜索记录")
    @GetMapping("/searchRecord")
    public AjaxResult searchRecord() {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        BusSearchRecord busSearchRecord = new BusSearchRecord();
        busSearchRecord.setPatientId(loginUser.getUserid());
        busSearchRecord.setHospitalId(loginUser.getHospitalId());
        // 0为全局类型
        ArrayList<Integer> searchTypeList = Lists.newArrayList(SearchTypeEnum.DOCTOR.getCode(), SearchTypeEnum.BE_GOOD_AT.getCode(), SearchTypeEnum.DEPT.getCode());
        busSearchRecord.setSearchTypeList(searchTypeList);
        return AjaxResult.success(busSearchRecordService.selectList(busSearchRecord));
    }

    /**
     * 删除患者搜索记录
     *
     * @param
     * @return
     */
    @Log(title = "删除患者搜索记录")
    @PostMapping("/delete/record")
    public AjaxResult deleteRecord(@RequestParam("id") Long id) {
        return AjaxResult.success(busSearchRecordService.deleteRecord(id));
    }

    /**
     * 获取机构code
     *
     * @param patientId
     * @return
     */
//    @GetMapping("/get/code")
//    public AjaxResult getPartnersCode(@RequestParam("patientId") Long patientId) {
//        return AjaxResult.success(busPatientService.getPartnersCode(patientId));
//    }

    /**
     * five远程调用获取患者信息
     * @return
     */
    @InnerAuth
    @GetMapping("/query/patient")
    public R queryPatientInfo() {
        return R.ok(busPatientService.queryPatientInfo());
    }

    /**
     * 校验患者是否是经纪人邀请
     * 经纪人相关接口，需要迁移 - 迁移到
     * @see BusChannelOrderController#checkPatientAgentRelation(Long, Long, Long, String, Integer)
     * @deprecated 废弃
     * @param hospitalId 医院id
     * @param patientId  患者id
     * @param id         订单id
     * @param orderType  订单类型
     * @return 患者信息校验结果
     */
    @Deprecated
    @InnerAuth
    @Log(title = "校验患者是否是经纪人邀请")
    @GetMapping("/check")
    public R<?> checkPatientInfo(@RequestParam("hospitalId") Long hospitalId,
                              @RequestParam("patientId") Long patientId,
                              @RequestParam("id") Long id,
                              @RequestParam("orderType") Integer orderType) {
        BusOrderDto busOrderDto = new BusOrderDto();
        busOrderDto.setHospitalId(hospitalId);
        busOrderDto.setPatientId(patientId);
        busOrderDto.setId(id);
        busOrderDto.setOrderType(orderType);
//        return R.ok(busPatientHospitalService.checkPatientInfo(busOrderDto));
        return R.ok(busChannelPatientAgentRelationService.checkPatientInfo(busOrderDto));
    }
    /**
     * 引导用户关注公众号
     *
     * @param qrCodeDto
     * @return
     */
    @Log(title = "引导用户关注公众号")
    @PostMapping("/getQrCode")
    public AjaxResult getDoctorCard(@RequestBody QrCodeDto qrCodeDto) {
        String url = "";

        url = qrcodeService.createTempTicket(qrCodeDto);

        return AjaxResult.success(url);
    }

    /**
     * 计算年龄
     * @param age
     * @return
     */
    @Log(title = "计算年龄")
    @GetMapping("/getCalculationAge")
    public AjaxResult getCalculationAge(@RequestParam("age") String age) {
        if (StringUtils.isEmpty(age)) {
            return AjaxResult.error("年龄不能为空");
        }
        String ageDetail = AgeUtil.getAgeDetail(age);
        return AjaxResult.success(ageDetail);
    }

    @GetMapping("/feign/info")
    public R<BusPatientFamilyVo> queryPatientInfo(@RequestParam("familyId") Long familyId) {
        return R.ok(busPatientService.queryFamilyInfo(familyId));
    }

    @GetMapping("/feign/patient-phone")
    public R<String> findPatientPhone(@RequestParam("patientId") Long patientId) {
        return R.ok(busPatientService.selectPatientInfo(patientId).getPhoneNumber());
    }

    /**
     * 保存投诉举报
     *
     * @param complaintVO 投诉举报
     * @return 保存结果
     */
    @PostMapping("/complaint/save")
    public R<Boolean> saveComplaint(@RequestBody BusComplaintVO complaintVO) {
        if (Objects.isNull(complaintVO)) {
            complaintVO = new BusComplaintVO();
        }
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        complaintVO.setHospitalId(loginUser.getHospitalId());
        complaintVO.setPatientId(loginUser.getUserid());
        return remoteComplaintService.save(complaintVO, SecurityConstants.INNER);
    }

    /**
     * 患者双通道医保授权成功
     *
     * @param dto dto
     * @return 患者授权成功结果
     */
    @PostMapping("/im/dual-channel/authorized")
    public AjaxResult dualChannelAuthorized(@RequestBody ImDualChannelAuthDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getGroupId())) {
            throw new ServiceException("参数错误");
        }
        if (Objects.isNull(dto.getConsultationOrderId())) {
            throw new ServiceException("当前问诊单id缺失");
        }
        if (StringUtils.isBlank(dto.getMiAuthCode())) {
            throw new ServiceException("您还未点击医保授权卡片或医保授权失败,请点击重试");
        }
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        if (Objects.isNull(loginUser)) {
            throw new ServiceException("请求非法");
        }
        BusDoctorPatientGroup groupInfo = busDoctorPatientGroupService.selectGroupInfo(dto.getGroupId());
        if (Objects.isNull(groupInfo)) {
            return AjaxResult.error("群组不存在！");
        }
        busConsultationOrderService.saveMiAuthCode(dto.getConsultationOrderId(), dto.getMiAuthCode(), groupInfo.getDoctorId());
        CommunicationMessageDTO messageDTO = new CommunicationMessageDTO();
        messageDTO.setDoctorPatientGroup(groupInfo);
        messageDTO.setGroupId(dto.getGroupId());
        messageDTO.setFromAccount(TencentyunImConstants.ADMINISTRATOR);
        messageDTO.setNickName(TencentyunImConstants.ADMINISTRATOR);
        messageDTO.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
        Map<String, Object> map = new HashMap<>();
        map.put("type", CustomMsgConstants.DUAL_CHANNEL_AUTHORIZED);
        messageDTO.setMap(map);
        busCommunicationMessageService.sendCustomImMsg(messageDTO);
        return AjaxResult.success();
    }

    /**
     * 获取医院患者信息
     *
     * @param patientId  患者id
     * @param hospitalId 医院id
     * @return 医院患者信息
     */
    @InnerAuth
    @GetMapping("/feign/hospital-patient-info")
    public R<BusPatientHospitalDTO> getHospitalPatientInfo(@RequestParam("patientId") Long patientId,
                                                           @RequestParam("hospitalId") Long hospitalId) {
        BusPatientHospital busPatientHospital = busPatientHospitalService.queryPatientHospitalInfo(hospitalId, patientId);
        if (Objects.isNull(busPatientHospital)) {
            return R.ok(null);
        }
        BusPatientHospitalDTO busPatientHospitalDTO = new BusPatientHospitalDTO();
        BeanUtils.copyProperties(busPatientHospital, busPatientHospitalDTO);
        return R.ok(busPatientHospitalDTO);
    }

    /**
     * 获取合作机构患者信息
     *
     * @param patientId   患者id
     * @param hospitalId  医院id
     * @param partnerCode 合作机构代码
     * @return 合作机构患者信息
     */
    @InnerAuth
    @GetMapping("/feign/partner-patient-info")
    public R<BusPatientPartnersDTO> getPartnerPatientInfo(@RequestParam("patientId") Long patientId,
                                                          @RequestParam("hospitalId") Long hospitalId,
                                                          @RequestParam("partnerCode") String partnerCode) {
        return R.ok(busPatientPartnersService.queryPatientPartner(hospitalId, patientId, partnerCode));
    }

}

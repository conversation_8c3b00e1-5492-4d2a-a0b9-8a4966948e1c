package com.puree.hospital.app.third.service;

import com.puree.hospital.app.api.model.dto.third.ThirdPartyCancelDTO;

/**
 * @ClassName ThirdPartyOrderService
 * <AUTHOR>
 * @Description 第三方订单服务
 * @Date 2024/11/18 16:10
 * @Version 1.0
 */
public interface ThirdPartyOrderService {

    /**
     * @Param dto
     * @Return Boolean
     * @Description 第三方取消药品订单
     * <AUTHOR>
     * @Date 2024/11/18 15:28
     **/
    Boolean thirdPartyCancel(ThirdPartyCancelDTO dto);


}

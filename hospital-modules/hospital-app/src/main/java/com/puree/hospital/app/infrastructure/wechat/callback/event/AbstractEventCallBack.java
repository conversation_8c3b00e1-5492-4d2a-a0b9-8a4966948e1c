package com.puree.hospital.app.infrastructure.wechat.callback.event;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.puree.hospital.app.domain.BusDoctorDepartment;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusHospitalWechatConfig;
import com.puree.hospital.app.domain.BusPartners;
import com.puree.hospital.app.domain.dto.WeChatQrCodeParamDto;
import com.puree.hospital.app.domain.vo.BusBizDepartmentVo;
import com.puree.hospital.app.helper.WxAccessTokenHelper;
import com.puree.hospital.app.infrastructure.wechat.ArticleItem;
import com.puree.hospital.app.infrastructure.wechat.WechatContant;
import com.puree.hospital.app.infrastructure.wechat.WechatInterface;
import com.puree.hospital.app.infrastructure.wechat.util.WechatUtil;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDrugsMapper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusPatientHospitalMapper;
import com.puree.hospital.app.service.IBusHospitalWechatConfigService;
import com.puree.hospital.app.service.IBusPartnersService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.util.DrawImgUtil;
import com.puree.hospital.app.infrastructure.wechat.callback.IWxEventCallBack;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.FiveRoleEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.JsonUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.http.HttpUtils;
import com.puree.hospital.common.oss.OSSProperties;
import com.puree.hospital.common.oss.OSSSaveDirectory;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.tool.api.model.dto.WxAccessTokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <p>
 * 订阅事件处理器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/11 18:57
 */
@Slf4j
public abstract class AbstractEventCallBack implements IWxEventCallBack {
    /**
     * 药品推荐二维码标识
     */
    protected static final String DRUG_REFERRAL_QRCODE_IDENTIFY = "DRUG_REFERRAL_QRCODE";

    private static final Pattern OFFLINE_QRCODE_PATTERN = Pattern.compile("^(\\w+_)?(?<hospitalId>\\d+),(?<doctorId>\\d+),(?<departmentId>\\d+),(?<status>\\d+),(?<uuid>\\w+),(?<name>.+),(?<phone>\\d+),(?<birthday>.+),(?<sex>.+)$");
    private static final Pattern NORMAL_QRCODE_PATTERN = Pattern.compile("^(\\w+_)?(?<hospitalId>\\d+),(?<doctorId>\\d+),(?<departmentId>\\d+),(?<status>\\d+),(?<uuid>\\w+)$");

    @Resource
    protected IBusHospitalWechatConfigService busHospitalWechatConfigService;

    @Resource
    protected WechatUtil weChatUtil;

    @Resource
    protected BusDoctorMapper busDoctorMapper;

    @Resource
    protected BusPatientHospitalMapper busPatientHospitalMapper;

    @Resource
    protected BusHospitalMapper busHospitalMapper;

    @Resource
    protected DrawImgUtil drawImgUtil;

    @Resource
    protected OSSUtil ossUtil;

    @Resource
    protected OSSProperties ossProperties;

    @Resource
    protected IBusPartnersService busPartnersService;

    @Resource
    protected IBusPatientHospitalService busPatientHospitalService;

    @Resource
    protected BusDrugsMapper busDrugsMapper;
    /**
     * 二维码推荐：药品详情地址
     */
    @Value("${referralQrcode.drugDetailUrl}")
    private String drugDetailUrl;

    @Resource
    private WxAccessTokenHelper wxAccessTokenHelper;

    protected void sendCustomerService(JSONObject jsonObject, Long hospitalId) {
        String token = this.getToken(hospitalId);
        String url = WechatInterface.CUSTOM_SEND_URL.replace("ACCESS_TOKEN", token);
        try {
            HttpUtils.sendPostJson(url, jsonObject.toString(), null);
        } catch (Exception e) {
            log.error("sendCustomerService 发送消息失败", e);
        }
    }
    protected String getToken(Long hospitalId, String code) {
        BusPartners busPartners = busPartnersService.queryPartners(hospitalId, code);
        if (busPartners == null) {
            throw new ServiceException("该合作机构公众号配置未设置");
        }
        WxAccessTokenDTO wxAccessTokenDTO = new WxAccessTokenDTO();
        wxAccessTokenDTO.setAppid(busPartners.getAppId());
        wxAccessTokenDTO.setAppSecret(busPartners.getAppSecret());
        wxAccessTokenDTO.setHospitalId(hospitalId);
        wxAccessTokenDTO.setPartnerCode(code);
        wxAccessTokenDTO.setClientType(ClientTypeEnum.WX_OFFICIAL_ACCOUNT);
        return wxAccessTokenHelper.getAccessToken(wxAccessTokenDTO);
    }

    protected String getToken(Long hospitalId) {
        BusHospitalWechatConfig hospitalWechatConfig = busHospitalWechatConfigService.selectByHospitalId(hospitalId, ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
        if (hospitalWechatConfig == null) {
            throw new ServiceException("该医院公众号配置未设置");
        }
        WxAccessTokenDTO wxAccessTokenDTO = new WxAccessTokenDTO();
        wxAccessTokenDTO.setAppid(hospitalWechatConfig.getAppid());
        wxAccessTokenDTO.setAppSecret(hospitalWechatConfig.getAppSecret());
        wxAccessTokenDTO.setHospitalId(hospitalId);
        wxAccessTokenDTO.setClientType(ClientTypeEnum.WX_OFFICIAL_ACCOUNT);
        return wxAccessTokenHelper.getAccessToken(wxAccessTokenDTO);
    }

    /**
     * 获取图文消息
     *
     * @param requestMap         微信请求对象
     * @param busBizDepartmentVo 公交业务部 vo
     * @param content            内容
     * @return {@link String }
     */
    protected String getArticleMsg(Map<String, String> requestMap, BusBizDepartmentVo busBizDepartmentVo, String content) {
        String respXml = "";
        Map<String, String> composeMap = new HashMap<>();
        composeMap.put("doctorName", busBizDepartmentVo.getFullName());
        composeMap.put("title", busBizDepartmentVo.getTitle());
        if (!FiveRoleEnum.DOCTOR.getCode().equals(busBizDepartmentVo.getRole())) {
            composeMap.put("title", FiveRoleEnum.getInfoByValue(busBizDepartmentVo.getRole()));
        }
        composeMap.put("departmentName", busBizDepartmentVo.getDepartmentName());
        composeMap.put("img", busBizDepartmentVo.getPhoto());
        composeMap.put("role", busBizDepartmentVo.getRole());
        ByteArrayOutputStream outStream = drawImgUtil.compose(composeMap);
        String doctorBusinessCard = ossUtil.simpleUpload(outStream, "doctorBusinessCard", ".png");
        List<ArticleItem> items = new ArrayList<>();
        ArticleItem item = new ArticleItem();
        item.setTitle("  ");
        item.setDescription("  ");
        item.setPicUrl(ossProperties.getFileAddressPrefix() + doctorBusinessCard);
        item.setUrl(content);
        items.add(item);
        ArticleItem item1 = new ArticleItem();
        String roleName = FiveRoleEnum.getInfoByValue(busBizDepartmentVo.getRole());
        item1.setTitle("加入" + roleName + "诊室，提供院前咨询、院后随访与" + roleName + "保持长期联系");
        item1.setDescription("加入诊室");
        item1.setUrl(content);
        items.add(item1);
        respXml = weChatUtil.sendArticleMsg(requestMap, items);
        return respXml;
    }

    /**
     * 填充扫码信息
     *
     * @param requestMap    请求参数
     * @param requestParam  请求参数
     * @return 填充结果
     */
    protected Map<String, String> informationFilling(Map<String, String> requestMap, WeChatQrCodeParamDto requestParam, BusHospitalWechatConfig wechatConfig) {
        log.debug("informationFilling 请求参数 requestMap：{}", JSON.toJSONString(requestMap));

        Map<String, String> returnMap = new HashMap<>();
        returnMap.put("ToUserName", requestMap.get("FromUserName"));
        returnMap.put("FromUserName", requestMap.get("ToUserName"));
        returnMap.put("CreateTime", DateUtils.getNowDate().getTime() + "");
        //微信转发回复文本
        StringBuilder content = new StringBuilder();
        //1 邀请患者 2快捷开处方 3线下扫码 4加入诊室 10绑定经纪人
        //判断科室是否存在
        if (!"10".equals(requestParam.getStatus()) && StringUtils.isNotEmpty(requestParam.getHospitalId())&& StringUtils.isNotEmpty(requestParam.getDoctorId()) && StringUtils.isNotEmpty(requestParam.getDepartmentId())) {
            BusDoctorDepartment doctorDepartment = new BusDoctorDepartment();
            doctorDepartment.setHospitalId(Long.valueOf(requestParam.getHospitalId()));
            // todo 生产 科室id 报空指针
            doctorDepartment.setDepartmentId(Long.valueOf(requestParam.getDepartmentId()));
            doctorDepartment.setDoctorId(Long.valueOf(requestParam.getDoctorId()));
            BusBizDepartmentVo bizDepartmentVo = busDoctorMapper.selectDoctorLeftDepartmentInfo(doctorDepartment);
            if (null == bizDepartmentVo) {
                throw new ServiceException("医生科室" + requestParam.getDepartmentId() + "不存在");
            }
            try {
                returnMap.put("doctor", JsonUtils.objectToJson(bizDepartmentVo));
            } catch (JsonProcessingException e) {
                log.error("json转换异常", e);
            }
        }
        //1 邀请患者 2快捷开处方 4加入诊室 10绑定经纪人
        String url = "<a href='" + wechatConfig.getTokenUrl() + "/pages/user/thePatient/index";
        returnMap.put("requestParam.getStatus()", requestParam.getStatus());
        switch (requestParam.getStatus()) {
            //1 邀请患者
            case "1":
                returnMap.put("MsgType", WechatContant.NEWS);
                content.append(wechatConfig.getTokenUrl()).append("/pages/user/thePatient/index")
                        .append("?hospitalId=").append(requestParam.getHospitalId())
                        .append("&doctorId=").append(requestParam.getDoctorId())
                        .append("&departmentId=").append(requestParam.getDepartmentId())
                        .append("&status=").append(requestParam.getStatus());
                break;
            //2快捷开处方
            case "2":
                returnMap.put("MsgType", "text");
                content.append("恭喜您进入快速开方诊室：\n" + "【1】请完善");
                content.append(url)
                        .append("?hospitalId=").append(requestParam.getHospitalId())
                        .append("&doctorId=").append(requestParam.getDoctorId())
                        .append("&departmentId=").append(requestParam.getDepartmentId())
                        .append("&status=").append(requestParam.getStatus())
                        .append("&uuid=").append(requestParam.getUuid())
                        .append("' >个人信息</a>");
                // todo 规范问题
                content.append("，通知医生，医生实时为您开具处方。\n"
                        + "【2】待药师审核通过后，您会收到处方开具成功的通知。\n"
                        + "【3】点击通知信息，选择药品的配送方式，在线完成支付，方可购药成功。\n"
                        + "【4】如果您选择快递送货到家，可随时留意物流信息。\n"
                        + "祝您生活愉快，身体健康！");

                break;
            //3线下扫码
            case "3":
                returnMap.put("MsgType", WechatContant.NEWS);
                content.append(wechatConfig.getTokenUrl()).append("/pages/user/thePatient/index")
                        .append("?hospitalId=").append(requestParam.getHospitalId())
                        .append("&doctorId=").append(requestParam.getDoctorId())
                        .append("&departmentId=").append(requestParam.getDepartmentId())
                        .append("&status=").append(requestParam.getStatus())
                        .append("&name=").append(requestParam.getName())
                        .append("&phone=").append(requestParam.getPhone())
                        .append("&dateOfBirth=").append(requestParam.getBirthday())
                        .append("&sex=").append(requestParam.getSex());
                break;
            //4加入诊室
            case "4":
                content.append(wechatConfig.getTokenUrl()).append("/pages/user/thePatient/index")
                        .append("?hospitalId=").append(requestParam.getHospitalId())
                        .append("&doctorId=").append(requestParam.getDoctorId())
                        .append("&departmentId=").append(requestParam.getDepartmentId())
                        .append("&status=").append(requestParam.getStatus()).append("&status=")
                        .append(requestParam.getStatus()).append("' >加入诊室</a>");
                break;
            //10绑定经纪人
            case "10":
//                String openid = requestMap.get("FromUserName");
//                BusPatientHospital patientHospital = busPatientHospitalMapper.selectOne(new LambdaQueryWrapper<BusPatientHospital>().eq(BusPatientHospital::getOpenid, openid));
//                if (null == patientHospital) {
//                    BusPatientHospital busPatientHospital = new BusPatientHospital();
//                    busPatientHospital.setHospitalId(Long.valueOf(requestParam.getHospitalId()));
//                    busPatientHospital.setOpenid(openid);
//                    busPatientHospital.setAgentId(Long.valueOf(requestParam.getDoctorId()));
//                    busPatientHospitalMapper.insert(busPatientHospital);
//                }
                BusHospital busHospital = busHospitalMapper.selectById(requestParam.getHospitalId());
                String hospitalName = busHospital.getHospitalName();
                hospitalName = hospitalName.replace("医院", "");
                String returnUrl = "<a href='" + wechatConfig.getTokenUrl() + "/pages/home/<USER>";
                returnMap.put("MsgType", "text");
                content.append(returnUrl)
                        .append("?hospitalId=").append(requestParam.getHospitalId())
                        .append("&agentId=").append(requestParam.getDoctorId())
                        .append("' >点击进入</a>「").append(hospitalName).append("互联网医院」\n");
                content.append("【1】极速在线问诊。\n"
                        + "【2】 名医专家专栏。\n"
                        + "【3】 复诊开方，在线购药，方便快捷。\n"
                        + "我们竭诚为您的健康保驾护航！");
                break;
            default:
                break;
        }
        //设置消息内容
        returnMap.put("content", content.toString());
        return returnMap;
    }

    /**
     * 获取用户信息
     *
     * @param openid       OpenID
     * @param hospitalId   医院 ID
     * @param partnersCode 合作机构code
     * @return {@link String }
     */
    public String getUserInfo(String openid, Long hospitalId, String partnersCode) {
        String accessToken;
        if (StringUtils.isNotEmpty(partnersCode)) {
            accessToken = getToken(hospitalId, partnersCode);
        } else {
            accessToken = getToken(hospitalId);
        }
        String url = WechatInterface.GETISGZH.replace("ACCESS_TOKEN", accessToken).replace("OPENID", openid);
        return weChatUtil.postRequestForWeiXinService(url);
    }

    /**
     * 获取二维码参数
     *
     * @param requestMap 请求参数
     * @return {@link WeChatQrCodeParamDto }
     */
    protected WeChatQrCodeParamDto getQrCodeParam(Map<String, String> requestMap) {

        String params = requestMap.get("EventKey");
        log.info("获取二维码参数 params:{}", params);
        //使用正则表达式 将字符串转成对象
        //线下二维码参数 正则表达式
        WeChatQrCodeParamDto qrCodeParam =  StringUtils.parseToBean(params, OFFLINE_QRCODE_PATTERN, WeChatQrCodeParamDto.class);

        //引导用户关注公众号二维码;邀请患者二维码;生成添加患者二维码;医生名片二维码 参数 正则表达式
        if (qrCodeParam == null){
            qrCodeParam = StringUtils.parseToBean(params, NORMAL_QRCODE_PATTERN, WeChatQrCodeParamDto.class);
        }
        return qrCodeParam;
    }

    /**
     * 推送药品详情链接
     */
    protected String sendDrugReferralQrcodeMsg(Map<String, String> requestMap, String eventKey, Long hospitalId){
        eventKey = eventKey.replace(String.format("qrscene_%s", DRUG_REFERRAL_QRCODE_IDENTIFY), "");
        //组合顺序：二维码id,医生id,医院id,药品id
        String[] splitArray = eventKey.split(",");
        if(splitArray.length != 4){
            log.error("药品推荐二维码参数错误：{}", requestMap);
            return null;
        }
        // 获取医院域名
        BusHospital busHospital = busHospitalMapper.selectById(hospitalId);
        if(busHospital == null || StringUtils.isEmpty(busHospital.getHospitalDomain())){
            log.error("医院未配置域名：{}", hospitalId);
            return null;
        }

        String url = String.format("https://%s%s?&qrcodeId=%s&doctorId=%s&hospitalId=%s&drugsId=%s&type=QRcode", busHospital.getHospitalDomain(),
                drugDetailUrl,
                Integer.valueOf(splitArray[0]),
                Long.valueOf(splitArray[1]),
                Long.valueOf(splitArray[2]),
                Long.valueOf(splitArray[3]));

        //获取药品详情，并组装微信卡片信息
        ByteArrayOutputStream outStream = drawImgUtil.composeDrugArticleMsg(busDrugsMapper.selectById(Long.valueOf(splitArray[3])), busHospital.getHospitalDomain());
        if(outStream == null){
            log.error("组装药品卡片信息失败：{}, 医院id：{}", requestMap, hospitalId);
            return null;
        }
        // 药品卡片上传
        String drugCard = ossUtil.simpleUpload(outStream, OSSSaveDirectory.DRUGS.getValue(), ".png");
        // 药品卡片信息组装
        List<ArticleItem> items = new ArrayList<>();
        ArticleItem item = new ArticleItem("  ", "  ", ossUtil.getFullPath(drugCard), url);
        items.add(item);
        ArticleItem item1 = new ArticleItem("用药咨询，安心购买，正品保障，快速送达。", "药品扫码", null, url);
        items.add(item1);
        String respXml = weChatUtil.sendArticleMsg(requestMap, items);
        log.info("药品推荐二维码，推送内容详情：{}", respXml);
        return respXml;
    }
}

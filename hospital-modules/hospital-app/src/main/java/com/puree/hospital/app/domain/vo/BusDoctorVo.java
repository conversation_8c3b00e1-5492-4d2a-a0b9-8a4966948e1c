package com.puree.hospital.app.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.app.domain.BusDoctorAuditRecord;
import com.puree.hospital.app.domain.BusDoctorConsultation;
import com.puree.hospital.app.domain.BusDoctorDepartment;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BusDoctorVo {

    @TableId(type= IdType.AUTO)
    private Long id;
    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 医生擅长 */
    private String beGoodAt;
    /** 医生编号 */
    private String doctorNumber;
    /** 医生姓名 */
    private String fullName;
    /** 身份证号码 */
    private String idCardNo;
    /** 医生身份证正反面图片 */
    private String idCardNoImg;
    /** 医生介绍 */
    private String introduce;
    /** 手机号码 */
    private String phoneNumber;
    /** 医生照片 */
    private String photo;
    /** 医生执业证 */
    private String practiceCertificate;
    /** 医生二维码 */
    private String qrCode;
    /** 医生资格证 */
    private String qualificationCertificate;
    /** 医生性别 0 男 1 女 */
    private Integer sex;
    /** 状态 0停用 1启用 */
    private Integer status;
    /** 医生职称(字典表关联数据) */
    private Long title;
    /** 医生职称证 */
    private String titleCertificate;

    private  String descriptionDetails;

    /**
     * 入驻时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settledTime;

    /**
     * 职称名称
     */
    private String titleValue;

    /**第一任职医院*/
    private String firstHospital;

    /**医院id*/
    private Long hospitalId;

    private Integer auditStatus;

    /**
     * 问诊设置list
     */
    private List<BusDoctorConsultation> busDoctorConsultationList;

    /**
     * 医生科室列表
     */
    private List<BusDoctorDepartmentVo> busDoctorDepartmentVoList;

    /**
     * 医生绑定医院
     */
    private List<BusDoctorHospitalVo> doctorHospitalList;
    /**
     * 近期看过的医生
     */
    private String yes;

    /**
     * 是否是中医
     */
    private Integer isTcm;
    /**
     * 是否开方医生
     */
    private Boolean isPreorderDoctor;

    private BusDoctorAuditRecord busDoctorAuditRecord;

    private String departmentName;
    private String doctorTitle;
    /**
     * 是否存在排版
     */
    private Boolean disable;
    private Long doctorId;
    /**
     * 是否关注(0未关注  1关注)
     */
    private Integer isAttention;
    /**
     * 医生角色 FiveRoleEnum枚举的info值
     */
    private String role;
    /**
     * 医生角色 FiveRoleEnum枚举的info值
     */
    private String roleId;
    /**
     * 审核内容（0西药 1中药 多选逗号分隔）审方药师独有
     */
    private String reviewContent;
    /**
     * 医生医保编码
     */
    private String nationalDoctorCode;
    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 执业类别编码
     * 代码 专业名称
     * 1 临床
     * 2 口腔
     * 3 公共卫生
     * 4 中医
     */
    private String practisingTypeCode;

    /**
     * 执业范围编码
     */
    private String practisingScopeCode;

    /**
     * 首次执业医院统一社会信用代码
     */
    private String firstHospitalUnifiedCreditCode;

    private String hospitalName;

    /**
     * 医生科室列表
     */
    private List<BusDoctorDepartment> doctorDepartmentList;

    /**
     * 医师签名url
     */
    private String certSignature;
}

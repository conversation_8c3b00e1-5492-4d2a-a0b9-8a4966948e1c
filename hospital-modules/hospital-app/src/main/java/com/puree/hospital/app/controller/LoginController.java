package com.puree.hospital.app.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.constant.SingleConstant;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDoctorPatient;
import com.puree.hospital.app.domain.BusFastPrescription;
import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPatientHospital;
import com.puree.hospital.app.domain.LoginLog;
import com.puree.hospital.app.domain.LoginMessage;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.AppletAuthorizationDTO;
import com.puree.hospital.app.domain.dto.AuthorizationDTO;
import com.puree.hospital.app.domain.dto.FastPrescriptionDto;
import com.puree.hospital.app.domain.dto.PatientDTO;
import com.puree.hospital.app.domain.dto.PatientLoginDTO;
import com.puree.hospital.app.domain.dto.PatientOfficialAccountLoginDTO;
import com.puree.hospital.app.domain.dto.PatientUnitAppLoginDTO;
import com.puree.hospital.app.domain.dto.DispensingPharmacistDTO;
import com.puree.hospital.app.domain.dto.SingleLoginDTO;
import com.puree.hospital.app.domain.dto.group.PhoneNumberSmsGroup;
import com.puree.hospital.app.domain.dto.group.UniAppAuthGroup;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.BusPatientVo;
import com.puree.hospital.app.domain.vo.LoginResultVO;
import com.puree.hospital.app.login.doctor.LoginStrategyManager;
import com.puree.hospital.app.mapper.BusDoctorHospitalMapper;
import com.puree.hospital.app.mapper.LoginLogMapper;
import com.puree.hospital.app.mapper.LoginMessageMapper;
import com.puree.hospital.app.service.IBusDoctorPatientService;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusFastPrescriptionService;
import com.puree.hospital.app.service.IBusHospitalAgreementService;
import com.puree.hospital.app.service.IBusOfficinaPharmacistService;
import com.puree.hospital.app.service.IBusPatientFamilyService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.app.service.IBusSignatureService;
import com.puree.hospital.app.service.ILoginService;
import com.puree.hospital.app.service.IWeChatService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.api.util.IpUtil;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.core.constant.LoginConstants;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.LoginTypeEnum;
import com.puree.hospital.common.core.exception.LoginException;
import com.puree.hospital.common.core.text.UUID;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.IdCardNumberUtils;
import com.puree.hospital.common.core.utils.IdUtils;
import com.puree.hospital.common.core.utils.ReqParamsHelper;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.ValidatorUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.core.utils.sign.MD5Util;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.five.api.RemoteFivePhysicianService;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.tool.api.RemoteSmsNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import static com.puree.hospital.common.api.constant.Constants.LOGIN_SUCCESS;

/**
 * 登录授权相关的控制器
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
@RefreshScope
@RestController
@RequestMapping("/login")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class LoginController extends BaseController {
    /**
     * 过期时间30天
     */
    private static final long EXPIRE_TIME = 30 * 24 * 60 * 60L;

    /**
     * 手机号的正则表达式-^[1][3,4,5,7,8][0-9]{9}$，这里先提供匹配9位及以上数字的正则表达式，后续根据实际情况调整
     */
    private final static Pattern NUMBERPATTERN = Pattern.compile("\\d+", 0);

    private final RedisService redisService;
    private final IBusDoctorService busDoctorService;
    private final IBusPatientService busPatientService;
    private final IBusOfficinaPharmacistService busOfficinaPharmacistService;
    private final IBusDoctorPatientService busDoctorPatientService;
    private final IBusFastPrescriptionService busFastPrescriptionService;
    private final IBusSignatureService busSignatureService;
    private final BusDoctorHospitalMapper busDoctorHospitalMapper;
    private final IBusHospitalAgreementService busHospitalAgreementService;
    private final IBusPatientHospitalService busPatientHospitalService;
    private final LoginMessageMapper loginMessageMapper;
    private final LoginLogMapper loginLogMapper;
    private final RemoteFivePhysicianService remoteFivePhysicianService;
    private final IBusPatientFamilyService busPatientFamilyService;
    private final ILoginService loginService;
    private final IWeChatService weChatService;
    private final RemoteSmsNotificationService remoteSmsNotificationService;
    private final LoginStrategyManager loginStrategyManager;

    /**
     * 医生、药师登录
     *
     * @param phoneNumbers 手机号
     * @param code 验证码
     * @return - {@link AjaxResult }
     */
    @PostMapping("doctorLogin")
    @Log(title = "医生、药师登录", businessType = BusinessType.OTHER)
    public AjaxResult doctorLogin(@RequestParam("phoneNumbers") String phoneNumbers,
                                  @RequestParam("code") String code, HttpServletRequest request) {
        try {
            AjaxResult verifyResult = remoteSmsNotificationService.checkVerifyCode(phoneNumbers, code);
            if (!verifyResult.isSuccess()) {
                return verifyResult;
            }
            return login(phoneNumbers, request);
        } catch (Exception e) {
            logger.error("医生登录异常={0}", e);
            return AjaxResult.error("验证码登录失败！");
        }
    }

    private AjaxResult<Map<String, Object>> login(String phoneNumbers, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        String token = IdUtils.fastSimpleUUID();
        String ipAddr = IpUtil.getIpAddr(request);
        String errMsg = physicianLoginCheck(map, token, phoneNumbers, null, ipAddr);
        if (StringUtils.isNotEmpty(errMsg) && (!LOGIN_SUCCESS.equals(errMsg))) {
            return AjaxResult.error(errMsg);
        }
        map.put(LoginConstants.TOKEN, token);
        map.put(LoginConstants.EXPIRE, EXPIRE_TIME);
        return AjaxResult.success(map);
    }

    /**
     * 医生、药师密码登录
     *
     * @param phoneNumbers 手机号
     * @param passWord 密码
     * @return - {@link AjaxResult }
     */
    @PostMapping("doctorPassWordLogin")
    @Log(title = "医生、药师密码登录", businessType = BusinessType.OTHER)
    public AjaxResult<Map<String, Object>> doctorPassWordLogin(@RequestParam("phoneNumbers") String phoneNumbers,
                                                               @RequestParam("passWord") String passWord,
                                                               HttpServletRequest request) {
        try {
            Map<String, Object> map = new HashMap<>();
            String token = IdUtils.fastSimpleUUID();
            String ipAddr = IpUtil.getIpAddr(request);
            String errMsg = physicianLoginCheck(map, token, phoneNumbers, passWord, ipAddr);
            if (StringUtils.isNotEmpty(errMsg) && (!LOGIN_SUCCESS.equals(errMsg))) {
                return AjaxResult.error(errMsg);
            }
            map.put(LoginConstants.TOKEN, token);
            map.put(LoginConstants.EXPIRE, EXPIRE_TIME);
            return AjaxResult.success(map);
        } catch (Exception e) {
            logger.error("医生登录异常={0}", e);
            return AjaxResult.error("账号或密码错误！");
        }
    }


    /**
     * 医生、药师(五师)登录
     *
     * @param phoneNumbers 手机号
     * @return - {@link AjaxResult }
     */
    @PostMapping("doctorOneClickLogin")
    @Log(title = "医生、药师(五师)登录", businessType = BusinessType.OTHER)
    public AjaxResult doctorOneClickLogin(@RequestParam("phoneNumbers") String phoneNumbers, HttpServletRequest request) {
        try {
            return login(phoneNumbers, request);
        } catch (Exception e) {
            logger.error("其他医师登录异常={0}", e);
            return AjaxResult.error("验证码登录失败！");
        }
    }

    /**
     * 患者登录
     *
     * @param dto     登录参数
     * @param request HttpServletRequest
     * @return {@link R }
     */
    @PostMapping("patientLogin")
    @Log(title = "患者登录", businessType = BusinessType.OTHER)
    public R<LoginResultVO> patientLogin(PatientOfficialAccountLoginDTO dto,
                                         HttpServletRequest request) {
        ValidatorUtils.validate(dto, PhoneNumberSmsGroup.class);
        dto.setLoginMethod(PatientLoginDTO.LoginMethodEnum.PHONE_NUMBER);
        dto.setLoginType(LoginTypeEnum.OFFICIAL_ACCOUNT);
        dto.setLoginIp(IpUtil.getIpAddr(request));
        dto.setPartnersCode(SecurityUtils.getPartnerscode());
        logger.info("患者公众号登录入参={}", dto);
        return busPatientService.patientLogin(dto);
    }


    /**
     * 患者登录
     *
     * @param dto      登录参数
     * @param request  HttpServletRequest
     * @return {@link R }
     */
    @PostMapping("uniappLogin")
    @Log(title = "患者小程序登录", businessType = BusinessType.OTHER)
    public R<LoginResultVO> uniAppLogin(PatientUnitAppLoginDTO dto, HttpServletRequest request) {
        ValidatorUtils.validate(dto, PhoneNumberSmsGroup.class);
        dto.setLoginMethod(PatientLoginDTO.LoginMethodEnum.PHONE_NUMBER);
        dto.setLoginType(LoginTypeEnum.UNI_APP_APPLET);
        dto.setLoginIp(IpUtil.getIpAddr(request));
        dto.setPartnersCode(SecurityUtils.getPartnerscode());
        logger.info("患者小程序登录入参={}", dto);
        return busPatientService.patientLogin(dto);
    }

    /**
     * 患者小程序授权登录
     *
     * @param dto      登录参数
     * @param request  HttpServletRequest
     * @return {@link R }
     */
    @PostMapping("uniappAuthLogin")
    @Log(title = "患者小程序授权登录", businessType = BusinessType.OTHER)
    public R<LoginResultVO> uniAppAuthLogin(@RequestBody PatientUnitAppLoginDTO dto, HttpServletRequest request) {
        ValidatorUtils.validate(dto, UniAppAuthGroup.class);
        if (dto.isAuthLogin()) {
            dto.setLoginMethod(PatientLoginDTO.LoginMethodEnum.AUTH);
        } else {
            dto.setLoginMethod(PatientLoginDTO.LoginMethodEnum.QUICK_LOGIN);
        }
        dto.setLoginType(LoginTypeEnum.UNI_APP_APPLET);
        dto.setLoginIp(IpUtil.getIpAddr(request));
        dto.setPartnersCode(SecurityUtils.getPartnerscode());
        logger.info("患者小程序登录入参={}", dto);
        return busPatientService.patientLogin(dto);
    }

    /**
     * 查询微信号是否绑定医院/合作机构
     * @param jsCode 微信jsCode
     * @param hospitalId 医院id
     * @return boolean 是否绑定
     */
    @GetMapping("patient/uni-app/check-bind")
    @Log(title = "微信号是否绑定医院查询", businessType = BusinessType.OTHER)
    public AjaxResult uniAppCheckBind(@RequestParam("jsCode") String jsCode,
                                      @RequestParam(value = "hospitalId") Long hospitalId) {
        return AjaxResult.success(busPatientService.uniAppCheckBind(jsCode, hospitalId, SecurityUtils.getPartnerscode()));
    }


    @Deprecated
    @PostMapping("bindAgentId")
    @Log(title = "患者绑定渠道码", businessType = BusinessType.OTHER)
    public AjaxResult bindAgentId(Long agentId, String openid) {
        SMSLoginUser smsLoginUser = redisService.getCacheObject(CacheConstants.LOGIN_TOKEN_KEY + SecurityUtils.getToken());
        if (Objects.isNull(smsLoginUser)) {
            return AjaxResult.error(HttpStatus.UNAUTHORIZED, null, "用户未登录");
        }
        if (StringUtils.isNotBlank(smsLoginUser.getIdentity()) && !AppRoleEnum.isPatient(smsLoginUser.getIdentity())) {
            return AjaxResult.error(HttpStatus.BAD_REQUEST, null, "非法请求");
        }
        if (agentId == null || StringUtils.isEmpty(openid)) {
            return AjaxResult.error("参数错误");
        }
        return AjaxResult.success();
    }

    /**
     * 获取患者信息
     *
     * @param request HttpServletRequest
     * @return {@link R }
     */
    @GetMapping("patientInfo")
    @Log(title = "获取患者信息", businessType = BusinessType.OTHER)
    public AjaxResult getPatientInfo(HttpServletRequest request) {
        String token = request.getHeader(SecurityConstants.TOKEN_AUTHENTICATION);
        if (StringUtils.isEmpty(token)) {
            return AjaxResult.error(HttpStatus.UNAUTHORIZED, null, "用户未登录");
        }
        token = token.replace(SecurityConstants.TOKEN_PREFIX, "");
        String openid = request.getHeader("openid");
        String partnersCode = request.getHeader("partnersCode");
        if (!org.springframework.util.StringUtils.hasText(openid)) {
            return AjaxResult.error(HttpStatus.UNAUTHORIZED, null, "openid缺失");
        }
        logger.info("患者登录token={}，openid={}，合作机构code={}", token, openid, partnersCode);
        if (StringUtils.isEmpty(token)) {
            return AjaxResult.error(HttpStatus.UNAUTHORIZED, null, "token缺失");
        }
        SMSLoginUser smsLoginUser = redisService.getCacheObject(CacheConstants.LOGIN_TOKEN_KEY + token);
        if (smsLoginUser == null) {
            return AjaxResult.error(HttpStatus.UNAUTHORIZED, null, "获取用户登录token失败");
        }
        logger.info("用户信息={}", smsLoginUser);
        BusPatientVo patientVo = busPatientService.selectPatientById(smsLoginUser, openid);
        BusPatientHospital patientHospital = patientVo.getBusPatientHospital();
        if (patientHospital == null) {
            return AjaxResult.error(HttpStatus.UNAUTHORIZED, null, "您还未注册");
        }
        return AjaxResult.success(patientVo);
    }

    /**
     * 获取医生、药师信息
     *
     * @param request HttpServletRequest
     * @return {@link R }
     */
    @GetMapping("info")
    @Log(title = "获取医生、药师信息", businessType = BusinessType.OTHER)
    public AjaxResult get(HttpServletRequest request) {
        String token = request.getHeader(SecurityConstants.TOKEN_AUTHENTICATION);
        token = token.replace(SecurityConstants.TOKEN_PREFIX, "");
        if (StringUtils.isEmpty(token)) {
            return AjaxResult.error("token缺失");
        }
        try {
            SMSLoginUser smsLoginUser = redisService.getCacheObject(CacheConstants.LOGIN_TOKEN_KEY + token);
            logger.info("获取缓存信息={}", smsLoginUser);
            if (null == smsLoginUser) {
                return AjaxResult.error(HttpStatus.UNAUTHORIZED, null, "登录状态已过期");
            }
            // 复核药师登录
            if (AppRoleEnum.DISPENSING_PHARMACIST.getCode().equals(smsLoginUser.getIdentity())) {
                // 复核药师信息
                return AjaxResult.success(busOfficinaPharmacistService.getReviewPharmacistInfoById(smsLoginUser.getUserid()));
            }
            BusDoctorVo busDoctorVo = busDoctorService.selectDoctorDetailInfoById(smsLoginUser.getUserid());
            if (busDoctorVo == null) {
                return AjaxResult.error(HttpStatus.UNAUTHORIZED, null, "账号已被停用,请联系管理员");
            }
            //判断匹配的手机号是否符合都是数字规则的
            if (!NUMBERPATTERN.matcher(busDoctorVo.getPhoneNumber()).matches()) {
                busDoctorVo.setPhoneNumber(DESUtil.decrypt(busDoctorVo.getPhoneNumber()));
            }
            if (CollectionUtil.isNotEmpty(busDoctorVo.getDoctorHospitalList())) {
                busDoctorVo.setReviewContent(busDoctorVo.getDoctorHospitalList().get(0).getReviewContent());
            }
            return AjaxResult.success(busDoctorVo);
        } catch (Exception e) {
            logger.error("查询医生详情信息={0}", e);
            return AjaxResult.error("获取用户登录信息失败");
        }
    }

    /**
     * 患者绑定到医生
     *
     * @param doctorPatient 患者医生关联信息
     * @return {@link R }
     */
    @PostMapping("patient/bindingToDoctor")
    @Log(title = "患者绑定到医生", businessType = BusinessType.OTHER)
    public AjaxResult bindingToDoctor(BusDoctorPatient doctorPatient) {
        return AjaxResult.success(busDoctorPatientService.insertDoctorPatient(doctorPatient));
    }

    /**
     * 医生绑定到医院
     *
     * @param phoneNumbers 手机号
     * @param code 验证码
     * @return {@link R }
     */
    @PostMapping("/doctor/bindingToHospital")
    @Log(title = "医生绑定到医院", businessType = BusinessType.OTHER)
    public AjaxResult doctorBinding(String phoneNumbers, String hospitalId, String code) {
        if ("undefined".equals(hospitalId) || "null".equals(hospitalId) || "null".equals(code)) {
            return AjaxResult.error("连接异常，请重新再试");
        }
        AjaxResult result = remoteSmsNotificationService.checkVerifyCode(phoneNumbers, code);
        if (!result.isSuccess()) {
            return result;
        }
        logger.info("手机号:{}", phoneNumbers);
        String decrypt = DESUtil.decrypt(hospitalId);
        BusDoctorVo doctorVo = busDoctorService.selectDoctorByPhoneAndHospitalId(phoneNumbers,
                Long.valueOf(decrypt));
        if (doctorVo != null) {
            if (YesNoEnum.NO.getCode().equals(doctorVo.getStatus())) {
                return AjaxResult.error("当前账号异常，如需关联请联系医院管理员");
            }
            return AjaxResult.success(CodeEnum.YES.getCode());
        }
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistService.selectPharmacistByPhone(DESUtil.decrypt(phoneNumbers));
        if (pharmacist != null) {
            return AjaxResult.success(CodeEnum.YES.getCode());
        }

        //五师角色校验
        R assistantByPhone = remoteFivePhysicianService.getRCAssistantByPhone(phoneNumbers, Long.valueOf(decrypt));
        if (Constants.FAIL.equals(assistantByPhone.getCode())) {
            throw new LoginException("内部服务器错误，请稍后重试");
        }
        Object data = assistantByPhone.getData();
        if (data != null) {
            return AjaxResult.success(CodeEnum.YES.getCode());
        }

        logger.info("医院id{}", decrypt);
        BusDoctor doctor = new BusDoctor();
        doctor.setPhoneNumber(phoneNumbers);
        return toAjax(busDoctorService.insertBusDoctor(doctor, Long.valueOf(decrypt)));
    }

    /**
     * 校验患者是否注册
     *
     * @param openid     微信openid
     * @return {@link R }
     */
    @GetMapping("patient/checkOpenid")
    @Log(title = "校验患者是否注册", businessType = BusinessType.OTHER)
    public AjaxResult checkOpenid(String openid) {
        BusPatientVo busPatientVo = busPatientService.selectPatientByOpenid(openid);
        return AjaxResult.success(busPatientVo == null ? false : busPatientVo.getId());
    }

    /**
     * 校验患者是否注册（合作机构患者）
     *
     * @param openid     微信openid
     * @return {@link R }
     */
    @Log(title = "校验患者是否注册")
    @GetMapping("partners/patient/checkOpenid")
    public AjaxResult checkPartnersOpenid(String openid) {
        BusPatientVo busPatientVo = busPatientService.selectPartnersPatientByOpenid(openid);
        return AjaxResult.success(busPatientVo == null ? false : busPatientVo.getId());
    }

    /**
     * 患者绑定快捷开处方二维码信息
     *
     * @param dto 患者绑定快捷开处方二维码信息参数
     * @return {@link R }
     */
    @PostMapping("bindingToFastQRCode")
    @Log(title = "患者绑定快捷开处方二维码信息", businessType = BusinessType.OTHER)
    public AjaxResult bindingToFastQrCode(@RequestBody FastPrescriptionDto dto) {
        logger.info("患者绑定快捷开处方二维码请求参数:dto={}", dto);
        BusFastPrescription busFastPrescription = OrikaUtils.convert(dto, BusFastPrescription.class);
        logger.info("患者绑定快捷开处方二维码转换参数:dto={}", busFastPrescription);
        BusFastPrescription result = busFastPrescriptionService.select(busFastPrescription);
        if (null != result) {
            return AjaxResult.error("本次快捷开处方已绑定过二维码了");
        }
        busFastPrescription.setCreateBy(SecurityUtils.getUsername());
        busFastPrescription.setCreateTime(DateUtils.getNowDate());
        return AjaxResult.success(busFastPrescriptionService.insert(busFastPrescription));
    }

    /**
     * 查询患者绑定的快捷开处方二维码信息
     *
     * @param uuid uuid
     * @return {@link R }
     */
    @GetMapping("queryFastQRCodeBindingInfo")
    @Log(title = "查询患者绑定的快捷开处方二维码信息", businessType = BusinessType.OTHER)
    public AjaxResult queryFastQrCodeBindingInfo(@RequestParam("uuid") Long uuid) {
        logger.info("查询患者绑定的快捷开处方二维码:uuid={}", uuid);
        BusFastPrescription busFastPrescription = new BusFastPrescription();
        busFastPrescription.setUuid(uuid);
        return AjaxResult.success(busFastPrescriptionService.select(busFastPrescription));
    }

    /**
     * 退出登录
     *
     * @return {@link R }
     */
    @PostMapping("logout")
    @Log(title = "退出登录", businessType = BusinessType.OTHER)
    public AjaxResult logout(@RequestParam("token") String token, HttpServletRequest request) {
        if (!org.springframework.util.StringUtils.hasText(token)) {
            token = request.getHeader(SecurityConstants.TOKEN_AUTHENTICATION).replace(SecurityConstants.TOKEN_PREFIX, "");
        }
        // 查询登录信息
        LambdaQueryWrapper<LoginMessage> queryWrapper = new LambdaQueryWrapper<LoginMessage>()
                .eq(LoginMessage::getToken, token);
        LoginMessage loginMessage = loginMessageMapper.selectOne(queryWrapper);
        if (loginMessage != null) {
            // 保存登出日志
            String ipAddr = IpUtil.getIpAddr(request);
            LoginLog loginLog = new LoginLog();
            loginLog.setHospitalId(loginMessage.getHospitalId());
            loginLog.setLoginId(loginMessage.getId());
            loginLog.setToken(token);
            loginLog.setLoginIp(ipAddr);
            loginLog.setLoginType(YesNoEnum.YES.getCode());
            loginLog.setOverdueTime(DateUtils.getNowDate());
            loginLog.setCreateTime(DateUtils.getNowDate());
            loginLogMapper.insert(loginLog);
        }
        String userSmsKey = CacheConstants.LOGIN_TOKEN_KEY + token;
        redisService.deleteObject(userSmsKey);
        loginMessageMapper.deleteById(loginMessage);
        return AjaxResult.success();
    }


    /**
     * 根据医院id获取医院协议详细信息
     */
    @GetMapping(value = "query/hospital/agreement")
    @Log(title = "根据医院id获取医院协议详细信息", businessType = BusinessType.OTHER)
    public AjaxResult query(@RequestParam("hospitalId") Long hospitalId) {
        return AjaxResult.success(busHospitalAgreementService.selectByHospitalId(hospitalId));
    }

    @GetMapping(value = "index")
    public AjaxResult index(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || "unknow".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if ("127.0.0.1".equals(ip)) {
                //根据网卡取本机配置的IP
                InetAddress inet;
                try {
                    inet = InetAddress.getLocalHost();
                    ip = inet.getHostAddress();
                } catch (Exception e) {
                    logger.error("获取本机IP地址失败", e);
                }
            }
        }
        // 多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        return AjaxResult.success(ip);
    }

    private String physicianLoginCheck(Map<String, Object> map, String token, String phoneNumbers, String passWord, String ipAddr) {
        return loginStrategyManager.loginCheck(map, token, phoneNumbers, passWord, ipAddr);
    }

    /**
     * 单点登录
     * @param dto 登录参数
     * @return {@link AjaxResult }
     */
    @Log(title = "单点登录")
    @PostMapping("/single")
    public AjaxResult singleLogin(@RequestBody SingleLoginDTO dto) {
        logger.info("单点登录入参={}", dto);
        HttpPost httpPost = new HttpPost(SingleConstant.URL + "/open/user/getSKUserInfo");
        Map<String, String> map = new HashMap<>();
        map.put("grantCode", dto.getPlatformGrantCode());
        String s1 = JSON.toJSONString(map);
        String openContentMd5 = MD5Util.md5Encrypt32Lower(s1);
        String openTimestamp = System.currentTimeMillis() + "";
        String openNonce = UUID.randomUUID().toString();
        String openHosId = SingleConstant.OPEN_HOS_ID;
        String secret = SingleConstant.SECRET;
        try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("X-Open-Content-MD5", openContentMd5);
            httpPost.setHeader("X-Open-Timestamp", openTimestamp);
            httpPost.setHeader("X-Open-Nonce", openNonce);
            httpPost.setHeader("X-Open-HosId", openHosId);
            // 生成签名
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("X-Open-HosId", openHosId);
            paramsMap.put("X-Open-Nonce", openNonce);
            paramsMap.put("X-Open-Timestamp", openTimestamp);
            paramsMap.put("X-Open-Content-MD5", openContentMd5);
            String s = ReqParamsHelper.genReqStringByMap(paramsMap);
            s = s + "&" + secret;
            String openSignature = MD5Util.md5Encrypt32Lower(s);
            httpPost.setHeader("X-Open-Signature", openSignature);
            httpPost.setEntity(new StringEntity(s1, "utf-8"));
            CloseableHttpResponse response = httpclient.execute(httpPost);
            String result = EntityUtils.toString(response.getEntity(), "utf-8");
            logger.info("查询导引平台用户信息={}", result);
            JSONObject jsonObject = JSON.parseObject(result);
            int code = jsonObject.getIntValue("code");
            if (HttpStatus.SUCCESS == code) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    String phone = data.getString("phone");
                    String name = data.getString("name");
                    String identity = data.getString("identity");
                    // 查询用户是否注册过
                    BusPatient busPatient = busPatientService.selectPatientByPhone(phone);
                    Long patientId;
                    if (busPatient == null) {
                        // 保存患者信息
                        BusPatient patient = new BusPatient();
                        patient.setPhoneNumber(phone);
                        BusPatientHospital busPatientHospital = new BusPatientHospital();
                        busPatientHospital.setHospitalId(dto.getHospitalId());
                        busPatientHospital.setOpenid(dto.getOpenid());
                        patientId = busPatientService.insertBusPatient(patient, busPatientHospital);
                        // 新增默认就诊人
                        BusPatientFamily busPatientFamily = new BusPatientFamily();
                        busPatientFamily.setIdNumber(identity);
                        busPatientFamily.setName(name);
                        busPatientFamily.setCellPhoneNumber(phone);
                        Date dateOfBirth = IdCardNumberUtils.getBirthDayFromIdCard(identity);
                        busPatientFamily.setDateOfBirth(dateOfBirth);
                        int sex = IdCardNumberUtils.getSexFromIdCard(identity);
                        busPatientFamily.setSex(sex == 1 ? "1" : "0");
                        // 查询患者关系信息
                        Long id = busPatientFamilyService.selectRelationShip("本人");
                        busPatientFamily.setPatientRelationship(id);
                        busPatientFamily.setStatus(YesNoEnum.YES.getCode());
                        busPatientFamily.setPatientId(patientId);
                        busPatientFamily.setSource("3");
                        busPatientFamily.setHospitalId(dto.getHospitalId());
                        busPatientFamily.setOutpatientNumber(String.valueOf(System.currentTimeMillis()));
                        busPatientFamilyService.addFamily(busPatientFamily);
                    } else {
                        patientId = busPatient.getId();
                    }
                    String token = IdUtils.fastSimpleUUID();
                    Map<String, Object> resultMap = new HashMap<>();
                    SMSLoginUser smsLoginUser = new SMSLoginUser();
                    smsLoginUser.setToken(token);
                    smsLoginUser.setHospitalId(dto.getHospitalId());
                    smsLoginUser.setUserid(patientId);
                    smsLoginUser.setUsername(phone);
                    // 缓存登录信息
                    String userSmsKey = CacheConstants.LOGIN_TOKEN_KEY + token;
                    redisService.setCacheObject(userSmsKey, smsLoginUser, EXPIRE_TIME, TimeUnit.SECONDS);
                    resultMap.put("token", token);
                    resultMap.put("expire_time", EXPIRE_TIME);
                    return AjaxResult.success(resultMap);
                }
            }
        } catch (Exception e) {
            logger.error("单点登录异常={0}", e);
        }
        return AjaxResult.success();
    }

    /**
     * 智能心管家小程序-授权登录
     * @param dto 登录参数
     * @return {@link AjaxResult }
     */
    @Log(title = "智能心管家小程序-授权登录")
    @PostMapping("/applet/authorization")
    public AjaxResult appletAuthorization(@RequestBody AppletAuthorizationDTO dto) {
        try {
            if (StringUtils.isEmpty(dto.getJsCode())) {
                return AjaxResult.error("登录凭证不能为空！");
            }
            if (StringUtils.isEmpty(dto.getCode())) {
                return AjaxResult.error("手机号获取凭证不能为空！");
            }
            String token = IdUtils.fastSimpleUUID();
            dto.setToken(token);
            String openid = loginService.appletAuthorization(dto);
            Map<String, Object> map = new HashMap<>(3);
            map.put("openid", openid);
            map.put("token", token);
            map.put("expire_time", EXPIRE_TIME);
            return AjaxResult.success(map);
        } catch (Exception ex) {
            logger.error("智能心管家小程序授权登录异常", ex);
            return AjaxResult.error("授权登录失败");
        }
    }

    /**
     * 修改患者登录手机号
     *
     * @param dto 登录参数
     * @return {@link AjaxResult }
     */
    @PutMapping("/modify/phoneNumber")
    @Log(title = "修改患者登录手机号", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult modifyPhoneNumber(@RequestBody PatientDTO dto) {
        logger.debug("修改患者登录手机号入参={}", dto);
        // TODO 待修改前端交互，不能一次校验两个手机号，拆分两次校验
        String oldCode = redisService.getCacheObject(CacheConstants.SMS_VERIFY_KEY + dto.getOldPhoneNumber());
        if (StringUtils.isEmpty(dto.getOldCode()) || !dto.getOldCode().equals(oldCode)) {
            return AjaxResult.error("原手机号验证码错误");
        }
        String newCode = redisService.getCacheObject(CacheConstants.SMS_VERIFY_KEY + dto.getNewPhoneNumber());
        if (StringUtils.isEmpty(dto.getNewCode()) || !dto.getNewCode().equals(newCode)) {
            return AjaxResult.error("新手机号验证码错误");
        }
        boolean result = loginService.modifyBinding(dto);
        redisService.deleteObject(CacheConstants.SMS_VERIFY_KEY + dto.getOldPhoneNumber());
        redisService.deleteObject(CacheConstants.SMS_VERIFY_KEY + dto.getNewPhoneNumber());
        return toAjax(result);
    }

    /**
     * 患者授权获取微信昵称
     * @param dto 授权参数
     * @return {@link AjaxResult }
     */
    @PostMapping("/authorization")
    @Log(title = "患者授权获取微信昵称", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public AjaxResult patientAuthorization(@RequestBody AuthorizationDTO dto) {
        String oauth2UserInfo = weChatService.oauth2UserInfo(dto.getCode(), dto.getHospitalId());
        JSONObject jsonObject = JSON.parseObject(oauth2UserInfo);
        String accessToken = jsonObject.getString("access_token");
        String openid = jsonObject.getString("openid");
        if (StringUtils.isNotEmpty(accessToken) && StringUtils.isNotEmpty(openid)) {
            JSONObject userInfo = weChatService.getUserInfoByNacos(openid, accessToken);
            String nickname = userInfo.getString("nickname");
            nickname = new String(nickname.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
            userInfo.put("nickname", nickname);
            return AjaxResult.success(userInfo);
        } else {
            return AjaxResult.error("获取微信授权token失败！");
        }
    }

    /**
     * 修改患者登录微信号
     * @param dto 登录参数
     * @return {@link AjaxResult }
     */
    @PutMapping("/modify/openid")
    @Log(title = "修改患者登录微信号", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult modifyOpenid(@RequestBody PatientDTO dto) {
        AjaxResult checkResult = remoteSmsNotificationService.checkVerifyCode(dto.getNewPhoneNumber(), dto.getNewCode());
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        boolean result = busPatientService.modifyOpenid(dto);
        return toAjax(result);
    }

    /**
     * 复核药师设置登录密码
     *
     * @param dto 登录信息
     * @return 设置结果
     */
    @PutMapping("/dispensing/password")
    @Log(title = "复核药师设置登录密码", businessType = BusinessType.UPDATE)
    public R<Integer> modifyPassword(@RequestBody @Validated DispensingPharmacistDTO dto) {
        return R.ok(busOfficinaPharmacistService.modifyPassword(dto));
    }
}

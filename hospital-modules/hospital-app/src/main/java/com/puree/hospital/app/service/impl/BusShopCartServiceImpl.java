package com.puree.hospital.app.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.app.config.DrugLimitConfig;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusDrugs;
import com.puree.hospital.app.domain.BusHospitalPa;
import com.puree.hospital.app.domain.BusPreorderDrugs;
import com.puree.hospital.app.domain.BusPreorderPatient;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusShopCart;
import com.puree.hospital.app.domain.BusShopCartItem;
import com.puree.hospital.app.domain.BusShopRxCart;
import com.puree.hospital.app.domain.vo.BusOfficinaEntDrugVO;
import com.puree.hospital.app.domain.vo.BusShopCartListVO;
import com.puree.hospital.app.domain.vo.DoctorOfficinaDrugsVo;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDrugsMapper;
import com.puree.hospital.app.mapper.BusHospitalPaMapper;
import com.puree.hospital.app.mapper.BusPreorderDrugsMapper;
import com.puree.hospital.app.mapper.BusPreorderPatientMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusShopCartMapper;
import com.puree.hospital.app.service.IBusHospitalOfficinaService;
import com.puree.hospital.app.service.IBusShopCartService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.ShopCartTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import com.puree.hospital.shop.api.RemoteShopGoodsService;
import com.puree.hospital.shop.api.model.BusShopGoods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

/**
 * 购物车表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-02
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusShopCartServiceImpl implements IBusShopCartService {
    private final BusShopCartMapper busShopCartMapper;
    private final RemoteShopGoodsService remoteShopGoodsService;
    private final IBusHospitalOfficinaService officinaService;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPrescriptionDrugsMapper prescriptionDrugsMapper;
    private final BusPreorderDrugsMapper busPreorderDrugsMapper;
    private final BusPreorderPatientMapper preorderMapper;
    private final BusHospitalPaMapper paMapper;
    public final RedisTemplate<String, String> redisTemplate;
    private final BusDrugsMapper drugsMapper;
    private final BusDoctorMapper doctorMapper;
    @Resource
    private RemoteHospitalSettingApi remoteHospitalSettingApi;

    private static final Long RX_Flag = 391L;
    public static final String DRUGS_LIMIT_KEY = "setting.goodDrugLimit";

    @Override
    public int insert(BusShopCart busShopCart) {
        busShopCart.setCreateTime(DateUtils.getNowDate());
        return busShopCartMapper.insert(busShopCart);
    }

    @Override
    public int delete(List<Long> cartIds) {
        return busShopCartMapper.deleteBatchIds(cartIds);
    }

    @Override
    public int deleteGoods(Long goodsId) {
        return busShopCartMapper.delete(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getType, ShopCartTypeEnum.GOODS.getCode())
                .eq(BusShopCart::getBusinessId, goodsId));
    }

    @Override
    public int deleteBusinessById(BusShopCart busShopCart) {
        return busShopCartMapper.delete(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getBusinessId, busShopCart.getBusinessId())
                .eq(BusShopCart::getType, busShopCart.getType())
                .eq(BusShopCart::getHospitalId, busShopCart.getHospitalId())
                .eq(BusShopCart::getPatientId, busShopCart.getPatientId()));
    }

    @Override
    public BusShopCartListVO listCart(Long hospitalId, Long patientId) {
        log.info("查询购物车列表医院ID={}，患者ID={}", hospitalId, patientId);
        BusShopCartListVO cartList = new BusShopCartListVO();
        List<BusShopRxCart> rxList = new ArrayList<>();
        List<BusShopRxCart> bookList = new ArrayList<>();
        List<BusShopCartItem> productList = new ArrayList<>();
        List<BusShopCartItem> invalidList = new ArrayList<>();
        // 查询患者全部购物车列表
        List<BusShopCart> shopCarts = busShopCartMapper.selectList(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getHospitalId, hospitalId)
                .eq(BusShopCart::getPatientId, patientId)
                .orderByDesc(BusShopCart::getCreateTime));
        for (BusShopCart cart : shopCarts) {
            if (ShopCartTypeEnum.PRESCRIPTION.getCode().equals(cart.getType())) {
                packRxCart(rxList, cart);
            } else if (ShopCartTypeEnum.ADVANCE_ORDER.getCode().equals(cart.getType())) {
                packPreOrderCart(bookList, cart);
            } else if (ShopCartTypeEnum.DRUGS.getCode().equals(cart.getType())) {
                packDrugsCart(invalidList, productList, cart);
            } else if (ShopCartTypeEnum.GOODS.getCode().equals(cart.getType())) {
                packGoodsCart(invalidList, productList, cart);
            }
        }
        cartList.setRxList(rxList);
        cartList.setBookList(bookList);
        cartList.setProductList(productList);
        cartList.setInvalidList(invalidList);
        return cartList;
    }

    /**
     * 添加中药处方药品
     *
     * @param rx      处方
     * @param rxDrugs 处方药品
     * @param list    购物车项药品集合
     * @param rxCart  处方购物车
     */
    private void addTcmDrugs(BusPrescription rx, List<BusPrescriptionDrugs> rxDrugs, List<BusShopCartItem> list, BusShopRxCart rxCart) {
        for (BusPrescriptionDrugs rxDrug : rxDrugs) {
            BusShopCartItem cartItem = new BusShopCartItem(YesNoEnum.NO.getCode(), Integer.valueOf(rxDrug.getWeight()),
                    rxDrug.getDrugsId(), rxDrug.getDrugsName(), null, rxDrug.getDrugsSpecification(), rxDrug.getSellingPrice());
            BusDrugs d = drugsMapper.selectById(rxDrug.getDrugsId());
            cartItem.setDrugsDosageForm(d.getDrugsDosageForm());
            cartItem.setNationalDrugCode(rxDrug.getMiCode());
            cartItem.setMedicalInsuranceType(rxDrug.getMedicalInsuranceType());
            list.add(cartItem);
        }
        String[] dosage = StringUtils.split(rx.getUsages(), ",");
        rxCart.setDosage(Integer.valueOf(dosage[1]));
        // 药品总金额 = 处方总金额 - 配药费 - 诊查费
        BigDecimal rxDrugTotal = rx.getPrescriptionAmount().subtract(rx.getProcessPrice()).subtract(rx.getExaminationFee());
        // 药品单剂价格 =  药品总金额/ 帖数
        BigDecimal price = rxDrugTotal.divide(BigDecimal.valueOf(rxCart.getDosage()), 2, RoundingMode.HALF_UP);
        rxCart.setRxPrice(price.doubleValue());
        rxCart.setRxType(YesNoEnum.YES.getCode());
        rxCart.setProcessPrice(rx.getProcessPrice().doubleValue());
        rxCart.setProcessingMethod(rx.getProcessingMethod());
    }

    @Override
    public int updateGoodsQuantity(BusShopCart shopCart) {
        return busShopCartMapper.updateById(shopCart);
    }

    @Override
    public List<BusShopCart> getGoodsIsExists(Long hospitalId, String type, Long patientId, List<Long> ids) {
        List<BusShopCart> shopCarts = busShopCartMapper.selectList(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getPatientId, patientId)
                .eq(BusShopCart::getHospitalId, hospitalId)
                .eq(BusShopCart::getType, type)
                .in(BusShopCart::getBusinessId, ids));
        return shopCarts;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePrescriptionOrOrder(Long businessId, boolean flag) {
        BusPrescription getPrescription = busPrescriptionMapper.selectById(businessId);
        BusShopCart shopCart = new BusShopCart();
        shopCart.setHospitalId(getPrescription.getHospitalId());
        shopCart.setPatientId(getPrescription.getPatientId());
        shopCart.setBusinessId(getPrescription.getId());
        //判断是否是用药预订单处方
        if (Objects.isNull(getPrescription.getPreorderId())) {
            shopCart.setBusinessId(getPrescription.getId());
            shopCart.setType(ShopCartTypeEnum.PRESCRIPTION.getCode());
        } else {
            shopCart.setBusinessId(getPrescription.getPreorderId());
            shopCart.setType(ShopCartTypeEnum.ADVANCE_ORDER.getCode());
            //药品补充
            if (flag) {
                this.drugsChange(shopCart.getHospitalId(), shopCart.getPatientId(), shopCart.getBusinessId());
            }
        }
        return this.deleteBusinessById(shopCart);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelAdvanceOrder(Long id) {
        BusShopCart shopCart = busShopCartMapper.selectById(id);
        if (Objects.isNull(shopCart)) {
            throw new ServiceException("未找到该订单");
        }
        this.drugsChange(shopCart.getHospitalId(), shopCart.getPatientId(), shopCart.getBusinessId());
        //作废未审核处方
        BusPrescription prescription = busPrescriptionMapper.selectOne(new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getPreorderId, shopCart.getBusinessId())
                .orderByDesc(BusPrescription::getId)
                .last("limit 1"));
        if (!Objects.isNull(prescription)) {
            BusPrescription updatePrescription = new BusPrescription();
            updatePrescription.setId(prescription.getId());
            updatePrescription.setStatus(PrescriptionStatus.CANCELLATION);
            updatePrescription.setUpdateTime(DateUtils.getNowDate());
            busPrescriptionMapper.updateById(updatePrescription);
        }
        int i = this.deleteBusinessById(shopCart);
        preorderMapper.deleteById(shopCart.getBusinessId());
        return i;
    }

    /**
     * 药品数据变更
     *
     * @param hospitalId  医院id
     * @param patientId   患者id
     * @param businessId  用药预订单id
     */
    private void drugsChange(Long hospitalId, Long patientId, Long businessId) {
        BusPreorderPatient preorder = preorderMapper.selectById(businessId);
        if (Objects.isNull(preorder)) {
            log.warn("未找到订单id为：{} 的用药订单信息", businessId);
            return;
        }
        List<Long> idList;
        if (preorder.getStatus().equals(CodeEnum.NO.getCode())) {
            List<BusPreorderDrugs> preDrugs = busPreorderDrugsMapper.selectList(new LambdaQueryWrapper<BusPreorderDrugs>()
                    .eq(BusPreorderDrugs::getPreorderId, businessId));
            idList = preDrugs.stream().map(BusPreorderDrugs::getDrugsId).collect(toList());
            // 查询已存在购物车药品
            List<BusShopCart> goodsIsExists = this.getGoodsIsExists(hospitalId, ShopCartTypeEnum.DRUGS.getCode(), patientId, idList);
            // 药品放回处理
            for (BusPreorderDrugs pd : preDrugs) {
                putBackCart(hospitalId, patientId, pd.getDrugsId(), pd.getQuantity(), pd.getReferrer(), goodsIsExists);
            }
        } else {
            BusPrescription preRx = busPrescriptionMapper.selectOne(new LambdaQueryWrapper<BusPrescription>()
                    .eq(BusPrescription::getPreorderId, businessId)
                    .orderByDesc(BusPrescription::getCreateTime).last("limit 1"));
            List<BusPrescriptionDrugs> rxDrugs = prescriptionDrugsMapper.selectPrescriptonDrugsList(preRx.getId());
            idList = rxDrugs.stream().map(BusPrescriptionDrugs::getDrugsId).collect(toList());
            // 查询已存在购物车药品
            List<BusShopCart> goodsIsExists = this.getGoodsIsExists(hospitalId, ShopCartTypeEnum.DRUGS.getCode(), patientId, idList);
            // 药品放回处理
            for (BusPrescriptionDrugs pd : rxDrugs) {
                putBackCart(hospitalId, patientId, pd.getDrugsId(), pd.getQuantity(), preRx.getReferrer(), goodsIsExists);
            }
        }
    }

    /**
     * 取消预定单药品放回购物车处理
     *
     * @param hospitalId    医院id
     * @param patientId     患者id
     * @param drugsId       药品id
     * @param quantity      放回数量
     * @param referrer      推荐人id
     * @param goodsIsExists 购物车存在药品id
     */
    private void putBackCart(Long hospitalId, Long patientId, Long drugsId, Integer quantity, Long referrer, List<BusShopCart> goodsIsExists) {
        // 批量更新已存在的
        for (BusShopCart g : goodsIsExists) {
            if (g.getBusinessId().equals(drugsId)) {
                int stock = this.checkStock(hospitalId, drugsId, quantity, g.getQuantity());
                g.setUpdateTime(DateUtils.getNowDate());
                //实际库存数不为0 并购物车数量小于实际库存数时  执行更新操作
                if (stock != 0) {
                    g.setQuantity(stock);
                    this.updateGoodsQuantity(g);
                    break;
                }
            }
        }
        // 插入不存在的
        boolean present = goodsIsExists.stream().anyMatch(m -> m.getBusinessId().equals(drugsId));
        if (!present) {
            int stock = this.checkStock(hospitalId, drugsId, quantity, 0);
            if (stock != 0) {
                BusShopCart busShopCart = new BusShopCart();
                busShopCart.setHospitalId(hospitalId);
                busShopCart.setPatientId(patientId);
                busShopCart.setBusinessId(drugsId);
                busShopCart.setQuantity(stock);
                busShopCart.setType(ShopCartTypeEnum.DRUGS.getCode());
                busShopCart.setReferrer(referrer);
                busShopCart.setCreateTime(DateUtils.getNowDate());
                this.insert(busShopCart);
            }
        }
    }

    /**
     * 校验库存
     *
     * @param hospitalId
     * @param drugsId
     * @param quantity
     * @param cartQuantity 购物车库存
     * @return
     */
    private int checkStock(Long hospitalId, Long drugsId, Integer quantity, Integer cartQuantity) {
        int drugsQuantity = quantity + cartQuantity;
        BusOfficinaEntDrugVO drug = officinaService.getOfficinaAndEntDrug(drugsId, hospitalId);
        if (Objects.isNull(drug)) {
            return 0;
        }
        return Math.min(drug.getTotalStock(), drugsQuantity);
    }

    @Override
    public int addProductCart(BusShopCart cart) {
        // 查询购物车中是否已存在该商品/药品
        BusShopCart shopCart = busShopCartMapper.selectOne(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getHospitalId, cart.getHospitalId())
                .eq(BusShopCart::getPatientId, cart.getPatientId())
                .eq(BusShopCart::getType, cart.getType())
                .eq(BusShopCart::getBusinessId, cart.getBusinessId()));
        if (ShopCartTypeEnum.GOODS.getCode().equals(cart.getType())) {
            BusShopGoods goods = selectGoodsDetail(cart.getBusinessId());
            return checkGoodsAndDrugsInventory(shopCart, cart, goods.getStock());
        } else if (ShopCartTypeEnum.DRUGS.getCode().equals(cart.getType())) {
            BusOfficinaEntDrugVO drug = officinaService.getOfficinaAndEntDrug(cart.getBusinessId(), cart.getHospitalId());
            StringUtils.isNullThrowExp(drug, "药品不见啦~请刷新后重试！");
            if (YesNoEnum.NO.getCode().equals(drug.getStatus())) {
                throw new ServiceException("药品已下架啦~请刷新后重试！");
            }
            return checkGoodsAndDrugsInventory(shopCart, cart, drug.getTotalStock());
        } else {
            throw new ServiceException("非商品/药品类型添加购物车！");
        }
    }

    @Override
    public int addDrugCart(BusShopCart cart) {
        // 药品数量限制
        limitDrugsNum(cart.getQuantity(), cart.getHospitalId());
        cart.setType(ShopCartTypeEnum.DRUGS.getCode());
        BusOfficinaEntDrugVO drug = officinaService.getOfficinaAndEntDrug(cart.getBusinessId(), cart.getHospitalId());
        StringUtils.isNullThrowExp(drug, "药品不见啦~请刷新后重试！");
        if (YesNoEnum.NO.getCode().equals(drug.getStatus())) {
            throw new ServiceException("药品已下架啦~请刷新后重试！");
        }
        BusShopCart shopCart = busShopCartMapper.selectOne(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getHospitalId, cart.getHospitalId())
                .eq(BusShopCart::getPatientId, cart.getPatientId())
                .eq(BusShopCart::getType, ShopCartTypeEnum.DRUGS.getCode())
                .eq(BusShopCart::getBusinessId, cart.getBusinessId()));
        //修改购物车
        if (shopCart != null) {
            // 修改后数量为0，删除购物车
            if (cart.getQuantity() == 0) {
                return delete(Lists.newArrayList(shopCart.getId()));
            }
            // 修改购物车数量
            shopCart.setQuantity(0);
            //设置推荐人
            if(cart.getReferrer() != null){
                shopCart.setReferrer(cart.getReferrer());
            }
            return checkGoodsAndDrugsInventory(shopCart, cart, drug.getTotalStock());
        }
        // 新增购物车
        return checkGoodsAndDrugsInventory(null, cart, drug.getTotalStock());
    }

    /**
     * 药品数量限制
     * @param quantity 数量
     */
    private void limitDrugsNum(Integer quantity, Long hospitalId) {
        String setting = remoteHospitalSettingApi.getSettingValue(DRUGS_LIMIT_KEY, hospitalId).getData();
        DrugLimitConfig drugLimitConfig = JSON.parseObject(setting, DrugLimitConfig.class);
        if (drugLimitConfig.isLimit() && quantity > drugLimitConfig.getMaxCount()) {
            throw new ServiceException("单品单次购买数量不能超过 " + drugLimitConfig.getMaxCount());
        }
    }

    /**
     * 校验商品/药品库存
     *
     * @param shopCart 已存在购物车商品/药品
     * @param cart     要添加购物车商品/药品
     * @param relStock 实际库存
     * @return 更新购物车结果
     */
    private int checkGoodsAndDrugsInventory(BusShopCart shopCart, BusShopCart cart, Integer relStock) {
        if (relStock < 1) {
            throw new ServiceException("库存为0");
        }
        if (shopCart != null) {
            if(cart.getReferrer() != null){
                shopCart.setReferrer(cart.getReferrer());
            }
            // 校验加入购物车的数量+已存在数量是否超过库存
            int total = cart.getQuantity() + shopCart.getQuantity();
            return updateMaxStock(relStock, total, shopCart);
        }
        if (cart.getQuantity() > relStock) {
            cart.setQuantity(relStock);
            insert(cart);
            throw new ServiceException("数量超出库存，已为您添加最大库存");
        }
        return insert(cart);
    }

    @Override
    public List<BusShopCartItem> listProductCart(Long hospitalId, Long patientId) {
        List<BusShopCart> productCarts = busShopCartMapper.selectList(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getHospitalId, hospitalId).eq(BusShopCart::getPatientId, patientId)
                .eq(BusShopCart::getType, ShopCartTypeEnum.GOODS.getCode()).orderByDesc(BusShopCart::getCreateTime));
        List<BusShopCartItem> list = new ArrayList<>();
        for (BusShopCart cart : productCarts) {
            R<BusShopGoods> detail = remoteShopGoodsService.getDetail(cart.getBusinessId());
            if (!Constants.SUCCESS.equals(detail.getCode())) {
                throw new ServiceException(detail.getMsg());
            }
            BusShopGoods goods = detail.getData() ;
            // 库存不足与下架的跳过
            if (cart.getQuantity() > goods.getStock() || goods.getStatus().equals(YesNoEnum.NO.getCode())) {
                continue;
            }
            BusShopCartItem cartItem = new BusShopCartItem(cart.getId(), YesNoEnum.YES.getCode(), cart.getQuantity(),
                    cart.getBusinessId(), goods.getBrandName(), goods.getTitle(), goods.getImg(), goods.getSpecification(),
                    goods.getSellingPrice(), goods.getStock(), cart.getChecked());
            cartItem.setHasVisitor(goods.getHasVisitor());
            cartItem.setHasCome(goods.getHasCome());
            cartItem.setExpressType(goods.getExpressType());
            cartItem.setReferrer(cart.getReferrer());
            cartItem.setTypeParentId(ObjectUtil.isNotEmpty(goods.getTypeList()) ? goods.getTypeList().get(0) : null);
            list.add(cartItem);
        }
        return list;
    }

    @Override
    public List<DoctorOfficinaDrugsVo> listDrugsCart(Long hospitalId, Long patientId) {
        // 查询患者全部药品购物车
        List<BusShopCart> carts = busShopCartMapper.selectList(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getHospitalId, hospitalId)
                .eq(BusShopCart::getPatientId, patientId)
                .eq(BusShopCart::getType, ShopCartTypeEnum.DRUGS.getCode())
                .orderByDesc(BusShopCart::getCreateTime));
        List<DoctorOfficinaDrugsVo> list = new ArrayList<>();
        for (BusShopCart cart : carts) {
            BusOfficinaEntDrugVO drug = officinaService.getOfficinaAndEntDrug(cart.getBusinessId(), cart.getHospitalId());
            if (null==drug) {
                throw new ServiceException("没有药品!") ;
            }
            // 库存不足与下架的跳过
            if (cart.getQuantity() > drug.getTotalStock() || drug.getStatus().equals(YesNoEnum.NO.getCode())) {
                continue;
            }
            String img = StringUtils.isNotEmpty(drug.getMainImg()) ? drug.getMainImg() : drug.getDrugsImg();
            DoctorOfficinaDrugsVo vo = new DoctorOfficinaDrugsVo();
            vo.setDrugsUsageValue(drug.getDrugsUsageValue());
            vo.setClassifyId(drug.getClassifyId());
            vo.setDrugsId(cart.getBusinessId());
            vo.setDrugsImg(img);
            vo.setDrugsManufacturer(drug.getDrugsManufacturer());
            vo.setDrugsName(drug.getDrugName());
            vo.setStandardCommonName(drug.getStandardCommonName());
            vo.setDrugsSpecification(drug.getSpecification());
            vo.setEnterpriseId(drug.getEnterpriseId());
            vo.setPreType(drug.judgeIsRx() ? "0" : "1");
            vo.setSellingPrice(drug.getSellingPrice());
            vo.setStock(drug.getTotalStock());
            vo.setQuantity(cart.getQuantity());
            vo.setCartId(cart.getId());
            vo.setDirectoryType(drug.getDirectoryType());
            vo.setMedicalInsuranceType(drug.getMedicalInsuranceType());
            vo.setNationalDrugCode(drug.getNationalDrugCode());
            // 获取药品的祖级ID
            vo.setAncestors(drug.getAncestors());
            vo.setReferrer(cart.getReferrer());
            if ("0".equals(drug.getAncestors())) {
                vo.setAncestorsId(drug.getClassifyId());
            } else if(StringUtils.isNotEmpty(drug.getAncestors())){
                String[] split = drug.getAncestors().split(",");
                vo.setAncestorsId(Long.valueOf(split[1]));
            }
            list.add(vo);
        }
        return list;
    }

    @Override
    public int updateCheck(List<BusShopCart> cartList) {
        cartList.forEach(busShopCartMapper::updateById);
        return 1;
    }

    @Override
    public int updateCartCount(Integer count, Long cartId, Long referrer) {
        BusShopCart shopCart = busShopCartMapper.selectById(cartId);
        if (Objects.isNull(shopCart)) {
            throw new ServiceException("物品已被删除，请刷新购物车后再试！");
        }
        if (shopCart.getType().equals(ShopCartTypeEnum.GOODS.getCode())) {
            // 商品修改数量
            BusShopGoods goods = selectGoodsDetail(shopCart.getBusinessId());
            if(referrer != null){//填充推荐人
                shopCart.setReferrer(referrer);
            }
            return updateMaxStock(goods.getStock(), count, shopCart);
        } else if (shopCart.getType().equals(ShopCartTypeEnum.DRUGS.getCode())) {
            // 药品数量限制
            limitDrugsNum(count, shopCart.getHospitalId());
            // 药品修改数量
            BusOfficinaEntDrugVO drug = officinaService.getOfficinaAndEntDrug(shopCart.getBusinessId(), shopCart.getHospitalId());
            StringUtils.isNullThrowExp(drug, "药品不见啦~请刷新后重试！");
            if (YesNoEnum.NO.getCode().equals(drug.getStatus())) {
                throw new ServiceException("药品已下架啦~请刷新后重试！");
            }
            return updateMaxStock(drug.getTotalStock(), count, shopCart);
        } else {
            throw new ServiceException("非商品/药品不能更改数量");
        }
    }

    /**
     * 查询商品详情信息
     *
     * @param goodsId 商品id
     * @return 商品详情
     */
    private BusShopGoods selectGoodsDetail(Long goodsId) {
        R<BusShopGoods> detail = remoteShopGoodsService.getDetail(goodsId);
        if (!Constants.SUCCESS.equals(detail.getCode())) {
            throw new ServiceException(detail.getMsg());
        }
        BusShopGoods goods = detail.getData();
        StringUtils.isNullThrowExp(goods, "商品不见啦~请刷新后重试！");
        if (YesNoEnum.NO.getCode().equals(goods.getStatus())) {
            throw new ServiceException("商品已下架啦~请刷新后重试！");
        }
        return goods;
    }

    /**
     * 远程调用查询购物车信息
     *
     * @param hospitalId
     * @param patientId
     * @return
     */
    @Override
    public List<BusShopCart> selectShopCart(Long hospitalId, Long patientId) {
        LambdaQueryWrapper<BusShopCart> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusShopCart::getHospitalId, hospitalId);
        lambdaQuery.eq(BusShopCart::getPatientId, patientId);
        return busShopCartMapper.selectList(lambdaQuery);
    }

    /**
     * 更新最大可修改库存
     *
     * @param relStock 仓库实际库存
     * @param addStock 需添加库存
     * @param shopCart 购物车实体
     * @return 更新结果
     */
    public int updateMaxStock(int relStock, int addStock, BusShopCart shopCart) {
        if (relStock < 1) {
            throw new ServiceException("库存为0");
        }
        if (relStock >= addStock) {
            shopCart.setQuantity(addStock);
            shopCart.setUpdateTime(new Date());
            return busShopCartMapper.updateById(shopCart);
        }
        shopCart.setQuantity(relStock);
        shopCart.setUpdateTime(new Date());
        busShopCartMapper.updateById(shopCart);
        throw new ServiceException("数量超出库存，已为您添加最大库存");
    }

    /**
     * 获取处方失效时间（秒）
     *
     * @param prescription prescription
     * @return 失效时间倒计时
     */
    private Long getRxInvalidTime(BusPrescription prescription) {
        if ( null!=prescription.getValidTime() ){
            return (prescription.getValidTime().getTime() - System.currentTimeMillis()) / 1000L;
        }
        return 0L;
    }

    /**
     * 处方类型购物车处理方法
     *
     * @param rxList 处方购物车集合
     * @param cart   购物车信息
     */
    private void packRxCart(List<BusShopRxCart> rxList, BusShopCart cart) {
        BusPrescription rx = busPrescriptionMapper.selectById(cart.getBusinessId());
        List<BusPrescriptionDrugs> rxDrugs = prescriptionDrugsMapper.queryDrugsList(rx.getId());
        List<BusShopCartItem> list = new ArrayList<>();
        BusShopRxCart rxCart = new BusShopRxCart(cart.getId(), cart.getChecked());
        rxCart.setReferrer(rx.getReferrer());
        if (PrescriptionTypeEnum.isZyxdf(rx.getPrescriptionType())) {
            BusHospitalPa pa = paMapper.selectById(rx.getPaId());
            rxCart.setPaName(rx.getPaName());
            if (pa.getType().equals(YesNoEnum.YES.getCode())) {
                // 中药经典方
                addTcmDrugs(rx, rxDrugs, list, rxCart);
            } else {
                // 中药协定方
                String[] dosage = StringUtils.split(rx.getUsages(), ",");
                rxCart.setDosage(Integer.valueOf(dosage[1]));
                // 计算价格 = (处方金额 - 配药费 - 诊查费) / 帖数
                BigDecimal price = (rx.getPrescriptionAmount().subtract(rx.getProcessPrice()).subtract(rx.getExaminationFee())).divide(BigDecimal.valueOf(rxCart.getDosage()));
                rxCart.setRxPrice(price.doubleValue());
                rxCart.setRxType(YesNoEnum.OTHER.getCode());
                rxCart.setProcessPrice(rx.getProcessPrice().doubleValue());
                rxCart.setProcessingMethod(rx.getProcessingMethod());
            }
        } else if (PrescriptionTypeEnum.isTcm(rx.getPrescriptionType())) {
            // 中药普通饮片
            addTcmDrugs(rx, rxDrugs, list, rxCart);
        } else if (PrescriptionTypeEnum.isMm(rx.getPrescriptionType())) {
            // 西药处方
            rxCart.setRxType(YesNoEnum.NO.getCode());
            for (BusPrescriptionDrugs rxDrug : rxDrugs) {
                BusShopCartItem cartItem = new BusShopCartItem(YesNoEnum.NO.getCode(), rxDrug.getQuantity(), rxDrug.getDrugsId(),
                        rxDrug.getDrugsName(), rxDrug.getDrugsImg(), rxDrug.getDrugsSpecification(), rxDrug.getSellingPrice());
                cartItem.setBrandName(rxDrug.getDrugsManufacturer());
                cartItem.setIsRxDrug("0".equals(rxDrug.getPreType()));
                cartItem.setMedicalInsuranceType(rxDrug.getMedicalInsuranceType());
                cartItem.setNationalDrugCode(rxDrug.getMiCode());
                list.add(cartItem);
            }
        }
        // 处方审核通过
        if (rx.getStatus().equals(PrescriptionStatus.PASS)) {
            rxCart.setDoctorName(rx.getDoctorName());
            rxCart.setRxStatus(YesNoEnum.OTHER.getCode());
            rxCart.setInvalidDate(getRxInvalidTime(rx));
        }
        rxCart.setExaminationFee(rx.getExaminationFee());
        rxCart.setExaminationName(rx.getExaminationName());
        rxCart.setRxId(rx.getId());
        rxCart.setDrugList(list);
        rxList.add(rxCart);
    }

    /**
     * 预定单类型购物车处理方法
     *
     * @param bookList 预定单集合
     * @param cart     购物车信息
     */
    private void packPreOrderCart(List<BusShopRxCart> bookList, BusShopCart cart) {
        // 预定单类型购物车(目前只考虑西药)
        BusShopRxCart rxCart = new BusShopRxCart(cart.getId(), cart.getChecked());
        BusPreorderPatient preorder = preorderMapper.selectById(cart.getBusinessId());
        // 待开方
        List<BusShopCartItem> list = new ArrayList<>();
        if (preorder.getStatus().equals(CodeEnum.NO.getCode())) {
            List<BusPreorderDrugs> preorderDrugs = busPreorderDrugsMapper.selectPreDrugs(cart.getBusinessId());
            for (BusPreorderDrugs drug : preorderDrugs) {
                BusShopCartItem cartItem = new BusShopCartItem(null, YesNoEnum.NO.getCode(), drug.getQuantity(), drug.getDrugsId(), null,
                        drug.getDrugsName(), drug.getDrugsImg(), drug.getDrugsSpecification(), drug.getSellingPrice(), null, null);
                cartItem.setIsRxDrug(drug.getPrescriptionIdentification().equals(RX_Flag));
                cartItem.setDirectoryType(drug.getDirectoryType() + "");
                cartItem.setNationalDrugCode(drug.getNationalDrugCode());
                cartItem.setMedicalInsuranceType(drug.getMedicalInsuranceType());
                cartItem.setDrugsUsageValue(drug.getDrugsUsageValue());
                cartItem.setReferrer(drug.getReferrer());
                list.add(cartItem);
            }
            rxCart.setRxStatus(YesNoEnum.NO.getCode());
        } else {
            BusPrescription preRx = busPrescriptionMapper.selectOne(new LambdaQueryWrapper<BusPrescription>()
                    .eq(BusPrescription::getPreorderId, cart.getBusinessId())
                    .orderByDesc(BusPrescription::getCreateTime).last("limit 1"));
            if (null == preRx) {
                log.error("预定单[{}]没有处方信息", cart.getBusinessId());
                return;
            }
            List<BusPrescriptionDrugs> rxDrugs = prescriptionDrugsMapper.queryDrugsList(preRx.getId());
            for (BusPrescriptionDrugs rxDrug : rxDrugs) {
                BusShopCartItem cartItem = new BusShopCartItem(null, YesNoEnum.NO.getCode(), rxDrug.getQuantity(), rxDrug.getDrugsId(), null,
                        rxDrug.getDrugsName(), rxDrug.getDrugsImg(), rxDrug.getDrugsSpecification(), rxDrug.getSellingPrice(), null, null);
                cartItem.setIsRxDrug("0".equals(rxDrug.getPreType()));
                cartItem.setBrandName(rxDrug.getDrugsManufacturer());
                cartItem.setDirectoryType(rxDrug.getDirectoryType());
                cartItem.setMedicalInsuranceType(rxDrug.getMedicalInsuranceType());
                cartItem.setNationalDrugCode(rxDrug.getMiCode());
                cartItem.setDrugsUsageValue(rxDrug.getDrugsUsageValue());
                list.add(cartItem);
            }
            // 预定单处方待审核
            if (preRx.getStatus().equals(PrescriptionStatus.NOT_EXAMINE)) {
                rxCart.setRxStatus(YesNoEnum.YES.getCode());
            } else if (preRx.getStatus().equals(PrescriptionStatus.PASS)) {
                // 预定单处方审核通过
                rxCart.setRxStatus(YesNoEnum.OTHER.getCode());
                rxCart.setInvalidDate(getRxInvalidTime(preRx));
                rxCart.setDoctorName(preRx.getDoctorName());
            }
            rxCart.setExaminationFee(preRx.getExaminationFee());
            rxCart.setExaminationName(preRx.getExaminationName());
            rxCart.setRxId(preRx.getId());
            rxCart.setReferrer(preRx.getReferrer());
        }
        rxCart.setDrugList(list);
        //设置推荐人
        if(rxCart.getReferrer() == null){
            Long referrerId = list.stream().filter(drug -> drug.getReferrer() != null).map(BusShopCartItem::getReferrer).findFirst().orElse(null);
            rxCart.setReferrer(referrerId);
        }
        rxCart.setRxType(YesNoEnum.NO.getCode());
        bookList.add(rxCart);
    }

    /**
     * 药品类型购物车处理方法
     *
     * @param invalidList 失效集合
     * @param productList 药品集合
     * @param cart        购物车信息
     */
    private void packDrugsCart(List<BusShopCartItem> invalidList, List<BusShopCartItem> productList, BusShopCart cart) {
        BusOfficinaEntDrugVO drug = officinaService.getOfficinaAndEntDrug(cart.getBusinessId(), cart.getHospitalId());
        String img = StringUtils.isNotEmpty(drug.getMainImg()) ? drug.getMainImg() : drug.getDrugsImg();
        BusShopCartItem cartItem = new BusShopCartItem(cart.getId(), YesNoEnum.NO.getCode(), cart.getQuantity(), cart.getBusinessId(),
                null, drug.getDrugName(), img, drug.getSpecification(), drug.getSellingPrice(), null, cart.getChecked());
        cartItem.setReferrer(cart.getReferrer());
        cartItem.setIsRxDrug(drug.judgeIsRx());
        cartItem.setBrandName(drug.getDrugsManufacturer());
        cartItem.setCommonName(drug.getStandardCommonName());
        cartItem.setEnterpriseId(drug.getEnterpriseId());
        cartItem.setDirectoryType(drug.getDirectoryType());
        cartItem.setMedicalInsuranceType(drug.getMedicalInsuranceType());
        cartItem.setNationalDrugCode(drug.getNationalDrugCode());
        cartItem.setDrugsUsageValue(drug.getDrugsUsageValue());
        // 失效购物车过滤
        if (drug.getTotalStock() < 1) {
            cartItem.setInvalidType(YesNoEnum.YES.getCode());
            invalidList.add(cartItem);
        } else if (drug.getStatus().equals(YesNoEnum.NO.getCode())) {
            cartItem.setInvalidType(YesNoEnum.NO.getCode());
            invalidList.add(cartItem);
        } else {
            cartItem.setStock(drug.getTotalStock());
            productList.add(cartItem);
        }
    }

    /**
     * 商品类型购物车处理方法
     *
     * @param invalidList 失效集合
     * @param productList 商品集合
     * @param cart        购物车信息
     */
    private void packGoodsCart(List<BusShopCartItem> invalidList, List<BusShopCartItem> productList, BusShopCart cart) {
        R<BusShopGoods> detail = remoteShopGoodsService.getDetail(cart.getBusinessId());
        if (!Constants.SUCCESS.equals(detail.getCode())) {
            throw new ServiceException(detail.getMsg());
        }
        BusShopGoods goods = detail.getData();
        BusShopCartItem cartItem = new BusShopCartItem(cart.getId(), YesNoEnum.YES.getCode(),
                cart.getQuantity(), cart.getBusinessId(), goods.getBrandName(),
                goods.getTitle(), goods.getImg(), goods.getSpecification(), goods.getSellingPrice(),
                goods.getStock(), cart.getChecked());
        cartItem.setReferrer(cart.getReferrer());
        cartItem.setExpressType(goods.getExpressType());
        cartItem.setHasVisitor(goods.getHasVisitor());
        cartItem.setHasCome(goods.getHasCome());
        cartItem.setTypeParentId(ObjectUtil.isNotEmpty(goods.getTypeList()) ? goods.getTypeList().get(0) : null);
        if (goods.getStock() < 1) {
            cartItem.setInvalidType(YesNoEnum.YES.getCode());
            invalidList.add(cartItem);
        } else if (goods.getStatus().equals(YesNoEnum.NO.getCode())) {
            cartItem.setInvalidType(YesNoEnum.NO.getCode());
            invalidList.add(cartItem);
        } else {
            productList.add(cartItem);
        }
    }
}

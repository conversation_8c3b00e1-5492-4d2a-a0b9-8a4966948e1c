package com.puree.hospital.app.his.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.dto.BusPatientMedicalRecordDTO;
import com.puree.hospital.app.domain.vo.BusExamineReportDetail;
import com.puree.hospital.app.domain.vo.BusExamineReportDetailVO;
import com.puree.hospital.app.domain.vo.BusInspectReportDetail;
import com.puree.hospital.app.domain.vo.BusPatientRegRecordVO;
import com.puree.hospital.app.his.constants.HisConstant;
import com.puree.hospital.app.his.domain.HisPatientInfo;
import com.puree.hospital.app.his.domain.HisQueryContext;
import com.puree.hospital.app.his.domain.query.HisExamineReportQuery;
import com.puree.hospital.app.his.domain.query.HisInspectReportQuery;
import com.puree.hospital.app.his.domain.query.HisOutPatientRecordQuery;
import com.puree.hospital.app.his.domain.query.HisPatientInfoQuery;
import com.puree.hospital.app.his.domain.query.HisPrescriptionInfoDTO;
import com.puree.hospital.app.his.domain.query.HisRegisteredInfoQuery;
import com.puree.hospital.app.his.domain.vo.BusEmrDetailVO;
import com.puree.hospital.app.his.domain.vo.BusPrescriptionInfoVO;
import com.puree.hospital.app.his.domain.vo.HisDrugsVO;
import com.puree.hospital.app.his.domain.vo.HisOutPatientRecord;
import com.puree.hospital.app.his.domain.vo.HisPrescriptionInfo;
import com.puree.hospital.app.his.domain.vo.HisRegisteredInfo;
import com.puree.hospital.app.his.enums.HisFrequenceEnum;
import com.puree.hospital.app.his.enums.HisPrescriptionTypeEnum;
import com.puree.hospital.app.his.helper.DrHisHelper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 东软（开发区）患者病例信息抽象类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/15 21:56
 */
@Slf4j
@AllArgsConstructor(onConstructor = @__(@Autowired))
@Component
public class DrPatientMedicalRecordHandler extends BasePatientMedicalRecordHandler {

    private final DrHisHelper drHisHelper;
    private final BusHospitalMapper busHospitalMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;

    /**
     * 获取患者就诊记录列表
     *
     * @param queryContext 查询参数上下文
     * @return 就诊记录列表
     */
    @Override
    public List<BusPatientRegRecordVO> getPatientMedicalRecordList(HisQueryContext queryContext) {
        HisRegisteredInfoQuery query = new HisRegisteredInfoQuery();
        query.setStartDate(DateUtil.format(queryContext.getQueryDto().getBeginDate(), HisConstant.DR_DATE_FORMAT));
        query.setEndDate(DateUtil.format(queryContext.getQueryDto().getEndDate(), HisConstant.DR_DATE_FORMAT));
        query.setPatientID(queryContext.getHisPatientId());
        List<HisRegisteredInfo> result = drHisHelper.getRegisteredInfo(query);
        if (log.isDebugEnabled()) {
            log.debug("患者就诊记录列表：{}", result);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result.stream().map(this::convertBusPatientRegRecordVO).sorted(Comparator.comparing(BusPatientRegRecordVO::getRegDate).reversed()).collect(Collectors.toList());
    }


    /**
     *  获取电子病历详情
     * @param queryContext  查询上下文
     * @return  电子病历详情
     */
    @Override
    public List<BusEmrDetailVO> getEmrDetail(HisQueryContext queryContext) {
        HisOutPatientRecordQuery query = new HisOutPatientRecordQuery();
        query.setPatientName(queryContext.getPatientName());
        query.setTranSerialNO(queryContext.getReportId());
        List<HisOutPatientRecord> result = drHisHelper.getOutPatientRecord(query);
        if (log.isDebugEnabled()) {
            log.debug("查询门诊患者文本病历，请求参数；{}，查询结果：{}", query, result);
        }
        return assembleEmrVO(new ArrayList<>(), result, HisOutPatientRecord::getWddmc, HisOutPatientRecord::getWdnr);
    }

    /**
     * 获取患者检查记录详情
     *
     * @param queryContext 查询参数上下文
     * @return 检查记录详情
     */
    @Override
    public List<BusInspectReportDetail> getInspectDetail(HisQueryContext queryContext) {
        HisInspectReportQuery query = new HisInspectReportQuery();
        query.setReportId(queryContext.getReportId());
        return drHisHelper.getInspectDetail(query);
    }

    /**
     * 获取患者检验记录详情
     *
     * @param queryContext 查询参数上下文
     * @return 检验记录详情
     */
    @Override
    public BusExamineReportDetailVO getExamineDetail(HisQueryContext queryContext) {
        HisExamineReportQuery query = new HisExamineReportQuery();
        query.setReportId(queryContext.getReportId());
        List<BusExamineReportDetail> list = drHisHelper.getExamineDetail(query);
        if (log.isDebugEnabled()) {
            log.debug("获取患者检验记录详情-查询结果：{}", list);
        }
        BusExamineReportDetailVO vo = new BusExamineReportDetailVO();
        vo.setReportDetails(list);
        if(!CollectionUtils.isEmpty(list)){
            vo.setTotalNum(list.size());
            vo.setExceptionNum((int) list.stream().filter(BusExamineReportDetail::checkException).count());
        }
        return vo;
    }

    @Override
    public BusPrescriptionInfoVO getPrescriptionInfo(HisQueryContext queryContext) {
        BusPatientMedicalRecordDTO dto = queryContext.getQueryDto();
        checkReq(dto);
        // 获取医院信息
        BusHospital hospitalInfo = busHospitalMapper.selectById(queryContext.getHospitalId());
        // 获取就诊记录
        String startTime = DateUtil.format(dto.getBeginDate(), DatePattern.NORM_DATETIME_PATTERN);
        String endTime = DateUtil.format(dto.getEndDate(), DatePattern.NORM_DATETIME_PATTERN);
        HisRegisteredInfoQuery hisRegisteredInfoQuery = new HisRegisteredInfoQuery();
        hisRegisteredInfoQuery.setPatientID(queryContext.getHisPatientId());
        hisRegisteredInfoQuery.setStartDate(startTime);
        hisRegisteredInfoQuery.setEndDate(endTime);
        List<HisRegisteredInfo> registeredInfos = drHisHelper.getRegisteredInfo(hisRegisteredInfoQuery);
        if (CollectionUtils.isEmpty(registeredInfos)){
            throw new ServiceException("该患者无就诊记录");
        }
        return assemblePrescriptionInfoVO(registeredInfos, queryContext, startTime, endTime, hospitalInfo);
    }

    /**
     * 校验参数
     * @param dto 入参
     */
    private void checkReq(BusPatientMedicalRecordDTO dto) {
        if (Objects.isNull(dto)|| Objects.isNull(dto.getPatientId())
                || Objects.isNull(dto.getBeginDate()) || Objects.isNull(dto.getEndDate()) || Objects.isNull(dto.getPrescriptionTypeName())) {
            throw new ServiceException("参数错误");
        }
        if (Objects.isNull(dto.getTranSerialNo())) {
            throw new ServiceException("门诊流水号不能为空");
        }
        if (Objects.isNull(dto.getHisOrderRecId())) {
            throw new ServiceException("处方号不能为空");
        }
    }

    /**
     * 组装处方信息
     *
     * @param registeredInfos 就诊记录
     * @param queryContext    查询上下文参数
     * @param startTime       格式化后的开始时间
     * @param endTime         格式化后的结束时间
     * @param hospitalInfo    医院信息
     * @return 处方信息
     */
    private BusPrescriptionInfoVO assemblePrescriptionInfoVO(List<HisRegisteredInfo> registeredInfos, HisQueryContext queryContext, String startTime, String endTime, BusHospital hospitalInfo) {
        // 获取处方信息
        List<BusPrescriptionInfoVO> resInfo = new ArrayList<>();
        List<HisDrugsVO> drugsInfo = new ArrayList<>();
        for (HisRegisteredInfo r : registeredInfos) {
            HisPrescriptionInfoDTO prescriptionInfo = new HisPrescriptionInfoDTO();
            prescriptionInfo.setTranSerialNO(r.getTranSerialNO());
            prescriptionInfo.setCardNO(queryContext.getHisPatientId());
            prescriptionInfo.setStartDate(startTime);
            prescriptionInfo.setEndDate(endTime);
            List<HisPrescriptionInfo> prescriptionInfos = drHisHelper.getPrescriptionInfo(prescriptionInfo);
            if (CollectionUtils.isNotEmpty(prescriptionInfos)) {
                // 组装数据
                assembleResult(queryContext.getQueryDto(), r, prescriptionInfos, queryContext.getPatientFamily(), drugsInfo, resInfo, hospitalInfo);
            }
        }
        if (CollectionUtils.isEmpty(drugsInfo)) {
            throw new ServiceException("无该处方类型记录");
        }
        if (CollectionUtils.isEmpty(resInfo)) {
            throw new ServiceException("该患者无处方信息");
        }
        for (BusPrescriptionInfoVO p : resInfo) {
            if (Objects.nonNull(p.getTranSerialNo()) && p.getTranSerialNo().equals(queryContext.getQueryDto().getTranSerialNo())) {
                return BeanUtil.copyProperties(p, BusPrescriptionInfoVO.class);
            }
        }
        return null;
    }


    /**
     * 组装结果
     *
     * @param dto               入参
     * @param r                 就诊记录
     * @param prescriptionInfos 处方信息
     * @param patientFamily     就诊人信息
     * @param drugsInfo         药品信息
     * @param resInfo           处方详情
     * @param hospitalInfo      医院信息
     */
    private void assembleResult(BusPatientMedicalRecordDTO dto, HisRegisteredInfo r, List<HisPrescriptionInfo> prescriptionInfos, BusPatientFamily patientFamily, List<HisDrugsVO> drugsInfo, List<BusPrescriptionInfoVO> resInfo, BusHospital hospitalInfo) {
        List<String> hisOrderRecIds = prescriptionInfos.stream().map(HisPrescriptionInfo::getHisOrderRecId).collect(Collectors.toList());
        List<BusPrescription> busPrescriptions = busPrescriptionMapper.selectList(new LambdaQueryWrapper<BusPrescription>().in(BusPrescription::getPrescriptionNumber, hisOrderRecIds));
        Map<String, BusPrescription> collect = busPrescriptions.stream().collect(Collectors.toMap(BusPrescription::getPrescriptionNumber, Function.identity()));
        for (HisPrescriptionInfo p : prescriptionInfos) {
            BusPrescriptionInfoVO result = new BusPrescriptionInfoVO();
            if (Objects.isNull(p.getOrderTypeName()) || !p.getOrderTypeName().equals(HisPrescriptionTypeEnum.getNameByCode(dto.getPrescriptionTypeName()))) {
                continue;
            }
            BeanUtils.copyProperties(p, result);
            result.setName(patientFamily.getName());
            result.setSex(patientFamily.getSex());
            result.setAge(r.getPatientAge());
            result.setTranSerialNo(r.getTranSerialNO());
            result.setOrderTypeName(HisPrescriptionTypeEnum.getCodeByName(p.getOrderTypeName()));
            result.setHospitalName(hospitalInfo.getHospitalName());
            BusPrescription busPrescription = collect.get(p.getHisOrderRecId());
            if (Objects.nonNull(busPrescription)) {
                result.setInputDoctorName(busPrescription.getDoctorName());
                result.setSubmitDoctorName(busPrescription.getDoctorName());
                result.setFrequencyName(HisFrequenceEnum.getNameByCode(p.getFrequencyId()));
            }
            result.setInputTime(DateUtil.format(DateUtil.parse(result.getInputTime(), HisConstant.DR_DATE_FORMAT), DatePattern.NORM_DATE_PATTERN));
            boolean isOther = drugsInfo.stream()
                    .filter(drugsVO -> Objects.nonNull(drugsVO.getOrderTypeName()))
                    .anyMatch(drugsVO -> !HisPrescriptionTypeEnum.getCodeByName(drugsVO.getOrderTypeName()).equals(dto.getPrescriptionTypeName()));
            if (!isOther && Objects.nonNull(p.getHisOrderRecId()) && p.getHisOrderRecId().equals(dto.getHisOrderRecId())) {
                HisDrugsVO hisDrugsVO = new HisDrugsVO();
                BeanUtil.copyProperties(p, hisDrugsVO);
                if (Objects.nonNull(busPrescription)) {
                    hisDrugsVO.setFrequencyName(HisFrequenceEnum.getNameByCode(p.getFrequencyId()));
                }
                drugsInfo.add(hisDrugsVO);
            }
            result.setDrugs(drugsInfo);
            resInfo.add(result);
        }
    }

    /**
     *  获取渠道名称
     * @return  渠道名称
     */
    @Override
    public String getChannelName() {
        return HisConstant.HisChannel.DR.name();
    }


    /**
     * 获取患者信息
     *
     * @param query 查询条件
     * @return 患者信息
     */
    @Override
    public HisPatientInfo getPatientInfo(HisPatientInfoQuery query) {
        HisPatientInfo hisPatient = drHisHelper.getPatientInfo(query.getPatientName(), query.getIDCardNO());
        if (Objects.isNull(hisPatient)) {
            return null;
        }
        HisPatientInfo patientInfo = new HisPatientInfo();
        patientInfo.setPatientId(hisPatient.getPatientId());
        patientInfo.setPatientName(hisPatient.getPatientName());
        patientInfo.setSex(hisPatient.getSex());
        patientInfo.setPatientBirthday(hisPatient.getPatientBirthday());
        patientInfo.setPatientAge(hisPatient.getPatientAge());
        patientInfo.setPhoneNumber(hisPatient.getPhoneNumber());
        return patientInfo;
    }

    /**
     *  转换为 BusPatientRegRecordVO
     * @param item  就诊人记录
     * @return BusPatientRegRecordVO
     */
    private BusPatientRegRecordVO convertBusPatientRegRecordVO(HisRegisteredInfo item) {
        BusPatientRegRecordVO vo = new BusPatientRegRecordVO();
        vo.setTranSerialNo(item.getTranSerialNO());
        try {
            if (StringUtils.isNotEmpty(item.getSeeTime())) {
                vo.setRegDate(DateUtils.parseDate(item.getSeeTime(), HisConstant.DR_DATE_FORMAT));
            } else if (StringUtils.isNotEmpty(item.getRegTime())) {
                vo.setRegDate(DateUtils.parseDate(item.getRegTime(), HisConstant.DR_DATE_FORMAT));
            }
        } catch (Exception e) {
            log.error("日期转换失败：{}", item);
            throw new ServiceException("系统异常");
        }
        vo.setDoctor(String.format("%s（%s）", item.getDoctorName(), item.getDepartmentName()));
        vo.setDisease(StringUtils.isEmpty(item.getWmDiagnose()) ? item.getTcmDiagnose() : item.getWmDiagnose());
        return vo;
    }

}

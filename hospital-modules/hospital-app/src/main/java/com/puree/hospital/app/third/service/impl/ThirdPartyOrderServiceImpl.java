package com.puree.hospital.app.third.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.puree.hospital.app.api.model.dto.third.ThirdPartyCancelDTO;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderShop;
import com.puree.hospital.app.mapper.BusOrderShopMapper;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.app.third.service.ThirdPartyOrderService;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.OrderStatusEnum;
import com.puree.hospital.common.core.enums.StockEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @ClassName ThirdPartyOrderServiceImpl
 * <AUTHOR>
 * @Description 第三方订单服务实现类
 * @Date 2024/11/18 16:10
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ThirdPartyOrderServiceImpl implements ThirdPartyOrderService {

    private final IBusOrderService busOrderService;
    private final IBusDrugsOrderService busDrugsOrderService;
    private final BusOrderShopMapper busOrderShopMapper;


    /**
     * @Param dto
     * @Return Boolean
     * @Description 第三方取消商品订单
     * <AUTHOR>
     * @Date 2024/11/18 15:32
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean thirdPartyCancel(ThirdPartyCancelDTO dto) {
        // 查询总订单信息
        List<BusOrder> busOrders = busOrderService.selectOrederByNo(dto.getOrderNo());
        if (ObjectUtil.isEmpty(busOrders)) {

            log.error("订单不存在 orderNo:{}",dto.getOrderNo());
            throw new ServiceException("订单不存在！");
        }
        for (BusOrder order : busOrders) {
            // 药品订单
            if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                BusDrugsOrder drugsOrder = busDrugsOrderService.selectById(order.getSubOrderId());
                // 订单是处方订单重新放回
                busDrugsOrderService.putRxCart(drugsOrder);
            } else {
                LambdaQueryWrapper<BusOrderShop> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusOrderShop::getOrderId, order.getSubOrderId());
                List<BusOrderShop> shopGoodsList = busOrderShopMapper.selectList(lambdaQuery);
                // 释放商品库存
                busOrderService.changeHospitalShopStock(order.getHospitalId(), shopGoodsList, StockEnum.INCREASE.getName());
            }
        }
        BusOrder busOrder = new BusOrder();
        busOrder.setOrderNo(dto.getOrderNo());
        busOrder.setCancelTime(DateUtils.getNowDate());
        busOrder.setOrderStatus(OrderStatusEnum.CANCEL.getCode());
        return SqlHelper.retBool(busOrderService.updateOrderStatus(busOrder));
    }
}

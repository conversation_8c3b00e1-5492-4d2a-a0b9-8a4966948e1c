package com.puree.hospital.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusFreightSetting;
import com.puree.hospital.app.api.model.dto.BusFreightQueryDTO;

import java.util.List;

public interface BusConsultationSettingsMapper extends BaseMapper<BusConsultationSettings> {

    BusConsultationSettings findByHospital(Long hospitalId) ;
    /**
     * 查询配送区域
     */
    List<BusFreightSetting> getFreight(BusFreightQueryDTO query);
}

package com.puree.hospital.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.CAPersonInfoDto;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusOfficinaPharmacistMapper;
import com.puree.hospital.app.mapper.BusSignatureMapper;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusSignatureService;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class BusSignatureServiceImpl implements IBusSignatureService {
    private final BusSignatureMapper busSignatureMapper;
    private final BusDoctorMapper busDoctorMapper;
    private final BusOfficinaPharmacistMapper busOfficinaPharmacistMapper;
    private final IBusDoctorService busDoctorService;

    private final static String SENSITIVE_PASSWORD = "******";

    @Autowired
    public BusSignatureServiceImpl(BusSignatureMapper busSignatureMapper, BusDoctorMapper busDoctorMapper,
                                   BusOfficinaPharmacistMapper busOfficinaPharmacistMapper, IBusDoctorService busDoctorService) {
        this.busSignatureMapper = busSignatureMapper;
        this.busDoctorMapper = busDoctorMapper;
        this.busOfficinaPharmacistMapper = busOfficinaPharmacistMapper;
        this.busDoctorService = busDoctorService;
    }

    @Override
    public BusSignature select(BusSignature busSignature) {
        LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusSignature::getObjectId, busSignature.getObjectId());
        lambdaQuery.eq(BusSignature::getObjectType, busSignature.getObjectType());
        return busSignatureMapper.selectOne(lambdaQuery);
    }

    @Override
    public int insert(BusSignature busSignature) {
        return busSignatureMapper.insert(busSignature);
    }

    @Override
    public int updateSignature(BusSignature busSignature) {
        return busSignatureMapper.updateSignature(busSignature);
    }

    @Override
    public int setPassword(BusSignature busSignature) {
        return busSignatureMapper.updateSignature(busSignature);
    }

    @Override
    public List<CAPersonInfoDto> getDoctorInfo(String phoneNumber) {
        return busDoctorMapper.getDoctorInfo(DESUtil.encrypt(phoneNumber));
    }

    @Override
    public CAPersonInfoDto getPharmacistInfo(String phoneNumber) {
        return busOfficinaPharmacistMapper.getPharmacistInfo(phoneNumber);
    }

    /**
     * 校验医师数字签名是否到期
     * @param id
     * @param role
     */
    @Override
    public BusSignature checkSignature(Long id, String role) {
        // 查询签名到期时间
        LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusSignature::getObjectType, role)
                .eq(BusSignature::getObjectId, id);
        BusSignature signature = busSignatureMapper.selectOne(lambdaQuery);
        if (null == signature || ObjectUtil.isNull(signature.getEndDate())) {
            throw new ServiceException("您还未认证签名，请前往设置！", HttpStatus.UNIQUE_ID_NOT_PASSED);
        }
        if (DateUtils.getNowDate().compareTo(signature.getEndDate()) >= 0) {
            BusSignature busSignature = new BusSignature();
            busSignature.setObjectType(role);
            busSignature.setObjectId(id);
            busSignatureMapper.cancelSignature(busSignature);
            throw new ServiceException("您的处方签名已到期，请重新申请！", HttpStatus.UNIQUE_ID_NOT_PASSED);
        }
        // 获取复核药师密码
        SMSLoginUser loginUser = busDoctorService.queryDrInfo();
        log.info("获取缓存信息:{}", loginUser);
        if (AppRoleEnum.DISPENSING_PHARMACIST.getCode().equals(loginUser.getIdentity())) {
            BusOfficinaPharmacist busOfficinaPharmacist = busOfficinaPharmacistMapper.selectById(loginUser.getUserid());
            if (Objects.nonNull(busOfficinaPharmacist)) {
                signature.setPassword(SENSITIVE_PASSWORD);
            }
        }
        if (StringUtils.isNotEmpty(signature.getPassword())) {
            signature.setPassword(SENSITIVE_PASSWORD);
        }
        return signature;
    }
}

package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.app.constant.ConsultationOrderStatus;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDrugOrderPackage;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusHospitalFamily;
import com.puree.hospital.app.domain.BusHospitalInvoiceConfig;
import com.puree.hospital.app.domain.BusInvoiceHeader;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderShop;
import com.puree.hospital.app.domain.BusOtcDrugs;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusShopOrder;
import com.puree.hospital.app.domain.GoodsMedicineDetail;
import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.domain.dto.CartOrderDTO;
import com.puree.hospital.app.domain.dto.DrugsOrderDto;
import com.puree.hospital.app.api.model.dto.GoodsAndDrugsDTO;
import com.puree.hospital.app.domain.dto.HospitalOfficinaDrugsDTO;
import com.puree.hospital.app.domain.dto.ShopCartDTO;
import com.puree.hospital.app.domain.vo.BusConsultationOrderVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.BusFiveServicePackOrderVo;
import com.puree.hospital.app.domain.vo.BusOrderVo;
import com.puree.hospital.app.domain.vo.BusShopOrderVO;
import com.puree.hospital.app.domain.vo.DrugsOrderVo;
import com.puree.hospital.app.domain.vo.OrderVo;
import com.puree.hospital.app.enums.BusOrderSubOrderTypeEnum;
import com.puree.hospital.app.enums.OrderSendTypeEnum;
import com.puree.hospital.app.helper.FreightCalculator;
import com.puree.hospital.app.mapper.BusAfterSaleMapper;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.app.mapper.BusDrugsOrderMapper;
import com.puree.hospital.app.mapper.BusFiveServicePackOrderMapper;
import com.puree.hospital.app.mapper.BusHospitalFamilyMapper;
import com.puree.hospital.app.mapper.BusHospitalInvoiceConfigMapper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusHospitalPaMapper;
import com.puree.hospital.app.mapper.BusInvoiceHeaderMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.mapper.BusOrderShopMapper;
import com.puree.hospital.app.mapper.BusShopOrderMapper;
import com.puree.hospital.app.service.IBusChannelOrderService;
import com.puree.hospital.app.service.IBusChannelPatientAgentRelationService;
import com.puree.hospital.app.service.IBusConsultationSettingsService;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusHospitalOfficinaService;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.app.service.IBusShopCartService;
import com.puree.hospital.app.api.model.dto.BusFreightResultDTO;
import com.puree.hospital.app.api.model.dto.BusFreightQueryDTO;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.InvoiceStatusEnum;
import com.puree.hospital.common.core.enums.InvoiceTypeEnum;
import com.puree.hospital.common.core.enums.PrescriptionStatusEnum;
import com.puree.hospital.common.core.enums.StockEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.DoubleUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.order.api.RemoteBusShopOrderService;
import com.puree.hospital.shop.api.RemoteShopGoodsService;
import com.puree.hospital.shop.api.model.BusShopGoods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.InvalidKeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOrderServiceImpl implements IBusOrderService {
    private static final Long SOURCE_ID = -1L;

    private final BusOrderMapper busOrderMapper;
    private final BusDoctorMapper busDoctorMapper;
    private final BusDrugsOrderMapper busDrugsOrderMapper;
    private final BusConsultationOrderMapper busConsultationOrderMapper;
    private final BusAfterSaleMapper busAfterSaleMapper;
    private final BusHospitalFamilyMapper busHospitalFamilyMapper;
    private final BusFiveServicePackOrderMapper busFiveServicePackOrderMapper;
    private final BusHospitalPaMapper busHospitalPaMapper;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    private final IBusHospitalOfficinaService busHospitalOfficinaService;
    private final IBusDrugsOrderService busDrugsOrderService;
    private final RemoteShopGoodsService remoteShopGoodsService;
    private final IBusPrescriptionService busPrescriptionService;
    private final IBusPatientService busPatientService;
    private final RemoteBusShopOrderService remoteBusShopOrderService;
    private final IBusShopCartService busShopCartService;
    private final BusShopOrderMapper busShopOrderMapper;
    private final BusOrderShopMapper busOrderShopMapper;
    private final IBusChannelPatientAgentRelationService busChannelPatientAgentRelationService;
    private final BusOrderInvoiceService orderInvoiceService ;
    private final BusHospitalInvoiceConfigMapper hospitalInvoiceConfigMapper ;
    private final BusInvoiceHeaderMapper invoiceHeaderMapper ;
    private final BusHospitalMapper busHospitalMapper;
    private final IBusChannelOrderService busChannelOrderService;

    @Resource
    private FreightCalculator freightCalculator;

    @Override
    public List<BusOrderVo> selectOrederList(BusOrderDto busOrderDto) {
        List<BusOrderVo> busOrderVos = busOrderMapper.selectAllOrderList(busOrderDto);
        if(CollectionUtil.isNotEmpty(busOrderVos)) {
            busOrderVos.forEach(b->{
                QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
                switch (b.getSubOrderType()) {
                    case "0": // 问诊订单
                        BusConsultationOrderVo consultationOrderVo = busConsultationOrderMapper.selectOrderById(b.getSubOrderId());
                        if (Objects.nonNull(consultationOrderVo)) {
                            b.setConsultationOrderVo(consultationOrderVo);
                            queryWrapper.eq("order_no", consultationOrderVo.getOrderNo());
                            BusAfterSale busAfterSale = busAfterSaleMapper.selectOne(queryWrapper);
                            if (Objects.nonNull(busAfterSale)) {
                                consultationOrderVo.setExitAfterSale(1);
                            }
                            // 查询医生擅长
                            BusDoctor doctor = busDoctorMapper.selectById(consultationOrderVo.getDoctorId());
                            if (Objects.nonNull(doctor)) {
                                consultationOrderVo.setBeGoodAt(doctor.getBeGoodAt());
                            }
                            // 校验患者是否上传过往病例
                            if (ConsultationOrderStatus.PRESCRIBED.equals(consultationOrderVo.getStatus()) ||
                                    ConsultationOrderStatus.COMPLETE.equals(consultationOrderVo.getStatus()) ||
                                    ConsultationOrderStatus.PRESCRIBED.equals(consultationOrderVo.getVideoStatus()) ||
                                    ConsultationOrderStatus.COMPLETE.equals(consultationOrderVo.getVideoStatus())) {
                                QueryWrapper<BusHospitalFamily> queryWrapper1 = new QueryWrapper<>();
                                queryWrapper1
                                        .eq("hospital_id", consultationOrderVo.getHospitalId())
                                        .eq("patient_id", consultationOrderVo.getPatientId())
                                        .eq("family_id", consultationOrderVo.getFamilyId());
                                BusHospitalFamily busHospitalFamily = busHospitalFamilyMapper.selectOne(queryWrapper1.last(" limit 1"));
                                if (Objects.nonNull(busHospitalFamily) && StringUtils.isNotEmpty(busHospitalFamily.getDiagnosisArchives())) {
                                    consultationOrderVo.setExitArchives(1);
                                }
                            }
                        }
                        break;
                    case "1": // 药品订单
                        DrugsOrderVo drugsOrderVo;
                        // 判断是否是非处方订单
                        if (CodeEnum.NO.getCode().equals(b.getMark())) {
                            log.info("非处方药品subOrderId={}",b.getSubOrderId());
                            drugsOrderVo = busDrugsOrderMapper.selectOtcOrder(b.getSubOrderId());
                        } else {
                            log.info("处方药品subOrderId={}",b.getSubOrderId());
                            drugsOrderVo = busDrugsOrderMapper.selectDetailById(b.getSubOrderId());
                        }
                        if (Objects.nonNull(drugsOrderVo)) {
                            b.setDrugsOrderVo(drugsOrderVo);
                            queryWrapper.eq("order_no", drugsOrderVo.getOrderNo());
                            BusAfterSale busAfterSale1 = busAfterSaleMapper.selectOne(queryWrapper);
                            if (Objects.nonNull(busAfterSale1)) {
                                drugsOrderVo.setExitAfterSale(1);
                            }
                            if (Objects.nonNull(drugsOrderVo.getPaId())){
                                drugsOrderVo.setZyfjType(busHospitalPaMapper.selectById(drugsOrderVo.getPaId()).getType());
                            }
                            // 查询包裹信息
                            LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                            lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, drugsOrderVo.getHospitalId());
                            lambdaQuery.isNotNull(BusDrugOrderPackage::getDeliveryNo);
                            if (Objects.nonNull(drugsOrderVo.getPrescriptionId())) {
                                lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, drugsOrderVo.getPrescriptionId());
                            } else {
                                lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrderVo.getId());
                            }
                            if (drugsOrderVo.getAmount().compareTo(new BigDecimal(0)) == 0) {
                                lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
                            } else {
                                lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
                            }
                            lambdaQuery.orderByAsc(BusDrugOrderPackage::getDeliveryTime);
                            List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectList(lambdaQuery);
                            if (CollectionUtil.isNotEmpty(busDrugOrderPackages)) {
                                drugsOrderVo.setDeliveryTime(busDrugOrderPackages.get(0).getDeliveryTime());
                            }
                        }
                        break;
                    case "2": // 服务包订单
                        BusFiveServicePackOrderVo servicePackOrderVo =
                                busFiveServicePackOrderMapper.selectDetailById(b.getSubOrderId());
                        // 查询医生信息
                        BusDoctorVo busDoctorVo =
                                busFiveServicePackOrderMapper.queryDoctorInfo(servicePackOrderVo.getWorkGroupId());
                        servicePackOrderVo.setDepartmentName(busDoctorVo.getDepartmentName());
                        servicePackOrderVo.setDoctorTitle(busDoctorVo.getDoctorTitle());
                        b.setServicePackOrderVo(servicePackOrderVo);
                        queryWrapper.eq("order_no", servicePackOrderVo.getOrderNo());
                        BusAfterSale busAfterSale2 = busAfterSaleMapper.selectOne(queryWrapper);
                        if (Objects.nonNull(busAfterSale2)) {
                            servicePackOrderVo.setExitAfterSale(1);
                        }
                        break;
                    default:
                }
            });
        }
         return busOrderVos;
    }

    /**
     * 新增购物车订单
     *
     * @param dto 创建订单请求参数
     * @return 订单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addCartOrder(CartOrderDTO dto) {
        log.debug("购物车下单入参:{}",JSON.toJSONString(dto));
        // 查询医院设置信息
        BusConsultationSettings consultationSettings = new BusConsultationSettings();
        consultationSettings.setHospitalId(dto.getHospitalId());
        BusConsultationSettings settings = busConsultationSettingsService.selectOne(consultationSettings);
        // 普通运费
        dto.setFreightSetting(settings.getFreightSetting());
        // 查询支付方式
        BusHospital busHospital = busHospitalMapper.selectById(dto.getHospitalId());
        dto.setPayWay(busHospital.getPayWay());
        dto.setHospitalName(busHospital.getHospitalName());
        // 购物车统一下单
        Map<String, Object> resultMap = placeOrdersUniformly(dto);
        if (Objects.isNull(resultMap.get("orderIdMap"))) {
            return resultMap;
        }
        // 获取子订单ID集合
        Map<String, List<Long>> orderIdMap = (Map<String, List<Long>>) resultMap.get("orderIdMap");
        // 获取药品订单ID
        List<Long> drugsOrderIds = orderIdMap.get("drugsOrderIds");
        // 获取商品订单ID
        List<Long> goodsOrderIds = orderIdMap.get("goodsOrderIds");
        // 保存总订单
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String orderNo = OrderTypeConstant.TOTAL_ORDER + simpleDateFormat.format(new Date());
        List<BusOrder> busOrders = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(drugsOrderIds)) {
            for (Long drugsOrderId : drugsOrderIds) {
                BusOrder busOrder = new BusOrder();
                busOrder.setOrderNo(orderNo);
                busOrder.setSubOrderType("0");
                busOrder.setSubOrderId(drugsOrderId);
                fillOrder(busOrder, dto);
                busOrders.add(busOrder);
            }
        }
        if (CollectionUtil.isNotEmpty(goodsOrderIds)) {
            for (Long goodsOrderId : goodsOrderIds) {
                BusOrder busOrder = new BusOrder();
                busOrder.setOrderNo(orderNo);
                busOrder.setSubOrderType("1");
                busOrder.setSubOrderId(goodsOrderId);
                //获取运费
                fillOrder(busOrder, dto);
                busOrders.add(busOrder);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("生成订单入参={}", JSONObject.toJSONString(busOrders));
        }
        busOrderMapper.batchInsert(busOrders);

        // 将订单加入待支付队列
        busDrugsOrderService.delayQueue(dto.getHospitalId(), "null", orderNo);

        // 校验患者是否是经纪人邀请
        busOrders.forEach(
                busOrder -> {
                    BusOrderDto busOrderDto = new BusOrderDto();
                    busOrderDto.setHospitalId(dto.getHospitalId());
                    // 总订单只能根据订单号来查询了
                    busOrderDto.setOrderNo(orderNo);
                    busOrderDto.setPatientId(dto.getPatientId());
                    busOrderDto.setId(busOrder.getId());
                    // 设置订单类型为 药品/商品订单
                    busOrderDto.setOrderType(1);
                    // 查看是否含有商品订单
                    busOrderDto.setHasShopOrder(BusOrderSubOrderTypeEnum.GOODS_ORDER.getCode().equals(busOrder.getSubOrderType()));
                    busChannelOrderService.createBusChannelOrderByOrderDTO(busOrderDto);
                }
        );
        Map<String, Object> map = new HashMap<>(1);
        map.put("orderNo", orderNo);
        return map;
    }

    private void fillOrder(BusOrder busOrder, CartOrderDTO dto) {
        Date nowDate = DateUtils.getNowDate();
        busOrder.setOrderTime(nowDate);
        busOrder.setCreateTime(nowDate);
        busOrder.setRemarks(dto.getRemarks());
        busOrder.setPartnersCode(dto.getPartnersCode());
        busOrder.setHospitalId(dto.getHospitalId());
        busOrder.setHospitalName(dto.getHospitalName());
        busOrder.setPatientId(dto.getPatientId());
        BigDecimal relPrice = dto.getRelPrice();
        // 自提与上门服务都免邮
        List<String> freeShippingList = Lists.newArrayList("0", "2","3");
        if (freeShippingList.contains(dto.getDeliveryType())) {
            busOrder.setRelPrice(relPrice);
            busOrder.setFreight(0d);
        } else {
            String[] split = dto.getFreightSetting().split(",");
            BigDecimal freeShipping = new BigDecimal(split[1]);
            if (relPrice.compareTo(freeShipping) >= 0) {
                busOrder.setRelPrice(relPrice);
                busOrder.setFreight(0d);
            } else {
                BigDecimal freight = new BigDecimal(split[0]);
                busOrder.setRelPrice(relPrice.add(freight));
                busOrder.setFreight(freight.doubleValue());
            }
        }
        busOrder.setDeliveryType(dto.getDeliveryType());
        busOrder.setPickUpTime(dto.getPickUpTime());
        if (CodeEnum.NO.getCode().equals(dto.getDeliveryType())) {
            BusPatient busPatient = busPatientService.selectPatientInfo(dto.getPatientId());
            busOrder.setReceiver(null);
            busOrder.setReceivePhone(busPatient.getPhoneNumber());
        } else {
            busOrder.setReceiver(dto.getReceivingUser());
            busOrder.setReceivePhone(dto.getReceivingTel());
        }
        busOrder.setReceiveAddress(dto.getReceivingAddress());
        busOrder.setPayWay(dto.getPayWay());
        busOrder.setOrderStatus("0");
        busOrder.setHospitalName(dto.getHospitalName());
        busOrder.setProvince(dto.getProvince());
        busOrder.setCity(dto.getCity());
        busOrder.setArea(dto.getArea());
        busOrder.setDetailedAddress(dto.getDetailedAddress());
        busOrder.setGroupId(dto.getGroupId());
        //设置诊查费
        busOrder.setExaminationFee(dto.getExaminationFee());
        busOrder.setExaminationName(dto.getExaminationName());

        //获取运费
        BusFreightResultDTO freightDTO = freightCalculator.calculate(convertFreightQuery(dto));
        busOrder.setFreight(Objects.nonNull(freightDTO.getFreight()) ? freightDTO.getFreight().doubleValue() : 0D);
        busOrder.setFreightType(freightDTO.getFreightType());
        busOrder.setRelPrice(dto.getRelPrice().add(BigDecimal.valueOf(busOrder.getFreight())));
        busOrder.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());
    }

    private BusFreightQueryDTO convertFreightQuery(CartOrderDTO dto) {
        if (dto.getAmount() == null) {
            return new BusFreightQueryDTO();
        }
        BusFreightQueryDTO query = new BusFreightQueryDTO();
        query.setHospitalId(dto.getHospitalId());
        query.setProvinceName(dto.getProvince());
        query.setCityName(dto.getCity());
        query.setAreaName(dto.getArea());
        query.setDeliveryType(dto.getDeliveryType());
        query.setAmount(new BigDecimal(dto.getAmount()));
        query.setGoodsAndDrugsList(dto.getGoodsAndDrugsList());
        return query;
    }

    @Override
    public BusOrder selectOreder(String code, Long subOrderId) {
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getSubOrderType, code);
        lambdaQuery.eq(BusOrder::getSubOrderId, subOrderId);
        return busOrderMapper.selectOne(lambdaQuery);
    }

    @Override
    public List<BusOrder> selectOrederByNo(String orderNo) {
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getOrderNo, orderNo);
        return busOrderMapper.selectList(lambdaQuery);
    }

    @Override
    public BigDecimal selectOrderAmount(List<BusOrder> busOrders) {
        BigDecimal orderAmount = new BigDecimal(0);
        BigDecimal orderFreight = new BigDecimal(0);
        for (BusOrder order : busOrders) {
            if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                // 查询药品订单信息
                BusDrugsOrder busDrugsOrder = busDrugsOrderService.selectOrderInfo(order.getSubOrderId());
                BigDecimal amount = new BigDecimal(busDrugsOrder.getAmount());
                orderFreight = BigDecimal.valueOf(busDrugsOrder.getFreight());
                BigDecimal subtract = amount.subtract(orderFreight);
                orderAmount = orderAmount.add(subtract);
            } else {
                // 查询商品订单信息
                R<com.puree.hospital.order.api.model.BusShopOrder> r = remoteBusShopOrderService.selectShopOrderById(order.getSubOrderId());
                if (!Constants.SUCCESS.equals(r.getCode())) {
                    throw new ServiceException(r.getMsg());
                }
                com.puree.hospital.order.api.model.BusShopOrder busShopOrder = r.getData();
                orderFreight = BigDecimal.valueOf(busShopOrder.getFreight());
                BigDecimal subtract = busShopOrder.getAmount().subtract(orderFreight);
                orderAmount = orderAmount.add(subtract);
            }
        }
        return orderAmount.add(orderFreight);
    }

    @Override
    public BusOrder selectOrederInfo(String code, Long subOrderId) {
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getSubOrderType, code);
        lambdaQuery.eq(BusOrder::getSubOrderId, subOrderId);
        return busOrderMapper.selectOne(lambdaQuery);
    }

    @Override
    public int updateOrderStatus(BusOrder busOrder) {
        int returnNum=0 ;
        if (null != busOrder.getId()) {
            returnNum = busOrderMapper.updateById(busOrder);
        } else {
            LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusOrder::getOrderNo, busOrder.getOrderNo());
            returnNum = busOrderMapper.update(busOrder, lambdaQuery);
        }
        // 5---订单状态 ： 已完成
        if ("5".equals( busOrder.getOrderStatus() )) {
            //----------宏捷荣---------------
            List<BusOrder> busOrderList = busOrderMapper.selectList(new LambdaQueryWrapper<BusOrder>().eq(BusOrder::getOrderNo, busOrder.getOrderNo()));
            BusOrder anyBusOrder = busOrderList.get(0);
            // 校验订单完成时间是否是一年内的 && 发票状态
            if ( InvoiceStatusEnum.TO_ISSUE.getStatus().equals(anyBusOrder.getInvoiceStatus()) ) {

                CompletableFuture.runAsync( ()->{
                    BusInvoiceHeader invoiceHeader = invoiceHeaderMapper.selectById(anyBusOrder.getInvoiceHeaderId());
                    BusHospitalInvoiceConfig hospitalInvoiceConfig = hospitalInvoiceConfigMapper.selectOne(new LambdaQueryWrapper<BusHospitalInvoiceConfig>().eq(BusHospitalInvoiceConfig::getHospitalId, anyBusOrder.getHospitalId() ));
                    List<GoodsMedicineDetail> goodsMedicineDetailList = orderInvoiceService.putGoodsMedicineData(busOrderList) ;
                    // 税控盘
                    if (InvoiceTypeEnum.TAX_CONTROl.getId().equals( hospitalInvoiceConfig.getType() )) {
                        try {
                            orderInvoiceService.taxControlIssueInvoice(invoiceHeader, hospitalInvoiceConfig, goodsMedicineDetailList, anyBusOrder.getOrderNo()) ;
                        } catch (IOException | NoSuchAlgorithmException | IllegalBlockSizeException |
                                 NoSuchPaddingException | BadPaddingException | InvalidKeyException |
                                 UnrecoverableKeyException | CertificateException | KeyStoreException |
                                 KeyManagementException e) {
                            throw new RuntimeException(e);
                        }
                    }

                }) ;
            }
            //----------宏捷荣---------------
        }

        return returnNum ;
    }

    @Override
    public int addOrder(BusOrder order) {
        return busOrderMapper.insert(order);
    }

    @Override
    public List<OrderVo> selectOrderList(DrugsOrderDto dto) {
        return busOrderMapper.selectOrderList(dto);
    }

    @Override
    public List<OrderVo> selectOrderInfo(String orderNo) {
        return busOrderMapper.selectOrder(orderNo);
    }

    @Override
    public BusShopOrderVO selectGoodsDetail(Long subOrderId) {
        return busOrderMapper.selectGoodsDetail(subOrderId);
    }

    @Override
    public List<BusOrder> selectDrugsAndGoodsList(String orderNo) {
        return busOrderMapper.selectDrugsAndGoodsList(orderNo);
    }

    @Override
    public BusShopOrderVO selectOrderAndGoodsDetail(String orderNo) {
        return busOrderMapper.selectOrderAndGoodsDetail(orderNo);
    }

    /**
     * 统一下单
     * @param dto 新增订单请求参数
     * @return 统一下单接口返回值
     */
    private Map<String, Object> placeOrdersUniformly(CartOrderDTO dto) {
        Map<String, Object> resultMap = new HashMap<>();
        // 定义存储生成订单ID集合
        Map<String, List<Long>> orderIdMap = new HashMap<>();
        // 处方生成订单
        insertDrugsOrder(dto, orderIdMap);
        // 商品/非处方药品信息
        List<GoodsAndDrugsDTO> goodsAndDrugsList = dto.getGoodsAndDrugsList();
        if (CollectionUtil.isNotEmpty(goodsAndDrugsList)) {
            // 定义无库存集合
            Map<String, Object> noStockMap = new HashMap<>();
            // 定义非处方药集合
            List<ShopCartDTO> otcDrugsList = new ArrayList<>();
            // 定义商品集合
            List<ShopCartDTO> goodsList = new ArrayList<>();
            // 定义购物车ID
            List<Long> cartIds = new ArrayList<>();
            for (GoodsAndDrugsDTO goodsAndDrugsDto : goodsAndDrugsList) {
                if ("0".equals(goodsAndDrugsDto.getType())) {
                    ShopCartDTO shopCartDto = new ShopCartDTO();
                    shopCartDto.setBusinessId(goodsAndDrugsDto.getBusinessId());
                    shopCartDto.setQuantity(goodsAndDrugsDto.getQuantity());
                    otcDrugsList.add(shopCartDto);
                } else if ("1".equals(goodsAndDrugsDto.getType())) {
                    ShopCartDTO shopCartDto = new ShopCartDTO();
                    shopCartDto.setBusinessId(goodsAndDrugsDto.getBusinessId());
                    shopCartDto.setQuantity(goodsAndDrugsDto.getQuantity());
                    goodsList.add(shopCartDto);
                }
                if (Objects.nonNull(goodsAndDrugsDto.getCartId())) {
                    cartIds.add(goodsAndDrugsDto.getCartId());
                }
            }
            if (CollectionUtil.isNotEmpty(otcDrugsList)) {
                // 查询非处方药品信息
                HospitalOfficinaDrugsDTO officinaDto = new HospitalOfficinaDrugsDTO();
                officinaDto.setHospitalId(dto.getHospitalId());
                officinaDto.setDrugsIds(otcDrugsList.stream().map(ShopCartDTO::getBusinessId).collect(Collectors.toList()));
                List<BusOtcDrugs> otcDrugsDtoList = busHospitalOfficinaService.selectDrugs(officinaDto);
                for (BusOtcDrugs otcDrugs : otcDrugsDtoList) {
                    for (ShopCartDTO shopCartDto : otcDrugsList) {
                        if (otcDrugs.getDrugsId().equals(shopCartDto.getBusinessId())) {
                            otcDrugs.setQuantity(shopCartDto.getQuantity());
                        }
                    }
                }
                // 生成非处方药品订单
                Map<String, Object> noStockOtcMap = insertOtcDrugsOrder(dto, orderIdMap, otcDrugsDtoList);
                if (MapUtil.isNotEmpty(noStockOtcMap)) {
                    List<Long> noStockIds = (List<Long>) noStockOtcMap.get("errorDrugsId");
                    List<Object> noStockList = new ArrayList<>();
                    for (Long id : noStockIds) {
                        Map<String, Object> otcNoStock = new HashMap<>();
                        otcNoStock.put("type", 0);
                        otcNoStock.put("id", id);
                        noStockList.add(otcNoStock);
                    }
                    noStockMap.put("noStockIds", noStockList);
                }
            }
            if (CollectionUtil.isNotEmpty(goodsList)) {
                // 生成商品订单
                Map<String, Object> noStockShopMap = insertGoodsOrder(dto, orderIdMap, goodsList);
                if (CollectionUtil.isNotEmpty(noStockShopMap)) {
                    List<Long> noStockIds = (List<Long>) noStockShopMap.get("noStockIds");
                    List<Object> noStockList = (List<Object>) noStockMap.get("noStockIds");
                    if (CollectionUtil.isEmpty(noStockList)) {
                        noStockList = new ArrayList<>();
                    }
                    for (Long id : noStockIds) {
                        Map<String, Object> goodsNoStock = new HashMap<>();
                        goodsNoStock.put("type", 1);
                        goodsNoStock.put("id", id);
                        noStockList.add(goodsNoStock);
                    }
                    noStockMap.put("noStockIds", noStockList);
                }
            }
            if (CollectionUtil.isNotEmpty(noStockMap)) {
                return noStockMap;
            }
            // 删除购物车中otc/商品信息
            if (CollectionUtil.isNotEmpty(cartIds)) {
                busShopCartService.delete(cartIds);
            }
        }
        resultMap.put("orderIdMap", orderIdMap);
        return resultMap;
    }

    /**
     * 新增商品订单
     * @param dto        新增订单请求参数
     * @param orderIdMap 订单ID集合
     * @param goodsList  商品列表
     * @return 订单ID集合
     */
    private Map<String, Object> insertGoodsOrder(CartOrderDTO dto, Map<String, List<Long>> orderIdMap, List<ShopCartDTO> goodsList) {
        Map<String, Object> noStockMap = new HashMap<>();
        List<Long> noStockIds = new ArrayList<>();
        List<BusOrderShop> shopGoodsList = new ArrayList<>();
        BigDecimal orderAmount = new BigDecimal(0);
        // 是否有配送企业商品
        boolean isEnterprise = false;
        // 校验商品库存
        for (ShopCartDTO goods : goodsList) {
            // 查询商品信息
            R<BusShopGoods> r = remoteShopGoodsService.getDetail(goods.getBusinessId());
            if (!Constants.SUCCESS.equals(r.getCode())) {
                throw new ServiceException(r.getMsg());
            }
            log.info("远程调用查询商品信息={}", JSONObject.toJSONString(r.getData()));
            BusShopGoods shopGoods = r.getData();
            if (YesNoEnum.NO.getCode().equals(shopGoods.getStatus())) {
                throw new ServiceException("商品已下架");
            }
            if (goods.getQuantity() > shopGoods.getStock()) {
                noStockIds.add(shopGoods.getId());
            }
            BusOrderShop busOrderShop = new BusOrderShop();
            busOrderShop.setShopId(shopGoods.getId());
            busOrderShop.setShopName(shopGoods.getName());
            busOrderShop.setShopTitle(shopGoods.getTitle());
            busOrderShop.setShopBrand(shopGoods.getBrandName());
            busOrderShop.setShopImg(shopGoods.getImg());
            busOrderShop.setShopSpecification(shopGoods.getSpecification());
            busOrderShop.setSellingPrice(shopGoods.getSellingPrice());
            busOrderShop.setQuantity(goods.getQuantity());
            busOrderShop.setHospitalId(dto.getHospitalId());
            if (Objects.isNull(shopGoods.getEnterpriseId())) {
                // 设置订单标识为众爱云仓
                if (Objects.nonNull(shopGoods.getHospitalId()) && !shopGoods.getParentId().equals(0L)) {
                    busOrderShop.setEnterpriseId(0L);
                }
            } else {
                busOrderShop.setEnterpriseId(shopGoods.getEnterpriseId());
            }
            busOrderShop.setReferencePurchasePrice(shopGoods.getCostPrice());
            //判断是否是院内商品
            if (!SOURCE_ID.equals(shopGoods.getSourceId())){
                isEnterprise = true;
            }
            // 设置商品的原始小计价格
            if (Objects.nonNull(busOrderShop.getSellingPrice()) &&
             Objects.nonNull(busOrderShop.getQuantity()) &&
             busOrderShop.getSellingPrice().compareTo(BigDecimal.ZERO) > 0 &&
                    busOrderShop.getQuantity() > 0) {
                busOrderShop.setOriginalSubtotalPrice(busOrderShop.getSellingPrice().multiply(new BigDecimal(goods.getQuantity())).setScale(2, RoundingMode.HALF_UP));
            }
            shopGoodsList.add(busOrderShop);
            orderAmount = orderAmount.add(shopGoods.getSellingPrice().multiply(new BigDecimal(goods.getQuantity())));
        }
        if (CollectionUtil.isNotEmpty(noStockIds)) {
            noStockMap.put("noStockIds", noStockIds);
            noStockMap.put("type", "3");
            return noStockMap;
        }
        // 新增商品订单
        BusShopOrder busShopOrder = new BusShopOrder();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        busShopOrder.setOrderNo(OrderTypeConstant.GOODS_ORDER + simpleDateFormat.format(new Date()));
        busShopOrder.setDeliveryType(dto.getDeliveryType());
        busShopOrder.setPatientId(dto.getPatientId());
        busShopOrder.setHospitalId(dto.getHospitalId());
        busShopOrder.setHospitalName(dto.getHospitalName());
        busShopOrder.setReferrer(dto.getReferrer());
        busShopOrder.setPayWay(dto.getPayWay());
        BigDecimal relPrice = dto.getRelPrice();
        if (Objects.isNull(relPrice)) {
            relPrice = new BigDecimal(0);
        }
        relPrice = relPrice.add(orderAmount);
        dto.setRelPrice(relPrice);
        busShopOrder.setAmount(orderAmount);
        busShopOrder.setFamilyId(dto.getFamilyId());
        busShopOrder.setFamilyName(dto.getFamilyName());
        busShopOrder.setFamilySex(dto.getFamilySex());
        busShopOrder.setFamilyAge(dto.getFamilyAge());
        busShopOrder.setRemarks(dto.getRemarks());
        busShopOrder.setOrderType(isEnterprise ? OrderSendTypeEnum.DELIVERY_COMPANY.getCode() : OrderSendTypeEnum.IN_HOSPITAL.getCode());
        busShopOrderMapper.insert(busShopOrder);
        for (BusOrderShop orderShop : shopGoodsList) {
            orderShop.setOrderId(busShopOrder.getId());
            orderShop.setCreateTime(DateUtils.getNowDate());
            busOrderShopMapper.insert(orderShop);
        }
        // 扣减医院商品库存
        changeHospitalShopStock(dto.getHospitalId(), shopGoodsList, StockEnum.REDUCE.getName());
        List<Long> goodsOrderIds = new ArrayList<>();
        goodsOrderIds.add(busShopOrder.getId());
        orderIdMap.put("goodsOrderIds", goodsOrderIds);
        return noStockMap;
    }

    /**
     * 更新商品库存
     * @param hospitalId 医院ID
     * @param shopList 商品集合
     * @param type 更新库存类型（"reduce 扣减"，"increase 释放"）
     */
    @Override
    public void changeHospitalShopStock(Long hospitalId, List<BusOrderShop> shopList, String type) {
        if (CollectionUtil.isEmpty(shopList)) {
            throw new ServiceException("商品缺失");
        }
        // 查询医院商品库存
        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("hospitalId", hospitalId);
        paramsMap.put("shopList", shopList);
        R<List<BusShopGoods>> r = remoteShopGoodsService.listShopStock(paramsMap);
        if (CollectionUtil.isNotEmpty(r.getData())) {
            List<BusShopGoods> shopGoods = r.getData() ;
            log.info("查询商品信息={}", JSONObject.toJSONString(shopGoods));
            for (BusOrderShop shop : shopList) {
                for (BusShopGoods shopGood : shopGoods) {
                    if (shop.getShopId().equals(shopGood.getId())) {
                        BusShopGoods busShopGoods = new BusShopGoods();
                        if (Objects.isNull(shop.getEnterpriseId())) {
                            busShopGoods.setId(shop.getShopId());
                        } else {
                            busShopGoods.setId(shopGood.getParentId());
                        }
                        int stock;
                        if (StockEnum.REDUCE.getName().equals(type)) {
                            stock = shopGood.getStock() - shop.getQuantity();
                            if (stock < 0) {
                                throw new ServiceException("库存不足");
                            }
                        } else {
                            stock = shopGood.getStock() + shop.getQuantity();
                        }
                        busShopGoods.setStock(stock);
                        // 更新商品库存
                        remoteShopGoodsService.updateStock(busShopGoods);
                    }
                }
            }
        } else {
            log.error("远程调用查询商品信息失败={}", r.getMsg());
        }
    }

    @Override
    public OrderVo queryOrderInfo(String orderType, String orderId) {
        OrderVo vo = new OrderVo();
        if (CodeEnum.NO.getCode().equals(orderType)) {
            BusConsultationOrder consultationOrder = busConsultationOrderMapper.selectById(Long.valueOf(orderId));
            vo.setOrderNo(consultationOrder.getOrderNo());
            String relPrice = DoubleUtils.roundByScaleOfStr(consultationOrder.getAmount(), 2);
            vo.setOrderAmount(relPrice);
        } else {
            BusDrugsOrder drugsOrder;
            if (orderId.contains("TO")){
                // orderId为总订单编号
                drugsOrder = busDrugsOrderMapper.selectDrugsOrder(orderId);
            }else {
                drugsOrder = busDrugsOrderMapper.selectById(Long.valueOf(orderId));
            }
            vo.setOrderNo(drugsOrder.getOrderNo());
            String relPrice = DoubleUtils.roundByScaleOfStr(drugsOrder.getAmount(), 2);
            vo.setOrderAmount(relPrice);
            if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
                BusPrescription prescription = busPrescriptionService.getById(drugsOrder.getPrescriptionId());
                vo.setFamilyName(prescription.getFamilyName());
                vo.setFamilyIdcard(prescription.getFamilyIdcard());
                vo.setDoctorName(prescription.getDoctorName());
                vo.setDepartmentName(prescription.getDepartmentName());
                vo.setDepartmentNumber(prescription.getDepartmentId().toString());
                BusDoctor doctor = busDoctorMapper.selectById(prescription.getDoctorId());
                vo.setDoctorNumber(doctor.getNationalDoctorCode());
                vo.setPrescriptionNumber(prescription.getPrescriptionNumber());
            }
        }
        return vo;
    }

    @Override
    public List<BusOrder> selectOrderById(Long id) {
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getSubOrderType, CodeEnum.NO.getCode());
        lambdaQuery.eq(BusOrder::getSubOrderId, id);
        BusOrder busOrder = busOrderMapper.selectOne(lambdaQuery);
        return selectOrederByNo(busOrder.getOrderNo());
    }

    @Override
    public List<OrderVo> selectWaitPayOrderList() {
        return busOrderMapper.selectWaitPayOrderList();
    }

    @Override
    public int modifyOrderAddr(BusOrder order) {
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getOrderNo, order.getOrderNo())
                .eq(BusOrder::getModifyAddress, YesNoEnum.NO.getCode());
        order.setModifyAddress(YesNoEnum.YES.getCode());
        return busOrderMapper.update(order, lambdaQuery);
    }

    /**
     * 新增otc药品订单
     * @param dto             新增订单请求参数
     * @param orderIdMap      订单id列表
     * @param otcDrugsDtoList otc药品列表
     * @return 新增订单结果
     */
    private Map<String, Object> insertOtcDrugsOrder(CartOrderDTO dto, Map<String, List<Long>> orderIdMap, List<BusOtcDrugs> otcDrugsDtoList) {
        // 填充订单信息
        BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
        busDrugsOrder.setDeliveryType(dto.getDeliveryType());
        busDrugsOrder.setReceivingUser(dto.getReceivingUser());
        busDrugsOrder.setReceivingTel(dto.getReceivingTel());
        busDrugsOrder.setReceivingAddress(dto.getReceivingAddress());
        BigDecimal orderAmount = new BigDecimal(0);

        for (BusOtcDrugs otcDrugs : otcDrugsDtoList) {
            BigDecimal sellPrice = otcDrugs.getSellingPrice().multiply(new BigDecimal(otcDrugs.getQuantity()));
            orderAmount = orderAmount.add(sellPrice);
        }
        BigDecimal relPrice = dto.getRelPrice();
        if (Objects.isNull(relPrice)) {
            relPrice = new BigDecimal(0);
        }
        relPrice = relPrice.add(orderAmount);
        dto.setRelPrice(relPrice);
        busDrugsOrder.setAmount(orderAmount.toString());
        busDrugsOrder.setOrderDrugsType("1");
        busDrugsOrder.setHospitalId(dto.getHospitalId());
        busDrugsOrder.setHospitalName(dto.getHospitalName());
        busDrugsOrder.setProvince(dto.getProvince());
        busDrugsOrder.setCity(dto.getCity());
        busDrugsOrder.setArea(dto.getArea());
        busDrugsOrder.setDetailedAddress(dto.getDetailedAddress());
        busDrugsOrder.setPatientId(dto.getPatientId());
        busDrugsOrder.setPayWay(dto.getPayWay());
        // 设置订单类别
        busDrugsOrder.setOrderClassify("1");
        busDrugsOrderService.addDrugsOrder(busDrugsOrder);
        busDrugsOrder.setList(otcDrugsDtoList);
        // 校验otc药品库存
        Map<String, Object> resultMap = busDrugsOrderService.checkOtcDrugsStock(busDrugsOrder);
        List<Long> drugsOrderIds = orderIdMap.get("drugsOrderIds");
        if (CollectionUtil.isEmpty(drugsOrderIds)) {
            drugsOrderIds = new ArrayList<>();
        }
        drugsOrderIds.add(busDrugsOrder.getId());
        orderIdMap.put("drugsOrderIds", drugsOrderIds);
        return resultMap;
    }

    /**
     * 新增处方药品订单
     * @param dto        新增订单请求参数
     * @param orderIdMap 子订单集合
     */
    private void insertDrugsOrder(CartOrderDTO dto, Map<String, List<Long>> orderIdMap) {
        // 定义存储处方ID的集合
        List<Long> prescriptionIdList = new ArrayList<>();
        List<Long> prescriptionList = dto.getPrescriptionIdList();
        if (CollectionUtil.isNotEmpty(prescriptionList)) {
            prescriptionIdList.addAll(prescriptionList);
        }
        List<Long> preOrderPrescriptionList = dto.getPreOrderPrescriptionIdList();
        if (CollectionUtil.isNotEmpty(preOrderPrescriptionList)) {
            prescriptionIdList.addAll(preOrderPrescriptionList);
        }
        if (CollectionUtil.isNotEmpty(prescriptionIdList)) {
            // 查询处方信息
            List<BusPrescription> busPrescriptions = busPrescriptionService.selectPrescriptionList(prescriptionIdList);
            if (CollectionUtil.isEmpty(busPrescriptions)) {
                throw new ServiceException("当前购物车未找到关联的处方信息");
            }

            List<Long> drugsOrderIds = new ArrayList<>();
            for (BusPrescription prescription : busPrescriptions) {
                if (PrescriptionStatusEnum.INVALID.getStatus().equals(prescription.getStatus())) {
                    throw new ServiceException("处方已失效请联系医生重新开方");
                }
                dto.setGroupId(prescription.getGroupId());
                // 填充药品订单信息
                BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
                busDrugsOrder.setPrescriptionId(prescription.getId());
                busDrugsOrder.setDeliveryType(dto.getDeliveryType());
                busDrugsOrder.setReceivingUser(dto.getReceivingUser());
                busDrugsOrder.setReceivingTel(dto.getReceivingTel());
                busDrugsOrder.setReceivingAddress(dto.getReceivingAddress());
                BigDecimal prescriptionAmount = prescription.getPrescriptionAmount();
                BigDecimal relPrice = dto.getRelPrice();
                if (Objects.isNull(relPrice)) {
                    relPrice = new BigDecimal(0);
                }
                relPrice = relPrice.add(prescriptionAmount);
                dto.setRelPrice(relPrice);
                BigDecimal examinationFee = prescription.getExaminationFee();
                //计算诊疗费总和
                BigDecimal examinationFeeAmount = dto.getExaminationFee();
                if (Objects.nonNull(examinationFee) && examinationFee.compareTo(BigDecimal.ZERO) > 0) {
                    examinationFeeAmount = examinationFeeAmount.add(examinationFee);
                    dto.setExaminationFee(examinationFeeAmount);
                    dto.setExaminationName(prescription.getExaminationName());
                }
                busDrugsOrder.setAmount(prescriptionAmount.toString());
                busDrugsOrder.setOrderDrugsType(prescription.getPrescriptionType());
                busDrugsOrder.setHospitalId(dto.getHospitalId());
                busDrugsOrder.setHospitalName(dto.getHospitalName());
                busDrugsOrder.setProvince(dto.getProvince());
                busDrugsOrder.setCity(dto.getCity());
                busDrugsOrder.setArea(dto.getArea());
                busDrugsOrder.setDetailedAddress(dto.getDetailedAddress());
                busDrugsOrder.setPatientId(prescription.getPatientId());
                busDrugsOrder.setPayWay(dto.getPayWay());
                busDrugsOrder.setExaminationFee(examinationFee);
                busDrugsOrder.setExaminationName(prescription.getExaminationName());
                List<BusPrescriptionDrugs> pdDrugsList = prescription.getPdDrugsList();
                boolean officinaFlag = false;
                boolean enterpriseFlag = false;
                for (BusPrescriptionDrugs drugs : pdDrugsList) {
                    if (Objects.isNull(drugs.getEnterpriseId()))  {
                        officinaFlag = true;
                    } else {
                        enterpriseFlag = true;
                    }
                }
                if (officinaFlag && enterpriseFlag) {
                    busDrugsOrder.setOrderType("0,1");
                } else if (officinaFlag) {
                    busDrugsOrder.setOrderType("0");
                } else if (enterpriseFlag) {
                    busDrugsOrder.setOrderType("1");
                }
                // 设置订单类别
                busDrugsOrder.setOrderClassify(prescription.getIdentity());
                busDrugsOrderService.addDrugsOrder(busDrugsOrder);
                // 补充药品订单包裹中订单ID
                if (enterpriseFlag) {
                    List<Long> enterpriseIds = pdDrugsList.stream().map(BusPrescriptionDrugs::getEnterpriseId)
                            .filter(Objects::nonNull).collect(Collectors.toList());
                    for (Long enterpriseId : enterpriseIds) {
                        // 保存订单ID到包裹表
                        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusDrugOrderPackage::getEnterpriseId, enterpriseId);
                        lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrder.getPrescriptionId());
                        lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
                        BusDrugOrderPackage drugOrderPackage = new BusDrugOrderPackage();
                        drugOrderPackage.setDrugsOrderId(busDrugsOrder.getId());
                        busDrugOrderPackageMapper.update(drugOrderPackage, lambdaQuery);
                    }
                }
                drugsOrderIds.add(busDrugsOrder.getId());
                // 修改处方状态为已使用
                busPrescriptionService.updatePrescription(prescription.getId());
            }
            orderIdMap.put("drugsOrderIds", drugsOrderIds);
            // 删除购物车处方信息
            for (Long prescriptionId : prescriptionIdList) {
                busShopCartService.deletePrescriptionOrOrder(prescriptionId,false);
            }
        }
    }
}

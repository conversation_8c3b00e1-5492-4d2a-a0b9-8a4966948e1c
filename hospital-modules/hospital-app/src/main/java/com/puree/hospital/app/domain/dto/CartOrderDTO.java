package com.puree.hospital.app.domain.dto;

import com.puree.hospital.app.api.model.dto.GoodsAndDrugsDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/8 14:35
 */
@Data
public class CartOrderDTO {
    /**
     * 普通处方
     */
    private List<Long> prescriptionIdList;
    /**
     * 预订单处方
     */
    private List<Long> preOrderPrescriptionIdList;
    /**
     * 商品/药品信息
     */
    private List<GoodsAndDrugsDTO> goodsAndDrugsList;
    /**
     * 机构标识
     */
    private String partnersCode;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 自提时间
     */
    private String pickUpTime;
    /**
     * 配送方式（0自提 1快递）
     */
    private String deliveryType;
    /**
     * 收货人
     */
    private String receivingUser;
    /**
     * 收货号码
     */
    private String receivingTel;
    /**
     * 收货地址
     */
    private String receivingAddress;
    /**
     * 订单金额
     */
    private String amount;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区/县
     */
    private String area;
    /**
     * 详细地址
     */
    private String detailedAddress;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 运费
     */
    private String freight;
    /**
     * 运费设置（10,100：表示基础运费10元，满100元包邮）
     */
    private String freightSetting;
    /**
     * 实付金额
     */
    private BigDecimal relPrice;

    /**
     * 推荐人
     */
    private Long referrer;
    /**
     *支付方式 1 微信支付 2 通联支付
     */
    private String payWay;
    /**
     * 就诊人ID
     */
    private Long familyId;
    /**
     * 就诊人姓名
     */
    private String familyName;
    /**
     * 就诊人性别
     */
    private Integer familySex;
    /**
     * 就诊人年龄
     */
    private String familyAge;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 诊查费
     */
    private BigDecimal examinationFee = BigDecimal.ZERO;

    /**
     * 诊查费名称
     */
    private String examinationName;

}

package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.puree.hospital.app.api.model.BusConsultationOrderDTO;
import com.puree.hospital.app.api.model.BusQuickConsultationDTO;
import com.puree.hospital.app.api.model.event.consultation.BaseConsultationEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationGuoHaoEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationPaySuccessEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationRefundSuccessEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationReturnNumberEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationEndedEvent;
import com.puree.hospital.app.constant.AfterSaleStatus;
import com.puree.hospital.app.constant.ConsultationOrderStatus;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusCommunicationMessage;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationPackage;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDepartment;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusDoctorPatient;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusDoctorScheduling;
import com.puree.hospital.app.domain.BusDoctorSchedulingTime;
import com.puree.hospital.app.domain.BusDoctorTdl;
import com.puree.hospital.app.domain.BusEhrCaseDiagnosisHistory;
import com.puree.hospital.app.domain.BusHospitalFamily;
import com.puree.hospital.app.domain.BusHospitalInvoiceConfig;
import com.puree.hospital.app.domain.BusHospitalPreorderDoctor;
import com.puree.hospital.app.domain.BusHospitalWechatConfig;
import com.puree.hospital.app.domain.BusInvoiceHeader;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPatientReportPhysician;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusProductInvoiceConfig;
import com.puree.hospital.app.domain.BusQuickConsultation;
import com.puree.hospital.app.domain.dto.BusDoctorDto;
import com.puree.hospital.app.domain.dto.BusDoctorSchedulingTimeDto;
import com.puree.hospital.app.domain.dto.BusOrderDto;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.dto.ConsultationOrderDto;
import com.puree.hospital.app.domain.dto.QuickConsultationDto;
import com.puree.hospital.app.domain.vo.BusConsultationOrderVo;
import com.puree.hospital.app.domain.vo.BusDoctorSchedulingTimeVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.BusPatientFamilyVo;
import com.puree.hospital.app.consultation.helper.BusQuickConsultationHelper;
import com.puree.hospital.app.his.domain.HisClientConfig;
import com.puree.hospital.app.his.helper.HospitalHisHelper;
import com.puree.hospital.app.mapper.BusAfterSaleMapper;
import com.puree.hospital.app.mapper.BusCommunicationMessageMapper;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusConsultationPackageMapper;
import com.puree.hospital.app.mapper.BusDoctorHospitalMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDoctorPatientMapper;
import com.puree.hospital.app.mapper.BusDoctorSchedulingMapper;
import com.puree.hospital.app.mapper.BusDoctorSchedulingTimeMapper;
import com.puree.hospital.app.mapper.BusDoctorTdlMapper;
import com.puree.hospital.app.mapper.BusEhrCaseDiagnosisHistoryMapper;
import com.puree.hospital.app.mapper.BusHospitalFamilyMapper;
import com.puree.hospital.app.mapper.BusHospitalInvoiceConfigMapper;
import com.puree.hospital.app.mapper.BusInvoiceHeaderMapper;
import com.puree.hospital.app.mapper.BusInvoiceMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPatientReportPhysicianMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusProductInvoiceConfigMapper;
import com.puree.hospital.app.mapper.BusQuickConsultationMapper;
import com.puree.hospital.app.prescription.helper.DualChannelDigitalRxParamHelper;
import com.puree.hospital.app.infrastructure.getui.enums.PageEnum;
import com.puree.hospital.app.infrastructure.getui.model.Notification;
import com.puree.hospital.app.infrastructure.getui.model.Transmission;
import com.puree.hospital.app.queue.QueueConstant;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.ConsultationNoReplyOverdueOneHourProducer;
import com.puree.hospital.app.queue.producer.ConsultationNoticeProducer;
import com.puree.hospital.app.queue.producer.ConsultationOrderAutoCompleteProducer;
import com.puree.hospital.app.queue.producer.ConsultationOrderCancelProducer;
import com.puree.hospital.app.queue.producer.ConsultationOrderRefundProducer;
import com.puree.hospital.app.queue.producer.VideoConsultationCancelProducer;
import com.puree.hospital.app.queue.producer.VideoConsultationGuohaoProducer;
import com.puree.hospital.app.queue.producer.VideoConsultationNoticeProducer;
import com.puree.hospital.app.queue.producer.VideoConsultationReachProducer;
import com.puree.hospital.app.queue.producer.VideoConsultationRefundProducer;
import com.puree.hospital.app.service.IBusAppMessagePushService;
import com.puree.hospital.app.service.IBusChannelOrderService;
import com.puree.hospital.app.service.IBusChannelPatientAgentRelationService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusConsultationSettingsService;
import com.puree.hospital.app.service.IBusDoctorHospitalService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusDoctorPatientService;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusHospitalPreorderDoctorService;
import com.puree.hospital.app.service.IBusHospitalWechatConfigService;
import com.puree.hospital.app.service.IBusPatientFamilyService;
import com.puree.hospital.app.service.ITongLianPayService;
import com.puree.hospital.app.service.IWxPayService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.core.domain.BusInvoice;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AfterSaleStatusEnum;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.BizStatusEnum;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ConsultationEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderStatusEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;
import com.puree.hospital.common.core.enums.DoctorPracticeEnum;
import com.puree.hospital.common.core.enums.FollowUpStatusEnum;
import com.puree.hospital.common.core.enums.ImGroupType;
import com.puree.hospital.common.core.enums.ImMemberEnum;
import com.puree.hospital.common.core.enums.InvoiceOrderTypeEnum;
import com.puree.hospital.common.core.enums.InvoiceStatusEnum;
import com.puree.hospital.common.core.enums.InvoiceTypeEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.PrescriptionStatusEnum;
import com.puree.hospital.common.core.enums.ProductTypeInvoiceEnum;
import com.puree.hospital.common.core.enums.RegisterTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.BeanUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.ehr.api.RemoteEhrCaseDiagnosisHistoryService;
import com.puree.hospital.ehr.api.RemoteEhrDataService;
import com.puree.hospital.ehr.api.model.BusEhrDataBmiRequest;
import com.puree.hospital.ehr.api.model.ConsultationDiagnosisVO;
import com.puree.hospital.followup.api.RemoteReportRegularRecordService;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import com.puree.hospital.followup.api.model.medical.ReportRegularRecordInsertDTO;
import com.puree.hospital.followup.api.model.medical.constant.RegularRecordEnum;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.im.api.model.MsgBody;
import com.puree.hospital.im.api.model.MsgContent;
import com.puree.hospital.im.api.model.SendMessageRequest;
import com.puree.hospital.insurance.api.RemotePreprocessorService;
import com.puree.hospital.insurance.api.model.OutpatientRegisterReqDTO;
import com.puree.hospital.insurance.api.model.OutpatientRegisterResultDTO;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.InvalidKeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusConsultationOrderServiceImpl extends ServiceImpl<BusConsultationOrderMapper,
        BusConsultationOrder> implements IBusConsultationOrderService {
    @Autowired
    @Lazy
    private IWxPayService wxPayService;
    @Autowired
    private IBusDoctorPatientService busDoctorPatientService;
    private final BusConsultationOrderMapper consultationOrderMapper;
    private final BusDoctorPatientMapper doctorPatientMapper;
    private final BusAfterSaleMapper afterSaleMapper;
    private final BusConsultationPackageMapper busConsultationPackageMapper;
    private final BusDoctorTdlMapper busDoctorTdlMapper;
    private final BusQuickConsultationMapper busQuickConsultationMapper;
    private final BusCommunicationMessageMapper busCommunicationMessageMapper;
    private final BusDoctorSchedulingMapper busDoctorSchedulingMapper;
    private final BusDoctorSchedulingTimeMapper busDoctorSchedulingTimeMapper;
    private final BusDoctorMapper busDoctorMapper;
    private final BusHospitalFamilyMapper busHospitalFamilyMapper;
    private final BusDoctorHospitalMapper busDoctorHospitalMapper;
    private final IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final RemoteImService remoteImService;
    private final IBusPatientFamilyService busPatientFamilyService;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    private final IBusHospitalWechatConfigService busHospitalWechatConfigService;
    private final IBusDrugsOrderService busDrugsOrderService;
    private final RemoteEhrDataService remoteEhrDataService;
    private final BusPatientReportPhysicianMapper reportPhysicianMapper;
    private final BusPatientFamilyMapper patientFamilyMapper;
    private final IBusAppMessagePushService busAppMessagePushService;
    @Lazy
    @Resource
    private ITongLianPayService iTongLianPayService;
    private final BusHospitalInvoiceService hospitalInvoiceService;
    private final BusProductInvoiceConfigMapper productInvoiceConfigMapper;
    private final BusHospitalInvoiceConfigMapper hospitalInvoiceConfigMapper;
    private final BusConsultationInvoiceService consultationInvoiceService;
    private final BusInvoiceHeaderMapper invoiceHeaderMapper;
    private final BusInvoiceMapper invoiceMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusEhrCaseDiagnosisHistoryMapper busEhrCaseDiagnosisHistoryMapper;

    @Autowired
    @Lazy
    private ConsultationNoReplyOverdueOneHourProducer noReplyOverdueOneHourProducer;
    @Autowired
    @Lazy
    private ConsultationOrderAutoCompleteProducer consultationOrderAutoCompleteProducer;
    @Autowired
    @Lazy
    private ConsultationOrderCancelProducer consultationOrderCancelProducer;
    @Autowired
    @Lazy
    private VideoConsultationCancelProducer videoConsultationCancelProducer;
    @Autowired
    @Lazy
    private VideoConsultationGuohaoProducer videoConsultationGuohaoProducer;
    @Autowired
    @Lazy
    private VideoConsultationNoticeProducer videoConsultationNoticeProducer;
    @Autowired
    @Lazy
    private VideoConsultationReachProducer videoConsultationReachProducer;
    @Autowired
    @Lazy
    private VideoConsultationRefundProducer videoConsultationRefundProducer;
    private final RemoteReportRegularRecordService reportRegularRecordService;
    private final RemotePreprocessorService reremotePreprocessorService;
    private final IBusDoctorHospitalService busDoctorHospitalService;
    private final IBusHospitalPreorderDoctorService busHospitalPreorderDoctorService;
    private final DualChannelDigitalRxParamHelper dualChannelDigitalRxParamHelper;

    @Resource
    private ApplicationEventPublisher publisher;

    @Lazy
    @Resource
    private ConsultationNoticeProducer consultationNoticeProducer;

    @Lazy
    @Resource
    private ConsultationOrderRefundProducer consultationOrderRefundProducer;

    @Resource
    private RemoteEhrCaseDiagnosisHistoryService remoteEhrCaseDiagnosisHistoryService;

    @Resource
    private HospitalHisHelper hospitalHisHelper;

    @Resource
    private RemoteHospitalSettingApi remoteHospitalSettingApi;

    private static final String HOSPITAL_HIS_CONFIG = "HIS";

    @Resource
    private IBusChannelPatientAgentRelationService busChannelPatientAgentRelationService;

    @Resource
    private IBusChannelOrderService busChannelOrderService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public long insert(ConsultationOrderDto orderDto) {
        BusQuickConsultation quickConsultation = orderDto.getQuickConsultation();
        BusQuickConsultationHelper.check(quickConsultation);
        // 获取问诊订单的类型是挂科室还是挂医生
        String type = orderDto.getConsultationType();
        // 拼接查询问诊订单参数
        BusConsultationOrder order = new BusConsultationOrder();
        order.setHospitalId(quickConsultation.getHospitalId());
        order.setPatientId(quickConsultation.getPatientId());
        order.setFamilyId(quickConsultation.getFamilyId());
        if (CodeEnum.NO.getCode().equals(type)) {
            // 极速问诊限制就诊人不能购买同个科室下的问诊订单
            order.setDepartmentId(quickConsultation.getDepartmentId());
            order.setConsultationType(type);
            order.setStatus("1,9");
            BusConsultationOrder consultationOrders = selectBusConsultationOrder(order);
            if (ObjectUtil.isNotNull(consultationOrders)) {
                throw new ServiceException("该科室订单还未结束，请不要重复下单！");
            }
        } else {
            // 校验同个就诊人在同个医生不可同时生成图文或者视频进行中的订单
            order.setDoctorId(quickConsultation.getDoctorId());
            order.setStatus("1,2,3,9");
            order.setVideoStatus("2,3,9,10,11,12");
            BusConsultationOrder consultationOrders = selectBusConsultationOrder(order);
            if (ObjectUtil.isNotNull(consultationOrders)) {
                throw new ServiceException("您在该医生下还有未完成的订单，不能购买！");
            }
        }
        //更新就诊人表身高体重
        if (orderDto.getQuickConsultation() != null) {

            Integer weight = orderDto.getQuickConsultation().getWeight();
            Integer height = orderDto.getQuickConsultation().getHeight();
            //为空则填充 0
            weight = null != weight ? weight : 0;
            height = null != height ? height : 0;

            BusPatientFamily busPatientFamily = new BusPatientFamily();
            busPatientFamily.setPatientId(orderDto.getQuickConsultation().getPatientId());
            busPatientFamily.setId(orderDto.getQuickConsultation().getFamilyId());
            busPatientFamily.setHeight(height);
            busPatientFamily.setWeight(weight);
            busPatientFamilyService.edit(busPatientFamily);

            //更新健康档案
            BusPatientFamily patientFamily = busPatientFamilyService.getById(orderDto.getQuickConsultation().getFamilyId());
            BusEhrDataBmiRequest busEhrDataBmi = new BusEhrDataBmiRequest();
            busEhrDataBmi.setHeight(null != orderDto.getQuickConsultation().getHeight() ? Double.valueOf(orderDto.getQuickConsultation().getHeight()) : 0);
            busEhrDataBmi.setWeight(null != orderDto.getQuickConsultation().getWeight() ? Double.valueOf(orderDto.getQuickConsultation().getWeight()) : 0);
            busEhrDataBmi.setFamilyIdCard(patientFamily.getIdNumber());
            busEhrDataBmi.setSource("0");
            busEhrDataBmi.setCreateBy(busPatientFamily.getPatientId() + "");
            R<Integer> objectR = remoteEhrDataService.insertEhrDataBmi(busEhrDataBmi);
            if (Constants.FAIL.equals(objectR.getCode())) {
                throw new ServiceException("更新健康档案失败");
            }

            String idNumber = patientFamily.getIdNumber();
            if (CharSequenceUtil.isNotEmpty(idNumber)) {
                //写入新健康档案
                insertRegularRecord(weight, height, patientFamily.getIdNumber(),
                        quickConsultation.getHospitalId(), quickConsultation.getFamilyId());
            }
        }

        // 新增问诊信息
        quickConsultation.setCreateTime(DateUtils.getNowDate());
        busQuickConsultationMapper.insert(quickConsultation);

        BusConsultationOrder consultationOrder = OrikaUtils.convert(orderDto, BusConsultationOrder.class);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        consultationOrder.setOrderNo(OrderTypeConstant.CONSULATION_ORDER + simpleDateFormat.format(new Date()));
        consultationOrder.setPatientId(quickConsultation.getPatientId());
        consultationOrder.setConsultationId(quickConsultation.getId());
        //根据是否有上传病例，区分是复诊 还是咨询业务
        if (CharSequenceUtil.isNotBlank(quickConsultation.getPicture())) {
            consultationOrder.setRegisterType(RegisterTypeEnum.VISIT.getCode());
        } else {
            consultationOrder.setRegisterType(RegisterTypeEnum.CONSULT.getCode());
        }
        // 图文
        if (CodeEnum.NO.getCode().equals(orderDto.getOrderType())) {
            // 查询该就诊人是否购买视频问诊
            order.setDoctorId(quickConsultation.getDoctorId());
            order.setDepartmentId(quickConsultation.getDepartmentId());
            order.setOrderType(CodeEnum.YES.getCode());
            order.setVideoStatus("0,2,3,9,10,11,12");
            BusConsultationOrder consultationOrders = selectBusConsultationOrder(order);
            if (ObjectUtil.isNotNull(consultationOrders)) {
                throw new ServiceException("视频问诊还未完成，不能购买！");
            }
            if (CodeEnum.NO.getCode().equals(consultationOrder.getAmount())) {
                consultationOrder.setPaymentTime(DateUtils.getNowDate());
                consultationOrder.setStatus(ConsultationOrderStatus.PAID);
            } else {
                consultationOrder.setStatus(ConsultationOrderStatus.UNPAID);
            }
        } else { // 视频
            Date subscribeTime = DateUtils.parseDate(orderDto.getSubscribeTime());
            if (ObjectUtil.isNull(subscribeTime)) {
                throw new ServiceException("预约开始时间缺失");
            }
            // 校验预约时间是否冲突
            QueryWrapper<BusConsultationOrder> queryWrapper4 = new QueryWrapper<>();
            queryWrapper4
                    .eq("hospital_id", quickConsultation.getHospitalId())
                    .eq("order_type", "1")
                    .eq("patient_id", quickConsultation.getPatientId())
                    .eq("family_id", quickConsultation.getFamilyId())
                    .inSql("video_status", "2,3,9,10,11,12")
                    .gt("start_time", DateUtils.getNowDate());
            List<BusConsultationOrder> busConsultationOrders = consultationOrderMapper.selectList(queryWrapper4);
            if (CollUtil.isNotEmpty(busConsultationOrders)) {
                busConsultationOrders.forEach(c -> {
                    if (DateUtils.isAvailableDate(subscribeTime, c.getStartTime(), c.getEndTime())) {
                        throw new ServiceException("预约时间冲突！");
                    }
                });
            }
            // 校验当前预约时间是否已被预约
            QueryWrapper<BusConsultationOrder> queryWrapper3 = new QueryWrapper<>();
            queryWrapper3
                    .eq("hospital_id", quickConsultation.getHospitalId())
                    .eq("doctor_id", quickConsultation.getDoctorId())
                    .eq("start_time", subscribeTime)
                    .inSql("video_status", "0,10")
                    .last("limit 1");
            BusConsultationOrder busConsultationOrder = consultationOrderMapper.selectOne(queryWrapper3);
            if (ObjectUtil.isNotNull(busConsultationOrder)) {
                throw new ServiceException("该时间已被预约！");
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String format = sdf.format(subscribeTime);
            quickConsultation.setSchedulingDate(format);
            // 查询医生排版时间
            List<BusDoctorSchedulingTime> list = busDoctorSchedulingMapper.selectSchedulingList(quickConsultation);
            String minute = "";
            if (CollUtil.isEmpty(list)) {
                throw new ServiceException("医生排班发生变化，请重新预约！");
            } else {
                for (BusDoctorSchedulingTime schedulingTime : list) {
                    if (StringUtils.isEmpty(minute)) {
                        Date start = DateUtils.pjTime(DateUtils.parseDate(format), schedulingTime.getStartTime());
                        Date end = DateUtils.pjTime(DateUtils.parseDate(format), schedulingTime.getEndTime());
                        boolean effectiveDate = DateUtils.isEffectiveDate(subscribeTime, start, end);
                        if (effectiveDate) {
                            minute = schedulingTime.getMinute();
                        }
                    }
                }
                if (StringUtils.isEmpty(minute)) {
                    throw new ServiceException("医生排班发生变化，请重新预约！");
                }
            }
            consultationOrder.setRound(0);
            consultationOrder.setStartTime(subscribeTime);
            Calendar nowTime = Calendar.getInstance();
            nowTime.setTime(subscribeTime);
            nowTime.add(Calendar.MINUTE, Integer.parseInt(minute));
            consultationOrder.setEndTime(nowTime.getTime());
            if (CodeEnum.NO.getCode().equals(consultationOrder.getAmount())) {
                consultationOrder.setPaymentTime(DateUtils.getNowDate());
                consultationOrder.setVideoStatus(ConsultationOrderStatus.RESERVED_NOT_ARRIVED);
            } else {
                consultationOrder.setVideoStatus(ConsultationOrderStatus.UNPAID);
            }
        }
        consultationOrder.setOrderTime(DateUtils.getNowDate());
        consultationOrder.setHospitalId(quickConsultation.getHospitalId());
        consultationOrder.setDoctorId(quickConsultation.getDoctorId());
        consultationOrder.setDepartmentId(quickConsultation.getDepartmentId());
        consultationOrder.setFamilyId(quickConsultation.getFamilyId());
        // 诊室外购买的问诊订单
        consultationOrder.setBuyType(CodeEnum.YES.getCode());
        consultationOrder.setConsultationType(type);
        consultationOrder.setCreateTime(DateUtils.getNowDate());
        consultationOrder.setPayWay(busDrugsOrderService.getHospitalPayWay(quickConsultation.getHospitalId()));

        // 创建问诊订单的时候，invoiceStatus 不可开具
        consultationOrder.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());

        consultationOrderMapper.insert(consultationOrder);
        // 挂医生零元的图文问诊推送消息给医生
        if (ObjectUtil.isNotNull(quickConsultation.getDoctorId()) && CodeEnum.NO.getCode().equals(consultationOrder.getAmount())) {
            pushTwMsgToDr(consultationOrder);
        }
        if (CodeEnum.NO.getCode().equals(consultationOrder.getAmount()) &&
                CodeEnum.YES.getCode().equals(consultationOrder.getConsultationType())) {
            // 加入代办任务
            BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
            busDoctorTdl.setDoctorId(consultationOrder.getDoctorId());
            busDoctorTdl.setHospitalId(consultationOrder.getHospitalId());
            busDoctorTdl.setType(consultationOrder.getOrderType());
            busDoctorTdl.setBusinessId(consultationOrder.getId());
            busDoctorTdl.setStatus(CodeEnum.NO.getCode());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", consultationOrder.getFamilyName());
            jsonObject.put("age", consultationOrder.getFamilyAge());
            busDoctorTdl.setContent(jsonObject.toJSONString());
            // 查询角色类型
            LambdaQueryWrapper<BusDoctorHospital> query = Wrappers.lambdaQuery();
            query.eq(BusDoctorHospital::getHospitalId, consultationOrder.getHospitalId());
            query.eq(BusDoctorHospital::getDoctorId, consultationOrder.getDoctorId());
            busDoctorTdl.setRoleType(ImMemberEnum.DOCTOR.getCode());
            busDoctorTdl.setCreateTime(DateUtils.getNowDate());
            busDoctorTdlMapper.insert(busDoctorTdl);
        }
        Long hospitalId = consultationOrder.getHospitalId();
        Long patientId = consultationOrder.getPatientId();
        // 校验患者是否是经纪人邀请
        BusOrderDto busOrderDto = new BusOrderDto();
        busOrderDto.setHospitalId(hospitalId);
        busOrderDto.setPatientId(patientId);
        busOrderDto.setId(consultationOrder.getId());
        busOrderDto.setOrderNo(consultationOrder.getOrderNo());
        // 设置订单类型为问诊单 0 - 问诊单 1 - 药品/商品订单 2 - 服务包订单
        busOrderDto.setOrderType(0);
        log.info("---------打印日志信息:{}", busOrderDto);
        busChannelOrderService.createBusChannelOrderByOrderDTO(busOrderDto);

        // 插入问诊包
        // 图文
        if (CodeEnum.NO.getCode().equals(orderDto.getOrderType())) {
            BusConsultationPackage consultationPackage = new BusConsultationPackage();
            consultationPackage.setPayTime(DateUtils.getNowDate());
            consultationPackage.setType(consultationOrder.getOrderType());
            consultationPackage.setHospitalId(hospitalId);
            consultationPackage.setDoctorId(consultationOrder.getDoctorId());
            consultationPackage.setPatientId(patientId);
            consultationPackage.setFamilyId(consultationOrder.getFamilyId());
            consultationPackage.setTotalTimes(consultationOrder.getRound());
            consultationPackage.setDepartmentId(consultationOrder.getDepartmentId());
            consultationPackage.setCreateTime(DateUtils.getNowDate());
            consultationPackage.setOrderId(consultationOrder.getId());
            consultationPackage.setPayAmount(consultationOrder.getAmount());
            busConsultationPackageMapper.insert(consultationPackage);
        }

        BigDecimal amount = StringUtils.isNotBlank(consultationOrder.getAmount()) ? new BigDecimal(consultationOrder.getAmount()) : BigDecimal.ZERO;
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            //0元单直接发送支付成功事件
            ConsultationPaySuccessEvent paySuccessEvent = new ConsultationPaySuccessEvent();
            paySuccessEvent.setEventType(BaseConsultationEvent.EventType.PAY_SUCCESS);
            paySuccessEvent.setConsultationOrderId(consultationOrder.getId());
            paySuccessEvent.setOrderNo(consultationOrder.getOrderNo());
            publisher.publishEvent(paySuccessEvent);
        }

        // region 问诊加入消息队列
        if (ConsultationOrderTypeEnum.isVideo(orderDto.getOrderType())) {
            Message message = new Message();
            message.setId(consultationOrder.getId());
            message.setHospitalId(hospitalId);
            message.setFireTime(consultationOrder.getStartTime().getTime());
            videoConsultationGuohaoProducer.sendOnTime(message, message.getFireTime());

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(consultationOrder.getStartTime());
            // 15分钟前
            calendar.add(Calendar.MINUTE, -15);
            message.setFireTime(calendar.getTime().getTime());
            videoConsultationNoticeProducer.sendOnTime(message, message.getFireTime());

            message.setFireTime(QueueConstant.QUEUE_VIDEO_CONSULTATION_CANCEL_TIME);
            videoConsultationCancelProducer.delaySend(message, message.getFireTime());
            return consultationOrder.getId();
        }
        BusConsultationSettings bs = new BusConsultationSettings();
        bs.setHospitalId(hospitalId);
        bs = busConsultationSettingsService.selectOne(bs);
        //如果是0元单
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            // 超时未接诊加入队列
            Message message = new Message();
            message.setId(consultationOrder.getId());
            message.setHospitalId(consultationOrder.getHospitalId());

            if (bs == null || bs.getImagetextNoreplyExpire() == null) {
                message.setFireTime(QueueConstant.QUEUE_CONSULTATION_ORDER_REFUND_TIME);
            } else {
                long time = bs.getImagetextNoreplyExpire() * 60 * 60 * 1000L;
                message.setFireTime(time);
            }
            //超时未接诊|未回复自动退款
            consultationOrderRefundProducer.delaySend(message, message.getFireTime());
            // 超时未接诊通知队列
            message.setFireTime(QueueConstant.QUEUE_NOT_ACCEPTED_NOTICE_TIME);
            consultationNoticeProducer.delaySend(message, QueueConstant.QUEUE_NOT_ACCEPTED_NOTICE_TIME);
        } else {
            Message message = new Message();
            message.setId(consultationOrder.getId());
            message.setHospitalId(hospitalId);

            if (bs == null || bs.getConsultationOrderExpire() == null) {
                message.setFireTime(QueueConstant.QUEUE_CONSULTATION_ORDER_CANCEL_TIME);
            } else {
                long time = bs.getConsultationOrderExpire() * 60 * 60 * 1000L;
                message.setFireTime(time);
            }
            consultationOrderCancelProducer.delaySend(message, message.getFireTime());
        }
        // endregion
        return consultationOrder.getId();
    }

    /**
     * @Param hospitalId 医院ID
     * @Param patientId 患者ID
     * @Param idNumber 身份证
     * @Param itemType 类型
     * @Param itemValue  值
     * @Return ReportRegularRecordInsertDTO
     * @Description 构建 ReportRegularRecordInsertDTO 实体
     * <AUTHOR>
     * @Date 2024/7/18 14:38
     **/
    private ReportRegularRecordInsertDTO buildReportRegularRecordInsertDTO(Long hospitalId, Long patientId,
                                                                           String idNumber, RegularRecordEnum itemType, String itemValue) {
        ReportRegularRecordInsertDTO dto = new ReportRegularRecordInsertDTO();
        dto.setHospitalId(hospitalId);
        dto.setPatientId(patientId);
        dto.setPatientIdNumber(idNumber);
        dto.setItemType(itemType);
        dto.setItemUnit(itemType.getUnit());
        dto.setItemValue(itemValue);
        dto.setSource(RecordSourceEnum.PATIENT_ADDED);
        return dto;
    }

    /**
     * @Param weight
     * @Param height
     * @Param idNumber
     * @Param hospitalId
     * @Param familyId
     * @Return Boolean
     * @Description 写入新健康档案
     * <AUTHOR>
     * @Date 2024/7/18 14:54
     **/
    private Boolean insertRegularRecord(Integer weight, Integer height, String idNumber,
                                        Long hospitalId, Long familyId) {
        //计算 BMI 体重(kg)/身高(m)^2
        BigDecimal w = new BigDecimal(weight * 10000);
        BigDecimal h = new BigDecimal(height * height);
        BigDecimal bmi = new BigDecimal(weight);
        if ((BigDecimal.ZERO).compareTo(h) != 0) {
            bmi = w.divide(h, 2, RoundingMode.DOWN);
        }

        List<ReportRegularRecordInsertDTO> list = new ArrayList<>();

        list.add(buildReportRegularRecordInsertDTO(hospitalId
                , familyId, idNumber, RegularRecordEnum.WEIGHT, weight.toString()));
        list.add(buildReportRegularRecordInsertDTO(hospitalId
                , familyId, idNumber, RegularRecordEnum.TALL, height.toString()));
        list.add(buildReportRegularRecordInsertDTO(hospitalId
                , familyId, idNumber, RegularRecordEnum.BMI, bmi.toString()));

        return insertRecord(list);
    }

    /**
     * @Param list
     * @Return Boolean
     * @Description 远程调用插入数据
     * <AUTHOR>
     * @Date 2024/7/18 14:51
     **/
    private Boolean insertRecord(List<ReportRegularRecordInsertDTO> list) {
        AjaxResult<Boolean> r = reportRegularRecordService.insertRecord(list);
        if (r == null || r.getCode() == null || !r.getCode().equals(HttpStatus.SUCCESS)) {
            log.error("插入健康档案失败 :{}", r);
            throw new RuntimeException("插入健康档案失败");
        }
        return true;
    }

    @Override
    public BusConsultationOrder queryOrderInfo(Long id) {
        BusConsultationOrder busConsultationOrder = consultationOrderMapper.selectById(id);
        // 查看是否存在售后信息
        QueryWrapper<BusAfterSale> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("order_no", busConsultationOrder.getOrderNo());
        BusAfterSale busAfterSale = afterSaleMapper.selectOne(queryWrapper2);
        if (ObjectUtil.isNotNull(busAfterSale) && AfterSaleStatusEnum.DISAPPROVAL.getCode().equals(busAfterSale.getStatus())) {
            busConsultationOrder.setRefundReason(busAfterSale.getReason());
        }

        // invoiceStatus是否显示，取决于后台系统的配置
        boolean flag = hospitalInvoiceService.getHospitalInvoiceStatus(busConsultationOrder.getHospitalId());
        if (flag) {
            if (InvoiceStatusEnum.ISSUE_SUCCESS.getStatus().equals(busConsultationOrder.getInvoiceStatus())) {
                BusInvoice busInvoice = invoiceMapper.selectOne(new LambdaQueryWrapper<BusInvoice>()
                        .eq(BusInvoice::getOrderNo, busConsultationOrder.getOrderNo())
                        .eq(BusInvoice::getOrderType, InvoiceOrderTypeEnum.CONSULTATION.getType()));
                busConsultationOrder.setDownloadUrl(busInvoice.getDownloadUrl());
                busConsultationOrder.setPicUrl(busInvoice.getPicUrl());
            }
        } else {
            busConsultationOrder.setInvoiceStatus(null);
        }

        return busConsultationOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelOrder(BusConsultationOrder order) {
        BusConsultationOrder consultationOrder = new BusConsultationOrder();
        if ("0".equals(order.getOrderType())) { // 图文问诊
            consultationOrder.setStatus(ConsultationOrderStatus.CANCEL);
        } else { // 视频问诊
            consultationOrder.setVideoStatus(ConsultationOrderStatus.CANCEL);
        }
        // 将医生相应代办任务状态置为已办
        LambdaUpdateWrapper<BusDoctorTdl> updateWrapper = Wrappers.lambdaUpdate(BusDoctorTdl.class)
                .eq(BusDoctorTdl::getDoctorId, order.getDoctorId())
                .eq(BusDoctorTdl::getHospitalId, order.getHospitalId())
                .eq(BusDoctorTdl::getRoleType, ImMemberEnum.DOCTOR.getCode()).set(BusDoctorTdl::getStatus, CodeEnum.YES.getCode())
                .eq(BusDoctorTdl::getBusinessId, order.getId())
                .set(BusDoctorTdl::getUpdateTime, DateUtils.getNowDate())
                .set(BusDoctorTdl::getStatus, "2");
        busDoctorTdlMapper.update(new BusDoctorTdl(), updateWrapper);
        consultationOrder.setCancelTime(DateUtils.getNowDate());
        consultationOrder.setUpdateTime(DateUtils.getNowDate());
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", order.getOrderNo());
        return consultationOrderMapper.update(consultationOrder, queryWrapper);
    }

    @Override
    public List<BusConsultationOrderVo> patientOrderList(BusConsultationOrder consultationOrder) {
        List<BusConsultationOrderVo> consultationOrderVos = consultationOrderMapper.selectOrderList(consultationOrder);
        if (CollectionUtils.isEmpty(consultationOrderVos)) {
            return Collections.emptyList();
        }
        //若查询的订单不为空，则查询医院的发票状态
        boolean hospitalInvoiceStatus = hospitalInvoiceService.getHospitalInvoiceStatus(consultationOrderVos.get(0).getHospitalId());
        for (BusConsultationOrderVo vo : consultationOrderVos) {
            // 查询医生擅长
            if (Objects.nonNull(vo.getDoctorId())) {
                BusDoctor doctor = busDoctorMapper.selectById(vo.getDoctorId());
                if (Objects.nonNull(doctor)) {
                    vo.setBeGoodAt(doctor.getBeGoodAt());
                }
            }
            // 查询补充信息
            querySupplementInfo(vo);
            if (!hospitalInvoiceStatus) {
                vo.setInvoiceStatus(null);
                vo.setBusAfterSale(null);
            } else {
                if (InvoiceStatusEnum.ISSUE_SUCCESS.getStatus().equals(vo.getInvoiceStatus())) {
                    BusInvoice busInvoice = invoiceMapper.selectOne(new LambdaQueryWrapper<BusInvoice>()
                            .eq(BusInvoice::getOrderNo, vo.getOrderNo())
                            .eq(BusInvoice::getOrderType, InvoiceOrderTypeEnum.CONSULTATION.getType()));
                    vo.setDownloadUrl(busInvoice.getDownloadUrl());
                    vo.setPicUrl(busInvoice.getPicUrl());
                }
            }

            allPrescriptionCancelled(vo);

        }

        // 组装医生创建的问诊数据
        assemblerDoctorCreateDiagnosisData(consultationOrderVos);
        return consultationOrderVos;
    }

    public void allPrescriptionCancelled(BusConsultationOrderVo vo) {
        // 获取处方列表，判断是否全部作废
        List<BusPrescription> prescriptionList = busPrescriptionMapper.selectList(new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getConsultationOrderId, vo.getConsultationOrderId()));
        vo.setAllPrescriptionCancelled(prescriptionList.stream()
                .allMatch(p -> PrescriptionStatusEnum.CANCELLATION.getStatus().equals(p.getStatus())));
        // 如果订单状态为2问诊中，并且该问诊订单已开处方，则修改订单状态为3问诊中 已开处方
        if (ConsultationOrderStatusEnum.VISITING.getCode().equals(vo.getStatus()) && CollectionUtils.isNotEmpty(prescriptionList)) {
            vo.setStatus(ConsultationOrderStatusEnum.PRESCRIBED.getCode());
        }
        if (ConsultationOrderStatusEnum.VISITING.getCode().equals(vo.getVideoStatus()) && CollectionUtils.isNotEmpty(prescriptionList)) {
            vo.setVideoStatus(ConsultationOrderStatusEnum.PRESCRIBED.getCode());
        }
    }

    private void assemblerDoctorCreateDiagnosisData(List<BusConsultationOrderVo> consultationOrderVos) {
        List<Long> collect = consultationOrderVos.stream().map(BusConsultationOrderVo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        R<List<ConsultationDiagnosisVO>> result = remoteEhrCaseDiagnosisHistoryService.getDoctorCreateDiagnosisHistoryByConsultationIds(collect);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            return;
        }
        Map<Long, ConsultationDiagnosisVO> consultationDiagnosisVOMap = result.getData().stream()
                .collect(Collectors.toMap(ConsultationDiagnosisVO::getConsultationId, Function.identity() , (a, b) -> a.getDiagnosisId() > b.getDiagnosisId() ? a : b));
        consultationOrderVos.forEach(item -> item.setConsultationDiagnosisVO(consultationDiagnosisVOMap.get(item.getId())));
    }

    @Override
    public List<BusConsultationOrder> doctorOrderList(Long hospitalId, Long doctorId) {
        List<String> statusTypes = Lists.newArrayList("2", "3", "4", "7");
        List<String> videoTypes = Lists.newArrayList("2", "3", "4", "7", "10", "11", "12");
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusConsultationOrder::getHospitalId, hospitalId)
                .eq(BusConsultationOrder::getDoctorId, doctorId)
                .and(wrapper -> wrapper.in(BusConsultationOrder::getVideoStatus, videoTypes)
                        .or().in(BusConsultationOrder::getStatus, statusTypes))
                .orderByDesc(BusConsultationOrder::getCreateTime);
        List<BusConsultationOrder> consultationOrderList = consultationOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(consultationOrderList)) {
            List<Long> consultationOrderIdList = consultationOrderList.stream().distinct().map(BusConsultationOrder::getId).collect(Collectors.toList());
            Map<Long, List<BusEhrCaseDiagnosisHistory>> ehrCaseDiagnosisHistoryMap = new HashMap<>(consultationOrderIdList.size());
            if (CollectionUtils.isNotEmpty(consultationOrderIdList)) {
                LambdaQueryWrapper<BusEhrCaseDiagnosisHistory> wrapper = Wrappers.lambdaQuery(BusEhrCaseDiagnosisHistory.class)
                        .select(BusEhrCaseDiagnosisHistory::getId, BusEhrCaseDiagnosisHistory::getConsultationId)
                        .in(BusEhrCaseDiagnosisHistory::getConsultationId, consultationOrderList);
                List<BusEhrCaseDiagnosisHistory> ehrCaseDiagnosisHistories = busEhrCaseDiagnosisHistoryMapper.selectList(wrapper);
                ehrCaseDiagnosisHistoryMap.putAll(ehrCaseDiagnosisHistories.stream().collect(Collectors.groupingBy(BusEhrCaseDiagnosisHistory::getConsultationId)));
            }
            consultationOrderList.forEach(consultationOrder -> {
                List<BusEhrCaseDiagnosisHistory> list = ehrCaseDiagnosisHistoryMap.get(consultationOrder.getId());
                if (CollectionUtils.isNotEmpty(list)) {
                    BusEhrCaseDiagnosisHistory diagnosisHistory = list.stream().filter(history -> StringUtils.isNotEmpty(history.getAdvice())).findFirst().orElseGet(() -> list.get(0));
                    if (Objects.nonNull(diagnosisHistory)) {
                        consultationOrder.setEhrCaseDiagnosisHistoryId(diagnosisHistory.getId());
                    }
                }
            });
        }
        return consultationOrderList;
    }

    @Override
    public int updateStatus(BusConsultationOrder consultationOrder) {
        consultationOrder.setUpdateTime(DateUtils.getNowDate());
        return consultationOrderMapper.updateStatus(consultationOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long register(ConsultationOrderDto orderDto) {
        QueryWrapper<BusConsultationOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("order_no", orderDto.getOrderNo());
        BusConsultationOrder order = consultationOrderMapper.selectOne(orderQueryWrapper.last("limit 1"));
        if (ObjectUtil.isNull(order)) {
            throw new ServiceException("订单查询失败");
        }
        if (!"10".equals(order.getVideoStatus())) {
            throw new ServiceException("订单状态已发生改变,无法进入诊室,请刷新列表");
        }
        Date nowDate = DateUtils.getNowDate();
        if (nowDate.compareTo(order.getStartTime()) > 0) {
            throw new ServiceException("报到时间已过无法报到");
        }
        if (DateUtils.checkDateSize(order.getStartTime(), nowDate, 15)) {
            throw new ServiceException("报到时间未到无法报到");
        }
        if ("10".equals(order.getVideoStatus())) {
            BusConsultationOrder consultationOrder = new BusConsultationOrder();
            consultationOrder.setVideoStatus("11");
            consultationOrder.setCheckTime(DateUtils.getNowDate());
            UpdateWrapper<BusConsultationOrder> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("order_no", orderDto.getOrderNo());
            consultationOrderMapper.update(consultationOrder, updateWrapper);
            //校验是否绑定医生
            BusDoctorPatient doctorPatient = new BusDoctorPatient();
            doctorPatient.setDoctorId(order.getDoctorId());
            doctorPatient.setPatientId(order.getPatientId());
            doctorPatient.setHospitalId(order.getHospitalId());
            doctorPatient.setDepartmentId(order.getDepartmentId());
            doctorPatient.setFamilyId(order.getFamilyId());
            BusDoctorPatient busPatient = busDoctorPatientService.selectInfo(doctorPatient);
            if (busPatient == null) {
                doctorPatient.setCreateTime(DateUtils.getNowDate());
                doctorPatient.setGrade(YesNoEnum.NO.getCode());
                doctorPatientMapper.insert(doctorPatient);
            }
            //校验是否创建群组
            doctorPatient.setType(ImGroupType.INQUIRIES.getCode());
            BusDoctorPatientGroup doctorPatientGroup = OrikaUtils.convert(doctorPatient, BusDoctorPatientGroup.class);
            BusDoctorPatientGroup checkGroup = busDoctorPatientGroupService.checkGroup(doctorPatientGroup);
            Long group;
            if (ObjectUtil.isNull(checkGroup)) {
                group = busDoctorPatientGroupService.createGroup(doctorPatientGroup, CodeEnum.NO.getCode());
            } else if (CodeEnum.YES.getCode().equals(checkGroup.getDelFlag())) {
                //后台逻辑删除im创建
                doctorPatientGroup.setId(checkGroup.getId());
                busDoctorPatientGroupService.createGroup(doctorPatientGroup, checkGroup.getDelFlag());
                group = checkGroup.getId();
            } else {
                group = checkGroup.getId();
            }
            boolean doctor = this.sendChatMsgToDoctor(group, order);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("线程被中断异常:", e);
            }
            boolean patient = this.sendChatMsgToPatient(group, order);
            if (!patient || !doctor) {
                log.error("发送消息失败");
            }

            // region加入消息队列
            Message message = new Message();
            message.setId(order.getId());
            message.setHospitalId(order.getHospitalId());
            message.setFireTime(order.getStartTime().getTime());
            videoConsultationReachProducer.sendOnTime(message, message.getFireTime());

            BusConsultationSettings bs = new BusConsultationSettings();
            bs.setHospitalId(order.getHospitalId());
            bs = busConsultationSettingsService.selectOne(bs);
            long startTime = order.getEndTime().getTime();
            if (bs == null || bs.getImagetextNoreplyExpire() == null) {
                message.setFireTime(QueueConstant.QUEUE_VIDEO_CONSULTATION_REFUND_TIME + startTime);
            } else {
                long time = bs.getImagetextNoreplyExpire() * 60 * 60 * 1000;
                message.setFireTime(time + startTime);
            }
            message.setExtend(String.valueOf(order.getStartTime().getTime()));
            videoConsultationRefundProducer.sendOnTime(message, message.getFireTime());

            // endregion
            return group;
        }
        return 2L;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int guohao(ConsultationOrderDto orderDto) {
        BusConsultationOrder consultationOrder = new BusConsultationOrder();
        consultationOrder.setIsGuoHao(orderDto.getIsGuoHao());
        consultationOrder.setUpdateTime(DateUtils.getNowDate());
        UpdateWrapper<BusConsultationOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_no", orderDto.getOrderNo());
        return consultationOrderMapper.update(consultationOrder, updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> receiveConsultation(BusConsultationOrder dto) {
        log.info("接诊参数={}", dto);
        Map<String, Object> resultMap = new HashMap<>();
        // 查询订单状态
        QueryWrapper<BusConsultationOrder> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1
                .eq("hospital_id", dto.getHospitalId())
                .eq("order_no", dto.getOrderNo());
        BusConsultationOrder order = consultationOrderMapper.selectOne(queryWrapper1);
        String consultationType = dto.getConsultationType();
        Long returnData = null;
        // 接诊操作
        if (ConsultationOrderStatus.VISITING.equals(dto.getStatus())) {
            if (!ConsultationOrderStatus.PAID.equals(order.getStatus())) {
                throw new ServiceException(BizStatusEnum.NOT_WAITING_STATUS.getCode(), HttpStatus.BIZ_SPECIAL_STATUS);
            }
            // 挂科室
            if (CodeEnum.NO.getCode().equals(consultationType)) {
                //查询该医生是否接过该患者的单
                QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
                queryWrapper
                        .eq("hospital_id", dto.getHospitalId())
                        .eq("doctor_id", dto.getDoctorId())
                        .eq("department_id", dto.getDepartmentId())
                        .eq("patient_id", dto.getPatientId())
                        .eq("family_id", dto.getFamilyId())
                        // 订单状态（0待支付,1待接诊 未使用,2问诊中 使用中, 3问诊中 已开处方,4已完成 已使用,5已失效,6已退号,7已退款,8已取消,9退款中）
                        .and(wrapper -> wrapper.inSql("status", "0,1,2,3,9")
                                // 0待支付,2问诊中(未开处方), 3问诊中(已开处方),4已完成,6已退号,7已退款,8已取消,9退款中,10已预约(未报到),11已预约(已报到),12已过号
                                .or().inSql("video_status", "0,2,3,9,10,11"));
                List<BusConsultationOrder> busConsultationOrders = consultationOrderMapper.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(busConsultationOrders)) {
                    // 查询群组ID
                    BusDoctorPatientGroup patientGroup = new BusDoctorPatientGroup();
                    patientGroup.setHospitalId(dto.getHospitalId());
                    patientGroup.setDoctorId(dto.getDoctorId());
                    patientGroup.setDepartmentId(dto.getDepartmentId());
                    patientGroup.setPatientId(dto.getPatientId());
                    patientGroup.setFamilyId(dto.getFamilyId());
                    patientGroup.setType(CodeEnum.NO.getCode());
                    BusDoctorPatientGroup doctorPatientGroup = busDoctorPatientGroupService.selectOne(patientGroup);
                    if (ObjectUtil.isNotNull(doctorPatientGroup)) {
                        resultMap.put("groupId", doctorPatientGroup.getId());
                        resultMap.put("msg", "已有该患者的单！");
                        // 0表示接诊失败
                        resultMap.put("status", 0);
                        return resultMap;
                    }
                }
            }
            //校验是否绑定医生
            BusDoctorPatient doctorPatient = new BusDoctorPatient();
            doctorPatient.setDoctorId(dto.getDoctorId());
            doctorPatient.setPatientId(dto.getPatientId());
            doctorPatient.setHospitalId(dto.getHospitalId());
            doctorPatient.setDepartmentId(dto.getDepartmentId());
            doctorPatient.setFamilyId(dto.getFamilyId());
            BusDoctorPatient busPatient = busDoctorPatientService.selectInfo(doctorPatient);
            if (busPatient == null) {
                doctorPatient.setCreateTime(DateUtils.getNowDate());
                doctorPatient.setGrade(YesNoEnum.NO.getCode());
                doctorPatientMapper.insert(doctorPatient);
            }
            //校验是否创建群组
            BusDoctorPatientGroup doctorPatientGroup = OrikaUtils.convert(doctorPatient, BusDoctorPatientGroup.class);
            doctorPatientGroup.setType(CodeEnum.NO.getCode());
            BusDoctorPatientGroup checkGroup = busDoctorPatientGroupService.checkGroup(doctorPatientGroup);
            if (ObjectUtil.isNull(checkGroup)) {
                returnData = busDoctorPatientGroupService.createGroup(doctorPatientGroup, CodeEnum.NO.getCode());
            } else if (CodeEnum.YES.getCode().equals(checkGroup.getDelFlag())) {
                doctorPatientGroup.setId(checkGroup.getId());
                busDoctorPatientGroupService.createGroup(doctorPatientGroup, checkGroup.getDelFlag());
                returnData = checkGroup.getId();
            } else {
                returnData = checkGroup.getId();
            }
            // 挂医生问诊订单
            if (CodeEnum.YES.getCode().equals(consultationType)) {
                // 修改订单状态
                dto.setStatus(ConsultationOrderStatus.VISITING);
                dto.setUpdateTime(DateUtils.getNowDate());
                dto.setCheckTime(DateUtils.getNowDate());
                consultationOrderMapper.updateStatus(dto);
                //加入待办任务
                BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
                busDoctorTdl.setStatus("2");
                busDoctorTdl.setUpdateTime(DateUtils.getNowDate());
                UpdateWrapper<BusDoctorTdl> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("type", CodeEnum.NO.getCode());
                updateWrapper.eq("business_id", dto.getId());
                updateWrapper.eq("doctor_id", dto.getDoctorId());
                updateWrapper.orderByDesc("create_time");
                busDoctorTdlMapper.update(busDoctorTdl, updateWrapper.last("limit 1"));
            } else if (CodeEnum.NO.getCode().equals(consultationType)) { // 挂科室问诊订单
                // 补充医生信息，修改订单状态
                BusConsultationOrder consultationOrder = new BusConsultationOrder();
                consultationOrder.setDoctorId(dto.getDoctorId());
                consultationOrder.setDoctorName(dto.getDoctorName());
                consultationOrder.setPhoto(dto.getPhoto());
                consultationOrder.setTitle(dto.getTitle());
                consultationOrder.setStatus(ConsultationOrderStatus.VISITING);
                consultationOrder.setUpdateTime(DateUtils.getNowDate());
                consultationOrder.setCheckTime(DateUtils.getNowDate());
                UpdateWrapper<BusConsultationOrder> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("order_no", dto.getOrderNo());
                consultationOrderMapper.update(consultationOrder, updateWrapper);
                // 补充问诊包信息
                BusConsultationPackage consultationPackage = new BusConsultationPackage();
                consultationPackage.setDoctorId(dto.getDoctorId());
                UpdateWrapper<BusConsultationPackage> updateWrapper1 = new UpdateWrapper<>();
                updateWrapper1.eq("order_id", dto.getId());
                busConsultationPackageMapper.update(consultationPackage, updateWrapper1);

                BusQuickConsultation busQuickConsultation = new BusQuickConsultation();
                busQuickConsultation.setDoctorId(dto.getDoctorId());
                busQuickConsultation.setId(dto.getConsultationId());
                busQuickConsultation.setUpdateTime(DateUtils.getNowDate());
                busQuickConsultationMapper.updateById(busQuickConsultation);
            }
            // 1表示接诊成功
            resultMap.put("status", 1);
        } else if (ConsultationOrderStatus.RETURNED.equals(dto.getStatus())) {
            // 校验该订单医生/医助是否同时操作
            String status = order.getStatus();
            log.info("问诊订单状态=" + status);
            // 医生操作
            if (YesNoEnum.NO.getCode().equals(dto.getRoleType()) && ConsultationOrderStatus.RETURNED.equals(status)) {
                throw new ServiceException("该问诊订单已被医助操作！");
            } else if (YesNoEnum.YES.getCode().equals(dto.getRoleType()) && ConsultationOrderStatus.PAID.equals(status)) {
                throw new ServiceException("该问诊订单已被医生操作！");
            }
            BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
            busDoctorTdl.setStatus("2");
            busDoctorTdl.setUpdateTime(DateUtils.getNowDate());
            UpdateWrapper<BusDoctorTdl> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("type", "0");
            updateWrapper.eq("business_id", dto.getId());
            updateWrapper.eq("doctor_id", dto.getDoctorId());
            updateWrapper.orderByDesc("create_time");
            busDoctorTdlMapper.update(busDoctorTdl, updateWrapper.last("limit 1"));

            log.info("问诊订单号=" + order.getOrderNo());
            BusConsultationOrder busConsultationOrder = new BusConsultationOrder();
            busConsultationOrder.setId(order.getId());
            busConsultationOrder.setOrderNo(order.getOrderNo());
            if (new BigDecimal(order.getAmount()).compareTo(new BigDecimal(0)) == 0) {
                busConsultationOrder.setStatus(ConsultationOrderStatus.CANCEL);
                busConsultationOrder.setCancelTime(DateUtils.getNowDate());
            }
            busConsultationOrder.setPreStatus(ConsultationOrderStatus.RETURNED);
            busConsultationOrder.setReason(dto.getReason());
            busConsultationOrder.setWithdrawalTime(DateUtils.getNowDate());
            int i = consultationOrderMapper.updateStatus(busConsultationOrder);
            log.info("修改退号是否成功=" + i);
        }
        resultMap.put("groupId", returnData);
        return resultMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int applyRefund(BusAfterSale afterSale) {
        // 查询订单状态信息
        QueryWrapper<BusConsultationOrder> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("order_no", afterSale.getOrderNo());
        BusConsultationOrder busConsultationOrder = consultationOrderMapper.selectOne(queryWrapper1);

        if (ObjectUtil.isNotNull(busConsultationOrder)) {
            if (ConsultationOrderStatus.COMPLETE.equals(busConsultationOrder.getStatus()) ||
                    ConsultationOrderStatus.COMPLETE.equals(busConsultationOrder.getVideoStatus())) {
                throw new ServiceException("该订单已结束无法退款!");
            }
            // 所有处方作废后可申请退款
            if (ConsultationOrderStatus.PRESCRIBED.equals(busConsultationOrder.getStatus())
                    || ConsultationOrderStatus.VISITING.equals(busConsultationOrder.getStatus())
                    || ConsultationOrderStatus.PRESCRIBED.equals(busConsultationOrder.getVideoStatus())
                    || ConsultationOrderStatus.VISITING.equals(busConsultationOrder.getVideoStatus())) {
                // 获取处方列表，判断是否全部作废
                List<BusPrescription> prescriptionList = busPrescriptionMapper.selectList(new LambdaQueryWrapper<BusPrescription>()
                        .eq(BusPrescription::getConsultationOrderId, busConsultationOrder.getId()));
                if (!(prescriptionList.stream()
                        .allMatch(p -> PrescriptionStatusEnum.CANCELLATION.getStatus().equals(p.getStatus())))) {
                    throw new ServiceException("处方已开具不可退款，请联系医生作废处方才能退款！");
                }
                if (ConsultationOrderStatus.VISITING.equals(busConsultationOrder.getStatus()) && CollectionUtils.isNotEmpty(prescriptionList)) {
                    busConsultationOrder.setStatus(ConsultationOrderStatus.PRESCRIBED);
                }
                if (ConsultationOrderStatus.VISITING.equals(busConsultationOrder.getVideoStatus()) && CollectionUtils.isNotEmpty(prescriptionList)) {
                    busConsultationOrder.setVideoStatus(ConsultationOrderStatus.PRESCRIBED);
                }
            }
            if ("0".equals(busConsultationOrder.getConsultationType())) { // 挂科室
                if (!ConsultationOrderStatus.PAID.equals(busConsultationOrder.getStatus())) {
                    throw new ServiceException("该科室订单医生已接诊，不能申请退款！");
                } else {
                    // 和前端约定，返回2调退款接口
                    return 2;
                }
            } else { // 挂医生
                if (!(ConsultationOrderStatus.PAID.equals(busConsultationOrder.getStatus())
                        || ConsultationOrderStatus.RESERVED_NOT_ARRIVED.equals(busConsultationOrder.getVideoStatus()))) {
                    if (!(afterSale.getOrderStatus().equals(busConsultationOrder.getStatus()) ||
                            afterSale.getOrderStatus().equals(busConsultationOrder.getVideoStatus()))) {
                        throw new ServiceException("请刷新后重新申请退款！");
                    }
                    BusConsultationOrder consultationOrder = new BusConsultationOrder();
                    consultationOrder.setUpdateTime(DateUtils.getNowDate());
                    // 图文问诊
                    if ("0".equals(busConsultationOrder.getOrderType())) {
                        consultationOrder.setStatus(ConsultationOrderStatus.REFUNDING);
                    } else { // 视频问诊
                        consultationOrder.setVideoStatus(ConsultationOrderStatus.REFUNDING);
                    }
                    QueryWrapper<BusConsultationOrder> queryWrapper2 = new QueryWrapper<>();
                    queryWrapper2.eq("order_no", afterSale.getOrderNo());
                    consultationOrderMapper.update(consultationOrder, queryWrapper2);

                    afterSale.setApplyTime(DateUtils.getNowDate());
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                    afterSale.setNumber(simpleDateFormat.format(new Date()));
                    afterSale.setStatus(AfterSaleStatus.UNDER_REVIEW);
                    QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("order_no", afterSale.getOrderNo());
                    BusAfterSale busAfterSale = afterSaleMapper.selectOne(queryWrapper);
                    if (ObjectUtil.isNotNull(busAfterSale)) {
                        afterSale.setUpdateTime(DateUtils.getNowDate());
                        afterSale.setId(busAfterSale.getId());
                        return afterSaleMapper.updateById(afterSale);
                    } else {
                        afterSale.setCreateTime(DateUtils.getNowDate());
                        return afterSaleMapper.insert(afterSale);
                    }
                } else {
                    return 2;
                }
            }
        } else {
            throw new ServiceException("订单不存在！！！");
        }
    }

    @Override
    public BusConsultationOrder queryConsultationOrder(String outTradeNo) {
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(outTradeNo), "order_no", outTradeNo);
        return consultationOrderMapper.selectOne(queryWrapper);
    }

    @Override
    public Long selectDoctor(BusDoctorDto busDoctorDto) {
        String partnersCode = SecurityUtils.getPartnerscode();
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", busDoctorDto.getHospitalId())
                .eq("patient_id", busDoctorDto.getPatientId())
                .eq(StringUtils.isNotEmpty(partnersCode), "partners_code", partnersCode)
                .and(wrapper -> wrapper.eq("status", ConsultationOrderStatus.COMPLETE).or()
                        .eq("video_status", ConsultationOrderStatus.COMPLETE))
                .isNotNull("doctor_id")
                .orderByDesc("order_time");
        List<BusConsultationOrder> consultationOrderList = consultationOrderMapper.selectList(queryWrapper);
        if (!consultationOrderList.isEmpty()) {
            Long aLong = consultationOrderList.stream().map(BusConsultationOrder::getDoctorId).findFirst().get();
            QueryWrapper<BusDoctorHospital> hospitalQueryWrapper = new QueryWrapper<>();
            hospitalQueryWrapper.eq("hospital_id", busDoctorDto.getHospitalId());
            hospitalQueryWrapper.eq("doctor_id", aLong);
            hospitalQueryWrapper.eq("status", YesNoEnum.YES.getCode());
            hospitalQueryWrapper.eq("online_status", YesNoEnum.YES.getCode());
            BusDoctorHospital busDoctorHospital = busDoctorHospitalMapper.selectOne(hospitalQueryWrapper);
            if (ObjectUtil.isNotNull(busDoctorHospital)) {
                return aLong;
            }
            return null;
        } else {
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateRefundStatus(BusConsultationOrder consultationOrder) {
        log.info("问诊订单信息={}", consultationOrder.getOrderNo());
        BusConsultationOrder busConsultationOrder = new BusConsultationOrder();
        busConsultationOrder.setUpdateTime(DateUtils.getNowDate());
        busConsultationOrder.setRefundTime(DateUtils.getNowDate());
        if (CodeEnum.NO.getCode().equals(consultationOrder.getOrderType())) {
            busConsultationOrder.setStatus(ConsultationOrderStatus.REFUNDED);
        } else {
            busConsultationOrder.setVideoStatus(ConsultationOrderStatus.REFUNDED);
        }
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", consultationOrder.getOrderNo());

        if (Arrays.asList(InvoiceStatusEnum.CAN_ISSUE.getStatus(), InvoiceStatusEnum.TO_ISSUE.getStatus()).contains(consultationOrder.getInvoiceStatus())) {
            busConsultationOrder.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());
        }
        int update = consultationOrderMapper.update(busConsultationOrder, queryWrapper);
        log.debug("订单状态是否修改成功={},对应的订单{},修改的订单参数{}", update, consultationOrder, busConsultationOrder);
        log.info("订单状态是否修改成功={}", update);
        BusAfterSale busAfterSale = new BusAfterSale();
        busAfterSale.setUpdateTime(DateUtils.getNowDate());
        busAfterSale.setStatus(AfterSaleStatus.REFUND_SUCCESS);
        UpdateWrapper<BusAfterSale> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_no", consultationOrder.getOrderNo());
        int result = afterSaleMapper.update(busAfterSale, updateWrapper);
        //退款成功后的同志
        ConsultationRefundSuccessEvent refundSuccessEvent = new ConsultationRefundSuccessEvent();
        refundSuccessEvent.setEventType(BaseConsultationEvent.EventType.REFUND_SUCCESS);
        refundSuccessEvent.setConsultationOrderId(consultationOrder.getId());
        refundSuccessEvent.setOrderNo(consultationOrder.getOrderNo());
        publisher.publishEvent(refundSuccessEvent);
        return result;
    }

    @Override
    public Long getConsultationOrderId(BusPrescriptionDto dto) {
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
        String status = "2,3";
        List<String> statusTypes = Arrays.asList(status.split(","));
        String videoStatus = "2,3";
        List<String> videoTypes = Arrays.asList(videoStatus.split(","));
        queryWrapper
                .eq(BusConsultationOrder::getHospitalId, dto.getHospitalId())
                .eq(BusConsultationOrder::getDepartmentId, dto.getDepartmentId())
                .eq(BusConsultationOrder::getPatientId, dto.getPatientId())
                .eq(BusConsultationOrder::getFamilyId, dto.getFamilyId())
                .and(wrapper -> wrapper.in(BusConsultationOrder::getStatus, statusTypes)
                        .or().in(BusConsultationOrder::getVideoStatus, videoTypes))
                .orderByDesc(BusConsultationOrder::getOrderTime);
        if (dto.getExpertId() != null) {
            queryWrapper.eq(BusConsultationOrder::getDoctorId, dto.getExpertId());
        } else {
            queryWrapper.eq(BusConsultationOrder::getDoctorId, dto.getDoctorId());
        }
        List<BusConsultationOrder> consultationOrders = consultationOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(consultationOrders)) {
            return consultationOrders.stream().findFirst().get().getId();
        } else {
            return 0L;
        }
    }

    @Override
    public int notFollow(QuickConsultationDto dto) {
        return consultationOrderMapper.updateDoctorFollow(dto);
    }

    @Override
    public boolean checkOrderState(BusPrescriptionDto busPrescription) {
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", busPrescription.getHospitalId())
                .eq("department_id", busPrescription.getDepartmentId())
                .eq("patient_id", busPrescription.getPatientId())
                .eq("family_id", busPrescription.getFamilyId())
                .orderByDesc("create_time");
        //如果 expert_id 不为空，则查询该专家的订单
        if (busPrescription.getExpertId() != null) {
            queryWrapper.eq("expert_id", busPrescription.getExpertId());
        } else {
            queryWrapper.eq("doctor_id", busPrescription.getDoctorId());
        }
        BusConsultationOrder order = consultationOrderMapper.selectOne(queryWrapper.last("limit 1"));
        if (ObjectUtil.isNotNull(order) && (ConsultationOrderStatus.REFUNDED.equals(order.getStatus())
                || ConsultationOrderStatus.REFUNDING.equals(order.getStatus()))) {
            return true;
        }
        return false;
    }

    @Override
    public BusConsultationOrder selectOrderStatus(long hospitalId, Long consultationOrderId) {
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", hospitalId)
                .eq("id", consultationOrderId);
        return consultationOrderMapper.selectOne(queryWrapper);
    }

    @Override
    public BusConsultationOrder queryConsultationOrderByOrderNo(String orderNo) {
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(orderNo), "order_no", orderNo);
        return consultationOrderMapper.selectOne(queryWrapper);
    }

    @Override
    public BusConsultationOrder call(ConsultationOrderDto orderDto) {
        log.info("叫号请求参数,o={}", JSON.toJSONString(orderDto));
        Date nowDate = new Date();
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        QueryWrapper<BusDoctorScheduling> doctorSchedulingDto = new QueryWrapper<>();
        doctorSchedulingDto.eq("doctor_id", orderDto.getDoctorId());
        doctorSchedulingDto.eq("hospital_id", orderDto.getHospitalId());
        doctorSchedulingDto.eq("scheduling_date", sd.format(nowDate));
        doctorSchedulingDto.orderByDesc("create_time");
        List<BusDoctorScheduling> doctorScheduling = busDoctorSchedulingMapper.selectList(doctorSchedulingDto);
        if (CollUtil.isEmpty(doctorScheduling)) {
            log.warn("当天未设置排班");
            return null;
        }
        log.info("排班参数请求参数,o={}", JSON.toJSONString(doctorScheduling));
        StringBuffer buffer = new StringBuffer();
        doctorScheduling.forEach(d -> buffer.append(d.getId()).append(","));
        BusDoctorSchedulingTimeDto timeQueryWrapper = new BusDoctorSchedulingTimeDto();
        timeQueryWrapper.setSchedulingIds(buffer.toString());
        timeQueryWrapper.setDateIndex(nowDate);
        List<BusDoctorSchedulingTimeVo> timeVos = busDoctorSchedulingTimeMapper.selectTimeList(timeQueryWrapper);
        if (timeVos.isEmpty()) {
            log.info("查询排班列表为空");
            return null;
        }
        // 判断最新区间是否有挂号
        BusConsultationOrder busConsultationOrder = null;
        Date check = null;
        boolean firstLoop = true;
        boolean secondLoop = true;
        for (BusDoctorSchedulingTimeVo t : timeVos) {
            if (!firstLoop) {
                break;
            }
            Date startTime = DateUtils.pjTime(nowDate, t.getStartTime());
            Date endTime = DateUtils.pjTime(nowDate, t.getEndTime());
            if (DateUtils.isEffectiveDate(nowDate, startTime, endTime)) {
                BusDoctorSchedulingTime convert = OrikaUtils.convert(t, BusDoctorSchedulingTime.class);
                List<BusDoctorSchedulingTime> busDoctorSchedulingTimes = this.splitDate(convert);
                log.info("查询拆分排班数据:time={}", JSON.toJSONString(busDoctorSchedulingTimes));
                for (BusDoctorSchedulingTime s : busDoctorSchedulingTimes) {
                    if (!secondLoop) {
                        break;
                    }
                    Date startTime1 = DateUtils.pjTime(nowDate, s.getStartTime());
                    if (startTime1.compareTo(nowDate) > 0) {
                        check = startTime1;
                        // 查询最新问诊订单信息
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("hospital_id", orderDto.getHospitalId());
                        queryWrapper.eq("order_type", ConsultationEnum.VIDEO.getCode());
                        queryWrapper.eq("doctor_id", orderDto.getDoctorId());
                        queryWrapper.eq("video_status", "11");
                        queryWrapper.eq("is_guo_hao", YesNoEnum.NO.getCode());
                        queryWrapper.ge("start_time", sdf.format(check));
                        queryWrapper.orderByAsc("start_time");
                        log.info("筛选时间条件:date={}", DateUtils.parse_YYYY_MM_DD_HH_MM_SS(check));
                        busConsultationOrder = consultationOrderMapper.selectOne(queryWrapper.last("limit 1"));
                        if (ObjectUtil.isNotNull(busConsultationOrder)) {
                            BusDoctorPatientGroup busDoctorPatientGroup = new BusDoctorPatientGroup();
                            busDoctorPatientGroup.setDoctorId(busConsultationOrder.getDoctorId());
                            busDoctorPatientGroup.setHospitalId(busConsultationOrder.getHospitalId());
                            busDoctorPatientGroup.setPatientId(busConsultationOrder.getPatientId());
                            busDoctorPatientGroup.setFamilyId(busConsultationOrder.getFamilyId());
                            busDoctorPatientGroup.setDepartmentId(busConsultationOrder.getDepartmentId());
                            busDoctorPatientGroup.setType(CodeEnum.NO.getCode());
                            BusDoctorPatientGroup patientGroup =
                                    busDoctorPatientGroupService.selectOne(busDoctorPatientGroup);
                            if (ObjectUtil.isNotNull(patientGroup)) {
                                busConsultationOrder.setGroupId(patientGroup.getId());
                            }
                            secondLoop = false;
                            firstLoop = false;
                        }
                        log.info("查询订单,o={}", busConsultationOrder == null ? "订单为空" :
                                busConsultationOrder.toString());
                    }
                }
            } else {
                BusDoctorSchedulingTime convert = OrikaUtils.convert(t, BusDoctorSchedulingTime.class);
                log.info("查询下一个排班数据:time={}", JSON.toJSONString(convert));
                List<BusDoctorSchedulingTime> busDoctorSchedulingTimes = this.splitDate(convert);
                log.info("查询下一个排班拆分排班数据:time={}", JSON.toJSONString(busDoctorSchedulingTimes));
                BusDoctorSchedulingTime busDoctorSchedulingTime = busDoctorSchedulingTimes.get(0);
                Date startTime1 = DateUtils.pjTime(nowDate, busDoctorSchedulingTime.getStartTime());
                Calendar cale = Calendar.getInstance();
                cale.setTime(startTime1);
                cale.add(Calendar.MINUTE, -15);
                Date time = cale.getTime();
                if (time.compareTo(nowDate) < 0) {
                    check = DateUtils.pjTime(nowDate, time);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("hospital_id", orderDto.getHospitalId());
                    queryWrapper.eq("order_type", ConsultationEnum.VIDEO.getCode());
                    queryWrapper.eq("doctor_id", orderDto.getDoctorId());
                    queryWrapper.eq("video_status", "11");
                    queryWrapper.eq("is_guo_hao", YesNoEnum.NO.getCode());
                    queryWrapper.ge("start_time", sdf.format(check));
                    queryWrapper.orderByAsc("start_time");
                    log.info("筛选时间条件:date={}", DateUtils.parse_YYYY_MM_DD_HH_MM_SS(check));
                    busConsultationOrder = consultationOrderMapper.selectOne(queryWrapper.last("limit 1"));
                    if (ObjectUtil.isNotNull(busConsultationOrder)) {
                        BusDoctorPatientGroup busDoctorPatientGroup = new BusDoctorPatientGroup();
                        busDoctorPatientGroup.setDoctorId(busConsultationOrder.getDoctorId());
                        busDoctorPatientGroup.setHospitalId(busConsultationOrder.getHospitalId());
                        busDoctorPatientGroup.setPatientId(busConsultationOrder.getPatientId());
                        busDoctorPatientGroup.setFamilyId(busConsultationOrder.getFamilyId());
                        busDoctorPatientGroup.setDepartmentId(busConsultationOrder.getDepartmentId());
                        busDoctorPatientGroup.setType(CodeEnum.NO.getCode());
                        BusDoctorPatientGroup patientGroup =
                                busDoctorPatientGroupService.selectOne(busDoctorPatientGroup);
                        if (ObjectUtil.isNotNull(patientGroup)) {
                            busConsultationOrder.setGroupId(patientGroup.getId());
                        }
                        firstLoop = false;
                    }
                }
            }
        }
        if (ObjectUtil.isNull(check)) {
            log.info("查询下一个排班为空");
            return null;
        }
        return busConsultationOrder;
    }

    /**
     * 推送图文消息给医生
     *
     * @param consultationOrder
     */
    @Override
    public void pushTwMsgToDr(BusConsultationOrder consultationOrder) {
        Notification notice = new Notification();
        notice.setTitle("患者挂号信息");
        notice.setChannelId(PageEnum.tw.getCode().toString());
        Transmission transmission = new Transmission();
        JSONObject json = new JSONObject();
        json.put("consultationOrderId", consultationOrder.getId());
        json.put("consultationOrderType", consultationOrder.getConsultationType());
        json.put("hospitalId", consultationOrder.getHospitalId());
        json.put("type", PageEnum.tw.getCode());
        transmission.setPayload(json.toJSONString());
        notice.setTransmission(transmission);
        notice.setBody(consultationOrder.getDoctorName() + "医生" + consultationOrder.getFamilyName() + "患者已在您"
                + consultationOrder.getDepartmentName() + "科室下挂号");
        notice.setChannelId(PageEnum.tw.getCode().toString());
        busAppMessagePushService.pushListByCid(consultationOrder.getDoctorId(), AppRoleEnum.DOCTOR.getCode(), notice);
    }

    private List<BusDoctorSchedulingTime> splitDate(BusDoctorSchedulingTime time) {
        List<BusDoctorSchedulingTime> list = new ArrayList<>();
        Date startTime = time.getStartTime();
        Date endTime = time.getEndTime();
        Calendar dd = Calendar.getInstance();// 定义日期实例
        dd.setTime(startTime);// 设置日期起始时间
        Calendar cale = Calendar.getInstance();
        //结束日期
        Calendar c = Calendar.getInstance();
        c.setTime(endTime);

        BusDoctorSchedulingTime schedulingTime = null;
        BusDoctorSchedulingTime fireschedulingTime = new BusDoctorSchedulingTime();
        fireschedulingTime.setStartTime(startTime);
        dd.setTime(dd.getTime());
        dd.add(Calendar.MINUTE, Integer.valueOf(time.getMinute()));
        fireschedulingTime.setEndTime(dd.getTime());
        fireschedulingTime.setSchedulingId(time.getSchedulingId());
        fireschedulingTime.setCreateTime(DateUtils.getNowDate());
        list.add(fireschedulingTime);
        while (dd.before(c)) {// 判断是否到结束日期
            schedulingTime = new BusDoctorSchedulingTime();
            Date date = dd.getTime();
            cale.setTime(date);
            cale.add(Calendar.MINUTE, Integer.valueOf(time.getMinute()));
            schedulingTime.setStartTime(date);
            schedulingTime.setEndTime(cale.getTime());
            schedulingTime.setSchedulingId(time.getSchedulingId());
            schedulingTime.setCreateTime(DateUtils.getNowDate());
            list.add(schedulingTime);
            dd.add(Calendar.MINUTE, Integer.valueOf(time.getMinute()));// 加上时间区间的分钟
        }
        if (!list.isEmpty()) {
            BusDoctorSchedulingTime doctorSchedulingTime = list.get(list.size() - 1);
            Date s = doctorSchedulingTime.getStartTime();
            Calendar cc = Calendar.getInstance();// 定义日期实例
            cc.setTime(s);
            long l = DateUtils.jsDate(s, time.getEndTime());
            Long aLong = Long.valueOf(time.getMinute());
            if (l < aLong) {
                list.remove(list.size() - 1);
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int videoOrderUpdate(BusConsultationOrder order) {
        log.info("更新订单信息:order={}", JSON.toJSONString(order));
        QueryWrapper<BusConsultationOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("order_no", order.getOrderNo());
        BusConsultationOrder consultationOrder = consultationOrderMapper.selectOne(orderQueryWrapper.last("limit 1"));
        log.info("当前订单信息:order={}", JSON.toJSONString(consultationOrder));
        if (ConsultationOrderStatus.NUMBER_PASSED.equals(order.getVideoStatus())) {
            if (DateUtils.getNowDate().compareTo(consultationOrder.getStartTime()) < 0) {
                throw new ServiceException("订单未到预约时间,无法操作过号");
            }
            if (ConsultationOrderStatus.VISITING.equals(consultationOrder.getVideoStatus()) || ConsultationOrderStatus.PRESCRIBED.equals(consultationOrder.getVideoStatus())) {
                throw new ServiceException("订单正在问诊中,无法操作过号");
            }
            Integer isGuoHao = 2;
            if (isGuoHao.equals(consultationOrder.getIsGuoHao())) {
                throw new ServiceException("叫号中,退号失败");
            }
            String videoStatus = "6,7,8,9";
            if (videoStatus.contains(consultationOrder.getVideoStatus())) {
                throw new ServiceException("退号失败，请重新刷新列表勾选");
            }
            //如果是零元过号场景，则直接取消
            int compareTo = new Date().compareTo(consultationOrder.getStartTime());
            BigDecimal orderAmount = StringUtils.isBlank(consultationOrder.getAmount()) ? BigDecimal.ZERO : new BigDecimal(consultationOrder.getAmount());
            if (orderAmount.compareTo(BigDecimal.ZERO) == 0 && compareTo > 0) {
                order.setCancelTime(DateUtils.getNowDate());
                order.setVideoStatus(ConsultationOrderStatus.CANCEL);
            }

            //发送过号通知事件
            ConsultationGuoHaoEvent guoHaoEvent = new ConsultationGuoHaoEvent();
            guoHaoEvent.setOrderNo(consultationOrder.getOrderNo());
            guoHaoEvent.setConsultationOrderId(consultationOrder.getId());
            guoHaoEvent.setEventType(BaseConsultationEvent.EventType.VIDEO_GUO_HAO);
            Map<String, Object> attachment = new HashMap<>(1);
            attachment.put("status", 5);
            guoHaoEvent.setAttachment(attachment);
            publisher.publishEvent(guoHaoEvent);
        }
        order.setUpdateTime(DateUtils.getNowDate());
        UpdateWrapper<BusConsultationOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_no", order.getOrderNo());
        consultationOrderMapper.update(order, updateWrapper);
        //订单扭转后续操作
        orderHandle(order);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long endOrder(BusConsultationOrder req) {
        LambdaQueryWrapper<BusConsultationOrder> orderQueryWrapper = new LambdaQueryWrapper<>();
        orderQueryWrapper.eq(BusConsultationOrder::getFamilyId, req.getFamilyId());
        orderQueryWrapper.eq(BusConsultationOrder::getDoctorId, req.getDoctorId());
        orderQueryWrapper.eq(BusConsultationOrder::getPatientId, req.getPatientId());
        orderQueryWrapper.eq(BusConsultationOrder::getDepartmentId, req.getDepartmentId());
        orderQueryWrapper.eq(BusConsultationOrder::getHospitalId, req.getHospitalId());
        orderQueryWrapper.and(wq ->
                wq.in(BusConsultationOrder::getStatus, Lists.newArrayList(ConsultationOrderStatus.VISITING, ConsultationOrderStatus.PRESCRIBED))
                        .or().in(BusConsultationOrder::getVideoStatus, Lists.newArrayList(ConsultationOrderStatus.VISITING, ConsultationOrderStatus.PRESCRIBED))
        );
        orderQueryWrapper.orderByDesc(BusConsultationOrder::getCreateTime);
        BusConsultationOrder consultationOrder = consultationOrderMapper.selectOne(orderQueryWrapper.last("limit 1"));
        if (Objects.isNull(consultationOrder)) {
            throw new ServiceException("当前没有问诊中的订单", HttpStatus.BIZ_SPECIAL_STATUS);
        }
        log.info("问诊订单信息={}", consultationOrder);
        if (ConsultationOrderStatus.REFUNDED.equals(consultationOrder.getStatus()) ||
                ConsultationOrderStatus.REFUNDING.equals(consultationOrder.getStatus()) ||
                ConsultationOrderStatus.REFUNDED.equals(consultationOrder.getVideoStatus()) ||
                ConsultationOrderStatus.REFUNDING.equals(consultationOrder.getVideoStatus())) {
            throw new ServiceException("该问诊正在退款中或已退款,无法结束问诊", HttpStatus.BIZ_SPECIAL_STATUS);
        }

        BusDoctorPatientGroup busDoctorPatientGroup = new BusDoctorPatientGroup();
        busDoctorPatientGroup.setDoctorId(req.getDoctorId());
        busDoctorPatientGroup.setFamilyId(req.getFamilyId());
        busDoctorPatientGroup.setPatientId(req.getPatientId());
        busDoctorPatientGroup.setDepartmentId(req.getDepartmentId());
        busDoctorPatientGroup.setHospitalId(req.getHospitalId());
        busDoctorPatientGroup.setType(CodeEnum.NO.getCode());
        BusDoctorPatientGroup patientGroup = busDoctorPatientGroupService.selectOne(busDoctorPatientGroup);
        if (Objects.nonNull(patientGroup)) {
            BusCommunicationMessage queryWrapper = new BusCommunicationMessage();
            queryWrapper.setGroupId(patientGroup.getId() + "");
            queryWrapper.setType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            BusCommunicationMessage busCommunicationMessage = busCommunicationMessageMapper.selectNewest(queryWrapper);
            log.info("最新群组消息：{}", busCommunicationMessage);
            // 新群组无消息
            if (Objects.isNull(busCommunicationMessage)) {
                throw new ServiceException("请回复患者，完成该患者的问诊服务后，再结束问诊", HttpStatus.BIZ_SPECIAL_STATUS);
            }
            // 最新的消息早于订单付款时间，代表新订单无回复
            Assert.notNull(consultationOrder.getPaymentTime(), "该订单患者未付款");
            if (busCommunicationMessage.getCreateTime().before(consultationOrder.getPaymentTime())) {
                throw new ServiceException("请回复患者，完成该患者的问诊服务后，再结束问诊", HttpStatus.BIZ_SPECIAL_STATUS);
            }
            // 最新消息来自患者
            if (busCommunicationMessage.getFrom().contains(TencentyunImConstants.PATIENT_IM_ACCOUNT_PREFIX)) {
                throw new ServiceException("请回复患者，完成该患者的问诊服务后，再结束问诊", HttpStatus.BIZ_SPECIAL_STATUS);
            }
        }

        BusHospitalWechatConfig hospitalWechatConfig =
                busHospitalWechatConfigService.selectByHospitalId(req.getHospitalId(), ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
        if (hospitalWechatConfig == null) {
            throw new ServiceException("该医院公众号配置未设置");
        }

        if (CodeEnum.YES.getCode().equals(consultationOrder.getOrderType())) {
            consultationOrder.setVideoStatus(ConsultationOrderStatus.COMPLETE);
        } else {
            consultationOrder.setStatus(ConsultationOrderStatus.COMPLETE);
        }
        consultationOrder.setCompleteTime(DateUtils.getNowDate());
        consultationOrder.setUpdateTime(DateUtils.getNowDate());
        consultationOrderMapper.updateById(consultationOrder);

        //----------宏捷荣---------------
        // 校验订单完成时间是否是一年内的 && 发票状态
        if (InvoiceStatusEnum.TO_ISSUE.getStatus().equals(consultationOrder.getInvoiceStatus())) {

            BusInvoiceHeader invoiceHeader = invoiceHeaderMapper.selectById(consultationOrder.getInvoiceHeaderId());

            // 发票 - 购买物品信息
            BusProductInvoiceConfig productInvoiceConfig = productInvoiceConfigMapper.selectOne(new LambdaQueryWrapper<BusProductInvoiceConfig>().eq(BusProductInvoiceConfig::getProductType, ProductTypeInvoiceEnum.CONSULT.getCode()));
            // 发票 - 卖方信息
            BusHospitalInvoiceConfig hospitalInvoiceConfig = hospitalInvoiceConfigMapper.selectOne(new LambdaQueryWrapper<BusHospitalInvoiceConfig>().eq(BusHospitalInvoiceConfig::getHospitalId, consultationOrder.getHospitalId()));

            // 税控盘
            if (InvoiceTypeEnum.TAX_CONTROl.getId().equals(hospitalInvoiceConfig.getType())) {
                // 异步调用
                CompletableFuture.runAsync(() -> {
                    try {
                        consultationInvoiceService.taxControlIssueInvoice(invoiceHeader, productInvoiceConfig, hospitalInvoiceConfig, consultationOrder);
                    } catch (IOException | NoSuchAlgorithmException | NoSuchPaddingException |
                             IllegalBlockSizeException | BadPaddingException | InvalidKeyException |
                             UnrecoverableKeyException | CertificateException | KeyStoreException |
                             KeyManagementException e) {
                        log.error("问诊订单请求开具发票异常，订单号 : {}", consultationOrder.getOrderNo());
                    }
                });
            }

            // 全电
            if (InvoiceTypeEnum.ELECTRONIC.getId().equals(hospitalInvoiceConfig.getType())) {
                //TODO: next sprint
            }

        }
        //----------宏捷荣---------------

        // 修改报告跟进状态为结束
        BusPatientFamily family = patientFamilyMapper.selectById(req.getFamilyId());
        if (Objects.nonNull(family)) {
            BusPatientReportPhysician reportPhysician = new BusPatientReportPhysician();
            reportPhysician.setFollowUpStatus(Integer.parseInt(FollowUpStatusEnum.FINISHED.getCode()));
            reportPhysician.setFollowUpTimeEnd(new Date());
            reportPhysicianMapper.update(reportPhysician, new LambdaQueryWrapper<BusPatientReportPhysician>()
                    .eq(BusPatientReportPhysician::getDoctorId, req.getDoctorId())
                    .eq(BusPatientReportPhysician::getIdNumber, family.getIdNumber())
                    .eq(BusPatientReportPhysician::getFollowUpStatus, FollowUpStatusEnum.FOLLOW_UP.getCode()));
        }
        // 新增/修改最后问诊时间
        QueryWrapper<BusHospitalFamily> familyQueryWrapper = new QueryWrapper<>();
        familyQueryWrapper
                .eq("hospital_id", req.getHospitalId())
                .eq("patient_id", req.getPatientId())
                .eq("family_id", req.getFamilyId());
        BusHospitalFamily hospitalFamily = busHospitalFamilyMapper.selectOne(familyQueryWrapper);
        BusHospitalFamily busHospitalFamily = new BusHospitalFamily();
        busHospitalFamily.setLastConsultationTime(DateUtils.getNowDate());
        if (Objects.nonNull(hospitalFamily)) {
            busHospitalFamily.setUpdateTime(DateUtils.getNowDate());
            busHospitalFamily.setId(hospitalFamily.getId());
            busHospitalFamilyMapper.updateById(busHospitalFamily);
        } else {
            busHospitalFamily.setCreateTime(DateUtils.getNowDate());
            busHospitalFamily.setHospitalId(req.getHospitalId());
            busHospitalFamily.setPatientId(req.getPatientId());
            busHospitalFamily.setFamilyId(req.getFamilyId());
            busHospitalFamilyMapper.insert(busHospitalFamily);
        }
        //清空问诊包
        QueryWrapper<BusConsultationPackage> packageQueryWrapper = new QueryWrapper<>();
        packageQueryWrapper.eq("order_id", consultationOrder.getId());
        BusConsultationPackage consultationPackage = busConsultationPackageMapper.selectOne(packageQueryWrapper.last(
                "limit 1"));
        BusConsultationPackage updateConsultationPackage = new BusConsultationPackage();
        updateConsultationPackage.setId(consultationPackage.getId());
        updateConsultationPackage.setUseTimes(consultationPackage.getTotalTimes());
        busConsultationPackageMapper.updateById(consultationPackage);
        //由于存在事务嵌套，这里使用SpringEvent管理进行处理
        ConsultationEndedEvent event = new ConsultationEndedEvent();
        event.setConsultationOrderId(consultationOrder.getId());
        event.setOrderNo(consultationOrder.getOrderNo());
        event.setEventType(BaseConsultationEvent.EventType.CONSULTATION_ENDED);
        publisher.publishEvent(event);
//        this.pushTemplateMsg(req);
        if (Objects.nonNull(patientGroup) && Boolean.TRUE.equals(req.getNeedSendEndImMsg())) {
            this.sendChatMsgToPatient(patientGroup.getId());
        }
        return consultationOrder.getId();
    }

    /**
     * 发送聊天信息给医生
     */
    private void sendChatMsgToPatient(Long groupId) {
        try {
            //查询就诊人群组信息
            BusDoctorPatientGroup patientGroup = busDoctorPatientGroupService.selectGroupInfo(groupId);
            if (Objects.isNull(patientGroup)) {
                log.warn("查询群组:{}为空", groupId);
                return;
            }
            BusDoctorVo busDoctorVo = busDoctorMapper.selectDoctorInfoById(patientGroup.getDoctorId());
            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            int i = (int) ((Math.random() * 9 + 1) * 100000);
            sendMessageRequest.setRandom(i + "");
            sendMessageRequest.setGroupId(patientGroup.getId() + "");
            sendMessageRequest.setFrom_Account(TencentyunImConstants.ADMINISTRATOR);
            List<MsgBody> msgBodyList = new ArrayList<>();
            MsgBody msgBody = new MsgBody();
            msgBody.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            MsgContent msgContent = new MsgContent();
            JSONObject data = new JSONObject();
            data.put("type", CustomMsgConstants.CONSULTATION_END);
            msgContent.setData(data.toJSONString());
            msgBody.setMsgContent(msgContent);
            msgBodyList.add(msgBody);
            sendMessageRequest.setMsgBody(msgBodyList);
            R<Boolean> groupMsg = remoteImService.sendGroupMsg(sendMessageRequest);
            if (Constants.FAIL.equals(groupMsg.getCode())) {
                return;
            }
            //添加消息记录
            JSONObject payload = new JSONObject();
            payload.put("data", data);
            BusCommunicationMessage communicationMessage = new BusCommunicationMessage();
            communicationMessage.setType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            communicationMessage.setPayload(payload.toJSONString());
            communicationMessage.setGroupId(patientGroup.getId() + "");
            communicationMessage.setConversationType("GROUP");
            communicationMessage.setTo(patientGroup.getId() + "");
            communicationMessage.setFrom(TencentyunImConstants.ADMINISTRATOR);
            communicationMessage.setFlow("out");
            communicationMessage.setTime(System.currentTimeMillis());
            communicationMessage.setStatus("success");
            communicationMessage.setPriority("Normal");
            communicationMessage.setNick(busDoctorVo.getFullName());
            communicationMessage.setCreateTime(DateUtils.getNowDate());
            busCommunicationMessageMapper.insertIgnoreNull(communicationMessage);
        } catch (Exception e) {
            log.error("发送消息失败", e);
        }
    }

    @Override
    public boolean check(BusConsultationOrder order) {
        LambdaQueryWrapper<BusConsultationOrder> orderQueryWrapper = new LambdaQueryWrapper<>();
        orderQueryWrapper.eq(BusConsultationOrder::getFamilyId, order.getFamilyId());
        orderQueryWrapper.eq(BusConsultationOrder::getDoctorId, order.getDoctorId());
        orderQueryWrapper.eq(BusConsultationOrder::getPatientId, order.getPatientId());
        orderQueryWrapper.eq(BusConsultationOrder::getDepartmentId, order.getDepartmentId());
        orderQueryWrapper.eq(BusConsultationOrder::getHospitalId, order.getHospitalId());
        orderQueryWrapper.and(wq ->
                wq.in(BusConsultationOrder::getStatus, Lists.newArrayList(ConsultationOrderStatus.VISITING, ConsultationOrderStatus.PRESCRIBED))
                        .or().in(BusConsultationOrder::getVideoStatus, Lists.newArrayList(ConsultationOrderStatus.VISITING, ConsultationOrderStatus.PRESCRIBED))
        );
        Long count = consultationOrderMapper.selectCount(orderQueryWrapper);
        return Objects.nonNull(count) && count > 0;
    }

    @Override
    public BusConsultationOrderVo selectNewest(BusConsultationOrder order) {
        QueryWrapper<BusConsultationOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("family_id", order.getFamilyId());
        orderQueryWrapper.eq("doctor_id", order.getDoctorId());
        orderQueryWrapper.eq("patient_id", order.getPatientId());
        orderQueryWrapper.eq("department_id", order.getDepartmentId());
        orderQueryWrapper.eq("hospital_id", order.getHospitalId());
        orderQueryWrapper.orderByDesc("create_time");
        BusConsultationOrder consultationOrder = consultationOrderMapper.selectOne(orderQueryWrapper.last("limit 1"));
        if (Objects.isNull(consultationOrder)) {
            return null;
        }
        BusConsultationOrderVo busConsultationOrderVo = OrikaUtils.convert(consultationOrder, BusConsultationOrderVo.class);
        if (Objects.nonNull(consultationOrder.getConsultationId())) {
            BusQuickConsultation quickConsultation = busQuickConsultationMapper.selectById(consultationOrder.getConsultationId());
            if (Objects.nonNull(quickConsultation)) {
                busConsultationOrderVo.setFeeSettleType(quickConsultation.getFeeSettleType());
                busConsultationOrderVo.setSpecialDiseaseInfo(quickConsultation.getSpecialDiseaseInfo());
            }
        }
        return busConsultationOrderVo;
    }

    @Override
    public BusConsultationOrder queryOrderNo(String orderNo) {
        QueryWrapper<BusConsultationOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("order_no", orderNo);
        orderQueryWrapper.orderByDesc("create_time");
        BusConsultationOrder consultationOrder = consultationOrderMapper.selectOne(orderQueryWrapper.last("limit 1"));
        if (ObjectUtil.isNotNull(consultationOrder)) {
            BusDoctorPatientGroup busDoctorPatientGroup = new BusDoctorPatientGroup();
            busDoctorPatientGroup.setDoctorId(consultationOrder.getDoctorId());
            busDoctorPatientGroup.setHospitalId(consultationOrder.getHospitalId());
            busDoctorPatientGroup.setPatientId(consultationOrder.getPatientId());
            busDoctorPatientGroup.setFamilyId(consultationOrder.getFamilyId());
            busDoctorPatientGroup.setDepartmentId(consultationOrder.getDepartmentId());
            busDoctorPatientGroup.setType(CodeEnum.NO.getCode());
            BusDoctorPatientGroup group = busDoctorPatientGroupService.selectOne(busDoctorPatientGroup);
            if (ObjectUtil.isNotNull(group)) {
                consultationOrder.setGroupId(group.getId());
            }
        }
        return consultationOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int backNumber(ConsultationOrderDto orderDto) {
        Integer isGuoHao = 2;
        List<Long> orderIds = orderDto.getOrderIds();
        List<BusConsultationOrder> orders = consultationOrderMapper.selectList(new LambdaQueryWrapper<BusConsultationOrder>()
                .in(BusConsultationOrder::getId, orderIds));
        for (BusConsultationOrder o : orders) {
            String videoStatus = "6,7,8,9";
            if (videoStatus.contains(o.getVideoStatus())) {
                throw new ServiceException("退号失败，请重新刷新列表勾选");
            }
            if (isGuoHao.equals(o.getIsGuoHao())) {
                throw new ServiceException("叫号中,退号失败，请重新刷新列表勾选");
            }
        }
        if (CollUtil.isNotEmpty(orderIds)) {
            if (StringUtils.isEmpty(orderDto.getReason())) {
                throw new ServiceException("退号原因不能空");
            }
            orderIds.forEach(o -> {
                BusConsultationOrder order = new BusConsultationOrder();
                order.setId(o);
                order.setOrderType(ConsultationEnum.VIDEO.getCode().toString());
                order.setReason(orderDto.getReason());
                order.setUpdateTime(DateUtils.getNowDate());
                order.setWithdrawalTime(DateUtils.getNowDate());
                //退款逻辑处理 未支付订单 不退款 已支付订单退款
                BusConsultationOrder busConsultationOrder = consultationOrderMapper.selectById(o);
                order.setVideoStatus("0".equals(busConsultationOrder.getVideoStatus()) ? "8" : "9");
                consultationOrderMapper.updateById(order);
                //结束待办任务
                BusConsultationOrder consultationOrder = consultationOrderMapper.selectById(o);
                log.info("批量退号订单信息: o={}", JSON.toJSONString(consultationOrder));
                this.endTdl(consultationOrder.getId(), consultationOrder.getDoctorId());

                //发送模板信息
                ConsultationReturnNumberEvent returnNumberEvent = new ConsultationReturnNumberEvent();
                returnNumberEvent.setEventType(BaseConsultationEvent.EventType.RETURN_NUMBER);
                returnNumberEvent.setConsultationOrderId(busConsultationOrder.getId());
                returnNumberEvent.setOrderNo(busConsultationOrder.getOrderNo());
                Map<String, Object> attachment = new HashMap<>();
                //前端约定，status=5 为跳转患者订单详情
                attachment.put("status", "5");
                returnNumberEvent.setAttachment(attachment);
                //设置原始状态
                returnNumberEvent.setOriginalStatus(busConsultationOrder.getVideoStatus());
                //发送退号事件
                publisher.publishEvent(returnNumberEvent);

                if (!"0".equals(busConsultationOrder.getVideoStatus())) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("outTradeNo", busConsultationOrder.getOrderNo());
                    String amount = busConsultationOrder.getAmount();
                    BigDecimal orderAmount = new BigDecimal(amount);
                    map.put("refundAmount", orderAmount);
                    map.put("orderAmount", orderAmount);
                    map.put("mchType", "2"); // 默认从众爱互联网医院商户退钱
                    map.put("appType", orderDto.getAppType()); // 默认从众爱互联网医院商户退钱
                    map.put("consultationOrder", busConsultationOrder);
                    map.put("automaticRefund", "1"); // 自动退款标识
                    map.put("hospitalId", busConsultationOrder.getHospitalId());
                    if (StringUtils.isNotEmpty(busConsultationOrder.getPartnersCode())) {
                        map.put("partnersCode", busConsultationOrder.getPartnersCode());
                    }
                    JSONObject refund = new JSONObject();
                    try {
                        if (PaysTypeEnum.WECHAT_PAY.getCode().equals(busConsultationOrder.getPayWay())) {
                            // 设置preStatus=1，标识自动退款
                            BusConsultationOrder busConsultationOrder1 = new BusConsultationOrder();
                            busConsultationOrder1.setId(busConsultationOrder.getId());
                            busConsultationOrder1.setOrderNo(busConsultationOrder.getOrderNo());
                            busConsultationOrder1.setPreStatus("6");
                            // 退款
                            consultationOrderMapper.updateStatus(busConsultationOrder1);
                            refund = wxPayService.refund(map);
                        } else {
                            BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
                            busRefundPayDTO.setOrderId(busConsultationOrder.getId());
                            busRefundPayDTO.setOrderType(OrderTypeEnum.CONSULTATION.getCode());
                            refund = iTongLianPayService.tongLianRefund(busRefundPayDTO).getData();
                        }
                    } catch (Exception e) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        log.error(e.getMessage());
                        e.printStackTrace();
                    }
                    log.info("退款是否成功={}", refund.toJSONString());
                }
            });
        }
        return 1;
    }

    @Override
    public String selectPatientInfo(String orderNo) {
        return consultationOrderMapper.selectPatientInfo(orderNo);
    }

    @Override
    public BusConsultationOrder selectBusConsultationOrder(BusConsultationOrder order) {
        LambdaQueryWrapper<BusConsultationOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(ObjectUtil.isNotNull(order.getHospitalId()), BusConsultationOrder::getHospitalId, order.getHospitalId())
                .eq(ObjectUtil.isNotNull(order.getDoctorId()), BusConsultationOrder::getDoctorId, order.getDoctorId())
                .eq(ObjectUtil.isNotNull(order.getDepartmentId()), BusConsultationOrder::getDepartmentId, order.getDepartmentId())
                .eq(ObjectUtil.isNotNull(order.getPatientId()), BusConsultationOrder::getPatientId, order.getPatientId())
                .eq(ObjectUtil.isNotNull(order.getFamilyId()), BusConsultationOrder::getFamilyId, order.getFamilyId())
                .eq(CharSequenceUtil.isNotEmpty(order.getOrderType()), BusConsultationOrder::getOrderType, order.getOrderType())
                .eq(StringUtils.isNotEmpty(order.getConsultationType()), BusConsultationOrder::getConsultationType, order.getConsultationType())
                .and(StringUtils.isNotEmpty(order.getStatus()) || StringUtils.isNotEmpty(order.getVideoStatus()),
                        w -> w.inSql(StringUtils.isNotEmpty(order.getStatus()), BusConsultationOrder::getStatus, order.getStatus())
                                .or().inSql(StringUtils.isNotEmpty(order.getVideoStatus()), BusConsultationOrder::getVideoStatus, order.getVideoStatus()))
                .orderByDesc(BusConsultationOrder::getCreateTime)
                .last(" limit 1");
        return consultationOrderMapper.selectOne(wrapper);
    }

    @Override
    public int checkUpdateFamily(BusConsultationOrder order) {
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("doctor_id", order.getDoctorId());
        queryWrapper.eq("patient_id", order.getPatientId());
        queryWrapper.eq("department_id", order.getDepartmentId());
        queryWrapper.eq("family_id", order.getFamilyId());
        queryWrapper.eq(ObjectUtil.isNotNull(order.getHospitalId()), "hospital_id", order.getHospitalId());
        queryWrapper.orderByDesc("create_time");
        List<BusConsultationOrder> orderList = consultationOrderMapper.selectList(queryWrapper);
        for (BusConsultationOrder o : orderList) {
            if (ConsultationOrderTypeEnum.isVideo(o.getOrderType())) {
                return HttpStatus.BIZ_SPECIAL_STATUS;
            }
            if (ConsultationOrderTypeEnum.isImageText(o.getOrderType()) && ConsultationOrderStatusEnum.COMPLETE.getCode().equals(o.getStatus())) {
                return HttpStatus.BIZ_SPECIAL_STATUS;
            }
        }
        return HttpStatus.SUCCESS;
    }

    @Override
    public BusConsultationOrderVo queryInfo(Long id, Long loginUserid) {
        BusConsultationOrderVo vo = consultationOrderMapper.queryInfo(id, loginUserid);
        // 查询补充信息
        querySupplementInfo(vo);
        // invoiceStatus是否显示，取决于后台系统的配置
        boolean flag = hospitalInvoiceService.getHospitalInvoiceStatus(vo.getHospitalId());
        if (!flag) {
            vo.setInvoiceStatus(null);
            vo.setBusAfterSale(null);
        } else {
            if (InvoiceStatusEnum.ISSUE_SUCCESS.getStatus().equals(vo.getInvoiceStatus())) {
                BusInvoice busInvoice = invoiceMapper.selectOne(new LambdaQueryWrapper<BusInvoice>()
                        .eq(BusInvoice::getOrderNo, vo.getOrderNo())
                        .eq(BusInvoice::getOrderType, InvoiceOrderTypeEnum.CONSULTATION.getType()));
                vo.setDownloadUrl(busInvoice.getDownloadUrl());
                vo.setPicUrl(busInvoice.getPicUrl());
            }
        }
        allPrescriptionCancelled(vo);
        return vo;
    }

    @Override
    public int remove(Long id) {
        BusConsultationOrder consultationOrder = new BusConsultationOrder();
        consultationOrder.setId(id);
        consultationOrder.setDelStatus(CodeEnum.YES.getCode());
        consultationOrder.setUpdateTime(DateUtils.getNowDate());
        return consultationOrderMapper.updateById(consultationOrder);
    }

    @Override
    public BusConsultationOrder selectNewestConsultationOrder(BusPrescriptionDto dto) {
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<BusConsultationOrder>()
                .eq(BusConsultationOrder::getHospitalId, dto.getHospitalId())
                .eq(BusConsultationOrder::getDepartmentId, dto.getDepartmentId())
                .eq(BusConsultationOrder::getPatientId, dto.getPatientId())
                .eq(BusConsultationOrder::getFamilyId, dto.getFamilyId())
                .orderByDesc(BusConsultationOrder::getCreateTime)
                .last(" limit 1");
        //如果为特聘专家 则查询该专家的最新订单
        if (dto.getExpertId() != null) {
            queryWrapper.eq(BusConsultationOrder::getDoctorId, dto.getExpertId());
        } else {
            queryWrapper.eq(BusConsultationOrder::getDoctorId, dto.getDoctorId());
        }
        return consultationOrderMapper.selectOne(queryWrapper);
    }

    @Override
    public List<BusConsultationOrder> queryAccepted() {
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<BusConsultationOrder>()
                .and(wrapper -> wrapper.inSql(BusConsultationOrder::getStatus, "2,3")
                        .or().inSql(BusConsultationOrder::getVideoStatus, "2,3"));
        return consultationOrderMapper.selectList(queryWrapper);
    }

    @Override
    public JSONObject checkNoPayOrder(ConsultationOrderDto orderDto) {
        JSONObject object = new JSONObject();
        object.put("isCheck", false);
        // 极速问诊-待支付订单
        BusQuickConsultation quickConsultation = orderDto.getQuickConsultation();
        if (CodeEnum.NO.getCode().equals(orderDto.getConsultationType())) {
            BusConsultationOrder consultationOrder = consultationOrderMapper.selectOne(new LambdaQueryWrapper<BusConsultationOrder>()
                    .eq(BusConsultationOrder::getConsultationType, orderDto.getConsultationType())
                    .eq(BusConsultationOrder::getHospitalId, quickConsultation.getHospitalId())
                    .eq(BusConsultationOrder::getDepartmentId, quickConsultation.getDepartmentId())
                    .eq(BusConsultationOrder::getPatientId, quickConsultation.getPatientId())
                    .eq(BusConsultationOrder::getFamilyId, quickConsultation.getFamilyId())
                    .eq(BusConsultationOrder::getStatus, "0")
                    .last("limit 1"));
            if (ObjectUtil.isNotNull(consultationOrder)) {
                object.put("orderId", consultationOrder.getId());
                object.put("isCheck", true);
                object.put("copywriting", "您在该科室下还有未付款的订单，请先支付");
            }
        } else {
            // 普通问诊-待支付订单
            BusConsultationOrder consultationOrder1 = consultationOrderMapper.selectOne(new LambdaQueryWrapper<BusConsultationOrder>()
                    .eq(BusConsultationOrder::getHospitalId, quickConsultation.getHospitalId())
                    .eq(BusConsultationOrder::getDoctorId, quickConsultation.getDoctorId())
                    .eq(BusConsultationOrder::getPatientId, quickConsultation.getPatientId())
                    .eq(BusConsultationOrder::getFamilyId, quickConsultation.getFamilyId())
                    .eq(BusConsultationOrder::getDepartmentId, quickConsultation.getDepartmentId())
                    .and(item -> item.eq(BusConsultationOrder::getStatus, "0")
                            .or().inSql(BusConsultationOrder::getVideoStatus, "0,12"))
                    .last("limit 1"));
            if (ObjectUtil.isNotNull(consultationOrder1)) {
                object.put("orderId", consultationOrder1.getId());
                object.put("isCheck", true);
                if (ConsultationOrderStatus.NUMBER_PASSED.equals(consultationOrder1.getVideoStatus())) {
                    object.put("copywriting", "您在该医生下还有已过号的订单，请申请退款或重新预约");
                } else {
                    object.put("copywriting", "您在该医生下还有未付款的订单，请先支付");
                }
            }
        }
        return object;
    }


    /**
     * 查询已过号的视频问诊订单
     *
     * @return
     */
    @Override
    public List<BusConsultationOrder> queryVideoOrder() {
        LambdaQueryWrapper<BusConsultationOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusConsultationOrder::getOrderType, "1");
        lambdaQuery.eq(BusConsultationOrder::getVideoStatus, "12");
        return consultationOrderMapper.selectList(lambdaQuery);
    }

    @Override
    public void saveMiAuthCode(Long consultationOrderId, String miAuthCode, Long doctorId) {
        BusConsultationOrder order = consultationOrderMapper.selectById(consultationOrderId);
        if (Objects.isNull(order)) {
            throw new ServiceException("问诊订单不存在");
        }
        if (!isVisiting(order)) {
            throw new ServiceException("当前问诊单不是问诊中状态");
        }
        BusQuickConsultation quickConsultation = busQuickConsultationMapper.selectById(order.getConsultationId());
        if (Objects.isNull(quickConsultation)) {
            throw new ServiceException("问诊订单关联信息缺失");
        }
        //outpatientRegister(doctorId, order);
        quickConsultation.setMiAuthNo(miAuthCode);
        quickConsultation.setUpdateTime(new Date());
        busQuickConsultationMapper.updateById(quickConsultation);
    }

    @Override
    public BusConsultationOrderDTO getOrderInfo(Long orderId, String orderNo) {
        if (Objects.isNull(orderId) && StringUtils.isBlank(orderNo)) {
            return null;
        }
        BusConsultationOrder order;
        if (Objects.nonNull(orderId)) {
            order = consultationOrderMapper.selectById(orderId);
        } else {
            LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusConsultationOrder::getOrderNo, orderNo);
            queryWrapper.last(" limit 1");
            order = consultationOrderMapper.selectOne(queryWrapper);
        }
        if (Objects.isNull(order)) {
            return null;
        }
        BusConsultationOrderDTO dto = new BusConsultationOrderDTO();
        BeanUtils.copyProperties(order, dto);
        //问诊关联信息
        if (Objects.nonNull(order.getConsultationId())) {
            BusQuickConsultation quickConsultation = busQuickConsultationMapper.selectById(order.getConsultationId());
            if (Objects.nonNull(quickConsultation)) {
                BusQuickConsultationDTO quickConsultationDTO = new BusQuickConsultationDTO();
                BeanUtils.copyProperties(quickConsultation, quickConsultationDTO);
                dto.setBusQuickConsultation(quickConsultationDTO);
            }
        }
        return dto;
    }

    /**
     * 医保门诊挂号
     *
     * @param doctorId 医生id
     * @param order    问诊单信息
     */
    private void outpatientRegister(Long doctorId, BusConsultationOrder order) {
        OutpatientRegisterReqDTO reqDTO = new OutpatientRegisterReqDTO();
        reqDTO.setHospitalId(order.getHospitalId());
        reqDTO.setFamilyName(order.getFamilyName());
        BusPatientFamily patientFamily = patientFamilyMapper.selectById(order.getFamilyId());
        if (Objects.isNull(patientFamily)) {
            throw new ServiceException("问诊单关联的患者信息缺失");
        }
        reqDTO.setFamilyIdCard(patientFamily.getIdNumber());
        reqDTO.setOutpatientRegTime(order.getCreateTime());
        reqDTO.setOutpatientRegNo(order.getOrderNo());
        //门诊挂号医生
        Long outpatientRegDoctorId = Objects.nonNull(order.getDoctorId()) ? order.getDoctorId() : doctorId;
        BusDoctorHospital doctorHospital = busDoctorHospitalService.selectDoctorHospitalInfo(order.getHospitalId(), outpatientRegDoctorId);
        if (doctorHospital == null) {
            throw new ServiceException("处方关联的医生信息不存在");
        }
        if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(doctorHospital.getIsThisCourt())) {
            //需要用院内医生进行挂号
            BusHospitalPreorderDoctor busHospitalPreorderDoctor = busHospitalPreorderDoctorService.selectByHospitalId(order.getHospitalId());
            if (busHospitalPreorderDoctor == null || busHospitalPreorderDoctor.getDoctorId() == null) {
                throw new IllegalArgumentException("医院未设置默认开方医生");
            }
            outpatientRegDoctorId = busHospitalPreorderDoctor.getDoctorId();
        }
        BusDoctor doctor = busDoctorMapper.selectById(outpatientRegDoctorId);
        if (Objects.isNull(doctor)) {
            throw new ServiceException("问诊订单关联的医生信息缺失");
        }
        if (StringUtils.isBlank(doctor.getNationalDoctorCode())) {
            throw new ServiceException("当前问诊医生没有配置医保编码");
        }
        reqDTO.setDoctorMiNo(doctor.getNationalDoctorCode());
        reqDTO.setDoctorName(doctor.getFullName());
        reqDTO.setDepartmentNumber(order.getDepartmentId() + "");
        reqDTO.setDepartmentName(order.getDepartmentName());
        BusDepartment department = dualChannelDigitalRxParamHelper.getDepartment(order.getDepartmentId(), order.getHospitalId(), order.getDepartmentName());
        //标准业务科室
        reqDTO.setDeptCode(department.getDepartmentNumber());
        R<OutpatientRegisterResultDTO> result = reremotePreprocessorService.outpatientRegister(reqDTO);
        if (Objects.isNull(result) || !result.isSuccess()) {
            log.error("问诊订单医保挂号失败：{}", result);
            throw new ServiceException("问诊订单医保挂号失败,请重试");
        }
    }

    /**
     * 判断是否在问诊中
     *
     * @param order 问诊订单
     * @return 是否在问诊中
     */
    private boolean isVisiting(BusConsultationOrder order) {
        return ConsultationOrderStatusEnum.VISITING.getCode().equals(order.getStatus())
                || ConsultationOrderStatusEnum.PRESCRIBED.getCode().equals(order.getStatus())
                || ConsultationOrderStatusEnum.VISITING.getCode().equals(order.getVideoStatus())
                || ConsultationOrderStatusEnum.PRESCRIBED.getCode().equals(order.getVideoStatus());
    }

    /**
     * 订单状态扭转后续操作
     *
     * @param order
     */
    private void orderHandle(BusConsultationOrder order) {
        String videoStatus = order.getVideoStatus();
        QueryWrapper<BusConsultationOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("order_no", order.getOrderNo());
        BusConsultationOrder consultationOrder = consultationOrderMapper.selectOne(orderQueryWrapper.last("limit 1"));
        switch (videoStatus) {
            //问诊中
            case "2":
                //赠送问诊包
                BusConsultationPackage busConsultationPackage = new BusConsultationPackage();
                busConsultationPackage.setOrderId(consultationOrder.getId());
                //默认为三次
                busConsultationPackage.setTotalTimes(3);
                busConsultationPackage.setCreateTime(DateUtils.getNowDate());
                busConsultationPackage.setFamilyId(consultationOrder.getFamilyId());
                busConsultationPackage.setPatientId(consultationOrder.getPatientId());
                busConsultationPackage.setHospitalId(consultationOrder.getHospitalId());
                busConsultationPackage.setDoctorId(consultationOrder.getDoctorId());
                busConsultationPackage.setType(consultationOrder.getOrderType());
                busConsultationPackage.setPayTime(DateUtils.getNowDate());
                busConsultationPackage.setDepartmentId(consultationOrder.getDepartmentId());
                busConsultationPackageMapper.insert(busConsultationPackage);

                //加入消息队列
                Message message = new Message();
                message.setId(consultationOrder.getId());
                message.setHospitalId(consultationOrder.getHospitalId());
                BusConsultationSettings bs = new BusConsultationSettings();
                bs.setHospitalId(consultationOrder.getHospitalId());
                bs = busConsultationSettingsService.selectOne(bs);

                if (bs == null || bs.getImagetextTotalExpire() == null) {
                    message.setFireTime(QueueConstant.QUEUE_CONSULTATION_ORDER_AUTO_COMPLETE_TIME);
                } else {
                    long time = bs.getImagetextTotalExpire() * 60 * 60 * 1000;
                    message.setFireTime(time);
                }
                consultationOrderAutoCompleteProducer.delaySend(message, message.getFireTime());
                break;
            case "6":
            case "12":
                break;
            default:
        }
        //结束待办任务
        this.endTdl(consultationOrder.getId(), consultationOrder.getDoctorId());
    }

    @Override
    public void endTdl(Long orderId, Long doctorId) {
        log.info("orderId={},doctorId={}", orderId, doctorId);
        BusDoctorTdl tdl = new BusDoctorTdl();
        //已办
        tdl.setStatus("2");
        UpdateWrapper<BusDoctorTdl> doctorTdlUpdateWrapper = new UpdateWrapper<>();
        doctorTdlUpdateWrapper.eq("business_id", orderId);
        doctorTdlUpdateWrapper.eq("doctor_id", doctorId);
        doctorTdlUpdateWrapper.eq("type", CodeEnum.YES.getCode());
        busDoctorTdlMapper.update(tdl, doctorTdlUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refund(BusConsultationOrder order) {
        // 退款,默认退到众爱医院
        String amount = order.getAmount();
        BigDecimal orderAmount = new BigDecimal(amount);
        Map<String, Object> map = new HashMap<>();
        map.put("outTradeNo", order.getOrderNo());
        map.put("refundAmount", orderAmount);
        map.put("orderAmount", orderAmount);
        map.put("mchType", "2");
        map.put("consultationOrder", order);
        map.put("automaticRefund", "1"); // 自动退款标识
        map.put("hospitalId", order.getHospitalId());
        if (StringUtils.isNotEmpty(order.getPartnersCode())) {
            map.put("partnersCode", order.getPartnersCode());
        }
        JSONObject result;
        if (PaysTypeEnum.WECHAT_PAY.getCode().equals(order.getPayWay())) {
            result = wxPayService.refund(map);
        } else {
            BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
            busRefundPayDTO.setOrderId(order.getId());
            busRefundPayDTO.setOrderType(OrderTypeEnum.CONSULTATION.getCode());
            result = iTongLianPayService.tongLianRefund(busRefundPayDTO).getData();
        }
        this.endTdl(order.getId(), order.getDoctorId());
        log.info("超时问诊订单退款状态：" + result.get("status") + "退款问诊订单编号：" + order.getId());
        log.info("视频问诊订单退款成功：{}", order);
    }

    /**
     * 发送聊天信息给患者
     */
    private boolean sendChatMsgToPatient(Long groupId, BusConsultationOrder order) {
        try {
            //查询就诊人群组信息
            BusDoctorPatientGroup patientGroup = busDoctorPatientGroupService.selectGroupInfo(groupId);
            if (ObjectUtil.isNull(patientGroup)) {
                log.error("查询群组为空");
                return false;
            }
            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            int i = (int) ((Math.random() * 9 + 1) * 100000);
            sendMessageRequest.setRandom(i + "");
            sendMessageRequest.setGroupId(patientGroup.getId() + "");
            sendMessageRequest.setFrom_Account(TencentyunImConstants.ADMINISTRATOR);
            List<MsgBody> msgBodyList = new ArrayList<>();
            MsgBody msgBody = new MsgBody();
            msgBody.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            MsgContent msgContent = new MsgContent();
            JSONObject data = new JSONObject();
            data.put("type", CustomMsgConstants.VIDEOCHAT_COUNTDOWN);
            data.put("subscribeTime", DateUtils.parse_YYYY_MM_DD_HH_MM_SS(order.getStartTime()));
            msgContent.setData(data.toJSONString());
            msgBody.setMsgContent(msgContent);
            msgBodyList.add(msgBody);
            sendMessageRequest.setMsgBody(msgBodyList);
            R<Boolean> groupMsg = remoteImService.sendGroupMsg(sendMessageRequest);
            if (Constants.FAIL.equals(groupMsg.getCode())) {
                log.error("调用im远程服务失败");
                return false;
            }
            //添加消息记录
            JSONObject payload = new JSONObject();
            payload.put("data", data);
            BusCommunicationMessage communicationMessage = new BusCommunicationMessage();
            communicationMessage.setType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            communicationMessage.setPayload(payload.toJSONString());
            communicationMessage.setGroupId(patientGroup.getId() + "");
            communicationMessage.setConversationType("GROUP");
            communicationMessage.setTo(patientGroup.getId() + "");
            communicationMessage.setFrom(TencentyunImConstants.ADMINISTRATOR);
            communicationMessage.setFlow("out");
            communicationMessage.setTime(System.currentTimeMillis());
            communicationMessage.setStatus("success");
            communicationMessage.setPriority("Normal");
            communicationMessage.setNick("administrator");
            communicationMessage.setCreateTime(DateUtils.getNowDate());
            busCommunicationMessageMapper.insertIgnoreNull(communicationMessage);
            return true;
        } catch (Exception e) {
            log.warn("发送im消息异常：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 无线下HIS就诊记录是否允许问诊
     *
     * @param orderDto 订单信息
     */
    @Override
    public void checkHasMedicalRecord(ConsultationOrderDto orderDto) {
        // 获取医院后台HIS配置
        R<String> configValue = remoteHospitalSettingApi.getSettingValue(HOSPITAL_HIS_CONFIG, orderDto.getHospitalId() == null ? SecurityUtils.getHospitalId() : orderDto.getHospitalId());
        HisClientConfig hisClientConfig = JSON.parseObject(configValue.getData(), HisClientConfig.class);
        if (!hisClientConfig.isConnected()) {
            return;
        }
        if (hisClientConfig.isVisitHisRecord() && !hisClientConfig.isWithoutHisRecord()) {
            if (StringUtils.isEmpty(orderDto.getMedicalRecord())) {
                throw new ServiceException("请线下就诊后，再进行线上复诊！");
            }
        }
    }

    /**
     * 发送聊天信息给医生
     */
    private boolean sendChatMsgToDoctor(Long groupId, BusConsultationOrder order) {
        try {
            //查询群组信息
            BusDoctorPatientGroup patientGroup = busDoctorPatientGroupService.selectGroupInfo(groupId);
            if (ObjectUtil.isNull(patientGroup)) {
                log.error("查询群组信息为空");
                return false;
            }
            BusPatientFamilyVo familyVo = busPatientFamilyService.selectPatientFamilyById(patientGroup.getFamilyId(), patientGroup.getHospitalId());
            if (ObjectUtil.isNull(familyVo)) {
                log.error("查询就诊人信息为空");
                return false;
            }
            Long consultationId = order.getConsultationId();
            BusQuickConsultation quickConsultation = busQuickConsultationMapper.selectById(consultationId);
            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            long timeMillis = System.currentTimeMillis();
            sendMessageRequest.setRandom(timeMillis + "");
            sendMessageRequest.setGroupId(patientGroup.getId() + "");
            sendMessageRequest.setFrom_Account(TencentyunImConstants.PATIENT_IM_ACCOUNT + quickConsultation.getPatientId());
            List<MsgBody> msgBodyList = new ArrayList<>();
            MsgBody msgBody = new MsgBody();
            msgBody.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            MsgContent msgContent = new MsgContent();
            JSONObject data = new JSONObject();
            data.put("type", CustomMsgConstants.PATIENT_QUICKCONSULTATION);
            data.put("symptomDescription", quickConsultation.getSymptomDescription());
            data.put("picture", quickConsultation.getPicture());
            data.put("name", familyVo.getName());
            data.put("sex", familyVo.getSex());

            data.put("age", AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd", familyVo.getDateOfBirth())));
            data.put("height", quickConsultation.getHeight());
            data.put("weight", quickConsultation.getWeight());
            data.put("allergicHistory", quickConsultation.getAllergicHistory());
            data.put("diagnosisArchives", familyVo.getDiagnosisArchives());
            if ("1".equals(quickConsultation.getAllergicHistory())) {
                data.put("allergicDrugs", quickConsultation.getAllergicDrugs());
            } else {
                data.put("allergicDrugs", "无");
            }
            data.put("previousIllness", quickConsultation.getPreviousIllness());
            if ("1".equals(quickConsultation.getPreviousIllness())) {
                data.put("anamneses", quickConsultation.getAnamneses());
            } else {
                data.put("anamneses", "无");
            }
            data.put("presentIllness", quickConsultation.getPresentIllness());
            if ("1".equals(quickConsultation.getPresentIllness())) {
                data.put("illness", quickConsultation.getIllness());
            } else {
                data.put("illness", "无");
            }
            msgContent.setData(data.toJSONString());
            msgBody.setMsgContent(msgContent);
            msgBodyList.add(msgBody);
            sendMessageRequest.setMsgBody(msgBodyList);
            R<Boolean> groupMsg = remoteImService.sendGroupMsg(sendMessageRequest);
            if (Constants.FAIL.equals(groupMsg.getCode())) {
                log.error("远程调用发送普通消息失败！");
                return false;
            }
            //添加消息记录
            JSONObject payload = new JSONObject();
            payload.put("data", data);
            BusCommunicationMessage communicationMessage = new BusCommunicationMessage();
            communicationMessage.setType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            communicationMessage.setPayload(payload.toJSONString());
            communicationMessage.setGroupId(patientGroup.getId() + "");
            communicationMessage.setConversationType("GROUP");
            communicationMessage.setTo(patientGroup.getId() + "");
            communicationMessage.setFrom(TencentyunImConstants.PATIENT_IM_ACCOUNT + quickConsultation.getPatientId());
            communicationMessage.setFlow("out");
            communicationMessage.setTime(timeMillis);
            communicationMessage.setStatus("success");
            communicationMessage.setPriority("Normal");
            communicationMessage.setNick(familyVo.getName());
            communicationMessage.setCreateTime(DateUtils.getNowDate());
            busCommunicationMessageMapper.insertIgnoreNull(communicationMessage);
            // 添加医生超时未回复队列
            noReplyOverdueOneHourProducer.delaySend(communicationMessage.getId());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private void querySupplementInfo(BusConsultationOrderVo vo) {
        // 查看是否存在售后信息

        LambdaQueryWrapper<BusAfterSale> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(BusAfterSale::getOrderNo, vo.getOrderNo());
        BusAfterSale busAfterSale = afterSaleMapper.selectOne(queryWrapper1);
        if (Objects.nonNull(busAfterSale)) {
            vo.setExitAfterSale(1);
            if (AfterSaleStatusEnum.DISAPPROVAL.getCode().equals(busAfterSale.getStatus())) {
                vo.setRefundReason(busAfterSale.getReason());
            }
            vo.setBusAfterSale(busAfterSale);
        }
        // 校验患者是否上传过往病例
        if (ConsultationOrderStatus.PRESCRIBED.equals(vo.getStatus()) || ConsultationOrderStatus.COMPLETE.equals(vo.getStatus()) ||
                ConsultationOrderStatus.PRESCRIBED.equals(vo.getVideoStatus()) || ConsultationOrderStatus.COMPLETE.equals(vo.getVideoStatus())) {
            LambdaQueryWrapper<BusHospitalFamily> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .eq(BusHospitalFamily::getHospitalId, vo.getHospitalId())
                    .eq(BusHospitalFamily::getPatientId, vo.getPatientId())
                    .eq(BusHospitalFamily::getFamilyId, vo.getFamilyId());
            BusHospitalFamily busHospitalFamily = busHospitalFamilyMapper.selectOne(queryWrapper.last(" limit 1"));
            if (Objects.nonNull(busHospitalFamily) && StringUtils.isNotEmpty(busHospitalFamily.getDiagnosisArchives())) {
                vo.setExitArchives(1);
            }
        }
    }
}

package com.puree.hospital.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.app.domain.BusNegotiationRecord;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 售后详情VO
 *
 * <AUTHOR>
 * @date 2023/3/10 18:21
 */
@Data
public class BusAfterSaleDetailVO {
    /**
     * 售后原因
     */
    private String afterSalesCause;
    /**
     * 售后编号
     */
    private String afterSalesNumber;
    /**
     * 售后状态，
     * 0-买家申请售后 1-不同意售后，待买家处理
     * 2-已同意售后，待买家退货 3-买家已退货，待确认收货
     * 4-已拒绝收货，待买家处理 5-已确认收货，待重新发货
     * 6-已重新发货，待买家收货 7-售后结束 8-售后关闭
     */
    private String afterSalesStatus;
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;
    /**
     * 买家
     */
    private String buyName;
    /**
     * 倒计时
     */
    private Long countDownTime;
    /**
     * 买家电话
     */
    private String buyPhone;
    /**
     * 配送方式 0-自提 1-快递
     */
    private Integer deliveryType;
    /**
     * 退款说明
     */
    private String description;
    /**
     * 售后凭证
     */
    private String afterSalesVoucher;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;
    /**
     * 收货地址
     */
    private String receiveAddress;
    /**
     * 收货人
     */
    private String receiveName;
    /**
     * 收货人电话
     */
    private String receivePhone;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 售后方式，0-仅退款 1-退货退款 2-退换货
     */
    private String refundType;
    /**
     * 换货订单编号
     */
    private String replaceOrderNo;
    /**
     * 售后商品列表
     */
    private List<BusAfterSaleGood> saleItemList;
    /**
     * 协商记录
     */
    private List<BusNegotiationRecord> negotiationList;
    /**
     * 退货物流公司名称
     */
    private String logisticsCompany;
    /**
     * 退货物流单号
     */
    private String deliveryNo;
    /**
     * 退货公司编码
     */
    private String expressCode;
    /**
     * 退货地址
     */
    private String returnAddress;
    /**
     * 退货联系人
     */
    private String returnContact;
    /**
     * 退货联系人电话
     */
    private String returnContactPhone;
    /**
     * 退货填写单号时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnTime;
    /**
     * 售后关闭时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date closeTime;
    /**
     * 售后单可退金额
     */
    private BigDecimal afterSaleAmount;

}


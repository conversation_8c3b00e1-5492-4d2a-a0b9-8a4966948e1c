package com.puree.hospital.app.hispay.upload;

import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * His 问诊订单信息上传
 * <AUTHOR>
 * @date 2025/1/9 14:54
 */
@Slf4j
@Component(OrderTypeConstant.CONSULATION_ORDER + IOrderUploadHandler.SUFFIX)
public class ConsulationOrderUploadHandler implements IOrderUploadHandler {

    /**
     * 问诊订单信息上传
     * @param hospitalId 医院id
     * @param orderNo 药品订单号
     */
    @Override
    public HisPayUploadDTO uploadOrder(Long hospitalId, String orderNo) {
        //TODO 目前开发区医院挂号费为0元，暂不支持上传
        return null;
    }

}


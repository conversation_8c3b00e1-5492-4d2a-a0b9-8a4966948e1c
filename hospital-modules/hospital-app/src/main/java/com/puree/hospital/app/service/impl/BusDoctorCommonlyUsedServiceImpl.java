package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusDoctorCommonlyUsed;
import com.puree.hospital.app.domain.dto.RxDrugQueryDTO;
import com.puree.hospital.app.domain.vo.DoctorOfficinaDrugsVo;
import com.puree.hospital.app.helper.SysDictDataHelper;
import com.puree.hospital.app.mapper.BusDoctorCommonlyUsedMapper;
import com.puree.hospital.app.mapper.BusDoctorOfficinaMapper;
import com.puree.hospital.app.service.IBusDoctorCommonlyUsedService;
import com.puree.hospital.common.core.enums.DirectoryTypeEnum;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.system.api.model.constant.SysDictTypeConstant;
import lombok.RequiredArgsConstructor;
import com.puree.hospital.system.api.model.SysDictData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 医生常用药管理
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorCommonlyUsedServiceImpl implements IBusDoctorCommonlyUsedService {

    private final BusDoctorCommonlyUsedMapper doctorCommonlyUsedMapper;
    private final BusDoctorOfficinaMapper doctorOfficinaMapper;
    private final SysDictDataHelper sysDictDataHelper;

    @Override
    public int addCommonlyUsedDrugs(BusDoctorCommonlyUsed doctorCommonlyUsed) {
        doctorCommonlyUsed.setCreateTime(DateUtils.getNowDate());
        //如果将药品将源为空，则设置为-1
        if (Objects.isNull(doctorCommonlyUsed.getDrugsSourceId())) {
            doctorCommonlyUsed.setDrugsSourceId(-1L);
        }
        return doctorCommonlyUsedMapper.insert(doctorCommonlyUsed);
    }

    @Override
    public List<DoctorOfficinaDrugsVo> selectCommonlyUsedDrugsList(RxDrugQueryDTO rxDrugQueryDTO) {
        if (Objects.nonNull(rxDrugQueryDTO.getClassifyId())) {
            List<Long> classifyIdList = doctorOfficinaMapper.selectDrugsClassifyId(rxDrugQueryDTO.getClassifyId());
            if (CollectionUtil.isEmpty(classifyIdList)) {
                classifyIdList.add(rxDrugQueryDTO.getClassifyId());
            }
            rxDrugQueryDTO.setClassifyIdList(classifyIdList);
        }
        if (StringUtils.isBlank(rxDrugQueryDTO.getDirectoryType())) {
            rxDrugQueryDTO.setDirectoryType(DirectoryTypeEnum.MM_INNER.getType());
        }
        Map<Long, SysDictData> dictDataMap = sysDictDataHelper.getDictDataMap(SysDictTypeConstant.DRUGS_DOSAGE_FORM);

        List<DoctorOfficinaDrugsVo> list = doctorCommonlyUsedMapper.selectCommonlyUsedDrugsList(rxDrugQueryDTO);
        list.forEach(d -> {
            SysDictData sysDictData = dictDataMap.getOrDefault(d.getDrugsDosageForm(), new SysDictData());
            d.setDrugsDosageFormName(sysDictData.getDictLabel());
        });
        if (CollectionUtil.isEmpty(list)) {
            return list;
        }
        Map<Long, SysDictData> packageUnitMap = sysDictDataHelper.getDictDataMap(SysDictTypeConstant.DRUGS_PACKAGING_UNIT);
        Iterator<DoctorOfficinaDrugsVo> it = list.iterator();
        while (it.hasNext()) {
            DoctorOfficinaDrugsVo vo = it.next();
            //元素为空则移除
            if (Objects.isNull(vo) || Objects.isNull(vo.getDrugsId())) {
                it.remove();
                continue;
            }
            //设置药品包装单位
            if (Objects.nonNull(vo.getDrugsPackagingUnit())) {
                SysDictData sysDictData = packageUnitMap.get(vo.getDrugsPackagingUnit());
                if (Objects.nonNull(sysDictData)) {
                    vo.setDrugsPackagingUnitName(sysDictData.getDictLabel());
                }
            }
            if (StringUtils.isNotBlank(vo.getMainImg()) && StringUtils.isNotBlank(vo.getDrugsImgDetail())) {
                vo.setDrugsImg(vo.getMainImg() + "," + vo.getDrugsImgDetail());
            } else if (StringUtils.isNotEmpty(vo.getMainImg())) {
                vo.setDrugsImg(vo.getMainImg());
            } else if (StringUtils.isNotEmpty(vo.getDrugsImgDetail())) {
                vo.setDrugsImg(vo.getDrugsImgDetail());
            }
            //设置药品的祖级分类ID
            if (StringUtils.isNotEmpty(vo.getAncestors())) {
                if ("0".equals(vo.getAncestors())) {
                    vo.setAncestorsId(vo.getClassifyId());
                } else {
                    String[] split = vo.getAncestors().split(",");
                    vo.setAncestorsId(Long.valueOf(split[1]));
                }
            }
            //兼容处理
            if (Objects.isNull(vo.getStock())) {
                vo.setStock(0);
            }
        }
        // 排序
        CollectionUtil.sort(list, Comparator.comparing(DoctorOfficinaDrugsVo::getStock).reversed());
        return list;
    }

    @Override
    public int cancelCommonlyUsedDrugs(BusDoctorCommonlyUsed doctorCommonlyUse) {
        LambdaQueryWrapper<BusDoctorCommonlyUsed> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(BusDoctorCommonlyUsed::getHospitalId, doctorCommonlyUse.getHospitalId())
                .eq(BusDoctorCommonlyUsed::getDoctorId, doctorCommonlyUse.getDoctorId())
                .eq(BusDoctorCommonlyUsed::getDrugsId, doctorCommonlyUse.getDrugsId())
                //增加数据源判断
                .eq(Objects.nonNull(doctorCommonlyUse.getDrugsSourceId()), BusDoctorCommonlyUsed::getDrugsSourceId, doctorCommonlyUse.getDrugsSourceId());
        return doctorCommonlyUsedMapper.delete(queryWrapper);
    }

    @Override
    public List<Long> selectListDrugsId(Long hospitalId, Long doctorId, String directoryType) {
        return doctorCommonlyUsedMapper.selectListDrugsId(hospitalId, doctorId, directoryType);
    }
}

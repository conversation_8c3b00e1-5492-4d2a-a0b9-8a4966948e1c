package com.puree.hospital.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.app.domain.BusOrderAfterSales;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单售后信息表 Mapper
 * <AUTHOR>
 * @since 2023-03-06
 */
public interface BusOrderAfterSalesMapper extends BaseMapper<BusOrderAfterSales> {
    /**
     * 批量新增售后单
     * @param orderAfterSalesList
     * @return
     */
    int batchInsert(@Param("orderAfterSalesList") List<BusOrderAfterSales> orderAfterSalesList);
    /**
     *  根据售后订单ID更新售后单状态为退款成功
     * @param orderAfterSaleIds - 售后订单IDs
     */
    void updateAfterSaleStatusRefundSuccessByIds(@Param("ids") List<Long> orderAfterSaleIds);

    /**
     * 根据订单号查询售后单
     * @param orderNo - 订单号
     * @return  - 售后单列表
     */
    List<BusOrderAfterSales> selectAfterSalesListByOrderNo(String orderNo);
}

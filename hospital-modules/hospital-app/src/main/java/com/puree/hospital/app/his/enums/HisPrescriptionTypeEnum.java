package com.puree.hospital.app.his.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 私有化部署 his处方类型
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Getter
@SuppressWarnings("unused")
public enum HisPrescriptionTypeEnum {
    WESTERN_MEDICINE(1, "西药"),
    PCZ_MEDICINE(2, "中成药"),
    CHINESE_MEDICINE(3, "中草药"),
    PHYSICAL_EXAMINATION(4, "检查"),
    INSPECT_REPORT(5, "检验"),
    TREAT_REPORT(6, "治疗");

    private Integer code;
    private String name;

    HisPrescriptionTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(HisPrescriptionTypeEnum.values())
                .filter(value -> value.getCode().equals(code))
                .map(HisPrescriptionTypeEnum::getName)
                .findFirst()
                .orElse(null);
    }

    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        return Arrays.stream(HisPrescriptionTypeEnum.values())
                .filter(value -> value.getName().equals(name))
                .map(HisPrescriptionTypeEnum::getCode)
                .findFirst()
                .orElse(null);
    }

}

package com.puree.hospital.app.login.processor;

import cn.hutool.core.util.PhoneUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.domain.BusPartners;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPatientHospital;
import com.puree.hospital.app.domain.BusPatientPartners;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.PatientLoginDTO;
import com.puree.hospital.app.domain.model.UniAppUserinfo;
import com.puree.hospital.app.domain.vo.LoginResultVO;
import com.puree.hospital.app.helper.ChannelPartnerHelper;
import com.puree.hospital.app.infrastructure.wechat.AppletInterface;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPatientPartnersMapper;
import com.puree.hospital.app.queue.producer.event.user.RegisterPatientEventProducer;
import com.puree.hospital.app.service.IBusPartnersService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.service.IBusPatientPartnersService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.business.api.event.RegisterPatientEvent;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.core.constant.LoginConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.LoginExceptionEnums;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.CheckedException;
import com.puree.hospital.common.core.exception.LoginException;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.IdUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.http.HttpUtil;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.tool.api.RemoteBaseWxService;
import com.puree.hospital.tool.api.RemoteSmsNotificationService;
import com.puree.hospital.tool.api.model.dto.WxAccessTokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 患者登录抽象处理器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/12 17:02
 */
@Slf4j
public abstract class AbstractPatientLoginProcessor<T extends PatientLoginDTO> implements IPatientLoginProcessor<T> {

    @Resource
    protected IBusPatientService busPatientService;

    @Resource
    private RemoteImService remoteImService;

    @Resource
    private BusPatientFamilyMapper busPatientFamilyMapper;

    @Resource
    protected IBusPatientHospitalService busPatientHospitalService;

    @Resource
    protected IBusPatientPartnersService busPatientPartnersService;

    @Resource
    private IBusPartnersService busPartnersService;

    @Resource
    protected BusPatientPartnersMapper busPatientPartnersMapper;

    @Resource
    protected RemoteBaseWxService remoteBaseWxService;

    @Resource
    private RemoteSmsNotificationService remoteSmsNotificationService;

    @Resource
    private ChannelPartnerHelper channelPartnerHelper;

    @Resource
    @Lazy
    private RegisterPatientEventProducer registerPatientEventProducer;

    @Override
    public LoginResultVO login(T loginDTO) {
        //1.校验短信验证码
        //短信验证码登录
        if (PatientLoginDTO.LoginMethodEnum.PHONE_NUMBER.equals(loginDTO.getLoginMethod())) {
            check(loginDTO);
        }
        //2.获取小程序登录信息
        UniAppUserinfo uniAppUserinfo = getUnitAppUserinfo(loginDTO);
        //3.通过手机号码查询患者信息
        // 微信授权登录
        if (PatientLoginDTO.LoginMethodEnum.AUTH.equals(loginDTO.getLoginMethod())) {
            // 微信小程序授权登录需要获取手机号
            loginDTO.setPhoneNumbers(getPhoneNumber(loginDTO.getHospitalId(), loginDTO.getPhoneNumberCode()));
        }
        //跳过微信授权登录 直接通过微信jsCode 进行登录
        BusPatient busPatient;
        if (PatientLoginDTO.LoginMethodEnum.QUICK_LOGIN.equals(loginDTO.getLoginMethod())) {
            busPatient = getBusPatient(uniAppUserinfo, loginDTO.getPartnersCode());
        } else {
            busPatient = busPatientService.selectPatientByPhone(loginDTO.getPhoneNumbers());
        }

        log.info("患者信息={}", JSON.toJSONString(busPatient));
        //合作机构代码
        String partnersCode = loginDTO.getPartnersCode();
        //4.如果患者不存在，则注册患者信息
        if (Objects.isNull(busPatient)) {
            busPatient = register(loginDTO, partnersCode);
            RegisterPatientEvent registerPatientEvent = new RegisterPatientEvent();
            registerPatientEvent.setPhoneNumber(busPatient.getPhoneNumber());
            registerPatientEvent.setEventType(Collections.singletonList(RegisterPatientEvent.EventType.UPDATE_CHANNEL));
            registerPatientEvent.setHospitalId(loginDTO.getHospitalId());
            registerPatientEventProducer.send(registerPatientEvent);
        }
        //5.患者于医院或合作机构建立绑定关系
        bind(loginDTO, partnersCode, busPatient, uniAppUserinfo);
        //7.写入登录信息
        LoginResultVO loginResultVO = afterLogin(loginDTO, busPatient,uniAppUserinfo);
        return preReturn(loginResultVO, uniAppUserinfo);
    }

    private BusPatient getBusPatient(UniAppUserinfo uniAppUserinfo, String partnersCode) {
        Long patientId;
        if (StringUtils.isNotEmpty(partnersCode)) {
            BusPatientPartners busPatientPartners = busPatientPartnersMapper.queryUnitAppOpenid(uniAppUserinfo.getOpenid());
            if (busPatientPartners == null) {
                throw new LoginException(LoginExceptionEnums.UNBOUND.name());
            }
            patientId = busPatientPartners.getPatientId();
        } else {
            BusPatientHospital busPatientHospital = busPatientHospitalService.queryPatientHospitalByUnitAppOpenid(uniAppUserinfo.getOpenid());
            if (busPatientHospital == null) {
                throw new LoginException(LoginExceptionEnums.UNBOUND.name());
            }
            patientId = busPatientHospital.getPatientId();
        }
        return busPatientService.selectPatientInfo(patientId);
    }

    private String getPhoneNumber(Long hospitalId, String phoneNumberCode) {
        String accessToken = getAccessToken(hospitalId, ClientTypeEnum.WX_UNI_APP);
        // 获取手机号
        String url = AppletInterface.PHONENUMBER_URL.replace("accessToken", accessToken);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", phoneNumberCode);
        JSONObject uniAppUserinfoStr = HttpUtil.doPost(url,jsonObject.toJSONString());
        log.info("获取手机号结果={}", uniAppUserinfoStr);
        UniAppUserinfo uniAppUserinfo = JSONObject.toJavaObject(uniAppUserinfoStr, UniAppUserinfo.class);
        if (YesNoEnum.NO.getCode().equals(uniAppUserinfo.getErrorCode())) {
            JSONObject phoneInfo = uniAppUserinfo.getPhoneInfo();
            return phoneInfo.get("phoneNumber").toString();
        } else {
            throw new LoginException(uniAppUserinfo.getErrorMessage());
        }
    }

    public String getAccessToken(Long hospitalId, ClientTypeEnum clientType) {
        WxAccessTokenDTO wxAccessTokenDTO = new WxAccessTokenDTO();
        wxAccessTokenDTO.setHospitalId(hospitalId);
        wxAccessTokenDTO.setClientType(clientType);
        R<String> result = remoteBaseWxService.getAccessToken(wxAccessTokenDTO, SecurityConstants.INNER);
        if (!result.isSuccess()) {
            throw new LoginException(result.getMsg());
        }
        return result.getData();
    }

    /**
     * 返回前补全参数
     *
     * @param loginResultVO 登录结果
     * @param uniAppUserinfo 小程序用户信息
     * @return {@link LoginResultVO }
     */
    protected abstract LoginResultVO preReturn(LoginResultVO loginResultVO, UniAppUserinfo uniAppUserinfo);

    /**
     * 校验信息
     *
     * @param loginDTO 登录参数
     */
    private void check(T loginDTO) {
        if (StringUtils.isNotBlank(loginDTO.getPhoneNumbers()) && !PhoneUtil.isMobile(loginDTO.getPhoneNumbers())) {
            throw new CheckedException("电话号码格式不合法");
        }
        //校验其他参数
        checkOtherParams(loginDTO);
        //校验短信验证码
        AjaxResult result = remoteSmsNotificationService.checkVerifyCode(loginDTO.getPhoneNumbers(), loginDTO.getCode());
        if (!result.isSuccess()) {
            throw new ServiceException(LoginConstants.COMMON_MSG);
        }
    }

    /**
     * 获取小程序登录信息
     *
     * @param loginDTO 登录参数
     * @return 小程序响应信息
     */
    protected UniAppUserinfo getUnitAppUserinfo(T loginDTO) {
        return null;
    }

    /**
     * 校验参数
     *
     * @param loginDTO 登录参数
     */
    protected abstract void checkOtherParams(T loginDTO);

    /**
     * 注册用户
     *
     * @param loginDTO 登录参数
     */
    @SuppressWarnings("DuplicatedCode")
    private BusPatient register(T loginDTO, String partnersCode) {
        Long hospitalId = loginDTO.getHospitalId();
        //注册患者信息
        BusPatient patient = new BusPatient();
        patient.setPhoneNumber(loginDTO.getPhoneNumbers());
        patient.setSource(loginDTO.getSource());
        patient.setDoctorId(loginDTO.getDoctorId());
        if (StringUtils.isNotEmpty(partnersCode)) {
            patient.setPartnersCode(partnersCode);
        }
        busPatientService.save(patient);
        // 默认为该账号添加一个就诊人
        insertPatientFamily(hospitalId, patient, loginDTO.getAgentId());
        R<Boolean> returnData = remoteImService.accountCheck(TencentyunImConstants.PATIENT_IM_ACCOUNT + patient.getId());
        if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) returnData.getData()) {
            R<Boolean> r = remoteImService.accountImport(TencentyunImConstants.PATIENT_IM_ACCOUNT + patient.getId(),
                    "", null);
            if (Constants.FAIL.equals(r.getCode())) {
                throw new CheckedException("账号导入患者信息失败");
            }
        }
        return patient;
    }

    /**
     * 当当前患者添加就诊人
     *
     * @param hospitalId 医院id
     * @param patient    患者新
     * @param agentId    合作渠道id
     */
    private void insertPatientFamily(Long hospitalId, BusPatient patient, Long agentId) {
        BusPatientFamily patientFamily = new BusPatientFamily();
        patientFamily.setHospitalId(hospitalId);
        patientFamily.setName(patient.getPhoneNumber());
        patientFamily.setCellPhoneNumber(patient.getPhoneNumber());
        patientFamily.setPatientId(patient.getId());
        patientFamily.setCreateTime(DateUtils.getNowDate());
        if (null != agentId) {
            // 2 - agent
            patientFamily.setSource("2");
        } else {
            patientFamily.setSource(patient.getSource());
        }
        patientFamily.setDoctorId(patient.getDoctorId());
        busPatientFamilyMapper.insert(patientFamily);
    }

    /**
     * 绑定医院或合作机构
     *
     * @param loginDTO     登录参数
     * @param partnersCode 合作机构代码
     * @param busPatient   患者信息
     */
    private void bind(T loginDTO, String partnersCode, BusPatient busPatient, UniAppUserinfo uniAppUserinfo) {
        if (StringUtils.isNotEmpty(partnersCode)) {
            bindPartners(loginDTO, partnersCode, busPatient, uniAppUserinfo);
        } else {
            bindHospital(loginDTO, busPatient, uniAppUserinfo);
        }
    }

    /**
     * 绑定合作医院
     *
     * @param loginDTO   登录参数
     * @param busPatient 患者信息
     */
    private void bindHospital(T loginDTO, BusPatient busPatient, UniAppUserinfo uniAppUserinfo) {
        // 根据openid查询医院与患者的绑定关系
        BusPatientHospital patientHospital = queryHospitalByOpenid(loginDTO, uniAppUserinfo);
        if (Objects.isNull(patientHospital)) {
            //通过医院id和患者id查询医院与患者的绑定关系
            patientHospital = busPatientHospitalService.queryPatientHospitalInfo(loginDTO.getHospitalId(), busPatient.getId());
            //如果不存则添加
            if (Objects.isNull(patientHospital)) {
                patientHospital = new BusPatientHospital();
                patientHospital.setPatientId(busPatient.getId());
                patientHospital.setHospitalId(loginDTO.getHospitalId());
                patientHospital.setCreateTime(new Date());
                assembleOpenid(patientHospital, loginDTO, uniAppUserinfo);
                busPatientHospitalService.save(patientHospital);
                return;
            }
        }

        // 如果 openid 为空
        if (isOpenidBlank(patientHospital)) {
            assembleOpenid(patientHospital, loginDTO, uniAppUserinfo);
            busPatientHospitalService.updateById(patientHospital);
            return;
        }
        //openid 是否相同
        isSameHospitalOpenid(loginDTO, patientHospital, uniAppUserinfo);
        // 校验是否是同一个 PatientHospital 对象
        if (Objects.nonNull(patientHospital.getPatientId()) && isSamePatient(patientHospital, loginDTO, busPatient.getId(), uniAppUserinfo)) {
            throw new LoginException(LoginExceptionEnums.BOUND_FAIL.getMessage(),
                    patientHospital.getPhoneNumber(),
                    uniAppUserinfo != null ? uniAppUserinfo.getOpenid() : "");
        }

        if (Objects.isNull(patientHospital.getPatientId())) {
            BusPatientHospital tempPatientHospital = busPatientHospitalService.queryPatientHospitalInfo(loginDTO.getHospitalId(), busPatient.getId());
            if (tempPatientHospital != null) {
                throw new LoginException(LoginExceptionEnums.ALREADY_LOGGED_IN.getMessage());
            }
            patientHospital.setPatientId(busPatient.getId());
            busPatientHospitalService.updateById(patientHospital);
        }
    }

    /**
     * 绑定合作机构
     *
     * @param loginDTO     登录参数
     * @param partnersCode 合作机构代码
     * @param busPatient   患者信息
     */
    private void bindPartners(T loginDTO, String partnersCode, BusPatient busPatient, UniAppUserinfo uniAppUserinfo) {
        // 查询机构信息
        BusPartners busPartners = busPartnersService.queryPartners(loginDTO.getHospitalId(), partnersCode);
        if (Objects.isNull(busPartners)) {
            throw new CheckedException("该医院还未绑定合作机构");
        }
        //通过openid查询合作机构与患者的绑定关系
        BusPatientPartners busPatientPartners = queryPartnersByOpenid(loginDTO, uniAppUserinfo);
        if (Objects.isNull(busPatientPartners)) {
            //通过医院id和患者id和合作机构id查询合作机构与患者的绑定关系
            busPatientPartners = busPatientPartnersService.queryInfo(loginDTO.getHospitalId(), busPatient.getId(), busPartners.getId());
            //如果不存则则板顶
            if (Objects.isNull(busPatientPartners)) {
                busPatientPartners = new BusPatientPartners();
                busPatientPartners.setPatientId(busPatient.getId());
                busPatientPartners.setHospitalId(loginDTO.getHospitalId());
                busPatientPartners.setPartnersId(busPartners.getId());
                assembleOpenid(busPatientPartners, loginDTO, uniAppUserinfo);
                busPatientPartnersMapper.insert(busPatientPartners);
                return;
            }
            // 如果 openid 为空
            if (isOpenidBlank(busPatientPartners)) {
                assembleOpenid(busPatientPartners, loginDTO, uniAppUserinfo);
                busPatientPartnersMapper.updateById(busPatientPartners);
                return;
            }
            isSamePartnersOpenid(loginDTO, busPatientPartners, uniAppUserinfo);
        }
        //校验是否是同一个对象
        if (!isSamePartners(busPatientPartners, loginDTO, busPatient.getId(), busPartners.getId(), uniAppUserinfo)) {
            throw new LoginException(LoginExceptionEnums.BOUND_FAIL.getMessage()
                    , busPatientPartners.getPhoneNumber()
                    , uniAppUserinfo != null ? uniAppUserinfo.getOpenid() : "");
        }
    }

    /**
     * 查询合作渠道关联的openid是否为空
     *
     * @param busPatientPartners 患者合作渠道信息
     * @return 合作渠道关联的openid是否为空
     */
    protected abstract boolean isOpenidBlank(BusPatientPartners busPatientPartners);

    /**
     * 根据openid查询医院与患者的绑定关系
     *
     * @param loginDTO       登录参数
     * @param uniAppUserinfo 小程序用户信息
     * @return BusPatientHospital
     */
    protected abstract BusPatientHospital queryHospitalByOpenid(T loginDTO, UniAppUserinfo uniAppUserinfo);

    /**
     * 比对busPatientPartners openid是否相同
     *
     * @param loginDTO 登录入参
     * @param busPatientPartners 患者与合作机构关系
     * @param uniAppUserinfo 小程序用户信息
     */
    protected abstract void isSamePartnersOpenid(T loginDTO, BusPatientPartners busPatientPartners, UniAppUserinfo uniAppUserinfo);

    /**
     * 比对busPatientHospital openid是否相同
     *
     * @param loginDTO 登录入参
     * @param busPatientHospital 患者与医院关系
     * @param uniAppUserinfo     用户的小程序信息
     */
    protected abstract void isSameHospitalOpenid(T loginDTO, BusPatientHospital busPatientHospital, UniAppUserinfo uniAppUserinfo);

    /**
     * 组装openid参数
     *
     * @param patientHospital 患者与医院关系
     * @param loginDTO        登录参数
     * @param uniAppUserinfo  用户小程序西悉尼
     */
    protected abstract void assembleOpenid(BusPatientHospital patientHospital, T loginDTO, UniAppUserinfo uniAppUserinfo);

    /**
     * 判断openid是否为空
     *
     * @param patientHospital 患者与医院关系
     * @return boolean
     */
    protected abstract boolean isOpenidBlank(BusPatientHospital patientHospital);

    /**
     * 判断是否是同一个openid
     *
     * @param patientHospital 患者与医院关系
     * @param loginDTO        登录参数
     * @param patientId       患者id
     * @param uniAppUserinfo  用户的小程序信息
     * @return boolean
     */
    protected abstract boolean isSamePatient(BusPatientHospital patientHospital, T loginDTO, Long patientId, UniAppUserinfo uniAppUserinfo);

    /**
     * 根据openid查询合作机构与患者的绑定关系
     *
     * @param loginDTO       登录参数
     * @param uniAppUserinfo 小程序用户信息
     * @return BusPatientPartners
     */
    protected abstract BusPatientPartners queryPartnersByOpenid(T loginDTO, UniAppUserinfo uniAppUserinfo);

    /**
     * 组装openid参数
     *
     * @param patientPartners 患者与合作机构关系
     * @param loginDTO        登录参数
     * @param uniAppUserinfo  用户的小程序信息
     */
    protected abstract void assembleOpenid(BusPatientPartners patientPartners, T loginDTO, UniAppUserinfo uniAppUserinfo);

    /**
     * 判断是否是同一个openid
     *
     * @param patientPartners 患者与合作机构关系
     * @param loginDTO        登录参数
     * @param patientId       患者id
     * @param partnersId      合作机构id
     * @param uniAppUserinfo  用户的小程序信息
     * @return boolean
     */
    protected abstract boolean isSamePartners(BusPatientPartners patientPartners, T loginDTO, Long patientId, Long partnersId, UniAppUserinfo uniAppUserinfo);


    /**
     * 登陆后处理方法
     *
     * @param loginDTO       登录参数
     * @param uniAppUserinfo 小程序用户信息
     * @return {@link LoginResultVO}
     */
    private LoginResultVO afterLogin(T loginDTO, BusPatient busPatient, UniAppUserinfo uniAppUserinfo) {
        String uuid = IdUtils.fastSimpleUUID();
        String partnersCode = loginDTO.getPartnersCode();
        SMSLoginUser smsLoginUser = new SMSLoginUser();
        smsLoginUser.setHospitalId(loginDTO.getHospitalId());
        smsLoginUser.setUserid(busPatient.getId());
        smsLoginUser.setUsername(busPatient.getPhoneNumber());
        if (StringUtils.isNotEmpty(partnersCode)) {
            smsLoginUser.setPartnersCode(partnersCode);
        }
        smsLoginUser.setLoginType(loginDTO.getLoginType().getCode());
        smsLoginUser.setIdentity(AppRoleEnum.PATIENT.getCode());
        //缓存短信登录信息
        String token = setTokenCache(uuid, smsLoginUser, loginDTO, uniAppUserinfo);
        busPatientService.saveMsg(loginDTO.getHospitalId(), busPatient.getId(), token, loginDTO.getLoginIp(), AppRoleEnum.PATIENT.getCode(), loginDTO.getLoginType().getCode());
        LoginResultVO resultVO = new LoginResultVO();
        resultVO.setToken(token);
        resultVO.setExpireTime(System.currentTimeMillis() + CacheConstants.LOGIN_TOKEN_EXPIRE_TIME * 1000 - 1000L * 60 * 30);
        return resultVO;
    }

    /**
     *  设置登录token缓存
     * @param token token
     * @param smsLoginUser 用户登录信息
     * @param dto 登录参数
     * @param uniAppUserinfo 小程序用户信息
     * @return token
     */
    protected abstract String setTokenCache(String token, SMSLoginUser smsLoginUser, T dto, UniAppUserinfo uniAppUserinfo);
}

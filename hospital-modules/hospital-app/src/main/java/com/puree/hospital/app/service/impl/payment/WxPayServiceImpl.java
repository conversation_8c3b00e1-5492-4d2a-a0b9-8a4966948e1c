package com.puree.hospital.app.service.impl.payment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.app.api.model.event.consultation.BaseConsultationEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationPaySuccessEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationRefundSuccessEvent;
import com.puree.hospital.app.api.model.event.order.OrderAgreeRefundEvent;
import com.puree.hospital.app.api.model.event.order.OrderCancelEvent;
import com.puree.hospital.app.api.model.event.order.OrderPaidEvent;
import com.puree.hospital.app.constant.AfterSaleStatus;
import com.puree.hospital.app.constant.ConsultationOrderStatus;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusDoctorTdl;
import com.puree.hospital.app.domain.BusDrugOrderPackage;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusEnterprise;
import com.puree.hospital.app.domain.BusEnterpriseProductOrder;
import com.puree.hospital.app.domain.BusEnterpriseProductOrderDetail;
import com.puree.hospital.app.domain.BusFiveServicePackOrder;
import com.puree.hospital.app.domain.BusHospitalInvoiceConfig;
import com.puree.hospital.app.domain.BusInvoiceHeader;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderAfterSales;
import com.puree.hospital.app.domain.BusOrderShop;
import com.puree.hospital.app.domain.BusOtcDrugs;
import com.puree.hospital.app.domain.BusPartners;
import com.puree.hospital.app.domain.BusPatientOrderInfo;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusProductInvoiceConfig;
import com.puree.hospital.app.domain.MiPayResult;
import com.puree.hospital.app.domain.dto.BusPatientHospitalDto;
import com.puree.hospital.app.domain.dto.CommunicationMessageDTO;
import com.puree.hospital.app.domain.dto.DrugsAndGoodsDTO;
import com.puree.hospital.app.domain.dto.PayDTO;
import com.puree.hospital.app.domain.vo.BusPatientHospitalVo;
import com.puree.hospital.app.enums.BusOrderSubOrderTypeEnum;
import com.puree.hospital.app.enums.OrderSendTypeEnum;
import com.puree.hospital.app.helper.BusPayConfigHelper;
import com.puree.hospital.app.infrastructure.supplier.SupplierFactory;
import com.puree.hospital.app.infrastructure.wechat.util.WxPayUtil;
import com.puree.hospital.app.mapper.BusAfterSaleMapper;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusDoctorTdlMapper;
import com.puree.hospital.app.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.app.mapper.BusDrugsOrderMapper;
import com.puree.hospital.app.mapper.BusEnterpriseMapper;
import com.puree.hospital.app.mapper.BusEnterpriseProductOrderDetailMapper;
import com.puree.hospital.app.mapper.BusEnterpriseProductOrderMapper;
import com.puree.hospital.app.mapper.BusFiveServicePackOrderMapper;
import com.puree.hospital.app.mapper.BusHospitalInvoiceConfigMapper;
import com.puree.hospital.app.mapper.BusInvoiceHeaderMapper;
import com.puree.hospital.app.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.mapper.BusOrderShopMapper;
import com.puree.hospital.app.mapper.BusOtcDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.mapper.BusProductInvoiceConfigMapper;
import com.puree.hospital.app.mapper.BusShopOrderMapper;
import com.puree.hospital.app.mapper.MiPayResultMapper;
import com.puree.hospital.app.queue.QueueConstant;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.ConsultationNoticeProducer;
import com.puree.hospital.app.queue.producer.ConsultationOrderAutoCompleteProducer;
import com.puree.hospital.app.queue.producer.ConsultationOrderRefundProducer;
import com.puree.hospital.app.queue.producer.NotAcceptedImageTextProducer;
import com.puree.hospital.app.queue.producer.event.consultation.ConsultationPaySuccessEventProducer;
import com.puree.hospital.app.queue.producer.event.consultation.ConsultationRefundSuccessEventProducer;
import com.puree.hospital.app.queue.producer.event.five.ServicePackageOrderRefundSuccessEventProducer;
import com.puree.hospital.app.queue.producer.event.order.OrderAgreeRefundEventProducer;
import com.puree.hospital.app.queue.producer.event.order.OrderPaidEventProducer;
import com.puree.hospital.app.service.IAsyncJoinFollowUpService;
import com.puree.hospital.app.service.IBusCommunicationMessageService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusConsultationSettingsService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusDoctorTdlService;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusOrderAfterSalesService;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.app.service.IBusPartnersService;
import com.puree.hospital.app.service.IBusPatientHospitalService;
import com.puree.hospital.app.service.IBusPatientPartnersService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.app.service.ITongLianPayService;
import com.puree.hospital.app.service.IWxPayService;
import com.puree.hospital.app.service.impl.BusFiveServicePackInvoiceService;
import com.puree.hospital.app.service.impl.BusOrderInvoiceService;
import com.puree.hospital.business.api.OrderSendOutResultVO;
import com.puree.hospital.business.api.RemoteDrugsOrderPackageService;
import com.puree.hospital.business.api.model.dto.BusHospitalOrderDTO;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.api.enums.DeliveryTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.core.enums.AfterSaleRefundTypeEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderStatusEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.EntOrderStatusEnum;
import com.puree.hospital.common.core.enums.ImMemberEnum;
import com.puree.hospital.common.core.enums.InvoiceStatusEnum;
import com.puree.hospital.common.core.enums.InvoiceTypeEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.enums.OrderStatusEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.ProductTypeInvoiceEnum;
import com.puree.hospital.common.core.enums.ServicePackOrderStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
import com.puree.hospital.five.api.RemoteServicePackImService;
import com.puree.hospital.five.api.RemoteServicePackOrderService;
import com.puree.hospital.five.api.model.BusDoctorPatientGroupRequest;
import com.puree.hospital.five.api.model.ServicePackOrderRequest;
import com.puree.hospital.five.api.model.ServicePackOrderResponse;
import com.puree.hospital.five.api.model.event.ServicePackageOrderRefundSuccessEvent;
import com.puree.hospital.followup.api.model.PatientFollowUpJoinRuleDTO;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.order.api.RemoteBusShopOrderService;
import com.puree.hospital.order.api.model.BusShopOrder;
import com.puree.hospital.pay.api.RemotePayService;
import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.api.model.dto.BusPayDTO;
import com.puree.hospital.pay.api.model.dto.BusQueryDTO;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.InvalidKeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@SuppressWarnings("SpellCheckingInspection")
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WxPayServiceImpl implements IWxPayService {
    @Resource
    private RemotePayService remotePayService;
    private final WxPayUtil wxPayUtil;
    private final IBusDrugsOrderService busDrugsOrderService;
    private final BusDoctorTdlMapper busDoctorTdlMapper;
    private final BusConsultationOrderMapper busConsultationOrderMapper;
    private final RemoteServicePackImService remoteServicePackImService;
    @Autowired
    private IBusDoctorTdlService busDoctorTdlService;
    private final RemoteServicePackOrderService remoteServicePackOrderService;
    private final IBusPrescriptionService busPrescriptionService;
    private final IBusConsultationOrderService consultationOrderService;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final BusAfterSaleMapper busAfterSaleMapper;
    private final IBusPartnersService busPartnersService;
    private final IBusPatientPartnersService busPatientPartnersService;
    private final BusOtcDrugsMapper busOtcDrugsMapper;
    private final IBusPatientHospitalService busPatientHospitalService;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final BusEnterpriseMapper busEnterpriseMapper;
    private final ITongLianPayService iTongLianPayService;
    private final RemoteBusShopOrderService remoteBusShopOrderService;
    private final IBusOrderService busOrderService;
    private final BusDrugsOrderMapper busDrugsOrderMapper;
    @Resource
    private SupplierFactory supplierFactory;
    private final IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final IBusOrderAfterSalesService busOrderAfterSalesService;
    private final BusOrderShopMapper busOrderShopMapper;
    private final BusEnterpriseProductOrderMapper busEnterpriseProductOrderMapper;
    private final BusEnterpriseProductOrderDetailMapper busEnterpriseProductOrderDetailMapper;
    private final IBusCommunicationMessageService communicationMessageService;
    private final BusInvoiceHeaderMapper invoiceHeaderMapper ;
    private final BusProductInvoiceConfigMapper productInvoiceConfigMapper ;
    private final BusHospitalInvoiceConfigMapper hospitalInvoiceConfigMapper ;
    private final BusFiveServicePackOrderMapper fiveServicePackOrderMapper ;
    private final BusFiveServicePackInvoiceService fiveServicePackInvoiceService ;
    private final BusOrderInvoiceService orderInvoiceService ;
    private final BusOrderAfterSalesMapper orderAfterSalesMapper ;
    private final BusOrderMapper busOrderMapper ;
    private final MiPayResultMapper miPayResultMapper;
    private final IAsyncJoinFollowUpService asyncJoinFollowUpService;
    @Autowired
    @Lazy
    private ConsultationNoticeProducer consultationNoticeProducer ;
    @Autowired
    @Lazy
    private ConsultationOrderAutoCompleteProducer consultationOrderAutoCompleteProducer ;
    @Autowired
    @Lazy
    private ConsultationOrderRefundProducer consultationOrderRefundProducer ;
    @Autowired
    @Lazy
    private NotAcceptedImageTextProducer notAcceptedImageTextProducer ;

    @Resource
    private PureeRabbitProducer pureeRabbitProducer;

    @Lazy
    @Resource
    private ConsultationRefundSuccessEventProducer consultationRefundSuccessEventProducer;

    @Lazy
    @Resource
    private ConsultationPaySuccessEventProducer consultationPaySuccessEventProducer;

    @Lazy
    @Resource
    private ServicePackageOrderRefundSuccessEventProducer servicePackageOrderRefundSuccessEventProducer;

    @Resource
    private BusPayConfigHelper busPayConfigHelper;


    @Autowired @Lazy
    private OrderAgreeRefundEventProducer orderAgreeRefundEventProducer;

    @Resource @Lazy
    private ApplicationEventPublisher publisher;

    @Autowired
    BusShopOrderMapper busShopOrderMapper ;

    @Autowired
    RemoteDrugsOrderPackageService remoteDrugsOrderPackageService;

    @Resource @Lazy
    OrderPaidEventProducer orderPaidEventProducer;

    @Override
    public JSONObject wxPay(Map<String, String> map) {
        log.info("支付入参={}", map);
        // 下单对应的真实就诊人id - 原来的familyId
        String patientId = map.get("patientId");
        // 下单对应的用户id - 原来的patientId
        String userId = map.get("userId");
        String openid = map.get("openid");
        String orderNo = map.get("orderNo");
        String content = map.get("content");
        BigDecimal payAmount = new BigDecimal(map.get("amount"));
        String appType = map.get("appType");
        String mchType = map.get("mchType");
        Long hospitalId = Long.valueOf(map.get("hospitalId"));
        String autoActivate = map.get("autoActivate");
        String appId = map.get("appId");
        // 返回前端数据集合
        JSONObject payMap = new JSONObject();
        // 查询微信支付的商户配置
        BusPayConfig payConfig = busPayConfigHelper.getPayConfig(hospitalId, appType);
        log.info("配置信息={}", payConfig);
        // 发起微信预支付订单请求
        BusPayDTO busPayDTO = new BusPayDTO();
        busPayDTO.setHospitalId(hospitalId);
        if (StringUtils.isNotEmpty(appType)) {
            busPayDTO.setAppType(appType);
        }
        busPayDTO.setMchType(mchType);
        busPayDTO.setPayConfigId(payConfig.getId());
        busPayDTO.setPayWay(PaysTypeEnum.WECHAT_PAY.getCode());
        busPayDTO.setPayAmount(payAmount);
        busPayDTO.setOpenid(openid);
        busPayDTO.setOrderNo(orderNo);
        busPayDTO.setContent(content);
        busPayDTO.setAppid(StringUtils.isNotEmpty(appId) ? appId : payConfig.getAppId());
        busPayDTO.setNotifyUrl(payConfig.getPayNotifyUrl());
        busPayDTO.setAutoActivate(autoActivate);
        if (StringUtils.isNotEmpty(userId) && !"null".equals(userId)){
            busPayDTO.setUserId(Long.valueOf(userId));
        }
        if (StringUtils.isNotEmpty(patientId) && !"null".equals(patientId)){
            busPayDTO.setPatientId(Long.valueOf(patientId));
        }
        R<JSONObject> pay = remotePayService.pay(busPayDTO);
        if (Constants.FAIL.equals(pay.getCode())) {
            throw new ServiceException(pay.getMsg());
        }
        log.info("支付返回参数pay={}", pay.getData());
        JSONObject payData = pay.getData();
        String prepayId = payData.getString("prepayId");
        if (StringUtils.isEmpty(prepayId)) {
            payMap.put("status", 0);
            return payMap;
        }
        // TODO - 后续迁移到pay模块
        // 填充返回前端数据
        payMap.put("appId", busPayDTO.getAppid());
        long timestamp = System.currentTimeMillis() / 1000;
        payMap.put("timeStamp", timestamp + "");
        String nonceStr = WxPayUtil.generateNonceStr();
        payMap.put("nonceStr", nonceStr);
        payMap.put("package", "prepay_id=" + prepayId);
        payMap.put("signType", "RSA");
        // 生成请求签名
        String paySign = wxPayUtil.getPaySign(nonceStr, timestamp, payMap, payConfig);
        payMap.put("paySign", paySign);
        return payMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject refund(Map<String, Object> map) {
        log.info("退款入参={}", map);
        String outTradeNo = (String) map.get("outTradeNo");
        if (ObjectUtil.isNull(map.get("refundAmount")) || ObjectUtil.isNull(map.get("orderAmount")) ||
                StringUtils.isEmpty(outTradeNo)) {
            throw new ServiceException("退款参数缺失！");
        }
        String flag = outTradeNo.substring(0, 2);
        BigDecimal refundAmount = BigDecimal.valueOf(Double.parseDouble(String.valueOf(map.get("refundAmount"))));
        BigDecimal orderAmount = BigDecimal.valueOf(Double.parseDouble(String.valueOf(map.get("orderAmount"))));
        if (orderAmount.compareTo(new BigDecimal(0)) == 0) {
            if (OrderTypeConstant.CONSULATION_ORDER.equals(flag)) {
                BusConsultationOrder consultationOrder = consultationOrderService.queryConsultationOrderByOrderNo(outTradeNo);
                // 修改订单为已取消
                BusConsultationOrder order = new BusConsultationOrder();
                order.setId(consultationOrder.getId());
                if (CodeEnum.NO.getCode().equals(consultationOrder.getOrderType())) {
                    order.setStatus(ConsultationOrderStatusEnum.CANCEL.getCode());
                } else {
                    order.setVideoStatus(ConsultationOrderStatusEnum.CANCEL.getCode());
                }
                order.setCancelTime(DateUtils.getNowDate());
                busConsultationOrderMapper.updateById(order);
                sendCancelOrderEvent(consultationOrder);
                return new JSONObject();
            }
        }
        String appType = (String) map.get("appType");
        String mchType = (String) map.get("mchType");
        Long hospitalId = (Long) map.get("hospitalId");
        Long orderAfterSaleId = (Long) map.get("orderAfterSaleId");
        // 退款标识
        Object obj = map.get("automaticRefund");
        String automaticRefund = "";
        if (ObjectUtil.isNotNull(obj)) {
            automaticRefund = (String) obj;
        }
        // 退款订单需要考虑旧数据的问题
        BusPayConfig payConfig = busPayConfigHelper.getPayConfigByOutTradeNo(hospitalId, appType, outTradeNo);
        // 拼接申请退款参数
        JSONObject params = new JSONObject();
        // 交易订单退款传微信支付订单号（解决同一笔订单支付多次订单号重复问题）
        if (OrderTypeConstant.TOTAL_ORDER.equals(flag)) {
            String transactionId = (String) map.get("transactionId");
            params.put("transaction_id", transactionId);
        } else {
            params.put("out_trade_no", outTradeNo);
        }
        if (ObjectUtil.isNotNull(orderAfterSaleId)) {
            params.put("out_refund_no", DateUtils.getNowDate().getTime() + "a" + orderAfterSaleId);
        } else {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            params.put("out_refund_no", simpleDateFormat.format(DateUtils.getNowDate()));
        }
        params.put("notify_url", payConfig.getRefundNotifyUrl());
        // 商品金额
        JSONObject amount = new JSONObject();
        refundAmount = refundAmount.setScale(2, RoundingMode.HALF_UP);
        BigDecimal refund = refundAmount.multiply(new BigDecimal(100));
        orderAmount = orderAmount.setScale(2, RoundingMode.HALF_UP);
        BigDecimal total = orderAmount.multiply(new BigDecimal(100));
        amount.put("refund", refund.intValue());
        amount.put("total", total.intValue());
        amount.put("currency", "CNY");
        params.put("amount", amount);
        // 发起微信退款请求
        BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
        busRefundPayDTO.setParams(params);
        busRefundPayDTO.setPayWay(PaysTypeEnum.WECHAT_PAY.getCode());
        busRefundPayDTO.setHospitalId(hospitalId);
        if (OrderTypeConstant.TOTAL_ORDER.equals(flag)) {
            busRefundPayDTO.setTransactionId(params.getString("transaction_id"));
        } else {
            busRefundPayDTO.setOutTradeNo(outTradeNo);
        }
        busRefundPayDTO.setAppType(appType);
        busRefundPayDTO.setMchType(mchType);
        busRefundPayDTO.setPayConfigId(payConfig.getId());
        busRefundPayDTO.setRefundAmount(refund.doubleValue());
        busRefundPayDTO.setOrderAmount(total.doubleValue());
        R<JSONObject> refundResp = remotePayService.refund(busRefundPayDTO);
        if (Constants.FAIL.equals(refundResp.getCode())) {
            throw new ServiceException(refundResp.getMsg());
        }
        JSONObject jsonObject = refundResp.getData();
        log.info("微信退款返回状态={}", jsonObject.getString("status"));

        // 修改订单状态
        switch (flag) {
            case OrderTypeConstant.DRUGS_ORDER:
                BusDrugsOrder order = new BusDrugsOrder()
                        .setOrderNo(outTradeNo)
                        .setStatus(DrugsOrderEnum.REFUNDED_ZHONG.getCode());
                busDrugsOrderService.updateStatus(order);
                //患者终止随访(异步处理)
                BusDrugsOrder drugsOrder = busDrugsOrderService.queryDrugsOrder(outTradeNo);
                PatientFollowUpJoinRuleDTO dto = new PatientFollowUpJoinRuleDTO();
                dto.setHospitalId(hospitalId);
                dto.setUserId(drugsOrder.getPatientId());
                dto.setFamilyId(drugsOrder.getFamilyId());
                dto.setOrderNo(outTradeNo);
                asyncJoinFollowUpService.terminateFollowUp(dto);
                break;
            case OrderTypeConstant.CONSULATION_ORDER:
                BusConsultationOrder consultationOrder = (BusConsultationOrder) map.get("consultationOrder");
                this.updateStatus(consultationOrder, automaticRefund);
                break;
            case OrderTypeConstant.SERVICE_PACK_ORDER:
                ServicePackOrderRequest request = new ServicePackOrderRequest();
                request.setOrderNo(outTradeNo);
                request.setStatus(ServicePackOrderStatusEnum.REFUNDING.getCode());
                R<Integer> r = remoteServicePackOrderService.remoteModifyOrderStatus(request,
                        SecurityConstants.INNER);
                log.info("远程修改服务包订单状态结果：{}", r.getCode());

                //患者终止随访(异步处理)
                ServicePackOrderResponse servicePackOrder = remoteServicePackOrderService.queryInfo(null, outTradeNo).getData();
                if(servicePackOrder != null){
                    dto = new PatientFollowUpJoinRuleDTO();
                    dto.setHospitalId(hospitalId);
                    dto.setUserId(servicePackOrder.getPatientId());
                    dto.setFamilyId(servicePackOrder.getFamilyId());
                    dto.setOrderNo(outTradeNo);
                    asyncJoinFollowUpService.terminateFollowUp(dto);
                }
                break;
            case OrderTypeConstant.TOTAL_ORDER:
                // 查询总订单信息
                List<BusOrder> busOrders = busOrderService.selectOrederByNo(outTradeNo);
                if (CollectionUtil.isNotEmpty(busOrders)) {
                    BusOrder busOrder = busOrders.get(0);
                    //患者终止随访(异步处理)
                    dto = new PatientFollowUpJoinRuleDTO();
                    dto.setHospitalId(busOrder.getHospitalId());
                    dto.setUserId(busOrder.getPatientId());
                    dto.setFamilyId(null);
                    dto.setOrderNo(busOrder.getOrderNo());
                    asyncJoinFollowUpService.terminateFollowUp(dto);
                }
                break;
            default:
        }
        return jsonObject;
    }


    @Override
    public void payUpdateDrugsOrderStatus(String orderNo) {
        // 查询药品订单信息
        BusDrugsOrder drugsOrder = busDrugsOrderService.queryDrugsOrder(orderNo);
        log.info("药品订单信息={}", drugsOrder);
        // 推单到配送企业
        //pushOrderToEnterprise(drugsOrder);
        // 修改订单状态
        BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
        busDrugsOrder.setOrderNo(orderNo);
        busDrugsOrder.setPaymentTime(DateUtils.getNowDate());
        // 自提
        if (CodeEnum.NO.getCode().equals(drugsOrder.getDeliveryType())) {
            busDrugsOrder.setStatus(DrugsOrderEnum.TOBEPICKEDUP.getCode());
        } else {
            busDrugsOrder.setStatus(DrugsOrderEnum.TO_BE_SHIPPED.getCode());
        }
        int i = busDrugsOrderService.updateStatus(busDrugsOrder);
        sendChargeEvent(drugsOrder.getHospitalId(), orderNo, busDrugsOrder.getPayWay());
        if (i <= 0) {
            BusDrugsOrder busDrugOrder = new BusDrugsOrder();
            busDrugOrder.setId(drugsOrder.getId());
            busDrugOrder.setIsSendSuccess(YesNoEnum.NO.getCode());
            busDrugsOrderMapper.insert(busDrugOrder);
            return;
        }

        //患者入组随访(异步处理)
        PatientFollowUpJoinRuleDTO dto = new PatientFollowUpJoinRuleDTO();
        dto.setHospitalId(drugsOrder.getHospitalId());
        //获取订单下的药品id列表
        List<BusPatientOrderInfo> orders = busDrugsOrderMapper.getDrugsIdsByOrderId(drugsOrder.getId(), drugsOrder.getHospitalId());
        log.debug("药品列表：{}", orders);
        dto.setDrugIds(orders.stream().map(BusPatientOrderInfo::getId).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(orders)) {
            dto.setUserId(orders.get(0).getPatientId());//购买药品的患者id
            dto.setDrugFamilyId(orders.get(0).getFamilyId());//购买药品的就诊人id
        }
        dto.setOrderNo(orderNo);
        asyncJoinFollowUpService.joinInFollowUp(dto);
    }

    /**
     * 推单到配送企业方法
     *
     * @param order
     */
    @Override
    public void pushDrugOrderToEnterprise(BusOrder order) {
        BusDrugsOrder drugsOrder = busDrugsOrderService.selectOrderInfo(order.getSubOrderId());
        drugsOrder.setReceivingUser(order.getReceiver());
        drugsOrder.setReceivingTel(order.getReceivePhone());
        drugsOrder.setReceivingAddress(order.getReceiveAddress());
        drugsOrder.setProvince(order.getProvince());
        drugsOrder.setCity(order.getCity());
        drugsOrder.setArea(order.getArea());
        drugsOrder.setReceivingAddress(order.getReceiveAddress());
        drugsOrder.setDeliveryType(order.getDeliveryType());
        drugsOrder.setPayWay(order.getPayWay());

        BusPrescription prescription;
        Long prescriptionId = drugsOrder.getPrescriptionId();
        // 查询包裹信息
        QueryWrapper<BusDrugOrderPackage> wrapper = Wrappers.query();
        wrapper.isNotNull("enterprise_id");
        wrapper.eq("package_type", YesNoEnum.NO.getCode());
        if (ObjectUtil.isNotNull(prescriptionId)) {
            wrapper.eq("prescription_id", prescriptionId);
            // 查询处方信息
            prescription = busPrescriptionService.selectPrescription(drugsOrder.getHospitalId(), prescriptionId);
        } else {
            wrapper.eq("drugs_order_id", drugsOrder.getId());
            prescription = new BusPrescription();
            prescription.setPrescriptionNumber(drugsOrder.getOrderNo());
            prescription.setPrescriptionAmount(new BigDecimal(drugsOrder.getAmount()));
        }
        List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(busDrugOrderPackages)) {
            for (BusDrugOrderPackage orderPackage : busDrugOrderPackages) {
                // 查询配送企业标识
                BusEnterprise busEnterprise = busEnterpriseMapper.selectById(orderPackage.getEnterpriseId());
                if (ObjectUtil.isNotNull(busEnterprise)) {
                    String identifying = busEnterprise.getIdentifying();
                    // 非处方药品包裹信息
                    if (YesNoEnum.NO.getCode().equals(orderPackage.getPackageDrugType())) {
                        LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusOtcDrugs::getHospitalId, drugsOrder.getHospitalId());
                        lambdaQuery.eq(BusOtcDrugs::getEnterpriseId, orderPackage.getEnterpriseId());
                        lambdaQuery.eq(BusOtcDrugs::getDrugsOrderId, drugsOrder.getId());
                        lambdaQuery.eq(BusOtcDrugs::getOtcOrNot, YesNoEnum.YES.getCode());
                        List<BusOtcDrugs> busOtcDrugsList = busOtcDrugsMapper.selectList(lambdaQuery);
                        prescription.setOtcDrugsList(busOtcDrugsList);
                    } else { // 处方药品包裹信息
                        LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusPrescriptionDrugs::getHospitalId, drugsOrder.getHospitalId());
                        lambdaQuery.eq(BusPrescriptionDrugs::getEnterpriseId, orderPackage.getEnterpriseId());
                        lambdaQuery.eq(BusPrescriptionDrugs::getPrescriptionId, prescriptionId);
                        List<BusPrescriptionDrugs> pdDrugsList = busPrescriptionDrugsMapper.selectList(lambdaQuery);
                        prescription.setPdDrugsList(pdDrugsList);
                    }
                    //发送订单
                    prescription.setEnterpriseId(orderPackage.getEnterpriseId());
                    log.info("发送订单前参数：{},{}", JSON.toJSONString(drugsOrder), JSON.toJSONString(prescription));
                    int result = supplierFactory.getProcessor(identifying).createOrder(drugsOrder, prescription);
                    if (result == 0) {
                        BusDrugsOrder busDrugOrder = new BusDrugsOrder();
                        busDrugOrder.setId(drugsOrder.getId());
                        busDrugOrder.setIsSendSuccess(YesNoEnum.NO.getCode());
                        busDrugsOrderMapper.updateById(busDrugOrder);
                    }
                }
            }
        }
    }

    @Override
    public void payUpdateConsultationOrderStatus(String orderNo) {
        log.info("问诊订单信息={}", orderNo);
        BusConsultationOrder consultationOrder = consultationOrderService.queryConsultationOrder(orderNo);
        // 订单不存在或订单状态异常(非待支付-不受理)
        if (consultationOrder == null) {
            log.warn("当前订单不存在，订单号：{}", orderNo);
            return;
        }
        // 获取订单类型
        String orderType = consultationOrder.getOrderType();
        // 图文问诊订单已支付
        if (ConsultationOrderTypeEnum.isImageText(orderType) &&
                !ConsultationOrderStatusEnum.UNPAID.getCode().equals(consultationOrder.getStatus())) {
            log.warn("当前图文订单已支付，不再处理,当前订单：{}，状态：{}", orderNo, consultationOrder.getStatus());
            return;
        }
        // 视频问诊订单已支付
        if (ConsultationOrderTypeEnum.isVideo(orderType) &&
                !ConsultationOrderStatusEnum.UNPAID.getCode().equals(consultationOrder.getVideoStatus())) {
            log.warn("当前视频订单已支付，不再处理,当前订单：{}，状态：{}", orderNo, consultationOrder.getStatus());
            return;
        }
//        String partnersCode = consultationOrder.getPartnersCode();
        // 查询订单购买类型
        String buyType = consultationOrder.getBuyType();
        // 查询订单问诊类型
        String consultationType = consultationOrder.getConsultationType();
        BusConsultationOrder busConsultationOrder = new BusConsultationOrder();
        busConsultationOrder.setUpdateTime(DateUtils.getNowDate());
        // 查询问诊设置
        BusConsultationSettings bs = new BusConsultationSettings();
        bs.setHospitalId(consultationOrder.getHospitalId());
        bs = busConsultationSettingsService.selectOne(bs);
        // 图文问诊
        if (ConsultationOrderTypeEnum.isImageText(orderType)) {
            // 诊室外购买的问诊订单
            if (CodeEnum.YES.getCode().equals(buyType)) {
                busConsultationOrder.setStatus(ConsultationOrderStatus.PAID);
                // 超时未接诊加入队列
                Message message = new Message();
                message.setId(consultationOrder.getId());
                message.setHospitalId(consultationOrder.getHospitalId());

                if (null != bs) {
                    long time = bs.getReceiveTime() * 60 * 60 * 1000L;
                    message.setFireTime(time);
                } else {
                    message.setFireTime(QueueConstant.QUEUE_NOT_ACCEPTED_IMAGETEXT_TIME);
                }
                notAcceptedImageTextProducer.delaySend(message, message.getFireTime());
                // 超时未接诊通知队列
                message.setFireTime(QueueConstant.QUEUE_NOT_ACCEPTED_NOTICE_TIME);
                consultationNoticeProducer.delaySend(message, QueueConstant.QUEUE_NOT_ACCEPTED_NOTICE_TIME);
            } else {
                busConsultationOrder.setStatus(ConsultationOrderStatus.VISITING);
                Message message = new Message();
                message.setId(consultationOrder.getId());
                message.setHospitalId(consultationOrder.getHospitalId());
                message.setExtend("purchase");

                if (bs == null || bs.getImagetextTotalExpire() == null) {
                    message.setFireTime(QueueConstant.QUEUE_CONSULTATION_ORDER_AUTO_COMPLETE_TIME);
                } else {
                    long time = bs.getImagetextTotalExpire() * 60 * 60 * 1000L;
                    message.setFireTime(time);
                }
                consultationOrderAutoCompleteProducer.delaySend(message, message.getFireTime());

                //给医生发送自定义消息，方便医生端im自动刷新
                BusDoctorPatientGroup query = new BusDoctorPatientGroup();
                query.setHospitalId(consultationOrder.getHospitalId());
                query.setDoctorId(consultationOrder.getDoctorId());
                query.setDepartmentId(consultationOrder.getDepartmentId());
                query.setPatientId(consultationOrder.getPatientId());
                query.setFamilyId(consultationOrder.getFamilyId());
                BusDoctorPatientGroup groupInfo = busDoctorPatientGroupService.selectOne(query);
                CommunicationMessageDTO messageDTO = new CommunicationMessageDTO();
                messageDTO.setDoctorPatientGroup(groupInfo);
                messageDTO.setGroupId(groupInfo.getId());
                messageDTO.setFromAccount(TencentyunImConstants.ADMINISTRATOR);
                messageDTO.setNickName(TencentyunImConstants.ADMINISTRATOR);
                messageDTO.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
                Map<String, Object> map = new HashMap<>();
                map.put("type", CustomMsgConstants.CONSULTATION_GO_ON);
                messageDTO.setMap(map);
                messageDTO.setSendNotification(false);
                messageDTO.setSaveMessage(false);
                messageDTO.setSendControls(Lists.newArrayList(TencentyunImConstants.NO_UN_READ));
                communicationMessageService.sendCustomImMsg(messageDTO);
            }
        } else { // 视频问诊
            busConsultationOrder.setVideoStatus(ConsultationOrderStatus.RESERVED_NOT_ARRIVED);
        }
        busConsultationOrder.setPaymentTime(DateUtils.getNowDate());
        busConsultationOrder.setInvoiceStatus(InvoiceStatusEnum.CAN_ISSUE.getStatus());
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        busConsultationOrderMapper.update(busConsultationOrder, queryWrapper);
        sendChargeEvent(consultationOrder.getHospitalId(), orderNo, consultationOrder.getPayWay());
        BusDoctorPatientGroup group = new BusDoctorPatientGroup();
        group.setHospitalId(consultationOrder.getHospitalId());
        group.setDoctorId(consultationOrder.getDoctorId());
        group.setPatientId(consultationOrder.getPatientId());
        group.setFamilyId(consultationOrder.getFamilyId());
        group.setDepartmentId(consultationOrder.getDepartmentId());
        group.setType(CodeEnum.NO.getCode());
        BusDoctorPatientGroup busDoctorPatientGroup = busDoctorPatientGroupService.checkGroup(group);
        log.info("查询是否存在群组:group={}", busDoctorPatientGroup);
        if (ObjectUtil.isNotNull(busDoctorPatientGroup)) {
            if (CodeEnum.NO.getCode().equals(busDoctorPatientGroup.getDelFlag())) {
                BusDoctorPatientGroupRequest groupRequest = OrikaUtils.convert(busDoctorPatientGroup, BusDoctorPatientGroupRequest.class);
                remoteServicePackImService.renewalDoctorAndAssistantGroup(groupRequest);
            } else {
                //判断重新激活im群组
                busDoctorPatientGroupService.createGroup(busDoctorPatientGroup, busDoctorPatientGroup.getDelFlag());
            }
        }
        // 类目模板推送需要发送视频问诊订单已预约
        // 2025-04-29修改为无论是否是图文问诊和视频问诊都发送该支付成功事件
        ConsultationPaySuccessEvent paySuccessEvent = new ConsultationPaySuccessEvent();
        paySuccessEvent.setEventType(BaseConsultationEvent.EventType.PAY_SUCCESS);
        paySuccessEvent.setConsultationOrderId(consultationOrder.getId());
        paySuccessEvent.setOrderNo(consultationOrder.getOrderNo());
        consultationPaySuccessEventProducer.send(paySuccessEvent);

        // 问诊订单是诊室外购买且挂医生的加入代办任务
        if (buyType.equals(CodeEnum.YES.getCode()) && consultationType.equals(CodeEnum.YES.getCode())) {
            BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
            busDoctorTdl.setDoctorId(consultationOrder.getDoctorId());
            busDoctorTdl.setHospitalId(consultationOrder.getHospitalId());
            busDoctorTdl.setType(consultationOrder.getOrderType());
            busDoctorTdl.setBusinessId(consultationOrder.getId());
            busDoctorTdl.setStatus(CodeEnum.NO.getCode());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", consultationOrder.getFamilyName());
            jsonObject.put("age", consultationOrder.getFamilyAge());
            busDoctorTdl.setContent(jsonObject.toJSONString());
            busDoctorTdl.setRoleType(ImMemberEnum.DOCTOR.getCode());
            busDoctorTdlService.insert(busDoctorTdl);
            //推送消息给医生
            consultationOrderService.pushTwMsgToDr(consultationOrder);
        }

        //加入消息队列
        if (CodeEnum.NO.getCode().equals(consultationOrder.getOrderType())) {
            Message message = new Message();
            message.setId(consultationOrder.getId());
            message.setHospitalId(consultationOrder.getHospitalId());

            if (bs == null || bs.getImagetextNoreplyExpire() == null) {
                message.setFireTime(QueueConstant.QUEUE_CONSULTATION_ORDER_REFUND_TIME);
            } else {
                long time = bs.getImagetextNoreplyExpire() * 60 * 60 * 1000L;
                message.setFireTime(time);
            }
            consultationOrderRefundProducer.delaySend(message, message.getFireTime());
        }
        log.info("问诊订单信息={}", JSON.toJSON(consultationOrder));
        if (null != consultationOrder.getFurtherConsultationId()) {
            iTongLianPayService.isFurther(consultationOrder.getFurtherConsultationId(), busDoctorPatientGroup.getId(),null);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void payUpdateServicePackOrderStatus(String orderNo, String autoActivate) {
        log.info("支付成功服务包订单号={},是否自动激活：{}", orderNo, autoActivate);
        //远程调用five模块，处理支付成功后的服务包相关更新
        remoteServicePackOrderService.paySuccessServicePackOrderHandler(orderNo);
    }

    @Override
    public void refundUpdateDrugsOrderStatus(String orderNo) {
        // 查询药品订单是否是已退款
        BusDrugsOrder drugsOrder = busDrugsOrderService.queryDrugsOrder(orderNo);
        String doStatus = drugsOrder.getStatus();
        log.info("订单状态={}", doStatus);
        if (!DrugsOrderEnum.REFUNDED.getCode().equals(doStatus)) {
            QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("order_no", orderNo);
            BusAfterSale busAfterSale = busAfterSaleMapper.selectOne(queryWrapper);
            if (ObjectUtil.isNotNull(busAfterSale) && ("全额退款".equals(busAfterSale.getRefundType()) ||
                    "退货且退款".equals(busAfterSale.getRefundType()))) {
                busDrugsOrderService.updateRefundStatus(drugsOrder, busAfterSale.getOrderStatus());
                //发送退费事件
                sendRefundEvent(drugsOrder.getHospitalId(), orderNo);
                //患者终止随访(异步处理)
                PatientFollowUpJoinRuleDTO dto = new PatientFollowUpJoinRuleDTO();
                dto.setHospitalId(drugsOrder.getHospitalId());
                dto.setUserId(drugsOrder.getPatientId());
                dto.setFamilyId(drugsOrder.getFamilyId());
                dto.setOrderNo(orderNo);
                asyncJoinFollowUpService.terminateFollowUp(dto);
            }
        }
    }

    @Override
    public void refundConsultationOrderStatus(String orderNo) {
        log.info("问诊订单信息={}", orderNo);
        // 查询问诊订单是否是已退款
        BusConsultationOrder consultationOrder = consultationOrderService.queryConsultationOrder(orderNo);
        String coStatus;
        // 图文问诊
        if (CodeEnum.NO.getCode().equals(consultationOrder.getOrderType())) {
            coStatus = consultationOrder.getStatus();
        } else { // 视频问诊
            coStatus = consultationOrder.getVideoStatus();
        }
        if (ConsultationOrderStatus.RETURNED.equals(consultationOrder.getPreStatus()) ||
                ConsultationOrderStatus.PAID.equals(consultationOrder.getPreStatus())) {
            coStatus = consultationOrder.getPreStatus();
        }
        log.info("问诊订单状态={}", coStatus);
        if (!ConsultationOrderStatus.REFUNDED.equals(coStatus)) {
            if (ConsultationOrderStatus.PAID.equals(coStatus) || ConsultationOrderStatus.RETURNED.equals(coStatus)) {
                BusConsultationOrder busConsultationOrder = new BusConsultationOrder();
                busConsultationOrder.setUpdateTime(DateUtils.getNowDate());
                busConsultationOrder.setRefundTime(DateUtils.getNowDate());
                // 图文问诊
                if (CodeEnum.NO.getCode().equals(consultationOrder.getOrderType())) {
                    if (ConsultationOrderStatus.RETURNED.equals(coStatus)) {
                        busConsultationOrder.setStatus(ConsultationOrderStatus.RETURNED);
                    } else {
                        busConsultationOrder.setStatus(ConsultationOrderStatus.REFUNDED);
                    }
                    // 类目模板推送问诊订单已退款
                } else { // 视频问诊
                    busConsultationOrder.setVideoStatus(ConsultationOrderStatus.REFUNDED);

                }
                QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("order_no", orderNo);
                if (Arrays.asList(InvoiceStatusEnum.CAN_ISSUE.getStatus(), InvoiceStatusEnum.TO_ISSUE.getStatus()).contains(consultationOrder.getInvoiceStatus())) {
                    busConsultationOrder.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());
                }
                busConsultationOrderMapper.update(busConsultationOrder, queryWrapper);
                ConsultationRefundSuccessEvent event = new ConsultationRefundSuccessEvent();
                event.setConsultationOrderId(consultationOrder.getId());
                event.setOrderNo(consultationOrder.getOrderNo());
                event.setEventType(BaseConsultationEvent.EventType.REFUND_SUCCESS);
                event.setOriginalStatus(coStatus);
                consultationRefundSuccessEventProducer.send(event);
            } else {
                consultationOrderService.updateRefundStatus(consultationOrder);
            }
            //发送问诊退号事件
            sendCancelOrderEvent(consultationOrder);
            //发送退款事件
            sendRefundEvent(consultationOrder.getHospitalId(), orderNo);

            //删除代办任务
            BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
            busDoctorTdl.setStatus("2");
            busDoctorTdl.setUpdateTime(DateUtils.getNowDate());
            UpdateWrapper<BusDoctorTdl> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("type", consultationOrder.getOrderType());
            updateWrapper.eq("business_id", consultationOrder.getId());
            updateWrapper.orderByDesc("create_time");
            busDoctorTdlMapper.update(busDoctorTdl, updateWrapper.last("limit 1"));
        }
    }

    @Override
    public void refundServicePackOrderStatus(String orderNo) {
        R<ServicePackOrderResponse> r = remoteServicePackOrderService.queryInfo(null, orderNo);
        ServicePackOrderResponse data = r.getData();
        log.info("远程调用code={},服务包订单查询结果={}", r.getCode(), data);
        if (!ServicePackOrderStatusEnum.REFUNDED.getCode().equals(data.getStatus())) {
            // 修改订单状态
            ServicePackOrderRequest request1 = new ServicePackOrderRequest();
            request1.setOrderNo(orderNo);
            request1.setStatus(ServicePackOrderStatusEnum.REFUNDED.getCode());
            request1.setRefundTime(DateUtils.getNowDate());

            if (Arrays.asList(InvoiceStatusEnum.CAN_ISSUE.getStatus(), InvoiceStatusEnum.TO_ISSUE.getStatus()).contains(data.getInvoiceStatus())) {
                BusAfterSale afterSale = busAfterSaleMapper.selectOne(new LambdaQueryWrapper<BusAfterSale>().eq(BusAfterSale::getOrderNo, orderNo));
                BigDecimal refundDecimal = (null == afterSale || CharSequenceUtil.isBlank(afterSale.getRefundAmount())) ? BigDecimal.ZERO : new BigDecimal(afterSale.getRefundAmount());
                BigDecimal amountDecimal = CharSequenceUtil.isBlank(data.getAmount()) ? BigDecimal.ZERO : new BigDecimal(data.getAmount());
                if (0 == amountDecimal.compareTo(refundDecimal) || 0 == BigDecimal.ZERO.compareTo(refundDecimal)) {
                    // 全额退款
                    request1.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());
                } else {
                    if (InvoiceStatusEnum.TO_ISSUE.getStatus().equals(data.getInvoiceStatus())) {
                        //----------宏捷荣---------------
                        CompletableFuture.runAsync(() -> {
                            BusFiveServicePackOrder fiveServicePackOrder = fiveServicePackOrderMapper.selectOne(new LambdaQueryWrapper<BusFiveServicePackOrder>().eq(BusFiveServicePackOrder::getOrderNo, orderNo));
                            fiveServicePackOrder.setStatus(ServicePackOrderStatusEnum.REFUNDED.getCode());
                            BusInvoiceHeader invoiceHeader = invoiceHeaderMapper.selectById(fiveServicePackOrder.getInvoiceHeaderId());
                            // 发票 - 购买物品信息
                            BusProductInvoiceConfig productInvoiceConfig = productInvoiceConfigMapper.selectOne(new LambdaQueryWrapper<BusProductInvoiceConfig>().eq(BusProductInvoiceConfig::getProductType, ProductTypeInvoiceEnum.CONSULT.getCode()));
                            // 发票 - 卖方信息
                            BusHospitalInvoiceConfig hospitalInvoiceConfig = hospitalInvoiceConfigMapper.selectOne(new LambdaQueryWrapper<BusHospitalInvoiceConfig>().eq(BusHospitalInvoiceConfig::getHospitalId, fiveServicePackOrder.getHospitalId()));
                            if (InvoiceTypeEnum.TAX_CONTROl.getId().equals(hospitalInvoiceConfig.getType())) {
                                try {
                                    fiveServicePackInvoiceService.taxControlIssueInvoice(invoiceHeader, productInvoiceConfig, hospitalInvoiceConfig, fiveServicePackOrder);
                                } catch (IOException | NoSuchPaddingException | NoSuchAlgorithmException |
                                         IllegalBlockSizeException | BadPaddingException | InvalidKeyException |
                                         UnrecoverableKeyException | CertificateException | KeyStoreException |
                                         KeyManagementException e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        });
                        //----------宏捷荣---------------
                    }
                }
            }

            //----------宏捷荣---------------
            if (InvoiceStatusEnum.ISSUE_SUCCESS.getStatus().equals(data.getInvoiceStatus())) {
                fiveServicePackInvoiceService.asyncRedBlueInvoice(orderNo, data.getAmount());
            }
            //----------宏捷荣---------------

            R<Integer> objectR = remoteServicePackOrderService.remoteModifyOrderStatus(request1,
                    SecurityConstants.INNER);
            log.info("远程调用修改服务包订单状态code={}", objectR.getCode());
            //远程调用修改服务包服务记录表，更新服务统计数据
            remoteServicePackOrderService.remoteUpdateServicePackPatientRecord(orderNo);

            if (HttpStatus.SUCCESS == objectR.getCode()) {
                // 修改售后状态
                BusAfterSale busAfterSale = new BusAfterSale();
                busAfterSale.setUpdateTime(DateUtils.getNowDate());
                busAfterSale.setStatus(AfterSaleStatus.REFUND_SUCCESS);
                LambdaQueryWrapper<BusAfterSale> wrapper =
                        new LambdaQueryWrapper<BusAfterSale>().eq(BusAfterSale::getOrderNo, orderNo);
                busAfterSaleMapper.update(busAfterSale, wrapper);

                //发送服务包退款成功事件
                ServicePackageOrderRefundSuccessEvent event = new ServicePackageOrderRefundSuccessEvent();
                event.setOrderId(data.getId());
                event.setOrderNo(data.getOrderNo());
                event.setEventType(ServicePackageOrderRefundSuccessEvent.EventType.REFUND_SUCCESS);
                servicePackageOrderRefundSuccessEventProducer.send(event);

                //患者终止随访(异步处理)
                PatientFollowUpJoinRuleDTO dto = new PatientFollowUpJoinRuleDTO();
                dto.setHospitalId(data.getHospitalId());
                dto.setUserId(data.getPatientId());
                dto.setFamilyId(data.getFamilyId());
                dto.setOrderNo(orderNo);
                asyncJoinFollowUpService.terminateFollowUp(dto);
            }
        }
    }

    @Override
    public JSONObject queryOrder(Long hospitalId, String appType,String mchType, String orderNo) {
        BusQueryDTO busQueryDTO = new BusQueryDTO();
        busQueryDTO.setOrderNo(orderNo);
        busQueryDTO.setOutTradeNo(orderNo);
        busQueryDTO.setPayWay(PaysTypeEnum.WECHAT_PAY.getCode());
        busQueryDTO.setHospitalId(hospitalId);
        BusPayConfig payConfig = busPayConfigHelper.getPayConfig(hospitalId, appType);
        busQueryDTO.setPayConfigId(payConfig.getId());
        R<JSONObject> query = remotePayService.query(busQueryDTO);
        if (query == null || query.getData() == null){
            log.error("微信支付订单查询失败{},orderNo:{}",query,orderNo);
            throw new ServiceException("微信支付订单查询失败");
        }
        return query.getData();
    }


    @Override
    public void updateStatus(BusConsultationOrder consultationOrder, String automaticRefund) {
        // 保存待接诊的状态
        BusConsultationOrder order = new BusConsultationOrder();
        if (ConsultationOrderStatus.PAID.equals(consultationOrder.getStatus())
                || ConsultationOrderStatus.RESERVED_NOT_ARRIVED.equals(consultationOrder.getVideoStatus())
                || "1".equals(automaticRefund)) {
            // 保存待接诊的状态为了在回调时修改订单状态
            String preStatus = consultationOrder.getPreStatus();
            if (StringUtils.isNotEmpty(preStatus) && !ConsultationOrderStatus.RETURNED.equals(preStatus)) {
                order.setPreStatus(ConsultationOrderStatus.PAID);
            }
        } else { // 保存9，代表后台已同意退款
            order.setPreStatus("9");
        }
        // 图文问诊
        if (CodeEnum.NO.getCode().equals(consultationOrder.getOrderType())) {
            order.setStatus(ConsultationOrderStatus.REFUNDING);
        } else { // 视频问诊
            order.setVideoStatus(ConsultationOrderStatus.REFUNDING);
        }
        order.setUpdateTime(DateUtils.getNowDate());
        QueryWrapper<BusConsultationOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", consultationOrder.getOrderNo());
        busConsultationOrderMapper.update(order, queryWrapper);

        // 修改代办任务状态
        log.info("问诊订单信息 consultationOrder={}", JSON.toJSONString(consultationOrder));
        BusDoctorTdl busDoctorTdl = new BusDoctorTdl();
        busDoctorTdl.setStatus("2");
        busDoctorTdl.setUpdateTime(DateUtils.getNowDate());
        UpdateWrapper<BusDoctorTdl> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("type", consultationOrder.getOrderType());
        updateWrapper.eq("business_id", consultationOrder.getId());
        updateWrapper.orderByDesc("create_time");
        busDoctorTdlMapper.update(busDoctorTdl, updateWrapper.last("limit 1"));
    }

    @Override
    public void consultationOrder(HashMap<String, String> map, Long subOrderId, Integer pCode) {
        // 查询问诊订单信息
        BusConsultationOrder order = consultationOrderService.queryOrderInfo(subOrderId);
        // 校验是否合作机构订单
        checkAppidAndOpenid(map, order.getHospitalId(), order.getPartnersCode(), order.getPatientId(), pCode);
        map.put("orderNo", order.getOrderNo());
        map.put("amount", order.getAmount());
        // 设置对应的问诊订单问诊的问诊人的id
        map.put("patientId", String.valueOf(order.getFamilyId()));
        // 设置对应的问诊订单问诊的用户id
        map.put("userId", String.valueOf(order.getPatientId()));
        if (CodeEnum.NO.getCode().equals(order.getOrderType())) {
            map.put("content", "图文问诊订单-订单编号" + order.getOrderNo());
        } else {
            map.put("content", "视频问诊订单-订单编号" + order.getOrderNo());
        }
    }

    @Override
    public void drugsOrder(HashMap<String, String> map, BusOrder busOrder, Integer pCode) {
        // 查询药品订单信息
        //BusDrugsOrder order = busDrugsOrderService.selectOrderInfo(busOrder.getSubOrderId());
        // 校验是否合作机构订单
        checkAppidAndOpenid(map, busOrder.getHospitalId(), busOrder.getPartnersCode(), busOrder.getPatientId(), pCode);
        map.put("orderNo", busOrder.getOrderNo());
        map.put("amount", busOrder.getRelPrice().toString());
        map.put("content", "药品订单-订单编号" + busOrder.getOrderNo());
        setDrugOrderPayDtoWithUserIdAndPatientId(map, busOrder);

    }

    @Override
    public void goodsOrder(HashMap<String, String> map, PayDTO dto) {
        String orderNo = dto.getOrderNo();
        Integer pCode = dto.getPCode();
        // 查询购物车关联订单信息
        List<BusOrder> busOrders = busOrderService.selectOrederByNo(orderNo);
        if (CollUtil.isNotEmpty(busOrders)) {
            BusOrder busOrder = busOrders.get(0);
            if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay()) &&
                    YesNoEnum.YES.getCode().equals(dto.getPay())) {
                LambdaQueryWrapper<MiPayResult> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(MiPayResult::getOrderNo, orderNo)
                        .last(" limit 1");
                MiPayResult miPayResult = miPayResultMapper.selectOne(lambdaQuery);
                // 4表示医保支付成功，支付失败直接返回，不支付运费
                if (!"4".equals(miPayResult.getOrdStas())) {
                    throw new ServiceException("医保支付失败！");
                }
            }
            // 校验是否合作机构订单
            checkAppidAndOpenid(map, busOrder.getHospitalId(), busOrder.getPartnersCode(), busOrder.getPatientId(), pCode);
            // 解决微信同一笔订单多次支付导致订单号重复问题
            String newOrderNo = orderNo + "_" + UUID.randomUUID().toString().split("-")[0];
            map.put("orderNo", newOrderNo);
            map.put("patientId", String.valueOf(busOrder.getPatientId()));
            setPayDtoWithUserIdAndPatientId(map, busOrder);
            // 判断该订单是否医保支付且支付成功
            if (Boolean.TRUE.equals(checkOrder(busOrder))) {
                map.put("amount", busOrder.getFreight().toString());
                map.put("content", "医保运费-订单编号" + busOrder.getOrderNo());
            } else {
                map.put("amount", busOrder.getRelPrice().toString());
                if (busOrders.size() == 1) {
                    if (CodeEnum.YES.getCode().equals(busOrder.getSubOrderType())) {
                        map.put("content", "商品订单-订单编号" + busOrder.getOrderNo());
                    } else {
                        map.put("content", "药品订单-订单编号" + busOrder.getOrderNo());
                    }
                } else {
                    map.put("content", "药品/商品订单-订单编号" + busOrder.getOrderNo());
                }
            }
        }
    }

    /**
     * 支付参数设置对应的用户id和就诊人id
     * @param map - 订单参数-需要设置的map
     * @param busOrder - 订单信息
     */
    private void setPayDtoWithUserIdAndPatientId(HashMap<String, String> map, BusOrder busOrder) {
        if (BusOrderSubOrderTypeEnum.DRUG_ORDER.getCode().equals(busOrder.getSubOrderType())) {
            // 药品订单-支付参数就诊人和用户id
            setDrugOrderPayDtoWithUserIdAndPatientId(map, busOrder);
        } else if (BusOrderSubOrderTypeEnum.GOODS_ORDER.getCode().equals(busOrder.getSubOrderType())) {
            // 商品订单
            R<BusShopOrder> busShopOrderResult = remoteBusShopOrderService.selectShopOrderById(busOrder.getSubOrderId());
            if (busShopOrderResult != null && Constants.SUCCESS == busShopOrderResult.getCode()
            && busShopOrderResult.getData() != null) {
                BusShopOrder busShopOrder = busShopOrderResult.getData();
                // 设置对应的商品订单的真实就诊人的id
                map.put("patientId", String.valueOf(busShopOrder.getFamilyId()));
            }
            map.put("userId", String.valueOf(busOrder.getPatientId()));
        }
    }

    /**
     * 设置药品订单支付参数的就诊人和用户id
     * @param map - 订单参数-需要设置的map
     * @param busOrder - 订单信息
     */
    private void setDrugOrderPayDtoWithUserIdAndPatientId(HashMap<String, String> map, BusOrder busOrder) {
        // 药品订单
        BusDrugsOrder busDrugsOrder = busDrugsOrderService.selectById(busOrder.getSubOrderId());
        if (busDrugsOrder == null) {
            throw new ServiceException("药品订单不存在");
        }
        BusPrescription busPrescription = busPrescriptionService.getById(busDrugsOrder.getPrescriptionId());
        if (busPrescription == null) {
            throw new ServiceException("处方信息不存在");
        }
        // 设置对应的药品订单的真实就诊人的id
        map.put("patientId", String.valueOf(busPrescription.getFamilyId()));
        // 设置对应的药品订单下单的用户id
        map.put("userId", String.valueOf(busOrder.getPatientId()));
    }

    @Override
    public void payUpdateDrugsAndGoodsOrderStatus(BusOrder busOrder) {
        String orderNo = busOrder.getOrderNo();
        // 查询总订单信息
        List<BusOrder> busOrders = busOrderService.selectOrederByNo(orderNo);
        BusOrder anyBusOrder = busOrders.get(0);
        log.info("查询交易订单信息={}", anyBusOrder);
        //这里做一下幂等判断，避免重复回调导致导致多次推送第三方企业
        if (Objects.nonNull(anyBusOrder) && !OrderStatusEnum.isWaitPay(anyBusOrder.getOrderStatus())) {
            log.warn("当前订单已经支付完成，无需再次修改订单状态, 订单号码 : {}", orderNo);
            return;
        }
        Date nowDate = DateUtils.getNowDate();
        //以下两个集合保存订单中商品和药品的id
        List<Long> goodsIds = new ArrayList<>();
        List<Long> drugIds = new ArrayList<>();
        //分别保存药品订单的就诊人和商品订单的就诊人：药品订单是一定有就诊人的，但是商品订单不一定有就诊人，如果没有就诊人，就不能加入随访
        Long drugFamilyId = null;
        Long goodsFamilyId = null;
        if (CollUtil.isNotEmpty(busOrders)) {
            String payWay = null;
            for (BusOrder order : busOrders) {
                // 药品订单
                if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                    // 推单到配送企业
                    pushDrugOrderToEnterprise(order);
                    // 发送医生端im消息提醒
                    pushDrImMsg(order);
                    payWay = order.getPayWay();
                    //获取订单下的药品id列表
                    List<BusPatientOrderInfo> orders = busDrugsOrderMapper.getDrugsIdsByOrderId(order.getSubOrderId(), order.getHospitalId());
                    drugIds.addAll(orders.stream().map(BusPatientOrderInfo::getId).collect(Collectors.toList()));
                    if (drugFamilyId == null && !CollectionUtils.isEmpty(orders)) {
                        drugFamilyId = orders.get(0).getFamilyId();
                    }
                } else {
                    // 推单到对应的商品供应商
                    pushShopOrderToEnterprise(order);
                    // 发送医生端im消息提醒
                    pushDrImMsg(order);
                    //获取订单下的商品id列表
                    List<BusPatientOrderInfo> orders = busOrderMapper.getGoodsIdsByOrderId(order.getSubOrderId(), order.getHospitalId());
                    goodsIds.addAll(orders.stream().map(BusPatientOrderInfo::getId).collect(Collectors.toList()));
                    if (goodsFamilyId == null && !CollectionUtils.isEmpty(orders)) {
                        goodsFamilyId = orders.get(0).getFamilyId();
                    }
                }
            }
            BusOrder order = new BusOrder();
            order.setOrderNo(orderNo);
            log.info("查询总订单信息={}", anyBusOrder);
            String orderStatus = getOrderStatus(busOrders);
            //虚拟商品订单支付完成后，订单状态改为待收货
            if (OrderStatusEnum.isWaitReceive(orderStatus)){
                order.setDeliveryTime(new Date());
            }
            order.setOrderStatus(orderStatus);
            order.setPaymentTime(nowDate);
            order.setTonglianTrxid(busOrder.getTonglianTrxid());

            Double relPrice = anyBusOrder.getRelPrice();
            Double freight = anyBusOrder.getFreight();
            BigDecimal relPriceDecimal = (null == relPrice) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(relPrice));
            BigDecimal freightDecimal = (null == freight) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(freight));
            order.setInvoiceStatus(relPriceDecimal.compareTo(freightDecimal) > 0 ? InvoiceStatusEnum.CAN_ISSUE.getStatus() : InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());

            busOrderService.updateOrderStatus(order);
            //发送药品订单监管事件
            sendChargeEvent(anyBusOrder.getHospitalId(), orderNo, payWay);
            //发生支付成功事件
            sendPaySuccessEvent(anyBusOrder.getHospitalId(), orderNo);
            log.debug("商品id列表：{}，药品id列表：{}", goodsIds, drugIds);
            //患者入组随访(异步处理)
            asyncJoinFollowUpService.joinInFollowUp(new PatientFollowUpJoinRuleDTO(goodsIds, drugIds, anyBusOrder.getHospitalId(),
                    anyBusOrder.getPatientId(), drugFamilyId, goodsFamilyId, orderNo));
        }
    }

    /**
     * 发送支付成功事件
     * @param hospitalId    医院ID
     * @param orderNo       总订单号
     */
    private void sendPaySuccessEvent(Long hospitalId, String orderNo) {
        OrderPaidEvent orderPaidEvent = new OrderPaidEvent();
        orderPaidEvent.setHospitalId(hospitalId);
        orderPaidEvent.setTotalOrderNo(orderNo);
        orderPaidEventProducer.send(orderPaidEvent);
    }


    private String getOrderStatus(List<BusOrder> busOrders) {
        if (busOrders.isEmpty()){
            throw new ServiceException("订单信息为空");
        }
        String drugsOrderType = OrderSendTypeEnum.IN_HOSPITAL.getCode();
        String shopOrderType = OrderSendTypeEnum.IN_HOSPITAL.getCode();
        List<BusHospitalOrderDTO> shopOrderList = new ArrayList<>();
        for (BusOrder busOrder : busOrders) {
            // 虚拟商品订单
            if (DeliveryTypeEnum.isVirtual(busOrder.getDeliveryType())){
                return OrderStatusEnum.WAIT_RECEIVE.getCode();
            }
            String subOrderType = busOrder.getSubOrderType();
            //自提 药品or商品订单； 一个总订单包含一个药品订单和一个商品订单 如果药品订单存在的话更新订单状态为待发货
            // 原始需求是 商品订单中的商品如果都是院内发货则支付后订单状态变为待自提；但是订单包含药品订单，则订单状态变为待发货（也就是走原流程）
            if ("0".equals(subOrderType)){
                return OrderStatusEnum.WAIT_DELIVER.getCode();
            }else {
                com.puree.hospital.app.domain.BusShopOrder busShopOrder = busShopOrderMapper.selectById(busOrder.getSubOrderId());
                LambdaQueryWrapper<BusOrderShop> eqed = Wrappers.lambdaQuery(BusOrderShop.class).eq(BusOrderShop::getOrderId, busShopOrder.getId());
                List<BusOrderShop> busOrderShops = busOrderShopMapper.selectList(eqed);
                for (BusOrderShop busOrderShop : busOrderShops) {
                    BusHospitalOrderDTO busHospitalOrderDTO = new BusHospitalOrderDTO();
                    busHospitalOrderDTO.setOrderNo(busShopOrder.getOrderNo());
                    busHospitalOrderDTO.setDrugsGoodsId(busOrderShop.getShopId());
                    busHospitalOrderDTO.setBarterOrNot(0);
                    shopOrderList.add(busHospitalOrderDTO);
                }
                shopOrderType = busShopOrder.getOrderType();
            }
        }

        //如果自提订单全部都是院内发货  则支付完后状态直接更新为待取件；否则支付完后状态直接更新为待发货
        if (DeliveryTypeEnum.isPickup(busOrders.get(0).getDeliveryType())&& OrderSendTypeEnum.isInHospital(shopOrderType)){
            R<OrderSendOutResultVO> listR = remoteDrugsOrderPackageService.insertSendOutGoods(shopOrderList, SecurityConstants.INNER);
            if (!listR.isSuccess()){
                log.error("自提订单发货失败！{}", listR.getMsg());
                throw new ServiceException("自提订单发货失败！");
            }
            return OrderStatusEnum.WAIT_PICK.getCode();
        }else {
            return OrderStatusEnum.WAIT_DELIVER.getCode();
        }
    }

    private void pushDrImMsg(BusOrder order) {
        if (null != order.getGroupId()) {
            CommunicationMessageDTO dto = new CommunicationMessageDTO();
            dto.setGroupId(order.getGroupId());
            dto.setFromAccount(TencentyunImConstants.ADMINISTRATOR);
            dto.setNickName(TencentyunImConstants.ADMINISTRATOR);
            dto.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            HashMap<String, Object> map = new HashMap<>();
            if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                map.put("type", CustomMsgConstants.PRESCRIPTION_PAID);
                String prescriptionNumber = busDrugsOrderMapper.selectPrescriptionNumber(order.getSubOrderId());
                map.put("prescriptionNumber", prescriptionNumber);
            } else {
                map.put("type", CustomMsgConstants.GOODS_PAID);
                LambdaQueryWrapper<BusOrderShop> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.select(BusOrderShop::getShopName)
                        .eq(BusOrderShop::getOrderId, order.getSubOrderId());
                List<BusOrderShop> shopList = busOrderShopMapper.selectList(lambdaQuery);
                map.put("goodsList", shopList);
            }
            dto.setMap(map);
            communicationMessageService.sendImMsg(dto);
        }
    }

    @Override
    public void pushShopOrderToEnterprise(BusOrder order) {
        log.info("推商品订单入参={}", JSON.toJSONString(order));
        R<BusShopOrder> r = remoteBusShopOrderService.selectShopOrderById(order.getSubOrderId());
        if (Constants.SUCCESS == r.getCode()) {
            //BusShopOrder busShopOrder = JSONObject.parseObject(JSONObject.toJSONString(r.getData()), BusShopOrder.class);
            BusShopOrder busShopOrder = r.getData();
            busShopOrder.setDeliveryType(order.getDeliveryType());
            busShopOrder.setOrderTime(order.getOrderTime());
            busShopOrder.setReceivingUser(order.getReceiver());
            busShopOrder.setReceivingTel(order.getReceivePhone());
            busShopOrder.setReceivingAddress(order.getReceiveAddress());
            // 查询商品订单详情
            LambdaQueryWrapper<BusOrderShop> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusOrderShop::getOrderId, busShopOrder.getId());
            List<BusOrderShop> busOrderShops = busOrderShopMapper.selectList(lambdaQuery);
            busOrderShops.removeIf(orderShop -> ObjectUtil.isNull(orderShop.getEnterpriseId()));
            if (CollUtil.isNotEmpty(busOrderShops)) {
                Map<Long, List<BusOrderShop>> map = busOrderShops.stream().collect(Collectors.groupingBy(BusOrderShop::getEnterpriseId));
                for (Long enterpriseId : map.keySet()) {
                    BusEnterpriseProductOrder enterpriseProductOrder = OrikaUtils.convert(busShopOrder, BusEnterpriseProductOrder.class);
                    enterpriseProductOrder.setId(null);
                    enterpriseProductOrder.setSubOrderId(busShopOrder.getId());
                    enterpriseProductOrder.setStatus("0");
                    enterpriseProductOrder.setCancelTime(null);
                    enterpriseProductOrder.setCompleteTime(null);
                    enterpriseProductOrder.setEnterpriseId(enterpriseId);
                    List<BusOrderShop> orderShopList = map.get(enterpriseId);
                    BigDecimal amount = new BigDecimal(0);
                    for (BusOrderShop orderShop : orderShopList) {
                        amount = amount.add(orderShop.getReferencePurchasePrice().multiply(new BigDecimal(orderShop.getQuantity())));
                    }
                    enterpriseProductOrder.setAmount(amount);
                    // 新增配送企业商品订单
                    busEnterpriseProductOrderMapper.insert(enterpriseProductOrder);
                    // 新增配送企业商品订单详情
                    List<BusEnterpriseProductOrderDetail> detailList = OrikaUtils.converts(orderShopList, BusEnterpriseProductOrderDetail.class);
                    for (BusEnterpriseProductOrderDetail detail : detailList) {
                        detail.setOrderId(enterpriseProductOrder.getId());
                        busEnterpriseProductOrderDetailMapper.insert(detail);
                    }
                }
            }
        } else {
            log.error("查询商品订单信息失败={}", r.getMsg());
        }
    }

    /**
     * 医保订单退款状态更新
     * @param orderNo - 订单号
     *
     */
    @Override
    public void updateInsuranceRefundAfterSaleOrderStatus(String orderNo) {
        // 判断该订单所有商品/药品是否全部成功退款，是订单状态变为已取消
        List<BusOrder> busOrders = busOrderService.selectOrederByNo(orderNo);
        BusOrder busOrder = getBusOrder(orderNo, busOrders);
        if (Objects.equals("0", busOrder.getSubOrderType())) {
            sendRefundEvent(busOrder.getHospitalId(), orderNo);
        }

        //发送订单同意退款事件通知
        OrderAgreeRefundEvent orderAgreeRefundEvent = new OrderAgreeRefundEvent();
        orderAgreeRefundEvent.setHospitalId(busOrder.getHospitalId());
        orderAgreeRefundEvent.setTotalOrderNo(orderNo);
        orderAgreeRefundEventProducer.send(orderAgreeRefundEvent);

        //患者终止随访(异步处理)
        PatientFollowUpJoinRuleDTO dto = new PatientFollowUpJoinRuleDTO();
        dto.setHospitalId(busOrder.getHospitalId());
        dto.setUserId(busOrder.getPatientId());
        dto.setFamilyId(null);
        dto.setOrderNo(orderNo);
        asyncJoinFollowUpService.terminateFollowUp(dto);

        // 查询售后信息
        List<BusOrderAfterSales> afterSales = busOrderAfterSalesService.selectAfterSalesListByOrderNo(orderNo);
        // 没有售后就不修改售后订单状态了
        if (CollectionUtils.isEmpty(afterSales)) {
            return;
        }
        // 有售后订单-并且售后订单的状态有退款过的，比如售后结束了，或者退款中的
        if (afterSales.stream()
                .allMatch(e -> OrderAfterSalesStatusEnum.AFTER_SALE.getCode().equals(e.getAfterSalesStatus())
                || OrderAfterSalesStatusEnum.UNDER_REFUND.getCode().equals(e.getAfterSalesStatus()))) {
            return;
        }
        // 修改售后状态
        List<Long> orderAfterSaleIds = afterSales.stream()
                .filter(e -> !OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode().equals(e.getAfterSalesStatus()))
                .map(BusOrderAfterSales::getId).collect(Collectors.toList());
        //批量更新售后状态
        busOrderAfterSalesService.updateAfterSaleStatusRefundSuccessByIds(orderAfterSaleIds);
    }

    /**
     * 获取订单信息
     * @param orderNo 订单号
     * @param busOrders 订单列表
     * @return 订单信息
     */
    private @NotNull BusOrder getBusOrder(String orderNo, List<BusOrder> busOrders) {
        if (CollectionUtils.isEmpty(busOrders)) {
            log.warn("未找到订单编号为：{} 的所有订单信息", orderNo);
            throw new ServiceException(String.format("未找到订单编号为：%s 的所有订单信息", orderNo));
        }
        BusOrder busOrder = busOrders.stream().filter(Objects::nonNull).findFirst().orElse(null);
        //如果是药品订单，则发送退款事件
        if (Objects.isNull(busOrder)) {
            log.warn("未找到订单编号为：{} 的订单信息", orderNo);
            throw new ServiceException(String.format("未找到订单编号为：%s 的任意订单信息", orderNo));
        }
        return busOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundTotalOrderStatus(Long orderAfterSaleId, String orderNo) {
        // 判断该订单所有商品/药品是否全部成功退款，是订单状态变为已取消
        List<BusOrder> busOrders = busOrderService.selectOrederByNo(orderNo);
        BusOrder busOrder = getBusOrder(orderNo, busOrders);
        //如果是药品订单，则发送退款事件
        if (Objects.equals("0", busOrder.getSubOrderType())) {
            sendRefundEvent(busOrder.getHospitalId(), orderNo);
        }
        if (Objects.isNull(orderAfterSaleId)) {
            //推送给患者 取消订单事件，由business迁移到这，有用户退费行为；有售后情况下不发取消通知，走下面对应的售后通知
            sendOrderCancelEventNotification(busOrder);
            return;
        }
        // 查询售后信息
        BusOrderAfterSales afterSales = busOrderAfterSalesService.selectAfterSales(orderAfterSaleId);
        if (null == afterSales || !OrderAfterSalesStatusEnum.AFTER_SALE.getCode().equals(afterSales.getAfterSalesStatus())) {
            // 修改售后状态
            if (null != afterSales) {
                BusOrderAfterSales busOrderAfterSales = new BusOrderAfterSales();
                busOrderAfterSales.setId(orderAfterSaleId);
                busOrderAfterSales.setRefundStatus(Integer.valueOf(CodeEnum.AGREE.getCode()));
                busOrderAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.AFTER_SALE.getCode());
                busOrderAfterSales.setReturnTime(DateUtils.getNowDate());
                busOrderAfterSalesService.updateAfterSaleStatus(busOrderAfterSales);
            }
            this.changeOrderStatus(busOrders);
            // 未发货释放库存
            if (OrderStatusEnum.WAIT_DELIVER.getCode().equals(busOrder.getOrderStatus())) {
                BusOrderAfterSales orderAfterSales = busOrderAfterSalesService.selectAfterSales(orderAfterSaleId);
                if (null != orderAfterSales && null != orderAfterSales.getPrescriptionId()) {
                    busPrescriptionService.increaseDrugsStock(busOrder.getHospitalId(), orderAfterSales.getPrescriptionId());
                }
            }

            // 发送订单同意退款事件通知
            OrderAgreeRefundEvent orderAgreeRefundEvent = new OrderAgreeRefundEvent();
            orderAgreeRefundEvent.setHospitalId(busOrder.getHospitalId());
            orderAgreeRefundEvent.setTotalOrderNo(orderNo);
            publisher.publishEvent(orderAgreeRefundEvent);

            // 判断是否全额退款
            Map<String, Boolean> tmpMap = new HashMap<>();
            tmpMap.put("isAllRefund", false);

            List<BusOrderAfterSales> orderAfterSalesList = orderAfterSalesMapper.selectList(new LambdaQueryWrapper<BusOrderAfterSales>().eq(BusOrderAfterSales::getOrderNo, orderNo)
                    .eq(BusOrderAfterSales::getAfterSalesStatus, "7").in(BusOrderAfterSales::getRefundType, Arrays.asList("0", "1"))); // 7-售后结束 ;  0仅退款 1退货退款
            BigDecimal totalRefund = BigDecimal.ZERO;
            if (null != orderAfterSalesList) {
                for (BusOrderAfterSales e : orderAfterSalesList) {
                    if (null != e.getRefundAmount()) {
                        totalRefund = totalRefund.add(e.getRefundAmount());
                    }
                }
            }

            BigDecimal relPriceDecimal = (null == busOrders.get(0).getRelPrice()) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(busOrders.get(0).getRelPrice()));
            BigDecimal freightDecimal = (null == busOrders.get(0).getFreight()) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(busOrders.get(0).getFreight()));
            if (0 == totalRefund.compareTo(relPriceDecimal.subtract(freightDecimal))
                    || 0 == totalRefund.compareTo(relPriceDecimal)) {

                // 全额退款
                tmpMap.put("isAllRefund", true);

                if (Arrays.asList(InvoiceStatusEnum.CAN_ISSUE.getStatus(), InvoiceStatusEnum.TO_ISSUE.getStatus(), InvoiceStatusEnum.ISSUE_FAIL.getStatus()).contains(busOrders.get(0).getInvoiceStatus())) {
                    busOrders.forEach(e -> e.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus()));
                    busOrderMapper.updateBusOrderInvoice(busOrders.get(0));
                }

            }

            //----------宏捷荣--------------
            BusOrder anyBusOrder = busOrders.get(0);
            if (InvoiceStatusEnum.ISSUE_SUCCESS.getStatus().equals(anyBusOrder.getInvoiceStatus())) {
                if (InvoiceTypeEnum.TAX_CONTROl.getId().equals(anyBusOrder.getInvoiceType())) {
                    CompletableFuture.runAsync(() -> {
                        try {
                            orderInvoiceService.taxControlOrderRedInvoiceAndThenBlueInvoice(busOrders, orderAfterSaleId, tmpMap.get("isAllRefund"));
                        } catch (IOException | NoSuchPaddingException | IllegalBlockSizeException |
                                 NoSuchAlgorithmException | BadPaddingException | InvalidKeyException |
                                 UnrecoverableKeyException | CertificateException | KeyStoreException |
                                 KeyManagementException e) {
                            throw new RuntimeException(e);
                        }
                    });
                }
            }
            //----------宏捷荣--------------

            //患者终止随访(异步处理)
            PatientFollowUpJoinRuleDTO dto = new PatientFollowUpJoinRuleDTO();
            dto.setHospitalId(busOrder.getHospitalId());
            dto.setUserId(busOrder.getPatientId());
            dto.setFamilyId(null);
            dto.setOrderNo(orderNo);
            asyncJoinFollowUpService.terminateFollowUp(dto);

        }
    }

    /**
     *  发送订单取消事件通知
     * @param order 订单信息
     */
    private void sendOrderCancelEventNotification(BusOrder order) {
        // 组装订单取消事件通知
        OrderCancelEvent orderCancelEvent = new OrderCancelEvent();
        orderCancelEvent.setHospitalId(order.getHospitalId());
        orderCancelEvent.setTotalOrderNo(order.getOrderNo());
        orderCancelEvent.setJumpHomePage(Boolean.TRUE);
        orderCancelEvent.setHasRefund(Boolean.TRUE);
        publisher.publishEvent(orderCancelEvent);
    }

    private void changeOrderStatus(List<BusOrder> busOrders) {
        String orderNo = busOrders.get(0).getOrderNo();
        // 查询该订单仅退款/退货且退款且售后完成的售后单
        BusOrderAfterSales orderAfterSales = new BusOrderAfterSales();
        orderAfterSales.setOrderNo(orderNo);
        orderAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.AFTER_SALE.getCode());
        orderAfterSales.setRefundType(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode());
        List<BusOrderAfterSales> orderAfterSalesList = busOrderAfterSalesService.selectAfterSaleList(orderAfterSales);
        if (StringUtils.isNotEmpty(orderAfterSalesList)) {
            log.info("售后完成的售后单信息={}", JSONObject.toJSONString(orderAfterSalesList));
            // 定义订单可申请的售后单数
            int i = 0;
            // 定义售后otc和商品的对象集合
            List<DrugsAndGoodsDTO> dtoList = new ArrayList<>();
            // 定义售后云仓/配送企业商品集合
            List<DrugsAndGoodsDTO> enterpriseGoodsList = new ArrayList<>();
            for (BusOrder order : busOrders) {
                if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                    BusDrugsOrder drugsOrder = busDrugsOrderService.selectOrderInfo(order.getSubOrderId());
                    if (ObjectUtil.isNotNull(drugsOrder)) {
                        if (ObjectUtil.isNotNull(drugsOrder.getPrescriptionId())) {
                            i += 1;
                        } else {
                            LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
                            lambdaQuery.eq(BusOtcDrugs::getDrugsOrderId, order.getSubOrderId());
                            List<BusOtcDrugs> otcDrugsList = busOtcDrugsMapper.selectList(lambdaQuery);
                            for (BusOtcDrugs otcDrugs : otcDrugsList) {
                                DrugsAndGoodsDTO dto = new DrugsAndGoodsDTO();
                                dto.setType(CodeEnum.NO.getCode());
                                dto.setSubOrderId(drugsOrder.getId());
                                dto.setQuantity(otcDrugs.getQuantity());
                                dto.setBusinessId(otcDrugs.getDrugsId());
                                dtoList.add(dto);
                            }
                            i = i + otcDrugsList.size();
                        }
                    }
                } else {
                    LambdaQueryWrapper<BusOrderShop> lambdaQuery = Wrappers.lambdaQuery();
                    lambdaQuery.eq(BusOrderShop::getOrderId, order.getSubOrderId());
                    List<BusOrderShop> shopList = busOrderShopMapper.selectList(lambdaQuery);
                    for (BusOrderShop shop : shopList) {
                        DrugsAndGoodsDTO dto = new DrugsAndGoodsDTO();
                        dto.setType(CodeEnum.YES.getCode());
                        dto.setSubOrderId(order.getSubOrderId());
                        dto.setQuantity(shop.getQuantity());
                        dto.setBusinessId(shop.getShopId());
                        dtoList.add(dto);
                        if (ObjectUtil.isNotNull(shop.getEnterpriseId())) {
                            dto.setEnterpriseId(shop.getEnterpriseId());
                            enterpriseGoodsList.add(dto);
                        }
                    }
                    i = i + shopList.size();
                }
            }
            // 所有的售后单都售后结束
            if (i == orderAfterSalesList.size()) {
                // 定义售后单中的otc/商品数量全部售后
                int j = 0;
                if (CollUtil.isNotEmpty(dtoList)) {
                    for (BusOrderAfterSales afterSales : orderAfterSalesList) {
                        for (DrugsAndGoodsDTO dto : dtoList) {
                            if (dto.getType().equals(afterSales.getAfterSalesType()) &&
                                    dto.getSubOrderId().equals(afterSales.getOrderId()) &&
                                    dto.getQuantity().equals(afterSales.getQuantity()) &&
                                    dto.getBusinessId().equals(afterSales.getGoodsId())) {
                                j += 1;
                                break;
                            }
                        }
                    }
                }
                // 售后单是处方或otc/商品购买数量全部售后
                if (CollUtil.isEmpty(dtoList) || j == dtoList.size()) {
                    // 修改订单状态为已取消
                    BusOrder busOrder = new BusOrder();
                    busOrder.setOrderNo(orderNo);
                    busOrder.setOrderStatus(OrderStatusEnum.CANCEL.getCode());
                    busOrder.setCancelTime(DateUtils.getNowDate());
                    busOrderService.updateOrderStatus(busOrder);
                }
            }

            // 修改云仓/配送企业商品订单为已取消
            if (CollUtil.isNotEmpty(enterpriseGoodsList)) {
                log.info("云仓/配送企业商品={}", JSONObject.toJSONString(enterpriseGoodsList));
                orderAfterSalesList.removeIf(o -> CodeEnum.NO.getCode().equals(o.getAfterSalesType()));
                log.info("云仓/配送企业售后商品={}", JSONObject.toJSONString(orderAfterSalesList));
                // 根据配送企业ID分组
                Map<Long, List<DrugsAndGoodsDTO>> listMap = enterpriseGoodsList.stream().collect(Collectors.groupingBy(DrugsAndGoodsDTO::getEnterpriseId));
                for (Long enterpriseId : listMap.keySet()) {
                    List<DrugsAndGoodsDTO> goodsList = listMap.get(enterpriseId);
                    int n = 0;
                    for (DrugsAndGoodsDTO dto : goodsList) {
                        for (BusOrderAfterSales afterSales : orderAfterSalesList) {
                            if (dto.getSubOrderId().equals(afterSales.getOrderId()) &&
                                    dto.getQuantity().equals(afterSales.getQuantity()) &&
                                    dto.getBusinessId().equals(afterSales.getGoodsId())) {
                                n += 1;
                                break;
                            }
                        }
                    }
                    if (n == goodsList.size()) {
                        LambdaQueryWrapper<BusEnterpriseProductOrder> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusEnterpriseProductOrder::getEnterpriseId, enterpriseId);
                        lambdaQuery.eq(BusEnterpriseProductOrder::getSubOrderId, goodsList.get(0).getSubOrderId());
                        BusEnterpriseProductOrder enterpriseProductOrder = new BusEnterpriseProductOrder();
                        enterpriseProductOrder.setStatus(EntOrderStatusEnum.CANCEL.getCode());
                        busEnterpriseProductOrderMapper.update(enterpriseProductOrder, lambdaQuery);
                    }
                }
            }
        }
    }

    /**
     * 检查订单openid和appid-是否是合作机构订单-是就替换
     * @param map - 支付订单信息
     * @param hospitalId - 医院ID
     * @param partnersCode - 合作机构编码
     * @param patientId - 换着id
     * @param pCode - 合作机构标识 - null标识机构 1标识医院
     */
    @Override
    public void checkAppidAndOpenid(HashMap<String, String> map, Long hospitalId, String partnersCode, Long patientId, Integer pCode) {
        if (!(ObjectUtil.isNull(pCode) || YesNoEnum.YES.getCode().equals(pCode))) {
            return;
        }
        if (StringUtils.isNotEmpty(partnersCode)) {
            // 查询机构信息
            BusPartners busPartners = busPartnersService.selectPartners(hospitalId, partnersCode);
            log.info("机构信息={}", busPartners.getId());
            map.put("appId", busPartners.getAppId());
            if (YesNoEnum.YES.getCode().equals(pCode)) {
                // 查询合作机构患者openid
                String openId = busPatientPartnersService.queryOpenidByCode(partnersCode, hospitalId, patientId);
                map.put("openid", openId);
            }
        } else {
            // 查询医院患者openid
            BusPatientHospitalDto dto = new BusPatientHospitalDto();
            dto.setHospitalId(hospitalId);
            dto.setPatientId(patientId);
            BusPatientHospitalVo patientHospitalVo = busPatientHospitalService.selectPatientHospitalByPid(dto);
            if (ClientTypeEnum.isWxUniApp(map.get("appType"))){
                map.put("openid", patientHospitalVo.getUniAppOpenid());
            }else {
                map.put("openid", patientHospitalVo.getOpenid());
            }
        }

    }

    /**
     * 校验订单是否医保支付成功
     *
     * @param busOrder
     * @return
     */
    private Boolean checkOrder(BusOrder busOrder) {
        log.info("支付交易订单信息={}", busOrder);
        if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay()) &&
                OrderStatusEnum.FREIGHT_WAIT_PAY.getCode().equals(busOrder.getOrderStatus())) {
            return true;
        }
        return false;
    }

    /**
     * 发送取消问诊订单事件
     *
     * @param order 问诊订单信息
     */

    private void sendCancelOrderEvent(BusConsultationOrder order) {
        if (Objects.nonNull(order)) {
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(order.getHospitalId(), RegulatoryEventTypeEnum.CONSULTATION_CANCEL_ORDER, order.getId() + "");
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        }
    }

    /**
     * 发送收费事件
     *
     * @param orderNo 问诊订单信息
     * @param payWay  支付方式
     */
    private void sendChargeEvent(Long hospitalId, String orderNo, String payWay) {
        if (Objects.nonNull(orderNo) && StringUtils.isNotBlank(payWay)) {
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(hospitalId, RegulatoryEventTypeEnum.CHARGE, orderNo);
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        }
    }

    /**
     * 发送退费事件
     *
     * @param orderNo 问诊订单信息
     */
    private void sendRefundEvent(Long hospitalId, String orderNo) {
        if (Objects.nonNull(orderNo)) {
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(hospitalId, RegulatoryEventTypeEnum.REFUND, orderNo);
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        }
    }
}

package com.puree.hospital.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.domain.BusAfterSale;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.ConsultationOrderDto;
import com.puree.hospital.app.queue.QueueConstant;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.ConsultationOrderCancelProducer;
import com.puree.hospital.app.queue.producer.VideoConsultationCancelProducer;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusConsultationSettingsService;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 问诊订单相关控制器
 */
@RestController
@RequestMapping("/consultation/order")
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class BusConsultationOrderController extends BaseController {
    private final IBusPatientService busPatientService;
    private final IBusDoctorService busDoctorService;
    private final IBusConsultationOrderService consultationOrderService;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    @Autowired
    @Lazy
    private ConsultationOrderCancelProducer consultationOrderCancelProducer;
    @Autowired
    @Lazy
    private VideoConsultationCancelProducer videoConsultationCancelProducer;

    @Resource
    private PureeRabbitProducer pureeRabbitProducer;

    /**
     * 新增问诊订单
     *
     * @param orderDto
     * @return
     */
    @PostMapping("create")
    @Log(title = "新增问诊订单", businessType = BusinessType.INSERT)
    public AjaxResult create(@RequestBody ConsultationOrderDto orderDto) {
        if (Objects.isNull(orderDto.getAmount())) {
            throw new ServiceException("网络异常，请重新发起问诊！");
        }
        consultationOrderService.checkHasMedicalRecord(orderDto);
        String age = AgeUtil.getAgeDetail(orderDto.getFamilyAge());
        orderDto.setFamilyAge(age);
        String partnersCode = SecurityUtils.getPartnerscode();
        if (StringUtils.isNotEmpty(partnersCode)) {
            orderDto.setPartnersCode(partnersCode);
        }
        String isCheck = "isCheck";
        JSONObject object = consultationOrderService.checkNoPayOrder(orderDto);
        if (object.getBoolean(isCheck)) {
            return AjaxResult.success(object);
        }
        long consultationOrderId = consultationOrderService.insert(orderDto);
        //此处的hospitalId为空，则取orderDto.getQuickConsultation().getHospitalId()
        Long hospitalId = orderDto.getHospitalId();
        if (Objects.isNull(hospitalId) && Objects.nonNull(orderDto.getQuickConsultation())) {
            hospitalId = orderDto.getQuickConsultation().getHospitalId();
        }
        RegulatoryCollectEvent event = new RegulatoryCollectEvent(hospitalId, RegulatoryEventTypeEnum.CONSULTATION_ORDER, consultationOrderId + "");
        pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        return AjaxResult.success(consultationOrderId);
    }

    /**
     * 获取订单详细信息
     *
     * @param id
     * @return
     */
    @GetMapping("queryInfo")
    @Log(title = "获取订单详细信息", businessType = BusinessType.OTHER)
    public AjaxResult queryOrderInfo(Long id) {
        BusConsultationOrder busConsultationOrder = consultationOrderService.queryOrderInfo(id);
        return AjaxResult.success(busConsultationOrder);
    }

    /**
     * 取消问诊订单
     *
     * @param orderNo
     * @return
     */
    @GetMapping("/cancel")
    @Log(title = "取消问诊订单", businessType = BusinessType.UPDATE)
    public AjaxResult cancelOrder(@RequestParam("orderNo") String orderNo,
                                  @RequestParam("orderType") String orderType) {
        BusConsultationOrder order = consultationOrderService.queryConsultationOrderByOrderNo(orderNo);
        if (order == null){
            return AjaxResult.error("订单不存在！刷新页面重试！");
        }
        order.setOrderType(orderType);
        int result = consultationOrderService.cancelOrder(order);
        RegulatoryCollectEvent event = new RegulatoryCollectEvent(order.getHospitalId(), RegulatoryEventTypeEnum.CONSULTATION_CANCEL_ORDER, order.getId() + "");
        pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        return toAjax(result);
    }

    /**
     * 待支付的订单加入队列
     *
     * @param id
     * @return
     */
    @PostMapping("delayQueue")
    @Log(title = "待支付的订单加入队列", businessType = BusinessType.OTHER)
    public AjaxResult delayQueue(@RequestParam("id") Long id) {
        SMSLoginUser loginUserInfo = busPatientService.queryPatientInfo();
        Long hospitalId = loginUserInfo.getHospitalId();
        logger.info("患者信息=" + loginUserInfo);
        BusConsultationSettings bs = new BusConsultationSettings();
        bs.setHospitalId(hospitalId);
        bs = busConsultationSettingsService.selectOne(bs);
        Message message = new Message();
        message.setId(id);
        message.setHospitalId(hospitalId);

        if (bs == null || bs.getConsultationOrderExpire() == null) {
            message.setFireTime(QueueConstant.QUEUE_CONSULTATION_ORDER_CANCEL_TIME);
        } else {
            long time = bs.getConsultationOrderExpire() * 60 * 60 * 1000;
            message.setFireTime(time);
        }
        consultationOrderCancelProducer.delaySend(message, message.getFireTime());
        message.setFireTime(QueueConstant.QUEUE_VIDEO_CONSULTATION_CANCEL_TIME);
        boolean ret = videoConsultationCancelProducer.delaySend(message, message.getFireTime());
        return ret ? AjaxResult.success() : AjaxResult.error("加入队列失败！");
    }

    /**
     * 患者端订单列表
     *
     * @return 返回用户订单
     */
    @Log(title = "患者端订单列表")
    @GetMapping("/patient/list")
    public TableDataInfo patientOrderList(BusConsultationOrder consultationOrder) {
        String partnersCode = SecurityUtils.getPartnerscode();
        logger.info("问诊订单机构code={}",partnersCode);
        if (StringUtils.isNotEmpty(partnersCode)) {
            consultationOrder.setPartnersCode(partnersCode);
        }
        // 根据token，从redis中获取患者信息
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        consultationOrder.setHospitalId(loginUser.getHospitalId());
        consultationOrder.setPatientId(loginUser.getUserid());
        startPage();
        return getDataTable(consultationOrderService.patientOrderList(consultationOrder));
    }

    /**
     * 医生端订单列表
     *
     * @return
     */
    @Log(title = "医生端订单列表")
    @GetMapping("doctor/list")
    public TableDataInfo doctorOrderList(@RequestParam("hospitalId") Long hospitalId,
                                         @RequestParam("doctorId") Long doctorId) {
        startPage();
        return getDataTable(consultationOrderService.doctorOrderList(hospitalId, doctorId));
    }

    /**
     * 申请退款
     *
     * @param afterSale
     * @return
     */
    @PostMapping("apply/refund")
    @Log(title = "申请退款", businessType = BusinessType.OTHER)
    public AjaxResult applyRefund(@RequestBody BusAfterSale afterSale) {
        if (logger.isDebugEnabled()) {
            logger.debug("申请退款入参：{}", afterSale);
        }
        return AjaxResult.success(consultationOrderService.applyRefund(afterSale));
    }

    /**
     * 视频订单患者报到
     *
     * @param orderDto
     * @return
     */
    @PostMapping("register")
    @Log(title = "视频订单患者报到", businessType = BusinessType.UPDATE)
    public AjaxResult register(@RequestBody ConsultationOrderDto orderDto) {
        return AjaxResult.success(consultationOrderService.register(orderDto));
    }

    /**
     * 医生叫号逻辑过号
     *
     * @param orderDto
     * @return
     */
    @PostMapping("guohao")
    @Log(title = "医生叫号逻辑过号", businessType = BusinessType.UPDATE)
    public AjaxResult guoHao(@RequestBody ConsultationOrderDto orderDto) {
        return AjaxResult.success(consultationOrderService.guohao(orderDto));
    }

    /**
     * 查询最新叫号人员
     *
     * @param orderDto
     * @return
     */
    @GetMapping("call")
    @Log(title = "查询最新叫号人员", businessType = BusinessType.UPDATE)
    public AjaxResult call(ConsultationOrderDto orderDto) {
        // 查询医生信息
        SMSLoginUser loginUser = busDoctorService.queryDrInfo();
        orderDto.setDoctorId(loginUser.getUserid());
        return AjaxResult.success(consultationOrderService.call(orderDto));
    }

    /**
     * 视频问诊订单修改状态
     *
     * @param order 修改视频问诊订单信息
     * @return 修改结果
     */
    @PostMapping("videoOrderUpdate")
    @Log(title = "视频问诊订单修改状态", businessType = BusinessType.UPDATE)
    public AjaxResult videoOrderUpdate(@RequestBody BusConsultationOrder order) {
        return AjaxResult.success(consultationOrderService.videoOrderUpdate(order));
    }

    /**
     * 问诊订单结束问诊
     *
     * @param order
     * @return
     */
    @PostMapping("endOrder")
    @Log(title = "问诊订单结束问诊", businessType = BusinessType.UPDATE)
    public AjaxResult endOrder(@RequestBody BusConsultationOrder order) {
        Long consultationOrderId = consultationOrderService.endOrder(order);

        //问诊结束事件上报
        RegulatoryCollectEvent event = new RegulatoryCollectEvent(order.getHospitalId(), RegulatoryEventTypeEnum.CONSULTATION_END, consultationOrderId + "");
        pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);


        return AjaxResult.success(1);
    }

    /**
     * 校验最新订单是否是进行中
     *
     * @param order
     * @return
     */
    @PostMapping("check")
    @Log(title = "校验最新订单是否是进行中", businessType = BusinessType.OTHER)
    public AjaxResult check(@RequestBody BusConsultationOrder order) {
        return AjaxResult.success(consultationOrderService.check(order));
    }

    /**
     * 查询最新问诊订单
     *
     * @param order
     * @return
     */
    @PostMapping("queryNewestOrder")
    @Log(title = "查询最新问诊订单", businessType = BusinessType.OTHER)
    public AjaxResult queryNewestOrder(@RequestBody BusConsultationOrder order) {
        return AjaxResult.success(consultationOrderService.selectNewest(order));
    }

    /**
     * 根据订单编号查询订单
     *
     * @param orderNo
     * @return
     */
    @PostMapping("queryOrderNo")
    @Log(title = "根据订单编号查询订单", businessType = BusinessType.OTHER)
    public AjaxResult queryOrderNo(@RequestParam("orderNo") String orderNo) {
        return AjaxResult.success(consultationOrderService.queryOrderNo(orderNo));
    }

    /**
     * 批量退号
     *
     * @param orderDto 订单信息
     * @return ajax
     */
    @PostMapping("backNumber")
    @Log(title = "批量退号", businessType = BusinessType.OTHER)
    public AjaxResult backNumber(@RequestBody ConsultationOrderDto orderDto) {
        return AjaxResult.success(consultationOrderService.backNumber(orderDto));
    }

    /**
     * 患者端-问诊订单详情
     *
     * @param id
     * @return
     */
    @Log(title = "问诊订单详情")
    @GetMapping("/query/info")
    public AjaxResult queryInfo(@RequestParam("id") Long id) {
        // 查询患者信息
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        return AjaxResult.success(consultationOrderService.queryInfo(id, loginUser.getUserid()));
    }

    /**
     * 患者端-问诊订单详情
     *
     * @param ids   问诊单ID
     */
    @Log(title = "内部查询问诊订单详情")
    @GetMapping("/feign/query/info")
    public AjaxResult feignQueryInfo(@RequestParam("ids") List<Long> ids) {
        return AjaxResult.success(consultationOrderService.listByIds(ids));
    }

    /**
     * 删除订单
     *
     * @param id
     * @return
     */
    @DeleteMapping("remove")
    @Log(title = "删除问诊订单", businessType = BusinessType.DELETE)
    public AjaxResult remove(@RequestParam("id") Long id) {
        return toAjax(consultationOrderService.remove(id));
    }

}

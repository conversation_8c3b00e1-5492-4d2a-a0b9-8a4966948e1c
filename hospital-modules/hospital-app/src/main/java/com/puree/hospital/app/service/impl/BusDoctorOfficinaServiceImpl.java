package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDirectoryDrugs;
import com.puree.hospital.app.domain.BusDoctorDepartment;
import com.puree.hospital.app.domain.BusDrugsInstruction;
import com.puree.hospital.app.domain.BusEnterpriseDrugs;
import com.puree.hospital.app.domain.BusEnterpriseDrugsPrice;
import com.puree.hospital.app.domain.BusHospitalOfficina;
import com.puree.hospital.app.domain.dto.BusDrugsDto;
import com.puree.hospital.app.domain.dto.RxDrugQueryDTO;
import com.puree.hospital.app.domain.dto.HospitalOfficinaDrugsDTO;
import com.puree.hospital.app.domain.vo.BusDoctorDepartmentVo;
import com.puree.hospital.app.domain.vo.DoctorOfficinaDrugsVo;
import com.puree.hospital.app.domain.vo.DrugsClassifyVo;
import com.puree.hospital.app.helper.SysDictDataHelper;
import com.puree.hospital.app.mapper.BusConsultationSettingsMapper;
import com.puree.hospital.app.mapper.BusDirectoryDrugsMapper;
import com.puree.hospital.app.mapper.BusDoctorDepartmentMapper;
import com.puree.hospital.app.mapper.BusDoctorOfficinaMapper;
import com.puree.hospital.app.mapper.BusDrugsInstructionMapper;
import com.puree.hospital.app.mapper.BusEnterpriseDrugsPriceMapper;
import com.puree.hospital.app.mapper.BusHospitalOfficinaMapper;
import com.puree.hospital.app.service.IBusBizDepartmentService;
import com.puree.hospital.app.service.IBusDoctorCommonlyUsedService;
import com.puree.hospital.app.service.IBusDoctorOfficinaService;
import com.puree.hospital.app.service.IBusPrescriptionDrugsService;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DirectoryTypeEnum;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.domain.MapResult;
import com.puree.hospital.system.api.model.SysDictData;
import com.puree.hospital.system.api.model.constant.SysDictTypeConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 药品药方信息
 * </p>
 *
 * <AUTHOR>
 * @date 2021/12/20
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorOfficinaServiceImpl implements IBusDoctorOfficinaService {

    private final BusDoctorOfficinaMapper doctorOfficinaMapper;
    private final BusConsultationSettingsMapper busConsultationSettingsMapper;
    private final BusHospitalOfficinaMapper busHospitalOfficinaMapper;
    private final BusEnterpriseDrugsPriceMapper busEnterpriseDrugsPriceMapper;
    private final BusDrugsInstructionMapper busDrugsInstructionMapper;
    private final BusDirectoryDrugsMapper busDirectoryDrugsMapper;
    private final BusDoctorDepartmentMapper busDoctorDepartmentMapper;
    @Lazy
    @Resource
    private IBusDoctorCommonlyUsedService doctorCommonlyUsedService;
    @Lazy
    @Resource
    private IBusPrescriptionDrugsService prescriptionDrugsService;
    @Lazy
    @Resource
    private IBusBizDepartmentService busBizDepartmentService;

    private final SysDictDataHelper sysDictDataHelper;

    @Override
    public List<DrugsClassifyVo> selectClassificationList(RxDrugQueryDTO drugsQueryDTO) {
        if (Objects.isNull(drugsQueryDTO) || Objects.isNull(drugsQueryDTO.getHospitalId())) {
            return Lists.newArrayList();
        }
        //兼容考虑，默认查询院内药品目录
        String directoryType = StringUtils.isNotEmpty(drugsQueryDTO.getDirectoryType()) ? drugsQueryDTO.getDirectoryType() : DirectoryTypeEnum.MM_INNER.getType();
        // 查询所有一级分类
        List<DrugsClassifyVo> vos = doctorOfficinaMapper.selectClassificationList();
        // 查询药房药品所有的分类ID
        List<String> lists = doctorOfficinaMapper.selectClassifyIds(drugsQueryDTO.getHospitalId(), directoryType);
        String classifyIds = String.join(",", lists);
        if (StringUtils.isNotEmpty(classifyIds)) {
            List<String> list = Arrays.asList(classifyIds.split(","));
            List<String> classifyIdList = list.stream().distinct().collect(Collectors.toList());
            vos.removeIf(vo -> !classifyIdList.contains(String.valueOf(vo.getId())));
            return vos;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<DoctorOfficinaDrugsVo> selectDrugsList(RxDrugQueryDTO rxDrugQueryDTO) {
        // 兼容型考虑
        if (StringUtils.isBlank(rxDrugQueryDTO.getDirectoryType())) {
            rxDrugQueryDTO.setDirectoryType(DirectoryTypeEnum.MM_INNER.getType());
        }
        //查询列表
        List<DoctorOfficinaDrugsVo> list = doctorOfficinaMapper.selectDrugsList(rxDrugQueryDTO);

        //设置药品剂型
        Map<Long, SysDictData> dictDataMap = sysDictDataHelper.getDictDataMap(SysDictTypeConstant.DRUGS_DOSAGE_FORM);
        list.forEach(d -> {
            SysDictData sysDictData = dictDataMap.getOrDefault(d.getDrugsDosageForm(), new SysDictData());
            d.setDrugsDosageFormName(sysDictData.getDictLabel());
        });

        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        // 查询医生常用药品id
        List<Long> drugsIds = doctorCommonlyUsedService.selectListDrugsId(rxDrugQueryDTO.getHospitalId(),
                rxDrugQueryDTO.getDoctorId(),
                rxDrugQueryDTO.getDirectoryType());
        // 查询医生已开具的药品id(增加药品目录类型)
        List<Long> listDrugsId = prescriptionDrugsService.selectListDrugsId(rxDrugQueryDTO.getHospitalId(),
                rxDrugQueryDTO.getDoctorId(),
                rxDrugQueryDTO.getDirectoryType());
        Map<Long, SysDictData> packageUnitMap = sysDictDataHelper.getDictDataMap(SysDictTypeConstant.DRUGS_PACKAGING_UNIT);
        list.forEach(d -> {
            //整理数据
            makeOfficinaDrugsVo(d, packageUnitMap);
            // 判断是否是常用药
            if (drugsIds.contains(d.getDrugsId())) {
                d.setStatus(YesNoEnum.YES.getCode());
            } else {
                d.setStatus(YesNoEnum.NO.getCode());
            }
            // 判断是否是历史用药
            if (listDrugsId.contains(d.getDrugsId())) {
                d.setUsedStatus(YesNoEnum.YES.getCode());
            } else {
                d.setUsedStatus(YesNoEnum.NO.getCode());
            }
            // 查询配送企业库存
//        queryEnterpriseStock(drugsInfo);
        });
        return list;
    }

    @Override
    public DoctorOfficinaDrugsVo selectDrugsInfo(RxDrugQueryDTO rxDrugQueryDTO) {
        if (Objects.isNull(rxDrugQueryDTO)
                || Objects.isNull(rxDrugQueryDTO.getHospitalId())
                || Objects.isNull(rxDrugQueryDTO.getDrugsId())) {
            return null;
        }
        // 兼容型考虑
        if (StringUtils.isBlank(rxDrugQueryDTO.getDirectoryType())) {
            rxDrugQueryDTO.setDirectoryType(DirectoryTypeEnum.MM_INNER.getType());
        }
        DoctorOfficinaDrugsVo drugsInfo = doctorOfficinaMapper.selectDrugsInfo(rxDrugQueryDTO);

        Map<Long, SysDictData> dictDataMap = sysDictDataHelper.getDictDataMap(SysDictTypeConstant.DRUGS_DOSAGE_FORM);

        SysDictData sysDictData = dictDataMap.getOrDefault(drugsInfo.getDrugsDosageForm(), new SysDictData());
        drugsInfo.setDrugsDosageFormName(sysDictData.getDictLabel());


        if (Objects.isNull(drugsInfo)) {
            throw new ServiceException("该用药预订单药品已被下架，请重新选择");
        }
        Map<Long, SysDictData> packageUnitMap = sysDictDataHelper.getDictDataMap(SysDictTypeConstant.DRUGS_PACKAGING_UNIT);
        makeOfficinaDrugsVo(drugsInfo, packageUnitMap);
        // 查询配送企业库存
//        queryEnterpriseStock(drugsInfo);

        // 查询有无说明书
        LambdaQueryWrapper<BusDrugsInstruction> queryInstructionWrapper = new LambdaQueryWrapper<BusDrugsInstruction>()
                .eq(BusDrugsInstruction::getDrugsId, rxDrugQueryDTO.getDrugsId());
        BusDrugsInstruction busDrugsInstruction = busDrugsInstructionMapper.selectOne(queryInstructionWrapper);
        if(null != busDrugsInstruction){
            // 有说明书为 true，该实体说明书默认为无 false
            drugsInfo.setIsDrugsInstruction(true);
            drugsInfo.setDrugsInstruction(busDrugsInstruction);
        }
        return drugsInfo;
    }

    @Override
    public void checkPrescriptionAddDrug(RxDrugQueryDTO rxDrugQueryDTO) {
        if (StringUtils.isBlank(rxDrugQueryDTO.getDirectoryType())) {
            rxDrugQueryDTO.setDirectoryType(DirectoryTypeEnum.MM_INNER.getType());
        }
        BusDirectoryDrugs directoryDrugs = busDirectoryDrugsMapper.selectOne(new LambdaQueryWrapper<BusDirectoryDrugs>()
                .eq(BusDirectoryDrugs::getDrugsId, rxDrugQueryDTO.getDrugsId())
                .eq(BusDirectoryDrugs::getHospitalId, rxDrugQueryDTO.getHospitalId())
                .eq(BusDirectoryDrugs::getDirectoryType, rxDrugQueryDTO.getDirectoryType())
                .eq(BusDirectoryDrugs::getDirectoryStatus, EnableStatusEnum.ENABLED.getStatus()));
        if (Objects.isNull(directoryDrugs)) {
            throw new ServiceException("当前药品不在医院的药品目录内或已下架");
        }
        //双通到药品则直接返回
        if (!DirectoryTypeEnum.isDualChannel(directoryDrugs.getDirectoryType())) {
            // 查询药品在药方中的信息
            LambdaQueryWrapper<BusHospitalOfficina> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusHospitalOfficina::getDrugsId, rxDrugQueryDTO.getDrugsId());
            queryWrapper.eq(BusHospitalOfficina::getHospitalId, rxDrugQueryDTO.getHospitalId());
            BusHospitalOfficina officina = busHospitalOfficinaMapper.selectOne(queryWrapper);
            if (Objects.isNull(officina) || !EnableStatusEnum.isEnabled(officina.getStatus())) {
                throw new ServiceException("该药品已下架");
            }
        }
        if (Objects.nonNull(rxDrugQueryDTO.getBizDepartmentId())) {
            // 根据业务科室ID查询可以开具的药品id
            Long count = doctorOfficinaMapper.countDrugByBizDepartmentId(rxDrugQueryDTO);
            if (Objects.isNull(count) || count <= 0) {
                // 校验业务科室ID是否绑定中药科室
                boolean b = busBizDepartmentService.checkTcmDepartment(rxDrugQueryDTO.getHospitalId(), rxDrugQueryDTO.getBizDepartmentId());
                if (!b) {
                    throw new ServiceException("药品添加失败,无添加该药品权限");
                }
            }
        } else {
            // 查询医生所有科室
            BusDoctorDepartment busDoctorDepartment = new BusDoctorDepartment();
            busDoctorDepartment.setDoctorId(rxDrugQueryDTO.getDoctorId());
            busDoctorDepartment.setHospitalId(rxDrugQueryDTO.getHospitalId());
            List<BusDoctorDepartmentVo> departmentList = busDoctorDepartmentMapper.selectList(busDoctorDepartment);
            boolean isTcm = false;
            for (BusDoctorDepartmentVo d : departmentList) {
                // 判断是否有中医科室
                boolean b = busBizDepartmentService.checkTcmDepartment(rxDrugQueryDTO.getHospitalId(), d.getDepartmentId());
                if (b) {
                    isTcm = true;
                    break;
                }
            }
            if (!isTcm) {
                // 查询医生所有科室下可以开具药品id
                List<Long> list = prescriptionDrugsService.checkPrescriptionDrugs(rxDrugQueryDTO.getHospitalId(), rxDrugQueryDTO.getDoctorId());
                if (!list.contains(rxDrugQueryDTO.getDrugsId())) {
                    throw new ServiceException("药品添加失败,无添加该药品权限");
                }
            }
        }
    }

    @Override
    public List<BusEnterpriseDrugs> selectEnterpriseDrugsId(Long hospitalId) {
        return doctorOfficinaMapper.selectEnterpriseDrugsId(hospitalId);
    }

    @Override
    public List<Long> queryTcmUpSet(Long hospitalId) {
        return doctorOfficinaMapper.queryTcmUpSet(hospitalId);
    }

    @Override
    public List<Long> getDrugsClassifyId(Long classifyId) {
        return doctorOfficinaMapper.selectDrugsClassifyId(classifyId);
    }

    /**
     * 查询药品库存
     *
     * @param dtoList 药品查询集合
     * @return HashMap
     */
    @Override
    public HashMap<String, Object> queryDrugsStock(List<BusDrugsDto> dtoList) {
        HashMap<String, Object> map = new HashMap<>();
        if (CollectionUtil.isEmpty(dtoList)) {
            return map;
        }
        List<Long> noStockDrugsIds = new ArrayList<>();
        Long hospitalId = dtoList.get(0).getHospitalId();
        String directoryType = dtoList.get(0).getDirectoryType();
        //如果是双通道药品则不返回库存不足问题
        if (DirectoryTypeEnum.isDualChannel(directoryType)) {
            return map;
        }
        // 查询医院设置的发货优先
        LambdaQueryWrapper<BusConsultationSettings> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusConsultationSettings::getHospitalId, hospitalId);
        BusConsultationSettings settings = busConsultationSettingsMapper.selectOne(lambdaQuery);
        if (CodeEnum.NO.getCode().equals(settings.getSendPrior())) {
            // 查询药房药品库存
            HospitalOfficinaDrugsDTO drugsIdDTO = new HospitalOfficinaDrugsDTO();
            drugsIdDTO.setDrugsIds(dtoList.stream().map(BusDrugsDto::getDrugsId).collect(Collectors.toList()));
            drugsIdDTO.setHospitalId(hospitalId);
            List<BusHospitalOfficina> drugsStocks = busHospitalOfficinaMapper.selectOffincinaList(drugsIdDTO);
            for (BusDrugsDto drugsDto : dtoList) {
                for (BusHospitalOfficina drugsStock : drugsStocks) {
                    if (drugsStock.getDrugsId().equals(drugsDto.getDrugsId()) && drugsDto.getQuantity() > drugsStock.getStock()) {
                        noStockDrugsIds.add(drugsStock.getDrugsId());
                    }
                }
            }
        } else {
            for (BusDrugsDto drugsDto : dtoList) {
                BusEnterpriseDrugsPrice r = busEnterpriseDrugsPriceMapper.getDrugsInfoByDrugsId(hospitalId, drugsDto.getEnterpriseId(), drugsDto.getDrugsId());
                if (Objects.isNull(r) || Objects.isNull(r.getStock())) {
                    log.error("该药品未加库存：{}", drugsDto);
                    throw new ServiceException("该药品还未加库存！");
                }
                if (r.getStock() < drugsDto.getQuantity()) {
                    noStockDrugsIds.add(drugsDto.getDrugsId());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(noStockDrugsIds)) {
            map.put("errorDrugsId", noStockDrugsIds);
        }
        return MapResult.success(map);
    }

    /**
     * 查询配送企业库存
     *
     * @param drugsInfo 药品详情
     */
    private void makeOfficinaDrugsVo(DoctorOfficinaDrugsVo drugsInfo, Map<Long, SysDictData> packageUnitMap) {
        if (StringUtils.isNotBlank(drugsInfo.getMainImg()) && StringUtils.isNotBlank(drugsInfo.getDrugsImgDetail())) {
            drugsInfo.setDrugsImg(drugsInfo.getMainImg() + "," + drugsInfo.getDrugsImgDetail());
        } else if (StringUtils.isNotBlank(drugsInfo.getMainImg())) {
            drugsInfo.setDrugsImg(drugsInfo.getMainImg());
        } else if (StringUtils.isNotBlank(drugsInfo.getDrugsImgDetail())) {
            drugsInfo.setDrugsImg(drugsInfo.getDrugsImgDetail());
        }
        //设置药品祖级分类id
        if (StringUtils.isNotEmpty(drugsInfo.getAncestors())) {
            if ("0".equals(drugsInfo.getAncestors())) {
                drugsInfo.setAncestorsId(drugsInfo.getClassifyId());
            } else {
                String[] split = drugsInfo.getAncestors().split(",");
                drugsInfo.setAncestorsId(Long.valueOf(split[1]));
            }
        }
        //设置药品包装单位
        if (Objects.nonNull(drugsInfo.getDrugsPackagingUnit())) {
            SysDictData sysDictData = packageUnitMap.get(drugsInfo.getDrugsPackagingUnit());
            if (Objects.nonNull(sysDictData)) {
                drugsInfo.setDrugsPackagingUnitName(sysDictData.getDictLabel());
            }
        }

    }
}

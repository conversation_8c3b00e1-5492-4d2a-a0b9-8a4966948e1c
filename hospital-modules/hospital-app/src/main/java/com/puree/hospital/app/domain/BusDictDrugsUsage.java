package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 药品用法信息表
 */
@Data
public class BusDictDrugsUsage extends Entity {

    /** 祖级列表 */
    private String ancestors;
    /** 编码 */
    private Integer code;
    /** 名称 */
    private String name;
    /** 父分类id */
    private Long parentId;
    /** 备注 */
    private String remark;
    /** 子菜单 */
    @TableField(exist = false)
    private List<BusDictDrugsUsage> children = new ArrayList<>();
    @TableField(exist = false)
    private String label;
}

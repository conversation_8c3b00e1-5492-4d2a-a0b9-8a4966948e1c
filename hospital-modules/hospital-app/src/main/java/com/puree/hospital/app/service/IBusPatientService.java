package com.puree.hospital.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPatientHospital;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.PatientDTO;
import com.puree.hospital.app.domain.dto.PatientLoginDTO;
import com.puree.hospital.app.domain.vo.BusPatientFamilyVo;
import com.puree.hospital.app.domain.vo.BusPatientVo;
import com.puree.hospital.app.domain.vo.LoginResultVO;
import com.puree.hospital.common.api.domain.R;

public interface IBusPatientService extends IService<BusPatient>, IUserVerifyService, IUserService {
    String getOpenIdByPatientId(Long patientId, Long hospitalId);
    BusPatient selectPatientByPhone(String mobile);
    BusPatientVo selectPatientByOpenid(String openid);
    BusPatientHospital selectPatientHospitalByOpenid(String openid);
    long insertBusPatient(BusPatient patient, BusPatientHospital busPatientHospital);
    BusPatientVo selectPatientById(SMSLoginUser smsLoginUser, String openid);
    Long perfectInfo(BusPatientFamily busPatientFamily);
    boolean checkIsPerfect(Long patientId);
    int update(BusPatient patient);
    String getPartnersCode(Long patientId);
    int insertPatientHospital(BusPatientHospital busPatientHospital);
    BusPatientVo selectPartnersPatientByOpenid(String openid);
    SMSLoginUser queryPatientInfo();

    /**
     *  查询患者信息
     * @param patientId
     * @return
     */
    BusPatient selectPatientInfo(Long patientId);

    /**
     * 查询就诊人信息
     * @param familyId
     * @return
     */
    BusPatientFamilyVo queryFamilyInfo(Long familyId);

    /**
     * 患者登录
     * @param dto
     * @return {@link R }
     */
    <Req extends PatientLoginDTO> R<LoginResultVO> patientLogin(Req dto);
    /**
     * 将token加入到redis 缓存中
     * @param token
     * @param smsLoginUser
     */
    void setTokenCache(String token, SMSLoginUser smsLoginUser);

    /**
     * 保存 MSG
     *
     * @param hospitalId 医院 ID
     * @param doctorId 医生 ID
     * @param token 令 牌
     * @param ipAddr IP 地址
     * @param code 法典
     * @param loginType 应用程序类型 {@link com.puree.hospital.common.core.enums.LoginTypeEnum }
     */
    void saveMsg(Long hospitalId, Long doctorId, String token, String ipAddr, String code,String loginType);


    boolean modifyOpenid(PatientDTO dto);

    /**
     * 小程序 用户是否绑定医院
     * @param jsCode 微信 jsCode
     * @param hospitalId 医院id
     * @param partnersCode 合作机构编码
     * @return true 已绑定 false 未绑定
     */
    boolean uniAppCheckBind(String jsCode, Long hospitalId,String partnersCode);
}

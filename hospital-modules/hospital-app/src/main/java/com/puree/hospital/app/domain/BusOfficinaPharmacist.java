package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * 药师信息表
 */
@Data
public class BusOfficinaPharmacist extends Entity {

    /** 药房ID */
    private Long officinaId;

    /** 药师头像 */
    private String pharmacistAvatar;

    /** 药师类别（ 0发货药师 1审方药师 2调剂(复核)药师 ） */
    private String pharmacistCategory;

    /** 药师身份证号码 */
    private String idNumber;

    /** 药师身份证（正反面逗号分割） */
    private String pharmacistIdCard;

    /** 药师介绍 */
    private String pharmacistIntroduction;

    /** 药师姓名 */
    private String pharmacistName;

    /** 药师编号 */
    private String pharmacistNumber;

    /** 药师执业资格证（多张逗号分隔） */
    private String pharmacistQualificationCertificate;

    /** 药师性别（0男 1女） */
    private String pharmacistSex;

    /** 药师签名 */
    private String pharmacistSignature;

    /** 手机号码 */
    private String phoneNumber;

    /** 审核内容（0西药 1中药 多选逗号分隔） */
    private String reviewContent;

    /** 状态（0停用 1启用） */
    private Integer status;

    /** 医院ID */
    private Long hospitalId;

    /**
     * 医院名称
     */
    @TableField(exist = false)
    private String hospitalName;
    /**
     * 药师登录token
     */
    private String officinaToken;
    /**
     * 是否删除（0否 1是）
     */
    private String delStatus;

    /**
     * 密码
     */
    private String password;

}

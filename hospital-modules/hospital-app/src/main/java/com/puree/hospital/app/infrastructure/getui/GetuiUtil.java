package com.puree.hospital.app.infrastructure.getui;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.AudienceDTO;
import com.getui.push.v2.sdk.dto.req.Settings;
import com.getui.push.v2.sdk.dto.req.Strategy;
import com.getui.push.v2.sdk.dto.req.message.PushChannel;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.req.message.PushMessage;
import com.getui.push.v2.sdk.dto.req.message.android.AndroidDTO;
import com.getui.push.v2.sdk.dto.req.message.android.ThirdNotification;
import com.getui.push.v2.sdk.dto.req.message.android.Ups;
import com.getui.push.v2.sdk.dto.req.message.ios.Alert;
import com.getui.push.v2.sdk.dto.req.message.ios.Aps;
import com.getui.push.v2.sdk.dto.req.message.ios.IosDTO;
import com.getui.push.v2.sdk.dto.res.TaskIdDTO;
import com.puree.hospital.app.infrastructure.getui.enums.PageEnum;
import com.puree.hospital.app.infrastructure.getui.model.Notification;
import com.puree.hospital.app.infrastructure.getui.model.Transmission;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class GetuiUtil {
    @Resource
    private GetuiProperties getuiProperties;
    private PushApi pushApi;

    private PushApi createApi() {
        if (null == pushApi) {
            // 设置httpClient最大连接数，当并发较大时建议调大此参数。或者启动参数加上 -Dhttp.maxConnections=200
            System.setProperty("http.maxConnections", "200");
            GtApiConfiguration apiConfiguration = new GtApiConfiguration();
            //填写应用配置
            apiConfiguration.setAppId(getuiProperties.getAppId());
            apiConfiguration.setAppKey(getuiProperties.getAppKey());
            apiConfiguration.setMasterSecret(getuiProperties.getMasterSecret());
            // 接口调用前缀，请查看文档: 接口调用规范 -> 接口前缀, 可不填写appId
            apiConfiguration.setDomain(getuiProperties.getDomain());
            // 实例化ApiHelper对象，用于创建接口对象
            ApiHelper apiHelper = ApiHelper.build(apiConfiguration);
            // 创建对象，建议复用。目前有PushApi、StatisticApi、UserApi
            pushApi = apiHelper.creatApi(PushApi.class);
        }
        return pushApi;
    }

    /**
     * 单推（通过cid）
     */
    @Async
    public void pushToSingleByCid(Notification notice) {
        log.info("推送信息: notice={}",JSON.toJSONString(notice));
        PushDTO<Audience> pushDTO = this.buildPushDTO(notice);
        // 设置接收人信息
        Audience audience = new Audience();
        pushDTO.setAudience(audience);
        // cid
        audience.addCid(notice.getCid());
        // 进行cid单推
        PushApi pushApi = createApi();
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushToSingleByCid(pushDTO);
        log.info("推送消息结果：{}", JSON.toJSONString(apiResult));
    }


    /**
     * cid批量推
     *
     * @param notice
     */
    @Async
    public Map<String, String> pushListByCid(Notification notice) {
        //批量发送
        AudienceDTO audienceDTO = new AudienceDTO();

        PushDTO<Audience> pushDTO = this.buildPushDTO(notice);

        PushApi pushApi = createApi();
        ApiResult<TaskIdDTO> createApiResult = pushApi.createMsg(pushDTO);
        if (!createApiResult.isSuccess()) {
            throw new ServiceException("推送：创建消息失败" + createApiResult.getMsg());
        }
        // 设置接收人信息
        Audience audience = new Audience();
        pushDTO.setAudience(audience);
        audience.setCid(notice.getCidList());
        audienceDTO.setAudience(audience);
        audienceDTO.setTaskid(createApiResult.getData().getTaskId());
        audienceDTO.setAsync(true);

        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushListByCid(audienceDTO);
        Map<String, String> map = new HashMap<>();
        map.put("code", String.valueOf(apiResult.getCode()));
        map.put("msg", apiResult.getMsg());
        return map;
    }

    /**
     * 初始化拼装参数
     * @param notice
     * @return
     */
    private PushDTO<Audience> buildPushDTO(Notification notice) {
        PushDTO<Audience> pushDTO = new PushDTO<>();
        // 设置推送参数
        //requestid需要每次变化唯一
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        pushDTO.setGroupName("za-group");

        // PushMessage在线走个推通道才会起作用的消息体
        PushMessage pushMessage = new PushMessage();
        Transmission transmission = notice.getTransmission();
        transmission.setTitle(notice.getTitle());
        transmission.setContent(notice.getBody());
        try {
            pushMessage.setTransmission(JsonUtils.objectToJson(transmission));
        } catch (JsonProcessingException e) {
            log.error("转换json失败 e",e);
        }
        pushDTO.setPushMessage(pushMessage);
        /**
          * 配置推送条件
          * 1: 表示该消息在用户在线时推送个推通道，用户离线时推送厂商通道;
          * 2: 表示该消息只通过厂商通道策略下发，不考虑用户是否在线;
          * 3: 表示该消息只通过个推通道下发，不考虑用户是否在线；
          * 4: 表示该消息优先从厂商通道下发，若消息内容在厂商通道代发失败后会从个推通道下发。
          */
        Strategy strategy = new Strategy();
        strategy.setDef(1);
        strategy.setSt(1);
        Settings settings = new Settings();
        settings.setStrategy(strategy);
        pushDTO.setSettings(settings);
        //消息有效期，走厂商消息需要设置该值
        settings.setTtl(3600000);

        //推送苹果离线通知标题内容
        Alert alert = new Alert();
        //苹果离线通知栏标题
        alert.setTitle(notice.getTitle());
        //苹果离线通知栏内容
        alert.setBody(notice.getBody());
        Aps aps = new Aps();
        //1表示静默推送(无通知栏消息)，静默推送时不需要填写其他参数。
        //苹果建议1小时最多推送3条静默消息
        aps.setContentAvailable(0);
        aps.setSound("default");
        aps.setAlert(alert);
        IosDTO iosDTO = new IosDTO();
        iosDTO.setAps(aps);
        iosDTO.setType("notify");
        //苹果角标
        iosDTO.setAutoBadge("+1");
        iosDTO.setPayload(transmission.getPayload());
        PushChannel pushChannel = new PushChannel();
        pushChannel.setIos(iosDTO);
        //安卓离线厂商通道推送消息体
        AndroidDTO androidDTO = new AndroidDTO();
        Ups ups = new Ups();
        ThirdNotification notification1 = new ThirdNotification();
        ups.setNotification(notification1);
//        安卓离线展示的标题
        notification1.setTitle(notice.getTitle());
//        安卓离线展示的内容
        notification1.setBody(notice.getBody());
        notification1.setClickType("intent");
        notification1.setIntent("intent://io.dcloud.unipush/?#Intent;scheme=unipush;launchFlags=0x4000000;component=uni.UNIF099812/io.dcloud.PandoraEntry;S.UP-OL-SU=true;" +
                "S.title=" + notice.getTitle() + ";S.content=" + notice.getTitle() + ";S.payload=" + transmission.getPayload() + ";end");
        log.info("个推Intent={}",JSON.toJSONString(notification1.getIntent()));
        //各厂商自有功能单项设置
        //华为
        ups.addOption("HW", "/message/android/notification/badge/class", "io.dcloud.PandoraEntry");
        ups.addOption("HW", "/message/android/notification/badge/add_num", 1);
        //荣耀
        ups.addOption("HO", "/android/notification/badge/badgeClass", "io.dcloud.PandoraEntry");
        ups.addOption("HO", "/android/notification/badge/addNum", 1);
        //vivo
        ups.addOption("VV","/category","IM");
        //oppo
        this.channelConfig(notice.getChannelId(),ups);
        androidDTO.setUps(ups);
        pushChannel.setAndroid(androidDTO);
        pushDTO.setPushChannel(pushChannel);
        return pushDTO;
    }

    /**
     * 私信通道配置 (华为和oppo)
     * @param channeId
     * @param ups
     */
    private void channelConfig(String channeId,Ups ups){
        //私信类型
        String im="IM";
        String work="WORK";
        PageEnum pageEnum = PageEnum.getTypeByCode(Integer.valueOf(channeId));
        switch (pageEnum){
            case sf:
                ups.addOption("HW", "/message/android/category", work);
                break;
            case zs:
                ups.addOption("HW", "/message/android/category", im);
                break;
            case tw:
                ups.addOption("HW", "/message/android/category", work);
                break;
            case sm:
                ups.addOption("HW", "/message/android/category", work);
                break;
            case xcf:
                ups.addOption("HW", "/message/android/category", work);
                break;
            case sp:
                ups.addOption("HW", "/message/android/category", work);
                break;
            case ydd:
                ups.addOption("HW", "/message/android/category", work);
                break;
            case qz:
                ups.addOption("HW", "/message/android/category", im);
                break;
            default:
                break;
        }
        ups.addOption("OP","/channel_id","ywxx20230207");
    }
}

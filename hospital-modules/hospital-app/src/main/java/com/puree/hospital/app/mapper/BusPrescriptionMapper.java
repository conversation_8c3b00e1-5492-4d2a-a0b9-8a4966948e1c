package com.puree.hospital.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.api.model.ApprovePrescriptionDTO;
import com.puree.hospital.app.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusPrescriptionMapper extends BaseMapper<BusPrescription> {
    List<BusPrescriptionVo> selectNotApprovedList(BusPrescription busPrescription);
    List<BusPrescriptionVo> selectApprovedList(BusPrescription busPrescription);

    /**
     * 获取经过审核的处方列表
     * @param busPrescription 处方信息
     * @return 处方列表
     */
    List<BusPrescriptionVo> selectApprovedPatientList(BusPrescription busPrescription);
    List<MMDiagnosisVo> selectMMDiagnosisList(@Param("key") String key);
    List<MMDiagnosisVo> selectMMDiagnosisListByIds(@Param("ids") List<Long> ids);
    List<TCMDiagnosisVo> selectTCMDiagnosisList(@Param("key") String key);
    TCMDiagnosisVo selectTCMById(Long id);
    TCMSyndromeVo selectTCMSyndromeById(Long id);
    PatientTemplateMsgVo selectPatient(@Param("prescriptionId") Long prescriptionId,@Param("hospitalId")Long hospitalId);
    List<PatientTemplateMsgVo> selectPrescriptionDrugs(Long prescriptionId);
    int discard(BusPrescription busPrescription);
    int approve(ApprovePrescriptionDTO dto);
    List<BusPrescriptionVo> selectPrescriptionList(BusPrescription busPrescription);
    List<TCMSyndromeVo> selectTCMSyndromeList(String key);
    BusHospitalVo selectHospitalInfo(Long hospitalId);

    /**
     * 查询处方失效处方列表
     *
     * @param idx       当前id偏移位
     * @param pageSize  分页大小
     * @return 处方列表
     */
    List<BusPrescription> queryInvalidList(@Param("idx") Long idx, @Param("pageSize") Integer pageSize);
    /**
     * 查询处方各个分类的数量
     */
    List<PrescriptionStatusTypeVO> selectPrescriptionStatusCount(@Param("hospitalId") Long hospitalId
            , @Param("doctorId") Long doctorId, @Param("expertId") Long expertId);

    BusPrescription selectPrescriptionInfo(@Param("prescriptionId") long prescriptionId);
    /**
     * 查询处方及药品信息
     * @param prescriptionIds
     * @return
     */
    List<BusPrescription> selectPdList(@Param("prescriptionIds") List<Long> prescriptionIds);

    /**
     * 查询处方及药品信息
     * @param prescriptionId
     * @return
     */
    BusPrescription selectPrescriptionDrugList(Long prescriptionId);

    /**
     * 查询医保处方信息
     *
     * @param rxNoList 处方号列表
     * @param idList   处方id列表
     * @return 处方
     */
    List<BusPrescription> getMiRx(@Param("rxNoList") List<String> rxNoList, @Param("idList") List<Long> idList);

    int countPreorderApproved(Long preorderId) ;

    List<BusPrescriptionVo> selectExpertPrescriptionList(BusPrescription busPrescription);

}

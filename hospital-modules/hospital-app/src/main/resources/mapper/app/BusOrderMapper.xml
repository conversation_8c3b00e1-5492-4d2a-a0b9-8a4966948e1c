<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusOrderMapper">
    <resultMap id="BaseResultMap" type="com.puree.hospital.app.domain.vo.BusOrderVo">
        <result column="id" property="id" />
        <result column="sub_order_type" property="subOrderType" />
        <result column="sub_order_id" property="subOrderId" />
        <result column="order_time" property="orderTime" />
        <result column="create_time" property="createTime" />
        <result column="mark" property="mark" />
        <association property="drugsOrderVo" javaType="com.puree.hospital.app.domain.vo.DrugsOrderVo">
            <result column="d_id" property="id" />
            <result column="prescription_id" property="prescriptionId" />
            <result column="order_no" property="orderNo" />
            <result column="delivery_type" property="deliveryType" />
            <result column="status" property="status" />
            <result column="d_order_time" property="orderTime" />
            <result column="receiving_user" property="receivingUser" />
            <result column="receiving_tel" property="receivingTel" />
            <result column="receiving_address" property="receivingAddress" />
            <result column="amount" property="amount" />
            <result column="logistics_company" property="logisticsCompany" />
            <result column="delivery_no" property="deliveryNo" />
            <result column="order_drugs_type" property="orderDrugsType" />
            <result column="drugs_store_id" property="drugsStoreId" />
            <result column="drugs_store_name" property="drugsStoreName" />
            <result column="hospital_name" property="hospitalName" />
            <result column="d_create_by" property="createBy" />
            <result column="d_create_time" property="createTime" />
            <result column="d_update_by" property="updateBy" />
            <result column="d_update_time" property="updateTime" />
            <result column="prescription_type" property="prescriptionType" />
            <result column="express_code" property="expressCode" />
            <result column="pa_name" property="paName" />
            <result column="usages" property="usages" />
            <result column="delivery_time" property="deliveryTime" />
            <result column="complete_time" property="completeTime" />
            <result column="cancel_time" property="cancelTime" />
            <result column="examination_fee" property="examinationFee"/>
            <result column="examination_name" property="examinationName"/>
            <collection property="list" ofType="BusPrescriptionDrugs">
                <result column="p_prescription_id" property="prescriptionId"/>
                <result column="drugs_id" property="drugsId"/>
                <result column="drugs_manufacturer" property="drugsManufacturer"/>
                <result column="drugs_name" property="drugsName"/>
                <result column="drugs_img" property="drugsImg"/>
                <result column="drugs_specification" property="drugsSpecification"/>
                <result column="selling_price" property="sellingPrice"/>
                <result column="medication_frequency" property="medicationFrequency"/>
                <result column="single_dose" property="singleDose"/>
                <result column="unit" property="unit"/>
                <result column="drugs_usage_value" property="drugsUsageValue"/>
                <result column="quantity" property="quantity"/>
                <result column="medication_days" property="medicationDays"/>
                <result column="weight" property="weight"/>
            </collection>
        </association>
        <association property="consultationOrderVo" javaType="BusConsultationOrderVo">
            <result column="c_id" property="id" />
            <result column="c_order_no" property="orderNo" />
            <result column="c_status" property="status" />
            <result column="c_amount" property="amount" />
            <result column="order_type" property="orderType" />
            <result column="family_id" property="familyId" />
            <result column="family_name" property="familyName" />
            <result column="family_sex" property="familySex" />
            <result column="family_age" property="familyAge" />
            <result column="c_doctor_id" property="doctorId" />
            <result column="photo" property="photo" />
            <result column="doctor_id" property="doctorId" />
            <result column="doctor_name" property="doctorName" />
            <result column="title" property="title" />
            <result column="department_name" property="departmentName" />
            <result column="round" property="round" />
            <result column="c_patient_id" property="patientId" />
            <result column="c_hospital_id" property="hospitalId" />
            <result column="consultation_type" property="consultationType" />
            <result column="pre_status" property="preStatus" />
            <result column="group_id" property="groupId" />
        </association>
    </resultMap>

    <resultMap id="orderMap" type="com.puree.hospital.app.domain.vo.OrderVo">
        <result column="order_no" property="orderNo" />
        <result column="hospital_id" property="hospitalId" />
        <result column="patient_id" property="patientId" />
        <result column="partners_code" property="partnersCode" />
        <result column="rel_price" property="relPrice" />
        <result column="freight" property="freight" />
        <result column="delivery_type" property="deliveryType" />
        <result column="order_time" property="orderTime" />
        <result column="payment_time" property="paymentTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="complete_time" property="completeTime" />
        <result column="receiver" property="receiver" />
        <result column="receive_phone" property="receivePhone" />
        <result column="receive_address" property="receiveAddress" />
        <result column="pay_way" property="payWay" />
        <result column="order_status" property="orderStatus" />
        <result column="hospital_name" property="hospitalName" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="area" property="area" />
        <result column="detailed_address" property="detailedAddress" />
        <result column="change_price" property="changePrice" />
        <result column="examination_fee" property="examinationFee"/>
        <result column="examination_name" property="examinationName"/>
        <collection property="subOrderList" ofType="SubOrderVO">
            <result column="sub_order_id" property="subOrderId" />
            <result column="sub_order_type" property="subOrderType" />
        </collection>
    </resultMap>

    <resultMap id="goodsOrderMap" type="com.puree.hospital.app.domain.vo.BusShopOrderVO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="delivery_type" property="deliveryType" />
        <result column="status" property="status" />
        <result column="order_time" property="orderTime" />
        <result column="receiving_user" property="receivingUser" />
        <result column="receiving_tel" property="receivingTel" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="area" property="area" />
        <result column="detailed_address" property="detailedAddress" />
        <result column="amount" property="amount" />
        <result column="hospital_id" property="hospitalId" />
        <result column="hospital_name" property="hospitalName" />
        <result column="pick_up_time" property="pickUpTime" />
        <result column="payment_time" property="paymentTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="complete_time" property="completeTime" />
        <result column="del_status" property="delStatus" />
        <result column="partners_code" property="partnersCode" />
        <result column="refund_time" property="refundTime" />
        <result column="patient_id" property="patientId" />
        <result column="freight" property="freight" />
        <result column="pay_way" property="payWay" />
        <result column="tonglian_trxid" property="tonglianTrxid" />
        <collection property="list" ofType="BusOrderShopVO">
            <result column="order_id" property="orderId" />
            <result column="shop_id" property="drugsId" />
            <result column="shop_title" property="drugsName" />
            <result column="shop_brand" property="shopBrand" />
            <result column="shop_img" property="drugsImg" />
            <result column="shop_specification" property="drugsSpecification" />
            <result column="selling_price" property="sellingPrice" />
            <result column="quantity" property="quantity" />
            <result column="hospital_id" property="hospitalId" />
            <result column="enterprise_id" property="enterpriseId" />
            <result column="reference_purchase_price" property="referencePurchasePrice" />
            <result column="shop_type" property="shopType" />
        </collection>
    </resultMap>

    <!-- 表字段 -->
    <sql id="baseColumns">
         t.id
        , t.sub_order_type
        , t.sub_order_id
        , t.order_time
        , t.create_by
        , t.create_time
        , t.update_by
        , t.update_time
    </sql>
    <!-- 药品订单表字段 -->
    <sql id="drugsOrderSql">
        o.id d_id
        , o.prescription_id
        , o.order_no
        , o.delivery_type
        , o.status
        , o.order_time d_order_time
        , o.receiving_user
        , o.receiving_tel
        , o.receiving_address
        , o.amount
        , o.logistics_company
        , o.delivery_no
        , o.order_drugs_type
        , o.drugs_store_id
        , o.drugs_store_name
        , o.hospital_id
        , o.hospital_name
        , o.create_by d_create_by
        , o.create_time d_create_time
        , o.update_by d_update_by
        , o.update_time d_update_time
        , o.del_status
        , o.express_code
        , o.delivery_time
        , o.complete_time
        , o.cancel_time
        , o.partners_code
    </sql>
    <!--处方药品信息表字段-->
    <sql id="prescriptionDrugsSql">
         pd.prescription_id p_prescription_id
        , pd.drugs_id
        , pd.drugs_manufacturer
        , pd.standard_common_name
        , pd.drugs_name
        , pd.drugs_img
        , pd.drugs_specification
        , pd.selling_price
        , pd.medication_frequency
        , pd.single_dose
        , pd.unit
        , pd.drugs_usage_value
        , pd.quantity
        , pd.medication_days
        , pd.weight
    </sql>
    <!-- 问诊订单表字段 -->
    <sql id="consultationOrderSql">
        c.id c_id
        , c.order_no c_order_no
        , c.status c_status
        , c.amount c_amount
        , c.order_type
        , c.family_id
        , c.family_name
        , c.family_sex
        , c.family_age
        , c.doctor_id c_doctor_id
        , c.photo
        , c.doctor_id
        , c.doctor_name
        , c.title
        , c.department_name
        , c.round
        , c.patient_id c_patient_id
        , c.hospital_id c_hospital_id
        , c.consultation_type
        , c.pre_status
        , c.partners_code
        , g.id group_id
    </sql>
    <!-- 查询全部 -->
    <select id="selectAllOrderList" parameterType="com.puree.hospital.app.domain.dto.BusOrderDto" resultMap="BaseResultMap">
		SELECT
            t.*
        FROM
            (
            SELECT
                o.id,
                o.sub_order_type,
                o.sub_order_id,
                o.order_time,
                o.create_time,
                t1.hospital_id,
                t1.patient_id,
                t1.del_status,
                t1.partners_code,
                IFNULL(t1.id,1) mark
            FROM
                bus_order o
                LEFT JOIN bus_consultation_order t1 ON o.sub_order_id = t1.id
                AND o.sub_order_type = '0' UNION ALL
            SELECT
                o.id,
                o.sub_order_type,
                o.sub_order_id,
                o.order_time,
                o.create_time,
                t2.hospital_id,
                t2.patient_id,
                t2.del_status,
                t2.partners_code,
                IFNULL(t2.prescription_id,0) mark
            FROM
                bus_order o
                LEFT JOIN bus_drugs_order t2 ON o.sub_order_id = t2.id
                AND o.sub_order_type = '1' UNION ALL
            SELECT
                o.id,
                o.sub_order_type,
                o.sub_order_id,
                o.order_time,
                o.create_time,
                t3.hospital_id,
                t3.patient_id,
                t3.del_flag del_status,
                t3.partners_code,
                IFNULL(t3.id,1) mark
            FROM
                bus_order o
                LEFT JOIN bus_five_service_pack_order t3 ON o.sub_order_id = t3.id
                AND o.sub_order_type = '2'
            ) t
        <where>
            t.hospital_id = #{hospitalId}
            AND t.patient_id = #{patientId}
            AND t.del_status = '0'
            <if test="partnersCode != null and partnersCode != ''">
                AND t.partners_code = #{partnersCode}
            </if>
        </where>
        ORDER BY
            t.order_time DESC
	</select>

    <insert id="batchInsert" parameterType="com.puree.hospital.app.domain.BusOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO bus_order (
            order_no,
            sub_order_id,
            sub_order_type,
            order_time,
            create_time,
            partners_code,
            hospital_id,
            patient_id,
            rel_price,
            freight,
            freight_type,
            delivery_type,
            pick_up_time,
            receiver,
            receive_phone,
            receive_address,
            pay_way,
            order_status,
            hospital_name,
            province,
            city,
            area,
            detailed_address,
            group_id,
            remarks,
            invoice_status,
            examination_fee,
            examination_name
        ) VALUES
        <foreach collection="busOrders" item="item" separator=",">
            (
            #{item.orderNo},
            #{item.subOrderId},
            #{item.subOrderType},
            #{item.orderTime},
            #{item.createTime},
            #{item.partnersCode},
            #{item.hospitalId},
            #{item.patientId},
            #{item.relPrice},
            #{item.freight},
            #{item.freightType},
            #{item.deliveryType},
            #{item.pickUpTime},
            #{item.receiver},
            #{item.receivePhone},
            #{item.receiveAddress},
            #{item.payWay},
            #{item.orderStatus},
            #{item.hospitalName},
            #{item.province},
            #{item.city},
            #{item.area},
            #{item.detailedAddress},
            #{item.groupId},
            #{item.remarks},
            #{item.invoiceStatus},
            #{item.examinationFee},
            #{item.examinationName}
            )
        </foreach>
    </insert>

    <select id="selectOrderList" parameterType="com.puree.hospital.app.domain.dto.DrugsOrderDto"
            resultType="com.puree.hospital.app.domain.vo.OrderVo">
        SELECT
            t.*
        FROM
            (
            SELECT
                t1.*,
                b1.after_sales_status,
                b1.apply_time
            FROM
                bus_order t1
                INNER JOIN bus_drugs_order t2 ON t1.sub_order_id = t2.id  AND t1.sub_order_type = '0'
                LEFT JOIN bus_order_after_sales b1 ON b1.order_id = t2.id AND b1.after_sales_type = 0
                UNION
            SELECT
                t3.*,
                b2.after_sales_status,
                b2.apply_time
            FROM
                bus_order t3
                INNER JOIN bus_shop_order t4 ON t3.sub_order_id = t4.id  AND t3.sub_order_type = '1'
                LEFT JOIN bus_order_after_sales b2 ON b2.order_id = t4.id AND b2.after_sales_type = 1
            ) t
        WHERE
            t.hospital_id = #{hospitalId}
            AND t.patient_id = #{patientId}
            AND t.del_status = '0'
            <if test = "partnersCode != null and partnersCode !=''">
                AND t.partners_code = #{partnersCode}
            </if>
            <if test = "status == 0">
                AND t.order_status in (0, 7)
            </if>
            <if test = "status == 1">
                AND t.order_status IN (1,2,3)
            </if>
            <if test = "status == 2">
                AND t.order_status in (5,6)
            </if>
            <if test = "status == 3">
                AND t.after_sales_status IS NOT NULL
            </if>
        GROUP BY
	        t.order_no
        <if test = "status == 3">
            ORDER BY
            t.apply_time DESC
        </if>
        <if test = "status != 3">
            ORDER BY
            t.create_time DESC
        </if>

    </select>

    <select id="selectOrder" parameterType="java.lang.String" resultMap="orderMap">
        SELECT
            *
        FROM
            bus_order
        WHERE
            order_no = #{orderNo}
    </select>

    <select id="selectGoodsDetail" parameterType="java.lang.Long" resultMap="goodsOrderMap">
        SELECT
            t1.*,
            t2.*,
            t3.shop_type
        FROM
            bus_shop_order t1
            INNER JOIN bus_order_shop t2 ON t1.id = t2.order_id
            LEFT JOIN bus_shop_goods t3 ON t2.shop_id = t3.id
        WHERE
            t1.id = #{orderId}
    </select>

    <select id="selectOrderAndGoodsDetail" parameterType="java.lang.String" resultMap="goodsOrderMap">
        SELECT
            t1.*,
            t2.*,
            t3.shop_type
        FROM
            bus_shop_order t1
            INNER JOIN bus_order_shop t2 ON t1.id = t2.order_id
            LEFT JOIN bus_shop_goods t3 ON t2.shop_id = t3.id
        WHERE
            t1.order_no = #{orderNo}
    </select>

    <select id="selectDrugsAndGoodsList" parameterType="java.lang.String" resultType="BusOrder">
        SELECT
            t1.*,
            t2.order_no drugs_order_no,
            t3.order_no goods_order_no
        FROM
            bus_order t1
            LEFT JOIN bus_drugs_order t2 ON t1.sub_order_id = t2.id
            AND t1.sub_order_type = '0'
            LEFT JOIN bus_shop_order t3 ON t1.sub_order_id = t3.id
            AND t1.sub_order_type = '1'
        WHERE
            t1.order_no = #{orderNo}
    </select>

    <select id="selectDrugsOrderInfo" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.BusDrugsOrder">
        select freight from bus_drugs_order where id = #{subOrderId}
    </select>

    <select id="selectGoodsOrderInfo" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.BusShopOrder">
        select freight from bus_shop_order where id = #{subOrderId}
    </select>

    <select id="selectWaitPayOrderList" resultType="com.puree.hospital.app.domain.vo.OrderVo">
        SELECT
            hospital_id,
            order_no,
            order_time,
            order_status AS 'status'
        FROM
            bus_order
        WHERE
            order_status = 0
        GROUP BY
            order_no
    </select>

    <select id="selectPrescriptionId" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT
            t2.prescription_id
        FROM
            bus_order t1
            LEFT JOIN bus_drugs_order t2 ON t1.sub_order_id = t2.id
            AND t1.sub_order_type = 0
        WHERE
            t1.order_no = #{orderNo}
    </select>


    <update id="updateBusOrderInvoice">
        update bus_order
        set
        invoice_status=#{invoiceStatus} , invoice_type=#{invoiceType} , invoice_header_id=#{invoiceHeaderId}
        where order_no=#{orderNo}
    </update>

    <select id="getGoodsIdsByOrderId" resultType="com.puree.hospital.app.domain.BusPatientOrderInfo" >
        select distinct t1.family_id as familyId, t2.shop_id as id
        from bus_shop_order t1 join bus_order_shop t2 on t1.id = t2.order_id
        where t1.id = #{id} and t2.hospital_id = #{hospitalId}
    </select>

</mapper>
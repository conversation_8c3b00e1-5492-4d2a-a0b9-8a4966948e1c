<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusConsultationSettingsMapper">
    <resultMap type="com.puree.hospital.app.domain.BusFreightSetting" id="settingResultMap">
        <id property="id"  column="id2" />
        <result property="freightType" column="freight_type" />
        <result property="hospitalId" column="hospital_id" />
        <result property="freight" column="freight" />
        <result property="freeShipping" column="free_shipping" />
        <collection property="freightAreaList" ofType="com.puree.hospital.app.domain.BusFreightArea">
            <id property="id"  column="id3" />
            <result property="freightId" column="freight_id" />
            <result property="provinceName" column="province_name" />
            <result property="provinceCode" column="province_code" />
            <result property="cityName" column="city_name" />
            <result property="cityCode" column="city_code" />
            <result property="areaName" column="area_name" />
            <result property="areaCode" column="area_code" />
        </collection>
    </resultMap>
    <select id="findByHospital" resultType="com.puree.hospital.app.domain.BusConsultationSettings">
        select * from bus_consultation_settings where hospital_id=#{hospitalId} limit 1
    </select>
    <select id="getFreight" parameterType="com.puree.hospital.app.api.model.dto.BusFreightQueryDTO"
            resultMap="settingResultMap">
        SELECT
            b.id AS id2,
            b.freight_type,
            b.freight,
            b.free_shipping,
            c.id AS id3,
            c.freight_id,
            c.province_name,
            c.province_code,
            c.city_name,
            c.city_code,
            c.area_name,
            c.area_code
        FROM bus_freight_setting b
        LEFT JOIN bus_freight_area c ON b.id = c.freight_id
        WHERE b.hospital_id = #{hospitalId}
        AND EXISTS (SELECT 1 from bus_freight_area
            <where>
                <if test="provinceName!=null || provinceName!=''">
                    and c.province_name=#{provinceName}
                </if>
                <if test="cityName!=null || cityName!=''">
                    and (c.city_name=#{cityName} or c.city_name='ALL')
                </if>
                <if test="areaName!=null || areaName!=''">
                    and (c.area_name=#{areaName} or c.area_name='ALL')
                </if>
            </where>
            )
    </select>


</mapper>
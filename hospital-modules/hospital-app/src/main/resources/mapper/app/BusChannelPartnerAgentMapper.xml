<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusChannelPartnerAgentMapper">
    <resultMap id="BaseResultMap" type="com.puree.hospital.app.domain.vo.BusChannelPartnerAgentVO">
        <result column="id" property="id"/>
        <result column="channel_partner_id" property="channelPartnerId"/>
        <result column="full_name" property="fullName"/>
        <result column="phonenumber" property="phonenumber"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="modify_flag" property="modifyFlag"/>
        <result column="channelPartnerName" property="channelPartnerName"/>
        <result column="hospital_id" property="hospitalId"/>
        <result column="token" property="token"/>
    </resultMap>

    <select id="getById" resultMap="BaseResultMap">
        SELECT
            t1.id,
            t1.channel_partner_id,
            t1.full_name,
            t1.phonenumber,
            t3.status,
            t1.del_flag,
            t1.modify_flag,
            t1.token,
            t2.hospital_id,
            t2.full_name channelPartnerName
        FROM `bus_channel_partner_agent` t1
        LEFT JOIN `bus_channel_partner` t2 ON t1.channel_partner_id = t2.id
        LEFT JOIN `bus_channel_identity` t3 ON t1.channel_identity_id = t3.id
        WHERE t1.id = #{id}
    </select>


    <select id="listByChannelPartner" resultMap="BaseResultMap">
        SELECT t1.id
             , t1.channel_partner_id
             , t1.full_name
             , t3.phone_number as phonenumber
             , t3.status
             , t1.del_flag
             , t1.modify_flag
             , t1.token
             , t2.hospital_id
             , t2.full_name channelPartnerName
        FROM `bus_channel_partner_agent` t1
                 LEFT JOIN `bus_channel_partner` t2 ON t1.channel_partner_id = t2.id
                 LEFT JOIN `bus_channel_identity` t3 ON t1.channel_identity_id = t3.id
        WHERE t1.channel_partner_id = #{id} and t1.del_flag='0'
    </select>
    <select id="listByChannelStatus" resultType="java.lang.Integer">
         SELECT
              t3.status
        FROM `bus_channel_partner_agent` t1
                 LEFT JOIN `bus_channel_partner` t2 ON t1.channel_partner_id = t2.id
                LEFT JOIN `bus_channel_identity` t3 ON t2.channel_identity_id = t3.id
        WHERE t1.id = #{id} and t1.del_flag='0'
    </select>

</mapper>
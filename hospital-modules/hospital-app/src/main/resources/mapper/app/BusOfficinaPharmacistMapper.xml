<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusOfficinaPharmacistMapper">

    <select id="selectById" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.BusOfficinaPharmacist">
        SELECT
            o.id,
            o.hospital_id,
            o.officina_id,
            o.pharmacist_number,
            o.pharmacist_name,
            o.pharmacist_category,
            o.review_content,
            o.pharmacist_sex,
            o.phone_number,
            o.password,
            o.pharmacist_avatar,
            o.id_number,
            o.pharmacist_id_card,
            o.pharmacist_qualification_certificate,
            o.pharmacist_introduction,
            o.pharmacist_signature,h.hospital_name
        FROM bus_officina_pharmacist o
        LEFT JOIN bus_hospital h ON h.id = o.hospital_id
        where o.id = #{id}
    </select>

    <!-- CA证书申请需要的药师信息 -->
    <select id="getPharmacistInfo" parameterType="java.lang.String" resultType="com.puree.hospital.app.domain.dto.CAPersonInfoDto">
        SELECT o.pharmacist_name      AS custName,
               o.pharmacist_sex    as sex,
               o.id_number     AS idNo,
               o.phone_number   AS mobileNo,
               h.id             AS hospitalId,
               h.hospital_name  as hospitalName,
               p.name           as province,
               c.name           as city,
               h.area_code      as zipCode,
               h.detail_address as address
        FROM bus_officina_pharmacist o
             LEFT JOIN bus_hospital h ON h.id = o.hospital_id
             LEFT JOIN sys_province p ON p.code = h.province_code
             LEFT JOIN sys_city c ON c.code = h.city_code
        where o.phone_number = #{phoneNumber}
          and o.del_status = '0'
    </select>

    <!-- CA证书申请需要的药师信息 -->
    <select id="selectOfficinaPharmacistList" parameterType="com.puree.hospital.app.domain.BusOfficinaPharmacist"
            resultType="com.puree.hospital.app.domain.BusOfficinaPharmacist">
        SELECT
            o.pharmacist_name,
            o.pharmacist_sex,
            o.id_number,
            o.phone_number,
            o.id
        FROM bus_officina_pharmacist o
        <where>
            o.del_status = '0'
            <if test="status!=null">
                and o.status=#{status}
            </if>
            <if test="hospitalId!=null">
                and o.hospital_id=#{hospitalId}
            </if>
            <if test="pharmacistCategory!=null">
                and o.pharmacist_category=#{pharmacistCategory}
            </if>
            <if test="reviewContent!=null">
                and find_in_set(#{reviewContent},o.review_content)
            </if>
        </where>
    </select>

</mapper>
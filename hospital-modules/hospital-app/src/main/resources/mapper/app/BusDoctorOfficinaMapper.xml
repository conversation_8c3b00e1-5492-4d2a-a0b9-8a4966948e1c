<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusDoctorOfficinaMapper">

    <select id="selectClassificationList" resultType="com.puree.hospital.app.domain.vo.DrugsClassifyVo">
        SELECT
            id,
            parent_id,
            ancestors,
            classify_name
        FROM
            bus_dict_drugs_classify
        WHERE
            parent_id = 0
        ORDER BY sort ASC
               ,update_time DESC
    </select>

    <select id="selectDrugsClassifyId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT
            id
        FROM
            bus_dict_drugs_classify
        WHERE
            FIND_IN_SET(#{classifyId}, ancestors)
    </select>

    <sql id="Drug_Detail_Select">
        SELECT
            t1.directory_type,
            t1.drugs_id,
            t1.hospital_id,
            <choose>
                <when test="directoryType == 1">
                    t2.id AS drugs_source_id,
                    t2.selling_price,
                    (IFNULL(t2.stock, 0) + IFNULL(begp.stock, 0)) as stock,
                    t2.enterprise_id,
                    t2.`status` as drug_status,
                </when>
                <when test="directoryType == 2">
                    -1 as drugs_source_id,
                    t3.reference_selling_price as selling_price,
                    9999 as stock,
                    1 as drug_status,
                </when>
            </choose>
            t3.drugs_name,
            t3.standard_common_name,
            t3.drugs_manufacturer,
            t3.drugs_specification,
            t3.drugs_dosage_form,
            t3.drugs_img,
            t3.main_img,
            t3.drugs_img_detail,
            t3.classify_id,
            t3.drugs_usage,
            t3.min_pack_num,
            t3.min_make_unit,
            t3.min_pack_unit,
            t3.medical_insurance_type,
            t3.national_drug_code,
            t3.detailed_specifications,
            t3.recommended_dosage,
            t3.nmpn,
            t3.drugs_packaging_unit,
            t3.drugs_details,
            t3.base_dose,
            t3.base_dose_unit,
            t3.single_dose,
            t3.default_frequency,
            t3.recommend_use_days,
            t4.ancestors,
            t4.classify_name,
            t5.`name` as drugs_usage_value,
            sd.dict_value pre_type
        FROM bus_directory_drugs t1
        <if test="directoryType == 1">
            INNER JOIN bus_hospital_officina t2 ON t1.id = t2.directory_id and t1.drugs_id = t2.drugs_id and t2.status = 1 and t2.hospital_id = #{hospitalId}
            left join bus_enterprise_drugs beg on t2.enterprise_id = beg.enterprise_id and t2.drugs_id = beg.drugs_id
            left join bus_enterprise_drugs_price begp on beg.id = begp.drug_ref_id and begp.hospital_id = #{hospitalId} and begp.enterprise_id = t2.enterprise_id
        </if>
        INNER JOIN bus_drugs t3 ON t3.id = t1.drugs_id and t3.type='0' and t3.status = 1
        LEFT JOIN bus_dict_drugs_classify t4 ON t4.id = t3.classify_id
        LEFT JOIN bus_dict_drugs_usage t5 ON t5.id = t3.drugs_usage
        LEFT JOIN sys_dict_data sd ON t3.prescription_identification = sd.dict_code
    </sql>

    <select id="selectDrugsList" parameterType="com.puree.hospital.app.domain.dto.RxDrugQueryDTO"
            resultType="com.puree.hospital.app.domain.vo.DoctorOfficinaDrugsVo">
        <include refid="Drug_Detail_Select"/>
        <where>
            t1.hospital_id = #{hospitalId}
            and t1.directory_type = #{directoryType}
            and t1.directory_status = 1
            <if test="drugsName != null and drugsName != ''">
                and t3.drugs_name like concat('%', #{drugsName}, '%')
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                <!-- feat 1014705 修改为 通过拼音码、商品名或通用名搜索-->
                and (
                t3.standard_common_name like concat('%', #{standardCommonName}, '%')
                or t3.drugs_name like concat('%', #{standardCommonName}, '%')
                or t3.pinyin_code like concat('%', #{standardCommonName}, '%')
                )
            </if>
            <if test="classifyIdList != null and classifyIdList.size > 0">
                and t3.classify_id in
                <foreach collection="classifyIdList" item="each" separator="," open="(" close=")">
                    #{each}
                </foreach>
            </if>
            <if test="directoryType == 1 and hasStock != null">
                <choose>
                    <when test="hasStock == true">
                        and ((IFNULL(t2.stock, 0) + IFNULL(begp.stock, 0)) > 0)
                    </when>
                    <otherwise>
                        and ((IFNULL(t2.stock, 0) + IFNULL(begp.stock, 0)) = 0)
                    </otherwise>
                </choose>
            </if>
            <if test="directoryType == 2 and hasStock != null">
                <choose>
                    <when test="hasStock == true">
                        and 1 = 1
                    </when>
                    <otherwise>
                        and 1 = 2
                    </otherwise>
                </choose>
            </if>
        </where>
        <!-- order by stock DESC 目前根据药品库存累加后排序会导致扫描全表，暂先把order by 移除 -->
    </select>

    <select id="selectDrugsInfo" parameterType="com.puree.hospital.app.domain.dto.RxDrugQueryDTO"
            resultType="com.puree.hospital.app.domain.vo.DoctorOfficinaDrugsVo">
        <include refid="Drug_Detail_Select"/>
        WHERE t1.hospital_id = #{hospitalId}
        AND t1.drugs_id = #{drugsId}
        and t1.directory_type = #{directoryType}
        and t1.directory_status = 1
        limit 1
    </select>

    <select id="selectEnterpriseDrugsId" parameterType="java.lang.Long"
            resultType="com.puree.hospital.app.domain.BusEnterpriseDrugs">
        SELECT
            t2.enterprise_drugs_id,
            t2.drugs_id
        FROM
            bus_hospital_officina t1
            INNER JOIN bus_enterprise_drugs t2 ON t1.drugs_id = t2.drugs_id AND t1.enterprise_id = t2.enterprise_id
        WHERE
            t2.enterprise_drugs_id > 0
            and t1.hospital_id = #{hospitalId}
    </select>

    <select id="countDrugByBizDepartmentId" parameterType="com.puree.hospital.app.domain.dto.RxDrugQueryDTO" resultType="java.lang.Long">
        SELECT
            count(bdd.drugs_id)
        FROM bus_directory_drugs bdd
            inner join bus_department_drugs dd ON dd.drugs_id = bdd.drugs_id
            inner JOIN bus_associate_department a ON dd.department_id = a.department_id
            inner JOIN bus_biz_department c ON a.biz_department_id = c.id
        WHERE
            c.id = #{bizDepartmentId}
            and bdd.hospital_id = #{hospitalId}
            and bdd.directory_type = #{directoryType}
            and bdd.drugs_id = #{drugsId}
    </select>

    <select id="queryTcmUpSet" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT
            enterprise_id
        FROM
            bus_hospital_tcm_enterprise
        WHERE
            hospital_id = #{hospitalId}
    </select>

    <select id="selectClassifyIds" resultType="java.lang.String">
        SELECT DISTINCT
            CONCAT( t3.ancestors, ',', t2.classify_id )
        FROM bus_directory_drugs t1
            <if test="directoryType == '1' or directoryType == '3'">
                INNER JOIN bus_hospital_officina t4 ON t1.drugs_id = t4.drugs_id
            </if>
            LEFT JOIN bus_drugs t2 ON t1.drugs_id = t2.id
            LEFT JOIN bus_dict_drugs_classify t3 ON t2.classify_id = t3.id
        <where>
            t1.hospital_id = #{hospitalId}
            and t1.directory_type = #{directoryType}
            <if test="directoryType == '1' or directoryType == '3'">
                AND t4.`status` = 1
            </if>
            AND t2.classify_id IS NOT NULL
        </where>
    </select>

</mapper>
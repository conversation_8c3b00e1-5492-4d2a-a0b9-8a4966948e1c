<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusPrescriptionMapper">

    <!-- 处方列表详情map -->
    <resultMap id="prescriptionListDetailResultMap" type="com.puree.hospital.app.domain.vo.BusPrescriptionVo">
        <result column="id" property="id"/>
        <result column="prescription_number" property="prescriptionNumber"/>
        <result column="doctor_id" property="doctorId"/>
        <result column="doctor_name" property="doctorName"/>
        <result column="prescription_time" property="prescriptionTime"/>
        <result column="review_pharmacist_id" property="reviewPharmacistId"/>
        <result column="review_pharmacist_name" property="reviewPharmacistName"/>
        <result column="review_time" property="reviewTime"/>
        <result column="patient_id" property="patientId"/>
        <result column="family_id" property="familyId"/>
        <result column="family_name" property="familyName"/>
        <result column="family_age" property="familyAge"/>
        <result column="family_sex" property="familySex"/>
        <result column="family_tel" property="familyTel"/>
        <result column="family_idcard" property="familyIdcard"/>
        <result column="allergic_history" property="allergicHistory"/>
        <result column="preliminary_diagnosis" property="preliminaryDiagnosis"/>
        <result column="prescription_amount" property="prescriptionAmount"/>
        <result column="prescription_real_amount" property="prescriptionRealAmount"/>
        <result column="decoct" property="decoct"/>
        <result column="prescription_type" property="prescriptionType"/>
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="clinical_diagnosis" property="clinicalDiagnosis"/>
        <result column="dispensing_pharmacist_id" property="dispensingPharmacistId"/>
        <result column="dispensing_pharmacist_name" property="dispensingPharmacistName"/>
        <result column="delivery_pharmacist_id" property="deliveryPharmacistId"/>
        <result column="delivery_pharmacist_name" property="deliveryPharmacistName"/>
        <result column="hospital_id" property="hospitalId"/>
        <result column="hospital_name" property="hospitalName"/>
        <result column="hospital_address" property="hospitalAddress"/>
        <result column="status" property="status"/>
        <result column="not_approved_reason" property="notApprovedReason"/>
        <result column="remark" property="remark"/>
        <result column="pre_audit_id" property="preAuditId"/>
        <result column="pre_risk_score" property="preRiskScore"/>
        <result column="drug_directory_type" property="drugDirectoryType"/>
        <result column="consultation_order_id" property="consultationOrderId"/>
        <result column="processing_method" property="processingMethod"/>
        <result column="usages" property="usages"/>
        <result column="pa_name" property="paName"/>
        <result column="type" property="type"/>
        <result column="service_pack_id" property="servicePackId"/>
        <result column="identity" property="identity"/>
        <result column="preorder_id" property="preorderId"/>
        <result column="expert_id" property="expertId"/>
        <result column="partners_code" property="partnersCode"/>
        <result column="pa_id" property="paId"/>
        <result column="zyfjType" property="zyfjType"/>
        <result column="examination_fee" property="examinationFee"/>
        <result column="examination_name" property="examinationName"/>
        <result column="fee_settle_type" property="feeSettleType"/>
        <result column="special_disease_info" property="specialDiseaseInfo"/>
        <result column="family_detail_address" property="familyDetailAddress"/>
        <collection property="list" column="id" javaType="ArrayList" ofType="com.puree.hospital.app.domain.BusPrescriptionDrugs"
                    select="selectRelPrescriptionDrugs">
        </collection>
    </resultMap>

    <resultMap id="BusPrescriptionDrugsResultMap" type="com.puree.hospital.app.domain.BusPrescriptionDrugs">
        <result column="id" property="id"/>
        <result column="prescription_id" property="prescriptionId"/>
        <result column="drugs_id" property="drugsId"/>
        <result column="drugs_manufacturer" property="drugsManufacturer"/>
        <result column="standard_common_name" property="standardCommonName"/>
        <result column="drugs_name" property="drugsName"/>
        <result column="drugs_img" property="drugsImg"/>
        <result column="drugs_specification" property="drugsSpecification"/>
        <result column="selling_price" property="sellingPrice"/>
        <result column="medication_frequency" property="medicationFrequency"/>
        <result column="single_dose" property="singleDose"/>
        <result column="unit" property="unit"/>
        <result column="drugs_usage_value" property="drugsUsageValue"/>
        <result column="quantity" property="quantity"/>
        <result column="medication_days" property="medicationDays"/>
        <result column="weight" property="weight"/>
        <result column="several_times_day" property="severalTimesDay"/>
        <result column="medication_frequency_remarks" property="medicationFrequencyRemarks"/>
        <result column="reference_purchase_price" property="referencePurchasePrice"/>
        <result column="drugs_dosage_form" property="drugsDosageForm"/>
        <result column="drugs_specification" property="drugsSpecification"/>
        <result column="hospital_approve_flag" property="hospitalApproveFlag"/>
        <result column="national_drug_code" property="miCode"/>
        <result column="min_pack_num" property="minPackNum"/>
        <result column="min_pack_unit" property="minPackUnit"/>
        <result column="min_make_unit" property="minMakeUnit"/>
        <result column="main_img" property="mainImg"/>
    </resultMap>

    <resultMap id="resultPdMap" type="com.puree.hospital.app.domain.BusPrescription">
        <id column="id" property="id"/>
        <result column="prescription_type" property="prescriptionType"/>
        <result column="status" property="status"/>
        <result column="prescription_amount" property="prescriptionAmount"/>
        <result column="identity" property="identity"/>
        <result column="patient_id" property="patientId"/>
        <result column="group_id" property="groupId"/>
        <result column="examination_fee" property="examinationFee"/>
        <result column="examination_name" property="examinationName"/>
        <result column="pre_audit_id" property="preAuditId"/>
        <result column="pre_risk_score" property="preRiskScore"/>
        <result column="fee_settle_type" property="feeSettleType"/>
        <result column="special_disease_info" property="specialDiseaseInfo"/>
        <collection property="pdDrugsList" ofType="com.puree.hospital.app.domain.BusPrescriptionDrugs">
            <result column="enterprise_id" property="enterpriseId"/>
            <result column="drugs_id" property="drugsId"/>
        </collection>
    </resultMap>

    <!-- 获取待审核列表详情 -->
    <select id="selectNotApprovedList" parameterType="com.puree.hospital.app.domain.BusPrescription" resultMap="prescriptionListDetailResultMap">
        select p.*,p2.type  as zyfjType
        from bus_prescription p
        LEFT JOIN bus_hospital_pa p2 ON p.pa_id = p2.id
        where p.hospital_id = #{hospitalId}
        and p.status = #{status}
        <if test="prescriptionType !='' and prescriptionType !=null">
            <choose>
                <when test='prescriptionType=="1"'>
                    and p.prescription_type = #{prescriptionType}
                </when>
                <otherwise>
                    and p.prescription_type in (0,2)
                </otherwise>
            </choose>
        </if>
        order by p.prescription_time
    </select>

    <!-- 获取处方药品 -->
    <select id="selectRelPrescriptionDrugs" parameterType="java.lang.Long" resultMap="BusPrescriptionDrugsResultMap">
        SELECT
            pd.id,
            pd.prescription_id,
            pd.drugs_id,
            pd.drugs_manufacturer,
            pd.standard_common_name,
            pd.drugs_name,
            pd.drugs_img,
            pd.drugs_specification,
            pd.selling_price,
            pd.medication_frequency,
            pd.single_dose,
            pd.unit,
            pd.drugs_usage_value,
            SUM(pd.quantity) quantity,
            pd.medication_days,
            pd.hospital_id,
            pd.create_time,
            pd.weight,
            pd.several_times_day,
            pd.medication_frequency_remarks,
            pd.enterprise_id,
            pd.reference_purchase_price,
            pd.hospital_approve_flag,
            bd.drugs_dosage_form,
            bd.detailed_specifications,
            bd.drugs_specification,
            bd.national_drug_code,
            bd.min_pack_num,
            bd.min_make_unit,
            bd.min_pack_unit,
            bd.main_img
        FROM bus_prescription_drugs pd
            JOIN bus_drugs bd ON pd.drugs_id = bd.id
        WHERE
            pd.prescription_id = #{id}
        GROUP BY
            pd.drugs_id
    </select>

    <!-- 获取已审核列表详情 -->
    <select id="selectApprovedList" parameterType="com.puree.hospital.app.domain.BusPrescription" resultMap="prescriptionListDetailResultMap">
        select p.*,p2.type as zyfjType
        from bus_prescription p
        LEFT JOIN bus_hospital_pa p2 ON p.pa_id = p2.id
        where p.hospital_id = #{hospitalId}
        and p.review_pharmacist_id = #{reviewPharmacistId}
        <if test="status !='' and status !=null">
            and p.status in (1,2)
        </if>
        order by p.prescription_time desc
    </select>

    <!-- 获取经过审核的列表详情 -->
    <select id="selectApprovedPatientList" parameterType="com.puree.hospital.app.domain.BusPrescription" resultMap="prescriptionListDetailResultMap">
        SELECT DISTINCT p.*,p2.type as zyfjType
        FROM bus_prescription p
        LEFT JOIN `bus_hospital_family` h ON h.family_id=p.family_id AND h.patient_id=p.patient_id AND p.hospital_id = h.hospital_id
        LEFT JOIN bus_hospital_pa p2 ON p.pa_id = p2.id
        <where>
            p.hospital_id = #{hospitalId}
            and p.patient_id = #{patientId}
            and p.status in (1, 2, 3, 4, 5)
            and p.review_pharmacist_id is not null
            <if test="partnersCode != null and partnersCode != ''">
                and p.partners_code = #{partnersCode}
            </if>
        </where>
        group by p.id
        order by p.review_time desc
    </select>

    <!-- 获取西医诊断 -->
    <select id="selectMMDiagnosisList" parameterType="java.lang.String" resultType="com.puree.hospital.app.domain.vo.MMDiagnosisVo">
        select d.id,d.disease_name,d.icd_code,d.pin_yin_code
        from bus_disease d
        where d.status = 1
        <if test="key != null and key != ''">
            and (d.disease_name like concat('%', #{key}, '%') or d.pin_yin_code like concat('%', #{key}, '%'))
        </if>
        ORDER BY CHAR_LENGTH(d.disease_name)
    </select>

    <!-- 获取西医诊断 -->
    <select id="selectMMDiagnosisListByIds" resultType="com.puree.hospital.app.domain.vo.MMDiagnosisVo">
        select d.id,d.disease_name,d.icd_code,d.pin_yin_code
        from bus_disease d
        where d.status = 1
        <if test="ids != null and ids.size() > 0">
            and d.id IN
            <foreach collection="ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 获取中医诊断 -->
    <select id="selectTCMDiagnosisList" parameterType="java.lang.String" resultType="com.puree.hospital.app.domain.vo.TCMDiagnosisVo">
        select td.id, td.tcm_diagnosis,td.pin_yin_code,td.diagnosis_code
        from bus_tcm_diagnosis td
        where td.status = 1
        <if test="key != null and key != ''">
            and (td.tcm_diagnosis like concat('%', #{key}, '%') or td.pin_yin_code like concat('%', #{key}, '%'))
        </if>
    </select>

    <!-- 获取中医诊断 -->
    <select id="selectTCMById" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.vo.TCMDiagnosisVo">
        select td.id, td.tcm_diagnosis,td.pin_yin_code,td.diagnosis_code
        from bus_tcm_diagnosis td
        where td.id = #{id}
    </select>

    <!--获取中医症型-->
    <select id="selectTCMSyndromeById" parameterType="java.lang.String" resultType="com.puree.hospital.app.domain.vo.TCMSyndromeVo">
        select d.id,d.tcm_syndrome,d.diagnosis_code,d.pin_yin_code
        from bus_tcm_syndrome d
        where d.id = #{id}
    </select>

    <!-- 根据处方ID获取患者信息 -->
    <select id="selectPatient" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.vo.PatientTemplateMsgVo">
       SELECT
            bp.partners_code,
			ph.openid,
            bp.id prescriptionId,
            bp.prescription_number,
            p.id patientId,
            bp.doctor_id,
            bp.doctor_name,
            bp.department_id,
            p.NAME patientName,
            bp.clinical_diagnosis,
            bp.family_id,
            bp.family_name,
            bp.hospital_id,
            bp.type,
            bp.pa_name,
            bp.expert_id,
            bp.group_id,
            p2.type AS zyfjType,
            bp.prescription_time,
            bp.usages,
            bp.family_age,
            bp.family_sex
        FROM
            bus_patient p
            LEFT JOIN bus_prescription bp ON bp.patient_id = p.id
            LEFT JOIN bus_patient_hospital ph ON p.id = ph.patient_id  AND bp.hospital_id = ph.hospital_id
            LEFT JOIN bus_hospital_pa p2 ON bp.pa_id = p2.id
        WHERE
            bp.id = #{prescriptionId}
            AND bp.hospital_id = #{hospitalId}
    </select>

    <!-- 根据处方ID获取处方药品信息 -->
    <select id="selectPrescriptionDrugs" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.vo.PatientTemplateMsgVo">
        select pd.drugs_name
        from bus_prescription_drugs pd
        where pd.prescription_id = #{prescriptionId}
    </select>

    <!-- 审核处方 -->
    <update id="approve" parameterType="com.puree.hospital.app.api.model.ApprovePrescriptionDTO">
        update bus_prescription p
        set p.status                 = #{status},
            p.review_pharmacist_id   = #{reviewPharmacistId},
            p.review_pharmacist_name = #{reviewPharmacistName},
            p.review_time            = #{reviewTime},
            p.not_approved_reason    = #{notApprovedReason},
            p.update_by              = #{updateBy},
            p.update_time            = #{updateTime},
            p.valid_time             = #{validTime}
        where p.id = #{id}
          and p.hospital_id = #{hospitalId}
    </update>

    <!-- 处方列表 -->
    <select id="selectPrescriptionList" parameterType="com.puree.hospital.app.domain.BusPrescription"
            resultMap="prescriptionListDetailResultMap">
        SELECT DISTINCT
            t1.id,
            t1.prescription_number,
            t1.doctor_id,
            t1.doctor_name,
            t1.prescription_time,
            t1.review_pharmacist_id,
            t1.review_pharmacist_name,
            t1.review_time,
            t1.patient_id,
            t1.family_id,
            t1.family_name,
            t1.family_age,
            t1.family_sex,
            t1.family_tel,
            t1.family_idcard,
            t1.allergic_history,
            t1.preliminary_diagnosis,
            t1.prescription_amount,
            t1.prescription_real_amount,
            t1.decoct,
            t1.prescription_type,
            t1.department_id,
            t1.department_name,
            t1.clinical_diagnosis,
            t1.dispensing_pharmacist_id,
            t1.dispensing_pharmacist_name,
            t1.delivery_pharmacist_id,
            t1.delivery_pharmacist_name,
            t1.STATUS,
            t1.hospital_id,
            t1.hospital_name,
            t1.hospital_address,
            t1.not_approved_reason,
            t1.remark,
            t1.pre_audit_id,
            t1.pre_risk_score,
            t1.consultation_order_id,
            t1.processing_method,
            t1.usages,
            t1.pa_name,
            t1.`type`,
            t1.service_pack_id,
            t1.identity,
            t1.send_to_dest,
            t1.preorder_id,
            t1.expert_id,
            t1.partners_code,
            t1.pa_id,
            t1.examination_fee,
            t1.examination_name,
            t1.fee_settle_type,
            t1.special_disease_info,
            t1.family_detail_address,
            t1.drug_directory_type,
            t1.path,
            t1.group_id,
            t1.valid_time,
            t1.create_time,
            t1.create_by,
            t1.update_time,
            t1.update_by
        FROM bus_prescription t1
        <where>
            <if test="hospitalId != null">
                AND t1.hospital_id = #{hospitalId}
            </if>
            <if test="doctorId != null ">
                AND t1.doctor_id = #{doctorId}
            </if>
            <if test="expertId != null ">
                AND t1.expert_id = #{expertId}
            </if>
            <if test="familyId != null">
                and t1.family_id =#{familyId}
            </if>
            <if test="patientId != null">
                and t1.patient_id =#{patientId}
            </if>
            <if test="type != null">
                and t1.`type` =#{type}
            </if>
            <if test="status != null and status!='' ">
                and status = #{status}
            </if>
            <if test="notPreorder">
                and t1.`preorder_id` IS NULL
            </if>
            <if test="prescriptionNumber != null and prescriptionNumber != ''">
                and t1.prescription_number = #{prescriptionNumber}
            </if>
            <if test="familyIdcard != null and familyIdcard != ''">
                and t1.family_idcard = #{familyIdcard}
            </if>
            <if test="prescriptionType != null and prescriptionType != ''">
                and t1.prescription_type = #{prescriptionType}
            </if>
            <if test="drugDirectoryType != null and drugDirectoryType!='' ">
                and t1.drug_directory_type = #{drugDirectoryType}
            </if>
            <if test="excludeDirectoryType != null and excludeDirectoryType!='' ">
                and t1.drug_directory_type != #{excludeDirectoryType}
            </if>

            <if test="statusList != null and statusList.size() > 0">
                and t1.`status` in
                <foreach collection="statusList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                # 过滤中药协定方，中药协定方没有处方
                and t1.prescription_type != 2
            </if>
            <if test="startTime != null">
                and t1.prescription_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and t1.prescription_time &lt;= #{endTime}
            </if>
        </where>
        order by t1.prescription_time desc
    </select>

    <!--获取中医症型-->
    <select id="selectTCMSyndromeList" parameterType="java.lang.String" resultType="com.puree.hospital.app.domain.vo.TCMSyndromeVo">
        select d.id,d.tcm_syndrome,d.diagnosis_code,d.pin_yin_code
        from bus_tcm_syndrome d
        where d.status = 1
        <if test="key != null and key != ''">
            and (d.tcm_syndrome like concat('%', #{key}, '%') or d.pin_yin_code like concat('%', #{key}, '%'))
        </if>
    </select>

    <select id="selectHospitalInfo" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.vo.BusHospitalVo">
        SELECT
            t1.hospital_phone,
            t1.hospital_seal,
            s1.NAME province,
            s2.NAME city,
            s3.NAME area
        FROM
            bus_hospital t1
            LEFT JOIN sys_province s1 ON t1.province_code = s1.
            CODE LEFT JOIN sys_city s2 ON t1.city_code = s2.
            CODE LEFT JOIN sys_area s3 ON t1.area_code = s3.CODE
        WHERE
	        t1.id = #{hospitalId}
    </select>

    <select id="queryInvalidList" resultType="com.puree.hospital.app.domain.BusPrescription">
        SELECT
            id,
            prescription_number,
            doctor_id,
            doctor_name,
            prescription_time,
            review_pharmacist_id,
            review_pharmacist_name,
            review_time,
            patient_id,
            family_id,
            family_name,
            family_sex,
            family_age,
            family_tel,
            family_idcard,
            allergic_history,
            preliminary_diagnosis,
            prescription_amount,
            prescription_real_amount,
            decoct,
            prescription_type,
            department_id,
            department_name,
            clinical_diagnosis,
            dispensing_pharmacist_id,
            dispensing_pharmacist_name,
            delivery_pharmacist_id,
            delivery_pharmacist_name,
            hospital_id,
            hospital_name,
            hospital_address,
            `status`,
            not_approved_reason,
            remark,
            pre_audit_id,
            pre_risk_score,
            create_by,
            create_time,
            update_by,
            update_time,
            drug_directory_type,
            consultation_order_id,
            processing_method,
            usages,
            pa_name,
            pa_id,
            enterprise_id,
            enterprise_name,
            `type`,
            send_to_dest,
            partners_code,
            `path`,
            service_pack_id,
            `identity`,
            preorder_id,
            expert_id,
            process_price,
            enterprise_process_price,
            group_id,
            valid_time,
            examination_fee,
            examination_name,
            fee_settle_type,
            special_disease_info,
            family_detail_address
        FROM bus_prescription
        WHERE `status` = 2
          and id <![CDATA[ > ]]> #{idx}
          and valid_time <![CDATA[ < ]]> now()
          limit #{pageSize}
    </select>

    <select id="selectPrescriptionStatusCount" parameterType="java.lang.Long"  resultType="com.puree.hospital.app.domain.vo.PrescriptionStatusTypeVO">
         SELECT
             status,
             hospital_id,
             CASE
                 status
             WHEN 0
                 THEN '审核中'
             WHEN 1
                 THEN '审核拒绝'
             WHEN 2
                 THEN '审核通过'
             WHEN 3
                  THEN '已作废'
             WHEN 4
                  THEN '已使用'
             WHEN 5
                  THEN '已失效'
             WHEN 6
                  THEN '未签名'
             END AS name,
             COUNT(1) AS count
         FROM `bus_prescription`
         WHERE status IN(0,1,3) AND hospital_id = #{hospitalId}
         <if test="doctorId != null and doctorId !='' ">
             AND doctor_id = #{doctorId}
         </if>
        <if test="expertId != null and expertId !='' ">
            AND expert_id = #{expertId}
        </if>
         GROUP BY status
    </select>

    <select id="selectPrescriptionInfo" resultType="com.puree.hospital.app.domain.BusPrescription">
        SELECT
            t1.*,
            t2.identifying
        FROM
            bus_prescription t1
            LEFT JOIN bus_enterprise t2 ON t1.enterprise_id = t2.id
        WHERE
            t1.id = #{prescriptionId}
    </select>

    <select id="selectPdList" parameterType="java.lang.Long" resultMap="resultPdMap">
        SELECT
            t1.id,
            t1.prescription_type,
            t1.`status`,
            t1.prescription_amount,
            t1.identity,
            t1.patient_id,
            t1.group_id,
            t1.examination_fee,
            t1.examination_name,
            t1.fee_settle_type,
            t1.special_disease_info,
            t2.enterprise_id,
            t2.drugs_id
        FROM
            bus_prescription t1
            INNER JOIN bus_prescription_drugs t2 ON t1.id = t2.prescription_id
        <where>
            t1.id IN
            <foreach collection="prescriptionIds" item="each" separator="," open="(" close=")">
                #{each}
            </foreach>
        </where>
    </select>

    <select id="selectPrescriptionDrugList" parameterType="java.lang.Long" resultMap="resultPdMap">
        SELECT
            t1.id,
            t1.prescription_type,
            t1.`status`,
            t1.prescription_amount,
            t1.identity,
            t1.examination_fee,
            t1.examination_name,
            t1.fee_settle_type,
            t1.special_disease_info,
            t2.enterprise_id,
            t2.drugs_id
        FROM
            bus_prescription t1
            INNER JOIN bus_prescription_drugs t2 ON t1.id = t2.prescription_id
        WHERE t1.id = #{prescriptionId}
    </select>

    <select id="getMiRx" resultMap="getMiRxResultMap">
        select
            t1.*,
            t2.*,
            t3.national_drug_code as miCode,
            t3.reference_purchase_price as drugs_reference_purchase_price
            from bus_prescription t1
            join bus_prescription_drugs t2 on t1.id = t2.prescription_id
            join bus_drugs t3 on t2.drugs_id = t3.id
        <where>
            <trim prefixOverrides="and">
                <if test="rxNoList != null and rxNoList.size > 0">
                    and t1.prescription_number in
                    <foreach collection="rxNoList" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="idList != null and idList.size > 0">
                    and t1.id in
                    <foreach collection="idList" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>

    </select>
    <resultMap id="getMiRxResultMap" type="com.puree.hospital.app.domain.BusPrescription">
        <result column="id" property="id"/>
        <result column="prescription_number" property="prescriptionNumber"/>
        <result column="doctor_id" property="doctorId"/>
        <result column="doctor_name" property="doctorName"/>
        <result column="prescription_time" property="prescriptionTime"/>
        <result column="review_pharmacist_id" property="reviewPharmacistId"/>
        <result column="review_pharmacist_name" property="reviewPharmacistName"/>
        <result column="review_time" property="reviewTime"/>
        <result column="patient_id" property="patientId"/>
        <result column="family_id" property="familyId"/>
        <result column="family_name" property="familyName"/>
        <result column="family_sex" property="familySex"/>
        <result column="family_age" property="familyAge"/>
        <result column="family_tel" property="familyTel"/>
        <result column="family_idcard" property="familyIdcard"/>
        <result column="allergic_history" property="allergicHistory"/>
        <result column="preliminary_diagnosis" property="preliminaryDiagnosis"/>
        <result column="prescription_amount" property="prescriptionAmount"/>
        <result column="prescription_real_amount" property="prescriptionRealAmount"/>
        <result column="decoct" property="decoct"/>
        <result column="prescription_type" property="prescriptionType"/>
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="clinical_diagnosis" property="clinicalDiagnosis"/>
        <result column="dispensing_pharmacist_id" property="dispensingPharmacistId"/>
        <result column="dispensing_pharmacist_name" property="dispensingPharmacistName"/>
        <result column="delivery_pharmacist_id" property="deliveryPharmacistId"/>
        <result column="delivery_pharmacist_name" property="deliveryPharmacistName"/>
        <result column="hospital_id" property="hospitalId"/>
        <result column="hospital_name" property="hospitalName"/>
        <result column="hospital_address" property="hospitalAddress"/>
        <result column="status" property="status"/>
        <result column="not_approved_reason" property="notApprovedReason"/>
        <result column="remark" property="remark"/>
        <result column="pre_audit_id" property="preAuditId"/>
        <result column="pre_risk_score" property="preRiskScore"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="drug_directory_type" property="drugDirectoryType"/>
        <result column="consultation_order_id" property="consultationOrderId"/>
        <result column="processing_method" property="processingMethod"/>
        <result column="usages" property="usages"/>
        <result column="pa_name" property="paName"/>
        <result column="pa_id" property="paId"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <result column="type" property="type"/>
        <result column="send_to_dest" property="sendToDest"/>
        <result column="partners_code" property="partnersCode"/>
        <result column="path" property="path"/>
        <result column="service_pack_id" property="servicePackId"/>
        <result column="identity" property="identity"/>
        <result column="preorder_id" property="preorderId"/>
        <result column="expert_id" property="expertId"/>
        <result column="process_price" property="processPrice"/>
        <result column="enterprise_process_price" property="enterpriseProcessPrice"/>
        <result column="examination_fee" property="examinationFee"/>
        <result column="examination_name" property="examinationName"/>
        <result column="fee_settle_type" property="feeSettleType"/>
        <result column="special_disease_info" property="specialDiseaseInfo"/>
        <result column="mi_auth_no" property="miAuthNo"/>
        <result column="family_detail_address" property="familyDetailAddress"/>
        <collection property="pdDrugsList" ofType="com.puree.hospital.app.domain.BusPrescriptionDrugs">
            <result column="prescription_id" property="prescriptionId"/>
            <result column="drugs_id" property="drugsId"/>
            <result column="drugs_manufacturer" property="drugsManufacturer"/>
            <result column="standard_common_name" property="standardCommonName"/>
            <result column="drugs_name" property="drugsName"/>
            <result column="drugs_img" property="drugsImg"/>
            <result column="drugs_specification" property="drugsSpecification"/>
            <result column="selling_price" property="sellingPrice"/>
            <result column="medication_frequency" property="medicationFrequency"/>
            <result column="single_dose" property="singleDose"/>
            <result column="unit" property="unit"/>
            <result column="drugs_usage_value" property="drugsUsageValue"/>
            <result column="quantity" property="quantity"/>
            <result column="medication_days" property="medicationDays"/>
            <result column="weight" property="weight"/>
            <result column="several_times_day" property="severalTimesDay"/>
            <result column="medication_frequency_remarks" property="medicationFrequencyRemarks"/>
            <result column="miCode" property="miCode"/>
            <result column="reference_purchase_price" property="referencePurchasePrice"/>
            <result column="drugs_reference_purchase_price" property="drugsReferencePurchasePrice"/>
            <result column="hospital_approve_flag" property="hospitalApproveFlag"/>
        </collection>
    </resultMap>

    <select id="countPreorderApproved" resultType="java.lang.Integer">
        select count(*) from bus_prescription where preorder_id=#{preorderId}
    </select>
    <select id="selectExpertPrescriptionList" resultType="com.puree.hospital.app.domain.vo.BusPrescriptionVo">
        SELECT DISTINCT
        t1.id,
        t1.prescription_number,
        t1.drug_directory_type,
        t1.doctor_name,
        t1.prescription_time,
        t1.department_name,
        t1.clinical_diagnosis,
        t1.STATUS,
        t1.review_time,
        t1.hospital_id,
        t1.doctor_id,
        t1.department_id,
        t1.patient_id,
        t1.family_id,
        t1.family_name,
        t1.prescription_type,
        t1.pa_name,
        t1.send_to_dest,
        t1.service_pack_id,
        t1.identity,
        t1.preorder_id,
        t1.expert_id,
        t1.partners_code,
        t1.pa_id,
        t1.examination_fee,
        t1.examination_name,
        t1.fee_settle_type,
        t1.special_disease_info,
        t1.family_detail_address
        FROM
        bus_prescription t1
        <where>
            <if test="hospitalId != null">
                AND t1.hospital_id = #{hospitalId}
            </if>
            <if test="expertId != null ">
                AND t1.expert_id = #{expertId}
            </if>
            <if test="familyId != null">
                and t1.family_id =#{familyId}
            </if>
            <if test="patientId != null">
                and t1.patient_id =#{patientId}
            </if>
            <if test="type != null">
                and t1.`type` =#{type}
            </if>
            <if test="status != null and status!='' ">
                and t1.`status` =#{status}
            </if>
            <if test="notPreorder">
                and t1.`preorder_id` IS NULL
            </if>
        </where>
        order by t1.prescription_time desc
    </select>

</mapper>
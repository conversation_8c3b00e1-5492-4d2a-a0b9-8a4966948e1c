<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.app.mapper.BusOrderAfterSalesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.puree.hospital.app.domain.BusOrderAfterSales">
        <id column="id" property="id" />
        <result column="hospital_id" property="hospitalId" />
        <result column="after_sales_number" property="afterSalesNumber" />
        <result column="apply_time" property="applyTime" />
        <result column="refund_type" property="refundType" />
        <result column="after_sales_cause" property="afterSalesCause" />
        <result column="refund_amount" property="refundAmount" />
        <result column="description" property="description" />
        <result column="after_sales_voucher" property="afterSalesVoucher" />
        <result column="after_sales_status" property="afterSalesStatus" />
        <result column="after_sales_type" property="afterSalesType" />
        <result column="order_no" property="orderNo" />
        <result column="logistics_company" property="logisticsCompany" />
        <result column="delivery_no" property="deliveryNo" />
        <result column="logistics_explain" property="logisticsExplain" />
        <result column="logistics_voucher" property="logisticsVoucher" />
        <result column="return_address" property="returnAddress" />
        <result column="return_contact_phone" property="returnContactPhone" />
        <result column="return_time" property="returnTime" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <insert id="batchInsert" parameterType="BusOrderAfterSales" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO bus_order_after_sales
        (
            hospital_id,
            after_sales_number,
            apply_time,
            refund_type,
            after_sales_cause,
            refund_amount,
            description,
            after_sales_voucher,
            after_sales_status,
            order_no,
            create_time,
            prescription_id,
            order_id,
            after_sales_type,
            goods_id,
            selling_price,
            quantity
        )
        VALUES
        <foreach collection="orderAfterSalesList" item="i" separator=",">
            (
                #{i.hospitalId},
                #{i.afterSalesNumber},
                #{i.applyTime},
                #{i.refundType},
                #{i.afterSalesCause},
                #{i.refundAmount},
                #{i.description},
                #{i.afterSalesVoucher},
                #{i.afterSalesStatus},
                #{i.orderNo},
                #{i.createTime},
                #{i.prescriptionId},
                #{i.orderId},
                #{i.afterSalesType},
                #{i.goodsId},
                #{i.sellingPrice},
                #{i.quantity}
            )
        </foreach>
    </insert>
    <update id="updateAfterSaleStatusRefundSuccessByIds">
        UPDATE bus_order_after_sales
        SET after_sales_status = '7', update_time = NOW(),refund_status = 2
        WHERE id IN
              <foreach collection="ids" item="id" separator="," open="(" close=")">
                  #{id}
              </foreach>
    </update>
    <select id="selectAfterSalesListByOrderNo" resultType="com.puree.hospital.app.domain.BusOrderAfterSales">
        SELECT * FROM bus_order_after_sales WHERE order_no = #{orderNo}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusDoctorCommonlyUsedMapper">

    <select id="selectCommonlyUsedDrugsList" parameterType="com.puree.hospital.app.domain.dto.RxDrugQueryDTO"
            resultType="com.puree.hospital.app.domain.vo.DoctorOfficinaDrugsVo">
        SELECT
            t1.id,
            t1.drugs_source_id,
            t1.drugs_id,
            t1.status,
            t1.doctor_id,
            t1.create_time,
            t1.hospital_id,
            t2.drugs_name,
            t2.standard_common_name,
            t2.drugs_manufacturer,
            t2.drugs_specification,
            t2.drugs_img,
            t2.main_img,
            t2.drugs_img_detail,
            t2.classify_id,
            t2.drugs_usage,
            t2.detailed_specifications,
            t2.medical_insurance_type,
            t2.national_drug_code,
            t2.drugs_packaging_unit,
            t2.min_pack_num,
            t2.min_make_unit,
            t2.min_pack_unit,
            t2.base_dose,
            t2.base_dose_unit,
            t2.single_dose,
            t2.default_frequency,
            t2.recommend_use_days,
            t2.drugs_dosage_form,
            t3.directory_type,
            <choose>
                <when test="directoryType == 1">
                    t6.selling_price,
                    IFNULL(t6.stock,0) + IFNULL(begp.stock,0) as stock,
                    t6.enterprise_id,
                </when>
                <when test="directoryType == 2">
                    t2.reference_selling_price as selling_price,
                    9999 as stock,
                </when>
            </choose>
            t4.name AS drugs_usage_value,
            t5.ancestors
        FROM bus_doctor_commonly_used t1
        INNER JOIN bus_drugs t2 ON t2.id = t1.drugs_id and t2.status = 1
        INNER JOIN bus_directory_drugs t3 ON t3.drugs_id = t1.drugs_id and t3.directory_status = 1 and t3.directory_type = #{directoryType} and t3.hospital_id = #{hospitalId}
        LEFT JOIN bus_dict_drugs_usage t4 ON t4.id = t2.drugs_usage
        LEFT JOIN bus_dict_drugs_classify t5 ON t5.id = t2.classify_id
        <if test="directoryType == 1">
            INNER JOIN bus_hospital_officina t6 ON t6.id = t1.drugs_source_id AND t6.status = 1 and t6.hospital_id = #{hospitalId}
            LEFT JOIN bus_enterprise_drugs beg ON t6.enterprise_id = beg.enterprise_id AND t6.drugs_id = beg.drugs_id
            LEFT JOIN bus_enterprise_drugs_price begp ON beg.id = begp.drug_ref_id AND begp.hospital_id = #{hospitalId}
        </if>
        <where>
            t1.hospital_id = #{hospitalId}
            and t1.doctor_id = #{doctorId}
            <choose>
                <when test="directoryType == 1">
                    and t1.drugs_source_id > 0
                </when>
                <when test="directoryType == 2">
                    and (t1.drugs_source_id = -1 or t1.drugs_source_id is null)
                </when>
            </choose>
            <if test="standardCommonName != null and standardCommonName != ''">
                and t2.standard_common_name like concat('%', #{standardCommonName}, '%')
            </if>
            <if test="classifyIdList != null and classifyIdList.size > 0">
                and t2.classify_id in
                <foreach collection="classifyIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by t1.create_time desc
    </select>

    <select id="selectListDrugsId" resultType="java.lang.Long">
        select
            drugs_id
        from bus_doctor_commonly_used
        where hospital_id = #{hospitalId} and doctor_id = #{doctorId}
        <choose>
            <when test="directoryType == 2">
                and drugs_source_id = -1
            </when>
            <when test="directoryType == 1">
                and drugs_source_id > 0
            </when>
        </choose>
    </select>

</mapper>
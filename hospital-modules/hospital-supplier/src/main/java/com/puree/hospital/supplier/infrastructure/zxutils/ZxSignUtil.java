package com.puree.hospital.supplier.infrastructure.zxutils;

import org.apache.tomcat.util.codec.binary.Base64;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * 至信签名工具类
 * <AUTHOR>
 */
public class ZxSignUtil {

    /**
     * 获取token
     * @return token
     */
    public static String getToken(String json) {
        String token="";
        try{
            //json 请求参数 参考下面业务报文请求示例
            String toVerifyText = URLEncoder.encode(json, "UTF-8");
            //进行Md5加密
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(toVerifyText.getBytes(StandardCharsets.UTF_8));
            byte[] md = md5.digest();
            //通过BASE64生成数字签名
            token = new String(new Base64().encode(md));
        }catch (Exception ex){
            throw new RuntimeException("获取至信签名异常！",ex);
        }
        return token;
    }

    public static void main(String[] args) {
        String ss = getToken("{\"code\":\"5d7694cbec221f9595f50860d08f2ab3\",\"sign\":\"biran\",\"currentTime\":**********,\"hospitalOrderCode\":\"9151964627693d\",\"cusCode\":\"810001\",\"orderType\":\"2\",\"orderDosis\":\"2\",\"friedNum\":\"3\",\"orderItemCnt\":12,\"patientId\":\"c001\",\"patientAge\":\"31\",\"sumPrice\":\"209.17\",\"patientGender\":\"1\",\"patientName\":\"王峰\",\"doctorName\":\"陆小风\",\"doctorPhone\":\"12345678911\",\"receiptAddress\":\"广东省广州市黄埔区北京路12号\",\"receiver\":\"陈浮生\",\"contactPhone\":\"18000000000\",\"orderRemark\":\"快递到付\",\"usingType\":\"煎服方法：600ml水煎至200ml，饭后，1次温服，复煎一次。医嘱：餐后30-60分钟服用\",\"isCollection\":\"1\",\"agencyFund\":\"209.17\",\"agentFlag\":\"1\",\"agentType\":\"1\",\"deskName\":\"0\",\"orderMakeTime\":\"2018-01-05 16:54:00\",\"province\":\"广东省\",\"city\":\"广州市\",\"district\":\"黄埔区\",\"suffix_addr\":\"北京路12号\",\"medicines\":[{\"hospitalMedicineCode\":\"A1004\",\"medicineName\":\"矮地茶\",\"specialDecoctionWay\":\"先煎\",\"purchaseNbr\":1,\"medPrice\":0.01,\"dosageUnit\":\"g\"}]}");
        ss = getToken("{\"code\":\"5d7694cbec221f9595f50860d08f2ab3\",\"sign\":\"biran\",\"currentTime\":**********,\"hospitalOrderCode\":\"9151964627693d\",\"cusCode\":\"810001\",\"orderType\":\"2\",\"orderDosis\":\"2\",\"friedNum\":\"3\",\"orderItemCnt\":12,\"patientId\":\"c001\",\"patientAge\":\"31\",\"sumPrice\":\"209.17\",\"patientGender\":\"1\",\"patientName\":\"王峰\",\"doctorName\":\"陆小风\",\"doctorPhone\":\"12345678911\",\"receiptAddress\":\"广东省广州市黄埔区北京路12号\",\"receiver\":\"陈浮生\",\"contactPhone\":\"18000000000\",\"orderRemark\":\"快递到付\",\"usingType\":\"煎服方法：600ml水煎至200ml，饭后，1次温服，复煎一次。医嘱：餐后30-60分钟服用\",\"isCollection\":\"1\",\"agencyFund\":\"209.17\",\"agentFlag\":\"1\",\"agentType\":\"1\",\"deskName\":\"0\",\"orderMakeTime\":\"2018-01-05 16:54:00\",\"province\":\"广东省\",\"city\":\"广州市\",\"district\":\"黄埔区\",\"suffix_addr\":\"北京路12号\",\"medicines\":[{\"hospitalMedicineCode\":\"A1638\",\"medicineName\":\"黄芪\",\"specialDecoctionWay\":\"先煎\",\"purchaseNbr\":1,\"medPrice\":0.01,\"dosageUnit\":\"g\"}]}");
        System.out.println(ss);
    }

}

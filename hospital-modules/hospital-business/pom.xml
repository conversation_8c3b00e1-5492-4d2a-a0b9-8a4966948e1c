<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hospital-modules</artifactId>
        <groupId>com.puree</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hospital-business</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-business-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 依赖业务api -->
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-im-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-supplier-api</artifactId>
            <version>${project.version}</version>

        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-app-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-tool-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-five-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-shop-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-order-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-system-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-insurance-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-monitor-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-operate-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-pay-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 依赖的组件 -->
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>puree-common-pdf</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-supplier</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-rabbitmq</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-job</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-feign</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-aliyun</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-security</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-log</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-datasource</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-logback</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-mysql</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.nekohtml</groupId>
            <artifactId>nekohtml</artifactId>
            <version>1.9.15</version>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
            <version>2.6.5</version>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <!--单元测试-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <copy file="target/${project.artifactId}.jar" todir="../../deploy">
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
package com.puree.hospital.business.controller;

import com.puree.hospital.business.HospitalBusinessApplication;
import com.puree.hospital.business.domain.BusDisease;
import com.puree.hospital.business.domain.BusTcmDiagnosis;
import com.puree.hospital.business.domain.BusTcmSyndrome;
import com.puree.hospital.business.domain.dto.DiagnosisQueryDTO;
import com.puree.hospital.business.service.IBusDiseaseService;
import com.puree.hospital.business.service.IBusTcmDiagnosisService;
import com.puree.hospital.business.service.IBusTcmSyndromeService;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = HospitalBusinessApplication.class)
class BusTcmDiagnosisControllerTest {

    @Resource
    private IBusDiseaseService busDiseaseService;

    @Resource
    private IBusTcmDiagnosisService busTcmDiagnosisService;

    @Resource
    private IBusTcmSyndromeService busTcmSyndromeService;

    @Test
    void checkDiagnosisStatus_diagnosis_enable() {
        DiagnosisQueryDTO DiagnosisStatusDTO = new DiagnosisQueryDTO();
        DiagnosisStatusDTO.setDiseaseId(Arrays.asList(108L));
        List<BusDisease> busDiseases = busDiseaseService.queryDiseaseByIds(DiagnosisStatusDTO.getDiseaseId());
        Assertions.assertTrue(busDiseases.stream().anyMatch(busDisease -> EnableStatusEnum.ENABLED.getStatus().equals(busDisease.getStatus())));
    }

    @Test
    void checkDiagnosisStatus_diagnosis_disable() {
        DiagnosisQueryDTO DiagnosisStatusDTO = new DiagnosisQueryDTO();
        DiagnosisStatusDTO.setDiseaseId(Arrays.asList(107L));
        List<BusDisease> busDiseases = busDiseaseService.queryDiseaseByIds(DiagnosisStatusDTO.getDiseaseId());
        Assertions.assertTrue(busDiseases.stream().anyMatch(busDisease -> EnableStatusEnum.DISABLED.getStatus().equals(busDisease.getStatus())));
    }

    @Test
    void checkDiagnosisStatus_tcm_diagnosis_enable() {
        DiagnosisQueryDTO DiagnosisStatusDTO = new DiagnosisQueryDTO();
        DiagnosisStatusDTO.setDiagnosisId(Arrays.asList(113L));
        List<BusTcmDiagnosis> busTcmDiagnoses = busTcmDiagnosisService.queryDiagnosisByIds(DiagnosisStatusDTO.getDiagnosisId());
        Assertions.assertTrue(busTcmDiagnoses.stream().anyMatch(busDisease -> EnableStatusEnum.ENABLED.getStatus().equals(busDisease.getStatus())));
    }

    @Test
    void checkDiagnosisStatus_tcm_diagnosis_disable() {
        DiagnosisQueryDTO DiagnosisStatusDTO = new DiagnosisQueryDTO();
        DiagnosisStatusDTO.setDiagnosisId(Arrays.asList(112L));
        List<BusTcmDiagnosis> busTcmDiagnoses = busTcmDiagnosisService.queryDiagnosisByIds(DiagnosisStatusDTO.getDiagnosisId());
        Assertions.assertTrue(busTcmDiagnoses.stream().anyMatch(busDisease -> EnableStatusEnum.DISABLED.getStatus().equals(busDisease.getStatus())));
    }

    @Test
    void checkDiagnosisStatus_tcm_symptom_enable() {
        DiagnosisQueryDTO DiagnosisStatusDTO = new DiagnosisQueryDTO();
        DiagnosisStatusDTO.setSyndromeId(Arrays.asList(106L));
        List<BusTcmSyndrome> busTcmSyndromes = busTcmSyndromeService.querySyndromeByIds(DiagnosisStatusDTO.getSyndromeId());
        Assertions.assertTrue(busTcmSyndromes.stream().anyMatch(busDisease -> EnableStatusEnum.ENABLED.getStatus().equals(busDisease.getStatus())));
    }

    @Test
    void checkDiagnosisStatus_tcm_symptom_disable() {
        DiagnosisQueryDTO DiagnosisStatusDTO = new DiagnosisQueryDTO();
        DiagnosisStatusDTO.setSyndromeId(Arrays.asList(105L));
        List<BusTcmSyndrome> busTcmSyndromes = busTcmSyndromeService.querySyndromeByIds(DiagnosisStatusDTO.getSyndromeId());
        Assertions.assertTrue(busTcmSyndromes.stream().anyMatch(busDisease -> EnableStatusEnum.DISABLED.getStatus().equals(busDisease.getStatus())));
    }

}

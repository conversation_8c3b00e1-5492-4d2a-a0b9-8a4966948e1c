<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.business.mapper.BusHospitalMapper">

    <resultMap type="com.puree.hospital.business.domain.BusHospital" id="BusHospitalResult">
        <result property="id"    column="id"    />
        <result property="hospitalNumber"    column="hospital_number"    />
        <result property="hospitalName"    column="hospital_name"    />
        <result property="hospitalAbbreviation"    column="hospital_abbreviation"    />
        <result property="hospitalPhone"    column="hospital_phone"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="cityCode"    column="city_code"    />
        <result property="areaCode"    column="area_code"    />
        <result property="detailAddress"    column="detail_address"    />
        <result property="hospitalDescription"    column="hospital_description"    />
        <result property="hospitalPhoto"    column="hospital_photo"    />
        <result property="medicalLicense"    column="medical_license"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="wxCheckFile"    column="wx_check_file"    />
        <result property="isTest"    column="is_test"    />
    </resultMap>

    <sql id="Base_Column_List">
           id,
           hospital_number,
           hospital_name,
           hospital_abbreviation,
           hospital_phone,
           province_code,
           city_code,
           area_code,
           detail_address,
           hospital_description,
           hospital_photo,
           medical_license,
           status,
           del_flag,
           create_by,
           create_time,
           update_by,
           update_time,
           is_test
    </sql>

    <select id="selectHospitalCombobox" resultType="HospitalComboboxVo">
        select id ,hospital_name from  bus_hospital;
    </select>
    <select id="selectBusHospitalList" parameterType="com.puree.hospital.business.domain.BusHospital" resultMap="BusHospitalResult">
        select
        <include refid="Base_Column_List"/>
        from bus_hospital
        <where>
            del_flag = 0
            <if test="hospitalNumber != null  and hospitalNumber != ''"> and hospital_number = #{hospitalNumber}</if>
            <if test="hospitalName != null  and hospitalName != ''"> and hospital_name like concat('%', #{hospitalName}, '%')</if>
            <if test="hospitalAbbreviation != null  and hospitalAbbreviation != ''"> and hospital_abbreviation = #{hospitalAbbreviation}</if>
            <if test="hospitalPhone != null  and hospitalPhone != ''"> and hospital_phone = #{hospitalPhone}</if>
            <if test="provinceCode != null "> and province_code = #{provinceCode}</if>
            <if test="cityCode != null "> and city_code = #{cityCode}</if>
            <if test="areaCode != null "> and area_code = #{areaCode}</if>
            <if test="detailAddress != null  and detailAddress != ''"> and detail_address = #{detailAddress}</if>
            <if test="hospitalDescription != null  and hospitalDescription != ''"> and hospital_description = #{hospitalDescription}</if>
            <if test="hospitalPhoto != null  and hospitalPhoto != ''"> and hospital_photo = #{hospitalPhoto}</if>
            <if test="medicalLicense != null  and medicalLicense != ''"> and medical_license = #{medicalLicense}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isTest != null"> and is_test = #{isTest}</if>
        </where>
        order  by  create_time desc
    </select>

    <select id="checkDuplicateName" parameterType="String" resultMap="BusHospitalResult">
        select
        <include refid="Base_Column_List"/>
        from bus_hospital
        where hospital_name = #{hospitalName} and del_flag = 0
    </select>

    <select id="checkLoginAccount" parameterType="String" resultMap="BusHospitalResult">
        select
        <include refid="Base_Column_List"/>
        from bus_hospital
        where  del_flag = 0
    </select>

    <select id="selectBusHospitalById" parameterType="Long" resultMap="BusHospitalResult">
        select
        <include refid="Base_Column_List"/>
        from bus_hospital
        where id = #{id}
    </select>

    <insert id="insertBusHospital" parameterType="BusHospital" useGeneratedKeys="true" keyProperty="id">
        insert into bus_hospital
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hospitalNumber != null and hospitalNumber != ''">hospital_number,</if>
            <if test="hospitalName != null and hospitalName != ''">hospital_name,</if>
            <if test="hospitalAbbreviation != null and hospitalAbbreviation != ''">hospital_abbreviation,</if>
            <if test="hospitalPhone != null and hospitalPhone != ''">hospital_phone,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="detailAddress != null and detailAddress != ''">detail_address,</if>
            <if test="hospitalDescription != null and hospitalDescription != ''">hospital_description,</if>
            <if test="hospitalPhoto != null and hospitalPhoto != ''">hospital_photo,</if>
            <if test="medicalLicense != null and medicalLicense != ''">medical_license,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isTest != null">is_test,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hospitalNumber != null and hospitalNumber != ''">#{hospitalNumber},</if>
            <if test="hospitalName != null and hospitalName != ''">#{hospitalName},</if>
            <if test="hospitalAbbreviation != null and hospitalAbbreviation != ''">#{hospitalAbbreviation},</if>
            <if test="hospitalPhone != null and hospitalPhone != ''">#{hospitalPhone},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="detailAddress != null and detailAddress != ''">#{detailAddress},</if>
            <if test="hospitalDescription != null and hospitalDescription != ''">#{hospitalDescription},</if>
            <if test="hospitalPhoto != null and hospitalPhoto != ''">#{hospitalPhoto},</if>
            <if test="medicalLicense != null and medicalLicense != ''">#{medicalLicense},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isTest != null">#{isTest},</if>
         </trim>
    </insert>

    <update id="updateBusHospital" parameterType="BusHospital">
        update bus_hospital
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusHospitalById" parameterType="Long">
        delete from bus_hospital where id = #{id}
    </delete>

    <delete id="deleteBusHospitalByIds" parameterType="String">
        delete from bus_hospital where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectHospitalTel" parameterType="Long" resultType="HospitalVo">
        SELECT
            t1.hospital_name,
            t1.hospital_phone,
            t2.NAME AS province,
            t3.NAME AS city,
            t4.NAME AS area,
            t1.detail_address
        FROM
            bus_hospital t1
            LEFT JOIN sys_province t2 ON t1.province_code = t2.
            CODE LEFT JOIN sys_city t3 ON t1.city_code = t3.
            CODE LEFT JOIN sys_area t4 ON t1.area_code = t4.CODE
        WHERE
            t1.id = #{hospitalId}
    </select>

    <select id="selectHospitalList" parameterType="BusHospital" resultType="BusHospitalVo">
        SELECT
        t1.*,
        s1.NAME province,
        s2.NAME city,
        s3.NAME area
        FROM
        bus_hospital t1
        LEFT JOIN sys_province s1 ON t1.province_code = s1.
        CODE LEFT JOIN sys_city s2 ON t1.city_code = s2.
        CODE LEFT JOIN sys_area s3 ON t1.area_code = s3.CODE
        <where>
            <if test="hospitalName != null and hospitalName != ''">
                and t1.hospital_name like concat('%', #{hospitalName}, '%')
            </if>
            <if test="status != null">
                and t1.status = #{status}
            </if>
        </where>
        order by t1.create_time desc
    </select>

    <update id="updateRegulator">
        update bus_hospital set is_regulator = #{status},update_time = now() where id = #{id}
    </update>
</mapper>
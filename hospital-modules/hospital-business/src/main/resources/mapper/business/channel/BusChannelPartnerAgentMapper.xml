<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.business.channel.mapper.BusChannelPartnerAgentMapper">
    <sql id="base_agent_identity_column" >
        ci.status,
            ci.phone_number as phonenumber,
            a.channel_identity_id,
            a.id,
            a.channel_partner_id,
            a.full_name,
            a.shop_name,
            a.hospital_id,
            a.create_time,
            a.create_by,
            a.update_time,
            a.update_by,
            a.modify_flag,
            a.del_flag
    </sql>

    <select id="selectAgentList" parameterType="com.puree.hospital.business.domain.BusChannelPartnerAgent"
            resultType="com.puree.hospital.business.domain.BusChannelPartnerAgent">
        SELECT
            ci.status,
            t1.*,t2.bank_code,t2.bank_name,t2.bank_icon_url,t2.bank_number,t2.account_name
        FROM
            bus_channel_partner_agent t1
            left join bus_channel_partner_agent_bankcard t2 on t2.partner_agent_id=t1.id
            LEFT JOIN bus_channel_identity ci on ci.id = t1.channel_identity_id
        <where>
            t1.channel_partner_id = #{id}
            AND t1.del_flag = 0
            <if test="dateList != null">
                AND t1.create_time &gt;= #{startTime}
                AND t1.create_time &lt;= #{endTime}
            </if>
            <if test="fullName != null and fullName != ''">
                AND t1.full_name like concat('%', trim(#{fullName}), '%')
            </if>
            <if test="status != null">
                AND ci.status = #{status}
            </if>
            <if test="phonenumber != null and phonenumber != ''">
                AND t1.phonenumber = #{phonenumber}
            </if>
        </where>
        ORDER BY t1.create_time DESC
    </select>


    <select id="listChannelPartnerAgentByHospitalId" resultType="com.puree.hospital.business.domain.BusChannelPartnerAgent">
        select
            <include refid="base_agent_identity_column"/>
        from  bus_channel_partner_agent a
        LEFT JOIN bus_channel_identity ci on ci.id = a.channel_identity_id
        where a.hospital_id=#{hospitalId}
        and a.del_flag = 0
    </select>
    <select id="getChannelInfoByAgentId" resultType="com.puree.hospital.business.domain.vo.ChannelInfoVO">
        select p.id as channelId,
               pa.id as agentId,
               pa.hospital_id as hospitalId,
               p.full_name as channelFullName,
               pa.full_name as agentFullName,
               pa.shop_name as agentShopName,
               p.contacts
        from bus_channel_partner_agent pa
        LEFT JOIN bus_channel_partner p on p.id = pa.channel_partner_id
        where pa.id in
        <foreach collection="agentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectListByChannelPartnerId"
            resultType="com.puree.hospital.business.domain.BusChannelPartnerAgent">
        SELECT
            <include refid="base_agent_identity_column"/>
        from  bus_channel_partner_agent a
                  INNER JOIN bus_channel_identity ci on ci.id = a.channel_identity_id AND a.del_flag = 0
        where a.channel_partner_id = #{channelPartnerId}
    </select>

</mapper>
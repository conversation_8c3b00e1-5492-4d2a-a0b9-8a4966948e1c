<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.business.mapper.BusDrugsMapper">
    <resultMap type="com.puree.hospital.business.domain.vo.BusDrugsVo" id="BusDrugsResult">
        <result property="id" column="id"/>
        <result property="drugsNumber" column="drugs_number"/>
        <result property="standardCommonName" column="standard_common_name"/>
        <result property="drugsManufacturer" column="drugs_manufacturer"/>
        <result property="drugsName" column="drugs_name"/>
        <result property="referenceSellingPrice" column="reference_selling_price"/>
        <result property="drugsStandardCode" column="drugs_standard_code"/>
        <result property="referencePurchasePrice" column="reference_purchase_price"/>
        <result property="nmpn" column="nmpn"/>
        <result property="pinyinCode" column="pinyin_code"/>
        <result property="drugsImg" column="drugs_img"/>
        <result property="drugsDetails" column="drugs_details"/>
        <result property="efficacyClassification" column="efficacy_classification"/>
        <result property="prescriptionIdentification" column="prescription_identification"/>
        <result property="drugsType" column="drugs_type"/>
        <result property="drugsDosageForm" column="drugs_dosage_form"/>
        <result property="drugsSpecification" column="drugs_specification"/>
        <result property="drugsPackagingUnit" column="drugs_packaging_unit"/>
        <result property="drugsUsage" column="drugs_usage"/>
        <result property="recommendedDosage" column="recommended_dosage"/>
        <result property="medicalInsuranceType" column="medical_insurance_type"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="classifyId" column="classify_id"/>
        <result property="yySeq" column="yy_seq"/>
        <result property="decoctingMethod" column="decocting_method"/>
        <result property="type" column="type"/>
        <result property="ypid" column="ypid"/>
        <result property="detailedSpecifications" column="detailed_specifications"/>
        <result property="drugsImgDetail" column="drugs_img_detail"/>
        <result property="mainImg" column="main_img"/>
        <result property="minPackNum" column="min_pack_num"/>
        <result property="minMakeUnit" column="min_make_unit"/>
        <result property="minPackUnit" column="min_pack_unit"/>
        <result property="icdCode" column="icd_code"/>
        <result property="nationalDrugCode" column="national_drug_code"/>
        <result property="origin" column="origin"/>
        <result property="baseDose" column="base_dose"/>
        <result property="baseDoseUnit" column="base_dose_unit"/>
        <result property="hisDrugsId" column="his_drugs_id"/>
        <result property="singleDose" column="single_dose"/>
        <result property="defaultFrequency" column="default_frequency"/>
        <result property="recommendUseDays" column="recommend_use_days"/>

        <!--扩展数据-->
        <result property="drugsId" column="drugs_id"/>
        <result property="efficacyClassificationValue" column="efficacy_classification_value"/>
        <result property="prescriptionIdentificationValue" column="prescription_identification_value"/>
        <result property="drugsTypeValue" column="drugs_type_value"/>
        <result property="drugsDosageFormValue" column="drugs_dosage_form_value"/>
        <result property="drugsPackagingUnitValue" column="drugs_packaging_unit_value"/>
        <result property="drugsUsageValue" column="drugs_usage_value"/>
        <result property="medicalInsuranceTypeValue" column="medical_insurance_type_value"/>
        <result property="classifyName" column="classify_name"/>
        <result property="eNum" column="e_num"/>
        <result property="dNum" column="d_num"/>
        <result property="drugsDosageFormName" column="drugs_dosage_form_name"/>
        <result property="medicalInsuranceTypeName" column="medical_insurance_type_name"/>
        <result property="decoctingMethodName" column="decocting_method_name"/>
        <result property="sellingPrice" column="selling_price"/>
        <result property="stock" column="stock"/>
        <result property="efficacyClassificationName" column="efficacy_classification_name"/>
        <result property="prescriptionIdentificationName" column="prescription_identification_name"/>
        <result property="drugsPackagingUnitName" column="drugs_packaging_unit_name"/>
        <result property="drugsUsageName" column="drugs_usage_name"/>
        <result property="drugsTypeName" column="drugs_type_name"/>
    </resultMap>
    
    <sql id="Base_Colum_List">
        d.id,
        d.drugs_number,
        d.standard_common_name,
        d.drugs_manufacturer,
        d.drugs_name,
        d.reference_selling_price,
        d.drugs_standard_code,
        d.reference_purchase_price,
        d.nmpn,
        d.pinyin_code,
        d.drugs_img,
        d.drugs_details,
        d.efficacy_classification,
        d.prescription_identification,
        d.drugs_type,
        d.drugs_dosage_form,
        d.drugs_specification,
        d.drugs_packaging_unit,
        d.drugs_usage,
        d.recommended_dosage,
        d.medical_insurance_type,
        d.status,
        d.del_flag,
        d.create_by,
        d.create_time,
        d.update_by,
        d.update_time,
        d.remark,
        d.classify_id,
        d.yy_seq,
        d.decocting_method,
        d.`type`,
        d.ypid,
        d.detailed_specifications,
        d.drugs_img_detail,
        d.main_img,
        d.min_pack_num,
        d.min_make_unit,
        d.min_pack_unit,
        d.icd_code,
        d.national_drug_code,
        d.origin,
        d.base_dose,
        d.base_dose_unit,
        d.his_drugs_id,
        d.single_dose,
        d.default_frequency,
        d.recommend_use_days
    </sql>

    <select id="selectBusDrugsList" parameterType="com.puree.hospital.business.domain.BusDrugs" resultMap="BusDrugsResult">
        select
            <include refid="Base_Colum_List"/>
        from bus_drugs d
        <where>
            d.del_flag =0
            <if test="drugsNumber != null  and drugsNumber != ''">
                and d.drugs_number = #{drugsNumber}
            </if>
            <if test="standardCommonName != null  and standardCommonName != ''">
                and d.standard_common_name like concat('%', #{standardCommonName}, '%')
            </if>
            <if test="drugsManufacturer != null  and drugsManufacturer != ''">
                and d.drugs_manufacturer = #{drugsManufacturer}
            </if>
            <if test="drugsName != null  and drugsName != ''">
                and d.drugs_name like concat('%', #{drugsName}, '%')
            </if>
            <if test="referenceSellingPrice != null ">
                and d.reference_selling_price = #{referenceSellingPrice}
            </if>
            <if test="drugsStandardCode != null  and drugsStandardCode != ''">
                and d.drugs_standard_code = #{drugsStandardCode}
            </if>
            <if test="referencePurchasePrice != null ">
                and d.reference_purchase_price = #{referencePurchasePrice}
            </if>
            <if test="nmpn != null  and nmpn != ''">
                and d.nmpn = #{nmpn}
            </if>
            <if test="pinyinCode != null  and pinyinCode != ''">
                and d.pinyin_code = #{pinyinCode}
            </if>
            <if test="drugsImg != null  and drugsImg != ''">
                and d.drugs_img = #{drugsImg}
            </if>
            <if test="drugsDetails != null  and drugsDetails != ''">
                and d.drugs_details = #{drugsDetails}
            </if>
            <if test="efficacyClassification != null ">
                and d.efficacy_classification = #{efficacyClassification}
            </if>
            <if test="prescriptionIdentification != null ">
                and d.prescription_identification = #{prescriptionIdentification}
            </if>
            <if test="drugsType != null ">
                and d.drugs_type = #{drugsType}
            </if>
            <if test="drugsDosageForm != null ">
                and d.drugs_dosage_form = #{drugsDosageForm}
            </if>
            <if test="drugsSpecification != null  and drugsSpecification != ''">
                and d.drugs_specification = #{drugsSpecification}
            </if>
            <if test="drugsPackagingUnit != null ">
                and d.drugs_packaging_unit = #{drugsPackagingUnit}
            </if>
            <if test="drugsUsage != null ">
                and d.drugs_usage = #{drugsUsage}
            </if>
            <if test="recommendedDosage != null  and recommendedDosage != ''">
                and d.recommended_dosage = #{recommendedDosage}
            </if>
            <if test="medicalInsuranceType != null ">
                and d.medical_insurance_type = #{medicalInsuranceType}
            </if>
            <if test="status != null ">
                and d.status = #{status}
            </if>
            <if test="classifyId != null ">
                and d.classify_id = #{classifyId}
            </if>
            <if test="type != null ">
                and d.`type` = #{type}
            </if>
            <if test="yySeq != null and yySeq != ''">
                and d.yy_seq = #{yySeq}
            </if>
            <if test="decoctingMethod != null ">
                and d.decocting_method = #{decoctingMethod}
            </if>
            <if test="ypid != null and ypid != ''">
                and d.ypid = #{ypid}
            </if>
            <if test="detailedSpecifications != null and detailedSpecifications != ''">
                and d.detailed_specifications = #{detailedSpecifications}
            </if>
            <if test="drugsImgDetail != null and drugsImgDetail != ''">
                and d.drugs_img_detail = #{drugsImgDetail}
            </if>
            <if test="mainImg != null  and mainImg != ''">
                and d.main_img = #{mainImg}
            </if>
            <if test="minPackNum != null ">
                and d.min_pack_num = #{minPackNum}
            </if>
            <if test="minMakeUnit != null and minMakeUnit != ''">
                and d.min_make_unit = #{minMakeUnit}
            </if>
            <if test="minPackUnit != null and minPackUnit != ''">
                and d.min_pack_unit = #{minPackUnit}
            </if>
            <if test="icdCode != null  and icdCode != ''">
                and d.icd_code = #{icdCode}
            </if>
            <if test="nationalDrugCode != null and nationalDrugCode != ''">
                and d.national_drug_code = #{nationalDrugCode}
            </if>
            <if test="origin != null  and origin != ''">
                and d.origin = #{origin}
            </if>
            <if test="baseDose != null">
                and d.base_dose = #{baseDose}
            </if>
            <if test="baseDoseUnit != null and baseDoseUnit != ''">
                and d.base_dose_unit = #{baseDoseUnit}
            </if>
            <if test="hisDrugsId != null and hisDrugsId != ''">
                and d.his_drugs_id = #{hisDrugsId}
            </if>
            <if test="singleDose != null">
                and d.single_dose = #{singleDose}
            </if>
            <if test="defaultFrequency != null and defaultFrequency != ''">
                and d.default_frequency = #{defaultFrequency}
            </if>
            <if test="recommendUseDays != null and recommendUseDays != ''">
                and d.recommend_use_days = #{recommendUseDays}
            </if>
        </where>
    </select>

    <select id="selBusDrugsConnectMultiTableList" parameterType="com.puree.hospital.business.domain.BusDrugs" resultMap="BusDrugsResult">
        SELECT
        <include refid="Base_Colum_List"/>,
        IFNULL((SELECT COUNT(dep.drugs_id) num FROM bus_department_drugs dep where dep.drugs_id =d.id), 0) d_num,
        IFNULL((SELECT COUNT(ent.drugs_id) num FROM bus_enterprise_drugs ent where ent.drugs_id =d.id), 0) e_num,
        g.classify_name,
        h.name AS drugs_usage_value,
        t6.dict_label AS prescription_identification_label,
        t8.dict_label AS medical_insurance_type_label
        FROM bus_drugs d
        LEFT JOIN `bus_dict_drugs_classify` g ON d.classify_id = g.id
        LEFT JOIN bus_dict_drugs_usage h ON d.drugs_usage = h.id
        LEFT JOIN (select drugs_id, `status` from bus_drug_audit where id in (select max(id) as id from bus_drug_audit group by drugs_id)) bda ON d.id = bda.drugs_id
        LEFT JOIN sys_dict_data t6 ON t6.dict_code = d.prescription_identification
        LEFT JOIN sys_dict_data t8 ON t8.dict_code = d.medical_insurance_type
        <where>
            d.del_flag =0
            <if test="drugsNumber != null  and drugsNumber != ''">
                and d.drugs_number = #{drugsNumber}
            </if>
            <if test="drugsName != null  and drugsName != ''">
                and d.drugs_name like concat('%', trim(#{drugsName}), '%')
            </if>
            <if test="standardCommonName != null  and standardCommonName != ''">
                and d.standard_common_name like concat('%', trim(#{standardCommonName}), '%')
            </if>
            <if test="drugsStandardCode != null  and drugsStandardCode != ''">
                and d.drugs_standard_code like concat('%',trim(#{drugsStandardCode}),'%')
            </if>
            <if test="nmpn != null  and nmpn != ''">
                and d.nmpn like concat('%',#{nmpn},'%')
            </if>
            <if test="drugsIds != null and drugsIds.size() > 0  ">
                and d.id IN
                <foreach collection="drugsIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="drugsType != null ">
                and d.drugs_type = #{drugsType}
            </if>
            <if test="classifyId != null">
                and find_in_set(#{classifyId}, concat(g.ancestors,',',g.id))
            </if>
            <if test="status != null ">
                and d.status = #{status}
            </if>
            <if test="drugsUsage != null">
                and find_in_set(#{drugsUsage},concat(h.ancestors,',',h.id))
            </if>
            and (bda.status is null or bda.status='1')
        </where>
        order by d.create_time desc
    </select>

    <select id="selBusDrugsWithHospitalId" parameterType="com.puree.hospital.business.domain.BusDrugs" resultMap="BusDrugsResult">
        SELECT
        <include refid="Base_Colum_List"/>,
        d.id as drugs_id,
        IFNULL((SELECT COUNT(dep.drugs_id) num FROM bus_department_drugs dep where dep.drugs_id =d.id), 0) d_num,
        IFNULL((SELECT COUNT(ent.drugs_id) num FROM bus_enterprise_drugs ent where ent.drugs_id =d.id), 0) e_num,
        t4.classify_name,
        h.NAME AS drugs_usage_value,
        t6.dict_label AS prescription_identification_label,
        t8.dict_label AS medical_insurance_type_label,
        t1.selling_price,
        IFNULL(t1.stock,0) as stock
        FROM bus_hospital_officina t1
        LEFT JOIN bus_enterprise t7 ON t7.id = t1.enterprise_id
        LEFT JOIN bus_directory_drugs t2 ON t2.id = t1.directory_id
        INNER JOIN bus_drugs d ON d.id = t2.drugs_id
        LEFT JOIN bus_dict_drugs_classify t4 ON t4.id = d.classify_id
        LEFT JOIN bus_commodity_classify t5 ON t5.id = d.drugs_type
        left join bus_dict_drugs_usage h on d.drugs_usage = h.id
        LEFT JOIN sys_dict_data t6 ON t6.dict_code = d.prescription_identification
        LEFT JOIN sys_dict_data t8 ON t8.dict_code = d.medical_insurance_type
        where t2.directory_type != '3'
        AND d.status = 1
        and t1.hospital_id = #{hospitalId}
        <if test="drugsIds != null and drugsIds.size() > 0  ">
            and t2.drugs_id IN
            <foreach collection="drugsIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectBusDrugsAndEnterpriseList" parameterType="com.puree.hospital.business.domain.BusDrugs"
            resultType="com.puree.hospital.business.domain.vo.BusEnterpriseDrugsVO">
        select
            d.id drugsId,
            c.id,
            d.drugs_manufacturer,
            d.drugs_name,
            c.reference_purchase_price,
            d.drugs_specification,
            b.classify_name drugs_type_value,
            c.enterprise_drugs_id
        FROM bus_drugs d
        LEFT JOIN bus_commodity_classify b ON d.drugs_type=b.id
        LEFT JOIN bus_enterprise_drugs c ON d.id=c.drugs_id
        <if test="params.myDrugs==0 and params.enterpriseId!=null">
            and c.enterprise_id =#{params.enterpriseId}
        </if>
        <where>
            d.del_flag =0
            <if test="params.myDrugs==0">
                and c.drugs_id IS NULL
            </if>
            <if test="params.myDrugs==1">
                and c.drugs_id  IS NOT NULL
            </if>
            <if test="params.myDrugs==1 and params.enterpriseId!=null">
                and c.enterprise_id =#{params.enterpriseId}
            </if>
            <if test="drugsNumber != null  and drugsNumber != ''">
                and d.drugs_number = #{drugsNumber}
            </if>
            <if test="drugsName != null  and drugsName != ''">
                and d.drugs_name like concat('%', #{drugsName}, '%')
            </if>
            <if test="drugsType != null ">
                and d.drugs_type = #{drugsType}
            </if>
            <if test="type != null ">
                and d.`type`=#{type}
            </if>
            <if test="drugsUsage != null ">
                and d.drugs_usage = #{drugsUsage}
            </if>
        </where>
    </select>

    <select id="selectBusDrugsById" parameterType="java.lang.Long" resultMap="BusDrugsResult">
        SELECT
            <include refid="Base_Colum_List"/>,
            d1.classify_name AS efficacy_classification_name,
            d2.dict_label AS prescription_identification_name,
            d3.dict_label AS drugs_dosage_form_name,
            d4.dict_label AS drugs_packaging_unit_name,
            d5.dict_label AS medical_insurance_type_name,
            d6.name AS drugs_usage_name,
            d7.classify_name,
            bcc1.classify_name AS drugs_type_name,
            bda.selling_price,
            bda.stock,
            max(bda.create_time)
        FROM bus_drugs d
        LEFT JOIN bus_dict_drugs_efficacy_classify d1 ON d1.id = d.efficacy_classification
        LEFT JOIN sys_dict_data d2 ON d2.dict_code = d.prescription_identification
        LEFT JOIN sys_dict_data d3 ON d3.dict_code = d.drugs_dosage_form
        LEFT JOIN sys_dict_data d4 ON d4.dict_code = d.drugs_packaging_unit
        LEFT JOIN sys_dict_data d5 ON d5.dict_code = d.medical_insurance_type
        LEFT JOIN bus_dict_drugs_usage d6 ON d6.id = d.drugs_usage
        LEFT JOIN bus_dict_drugs_classify d7 ON d7.id = d.classify_id
        LEFT JOIN bus_commodity_classify bcc1 ON bcc1.id = d.drugs_type
        LEFT JOIN (select *  from bus_drug_audit where drugs_id=#{id} and  status='0' ORDER BY create_time desc LIMIT 1) bda ON  d.id= bda.drugs_id and (bda.status='0' or bda.status is null)
        where d.id = #{id}
    </select>

    <insert id="insertBusDrugs" parameterType="com.puree.hospital.business.domain.BusDrugs" useGeneratedKeys="true" keyProperty="id">
        insert into bus_drugs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="drugsNumber != null and drugsNumber != ''">
                drugs_number,
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                standard_common_name,
            </if>
            <if test="drugsManufacturer != null and drugsManufacturer != ''">
                drugs_manufacturer,
            </if>
            <if test="drugsName != null and drugsName != ''">
                drugs_name,
            </if>
            <if test="referenceSellingPrice != null">
                reference_selling_price,
            </if>
            <if test="drugsStandardCode != null and drugsStandardCode != ''">
                drugs_standard_code,
            </if>
            <if test="referencePurchasePrice != null">
                reference_purchase_price,
            </if>
            <if test="nmpn != null and nmpn != ''">
                nmpn,
            </if>
            <if test="pinyinCode != null and pinyinCode != ''">
                pinyin_code,
            </if>
            <if test="drugsImg != null and drugsImg != ''">
                drugs_img,
            </if>
            <if test="drugsDetails != null and drugsDetails != ''">
                drugs_details,
            </if>
            <if test="efficacyClassification != null">
                efficacy_classification,
            </if>
            <if test="prescriptionIdentification != null">
                prescription_identification,
            </if>
            <if test="drugsType != null">
                drugs_type,
            </if>
            <if test="drugsDosageForm != null">
                drugs_dosage_form,
            </if>
            <if test="drugsSpecification != null and drugsSpecification != ''">
                drugs_specification,
            </if>
            <if test="drugsPackagingUnit != null">
                drugs_packaging_unit,
            </if>
            <if test="drugsUsage != null">
                drugs_usage,
            </if>
            <if test="recommendedDosage != null and recommendedDosage != ''">
                recommended_dosage,
            </if>
            <if test="medicalInsuranceType != null">
                medical_insurance_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="classifyId != null">
                classify_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="nationalDrugCode != null">
                national_drug_code,
            </if>
            <if test="icdCode != null and icdCode != ''">
                icd_code,
            </if>
            <if test="minPackNum != null">
                min_pack_num,
            </if>
            <if test="minPackUnit != null and minPackUnit != ''">
                min_pack_unit,
            </if>
            <if test="minMakeUnit != null and minMakeUnit != ''">
                min_make_unit,
            </if>
            <if test=" origin != null ">
                origin,
            </if>
            <if test="baseDose != null">
                base_dose,
            </if>
            <if test="baseDoseUnit != null and baseDoseUnit != ''">
                base_dose_unit,
            </if>
            <if test="hisDrugsId != null">
                his_drugs_id,
            </if>
            <if test="singleDose != null">
                single_dose,
            </if>
            <if test="defaultFrequency != null and defaultFrequency != ''">
                default_frequency,
            </if>
            <if test="recommendUseDays != null">
                recommend_use_days,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="drugsNumber != null and drugsNumber != ''">
                #{drugsNumber},
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                #{standardCommonName},
            </if>
            <if test="drugsManufacturer != null and drugsManufacturer != ''">
                #{drugsManufacturer},
            </if>
            <if test="drugsName != null and drugsName != ''">
                #{drugsName},
            </if>
            <if test="referenceSellingPrice != null">
                #{referenceSellingPrice},
            </if>
            <if test="drugsStandardCode != null and drugsStandardCode != ''">
                #{drugsStandardCode},
            </if>
            <if test="referencePurchasePrice != null">
                #{referencePurchasePrice},
            </if>
            <if test="nmpn != null and nmpn != ''">
                #{nmpn},
            </if>
            <if test="pinyinCode != null and pinyinCode != ''">
                #{pinyinCode},
            </if>
            <if test="drugsImg != null and drugsImg != ''">
                #{drugsImg},
            </if>
            <if test="drugsDetails != null and drugsDetails != ''">
                #{drugsDetails},
            </if>
            <if test="efficacyClassification != null">
                #{efficacyClassification},
            </if>
            <if test="prescriptionIdentification != null">
                #{prescriptionIdentification},
            </if>
            <if test="drugsType != null">
                #{drugsType},
            </if>
            <if test="drugsDosageForm != null">
                #{drugsDosageForm},
            </if>
            <if test="drugsSpecification != null and drugsSpecification != ''">
                #{drugsSpecification},
            </if>
            <if test="drugsPackagingUnit != null">
                #{drugsPackagingUnit},
            </if>
            <if test="drugsUsage != null">
                #{drugsUsage},
            </if>
            <if test="recommendedDosage != null and recommendedDosage != ''">
                #{recommendedDosage},
            </if>
            <if test="medicalInsuranceType != null">
                #{medicalInsuranceType},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="classifyId != null">
                #{classifyId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="nationalDrugCode != null">
                #{nationalDrugCode},
            </if>
            <if test="icdCode != null and icdCode != ''">
                #{icdCode},
            </if>
            <if test="minPackNum != null">
                #{minPackNum},
            </if>
            <if test="minPackUnit != null and minPackUnit != ''">
                #{minPackUnit},
            </if>
            <if test="minMakeUnit != null and minMakeUnit != ''">
                #{minMakeUnit},
            </if>
            <if test=" origin != null ">
                #{origin},
            </if>
            <if test="baseDose != null">
                #{baseDose},
            </if>
            <if test="baseDoseUnit != null and baseDoseUnit != ''">
                #{baseDoseUnit},
            </if>
            <if test="hisDrugsId != null">
                #{hisDrugsId},
            </if>
            <if test="singleDose != null">
                #{singleDose},
            </if>
            <if test="defaultFrequency != null and defaultFrequency != ''">
                #{defaultFrequency},
            </if>
            <if test="recommendUseDays != null">
                #{recommendUseDays},
            </if>
        </trim>
    </insert>

    <update id="updateBusDrugs" parameterType="com.puree.hospital.business.domain.BusDrugs">
        update bus_drugs
        <trim prefix="SET" suffixOverrides=",">
            <if test="drugsNumber != null and drugsNumber != ''">
                drugs_number = #{drugsNumber},
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                standard_common_name = #{standardCommonName},
            </if>
            <if test="drugsManufacturer != null and drugsManufacturer != ''">
                drugs_manufacturer = #{drugsManufacturer},
            </if>
            <if test="drugsName != null and drugsName != ''">
                drugs_name = #{drugsName},
            </if>
            <if test="referenceSellingPrice != null">
                reference_selling_price = #{referenceSellingPrice},
            </if>
            <if test="drugsStandardCode != null and drugsStandardCode != ''">
                drugs_standard_code = #{drugsStandardCode},
            </if>
            <if test="referencePurchasePrice != null">
                reference_purchase_price = #{referencePurchasePrice},
            </if>
            <if test="nmpn != null and nmpn != ''">
                nmpn = #{nmpn},
            </if>
            <if test="pinyinCode != null and pinyinCode != ''">
                pinyin_code = #{pinyinCode},
            </if>
            <if test="drugsImg != null and drugsImg != ''">
                drugs_img = #{drugsImg},
            </if>
            <if test="drugsDetails != null and drugsDetails != ''">
                drugs_details = #{drugsDetails},
            </if>
            <if test="efficacyClassification != null">
                efficacy_classification = #{efficacyClassification},
            </if>
            <if test="prescriptionIdentification != null">
                prescription_identification = #{prescriptionIdentification},
            </if>
            <if test="drugsType != null">
                drugs_type = #{drugsType},
            </if>
            <if test="drugsDosageForm != null">
                drugs_dosage_form = #{drugsDosageForm},
            </if>
            <if test="drugsSpecification != null and drugsSpecification != ''">
                drugs_specification = #{drugsSpecification},
            </if>
            <if test="drugsPackagingUnit != null">
                drugs_packaging_unit = #{drugsPackagingUnit},
            </if>
            <if test="drugsUsage != null">
                drugs_usage = #{drugsUsage},
            </if>
            <!--<if test="recommendedDosage != null and recommendedDosage != ''">-->
                recommended_dosage = #{recommendedDosage},
            <!--</if>-->
            <if test="medicalInsuranceType != null">
                medical_insurance_type = #{medicalInsuranceType},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="classifyId != null">
                classify_id=#{classifyId},
            </if>
            <!--<if test="nationalDrugCode != null">-->
                national_drug_code=#{nationalDrugCode},
            <!--</if>-->
            <if test="icdCode != null and icdCode != ''">
                icd_code=#{icdCode},
            </if>
            <if test="minPackNum != null">
                min_pack_num=#{minPackNum},
            </if>
            <if test="minMakeUnit != null and minMakeUnit != ''">
                min_make_unit=#{minMakeUnit},
            </if>
            <if test="minPackUnit != null and minPackUnit != ''">
                min_pack_unit=#{minPackUnit},
            </if>
            <if test=" origin != null ">
                origin = #{origin},
            </if>
            <if test="baseDose != null">
                base_dose = #{baseDose},
            </if>
            <if test="baseDoseUnit != null and baseDoseUnit != ''">
                base_dose_unit = #{baseDoseUnit},
            </if>
            <if test="hisDrugsId != null">
                his_drugs_id = #{hisDrugsId},
            </if>
            <!--<if test="singleDose != null">-->
                single_dose = #{singleDose},
            <!--</if>-->
            <!--<if test="defaultFrequency != null and defaultFrequency != ''">-->
                default_frequency = #{defaultFrequency},
            <!--</if>-->
            <!--<if test="recommendUseDays != null">-->
                recommend_use_days = #{recommendUseDays},
            <!--</if>-->
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusDrugsById" parameterType="java.lang.Long">
        update bus_drugs set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteBusDrugsByIds" parameterType="java.lang.String">
        delete from bus_drugs where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertDrugs" useGeneratedKeys="true" keyProperty="id">
        insert into bus_drugs(
            drugs_number,
            standard_common_name,
            drugs_manufacturer,
            drugs_name,
            reference_selling_price,
            drugs_standard_code,
            reference_purchase_price,
            nmpn,
            pinyin_code,
            drugs_img,
            drugs_details,
            efficacy_classification,
            prescription_identification,
            drugs_type,
            drugs_dosage_form,
            drugs_specification,
            drugs_packaging_unit,
            drugs_usage,
            recommended_dosage,
            medical_insurance_type,
            `status`,
            del_flag,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            classify_id,
            yy_seq,
            decocting_method,
            `type`,
            ypid,
            detailed_specifications,
            drugs_img_detail,
            main_img,
            min_pack_num,
            min_make_unit,
            min_pack_unit,
            icd_code,
            national_drug_code,
            origin,
            base_dose,
            base_dose_unit,
            his_drugs_id,
            single_dose,
            default_frequency,
            recommend_use_days
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.drugsNumber},
             #{item.standardCommonName},
             #{item.drugsManufacturer},
             #{item.drugsName},
             #{item.referenceSellingPrice},
             #{item.drugsStandardCode},
             #{item.referencePurchasePrice},
             #{item.nmpn},
             #{item.pinyinCode},
             #{item.drugsImg},
             #{item.drugsDetails},
             #{item.efficacyClassification},
             #{item.prescriptionIdentification},
             #{item.drugsType},
             #{item.drugsDosageForm},
             #{item.drugsSpecification},
             #{item.drugsPackagingUnit},
             #{item.drugsUsage},
             #{item.recommendedDosage},
             #{item.medicalInsuranceType},
             #{item.status},
             #{item.delFlag},
             #{item.createBy},
             #{item.createTime},
             #{item.updateBy},
             #{item.updateTime},
             #{item.remark},
             #{item.classifyId},
             #{item.yySeq},
             #{item.decoctingMethod},
             #{item.`type`},
             #{item.ypid},
             #{item.detailedSpecification},
             #{item.drugsImgDetail},
             #{item.mainImg},
             #{item.minPackNum},
             #{item.minMakeUnit},
             #{item.minPackUnit},
             #{item.icdCode},
             #{item.nationalDrugCode},
             #{item.origin},
             #{item.baseDose},
             #{item.baseDoseUnit},
             #{item.hisDrugsId},
             #{item.singleDose},
             #{item.defaultFrequency},
             #{item.recommendUseDays}
            )
        </foreach>
    </insert>

    <select id="selectEsDrugs" resultType="com.puree.hospital.business.domain.ESDrugs">
        select *
        from es_drugs
        where approval_number like concat('%',#{nmpn},'')
        order by seq desc limit 1;
    </select>

    <select id="selectEsDrugsByProductName" resultType="com.puree.hospital.business.domain.ESDrugs">
        select *
        from es_drugs
        where product_name = #{productName}
        order by seq desc;
    </select>

    <select id="listChineseDrugs" resultMap="BusDrugsResult">
        select
            <include refid="Base_Colum_List"/>,
            df.dict_label as drugs_dosage_form_name,
            mt.dict_label as medical_insurance_type_name,
            dm.dict_label as decocting_method_name
        from bus_drugs d
        left join sys_dict_data mt on d.medical_insurance_type = mt.dict_code
        left join sys_dict_data dm on d.decocting_method = dm.dict_code
        left join sys_dict_data df on d.drugs_dosage_form = df.dict_code
        where del_flag = 0 and type = 1
        <if test="drugsName != null and drugsName != ''">
            and d.drugs_name like concat('%',#{drugsName},'%')
        </if>
        <if test="drugsDosageForm != null">
            and d.drugs_dosage_form = #{drugsDosageForm}
        </if>
        <if test="medicalInsuranceType != null">
            and d.medical_insurance_type = #{medicalInsuranceType}
        </if>
        order by d.create_time desc
    </select>

    <select id="selectDepartmentByDrugId" resultType="com.puree.hospital.business.domain.vo.DepartmentInfoVO">
        select
            t1.id,
            t1.parent_id,
            t1.ancestors,
            t1.department_name as sonDepartmentName ,
            t1.department_number,
            t1.status,
            t1.del_flag,
            t3.department_name as fatherDepartmentName
        from bus_department t1
        inner join bus_department_drugs t2 on t1.id = t2.department_id and t2.drugs_id = #{drugsId}
        left join bus_department t3 on t3.id=t1.parent_id
    </select>

    <select id="selectDraftDrugsByDrugsId" resultMap="BusDrugsResult">
        SELECT
            d.id,
            d.drugs_id,
            d.drugs_number,
            d.standard_common_name,
            d.drugs_manufacturer,
            d.drugs_name,
            d.reference_selling_price,
            d.drugs_standard_code,
            d.reference_purchase_price,
            d.nmpn,
            d.pinyin_code,
            d.drugs_img,
            d.drugs_details,
            d.efficacy_classification,
            d.prescription_identification,
            d.drugs_type,
            d.drugs_dosage_form,
            d.drugs_specification,
            d.drugs_packaging_unit,
            d.drugs_usage,
            d.recommended_dosage,
            d.medical_insurance_type,
            d.status,
            d.del_flag,
            d.create_by,
            d.create_time,
            d.update_by,
            d.update_time,
            d.decocting_method,
            d.remark,
            d.classify_id,
            d.icd_code,
            d.national_drug_code,
            d.min_pack_num,
            d.min_make_unit,
            d.min_pack_unit,
            d.base_dose,
            d.base_dose_unit,
            d.is_cold_chain,
            d.origin,
            d.single_dose,
            d.default_frequency,
            d.recommend_use_days,
            d1.classify_name AS efficacy_classification_name,
            d2.dict_label AS prescription_identification_name,
            d3.dict_label AS drugs_dosage_form_name,
            d4.dict_label AS drugs_packaging_unit_name,
            d5.dict_label AS medical_insurance_type_name,
            d6.name AS drugs_usage_name,
            d7.classify_name AS classify_name,
            bcc1.classify_name AS drugs_type_name,
            bda.selling_price,
            bda.stock,
            bda.create_time
        FROM bus_drugs_draft d
        LEFT JOIN bus_dict_drugs_efficacy_classify d1 ON d1.id = d.efficacy_classification
        LEFT JOIN sys_dict_data d2 ON d2.dict_code = d.prescription_identification
        LEFT JOIN sys_dict_data d3 ON d3.dict_code = d.drugs_dosage_form
        LEFT JOIN sys_dict_data d4 ON d4.dict_code = d.drugs_packaging_unit
        LEFT JOIN sys_dict_data d5 ON d5.dict_code = d.medical_insurance_type
        LEFT JOIN bus_dict_drugs_usage d6 ON d6.id = d.drugs_usage
        LEFT JOIN bus_dict_drugs_classify d7 ON d7.id = d.classify_id
        LEFT JOIN bus_commodity_classify bcc1 ON bcc1.id = d.drugs_type
        LEFT JOIN (select * from bus_drug_audit where drugs_id=#{drugsId} and status='0' ORDER BY create_time desc LIMIT 1) bda ON  d.drugs_id= bda.drugs_id and (bda.status='0' or bda.status is null)
        where d.drugs_id = #{drugsId}
        order by d.id desc limit #{count}
    </select>

    <select id="selectDepartmentForDraftDrug" resultType="com.puree.hospital.business.domain.vo.DepartmentInfoVO">
        select
            t1.id,
            t1.parent_id,
            t1.ancestors,
            t1.department_name as sonDepartmentName,
            t1.department_number,
            t1.status,
            t1.del_flag,
            t2.department_name as fatherDepartmentName
        from bus_department as t1
        left join bus_department as t2 on t2.id = t1.parent_id
        where t1.id in
        <foreach collection="departmentIdList" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>
    </select>
    <select id="selectIdsByBusDrug" resultType="java.lang.Long">
        SELECT id FROM bus_drugs
        <where>
            <if test="drugsNumber != null and drugsNumber != ''">
                AND drugs_number = #{drugsNumber}
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                AND standard_common_name = #{standardCommonName}
            </if>
            <if test="drugsManufacturer != null and drugsManufacturer != ''">
                AND drugs_manufacturer = #{drugsManufacturer}
            </if>
            <if test="drugsName != null and drugsName != ''">
                AND drugs_name = #{drugsName}
            </if>
            <if test="referenceSellingPrice != null">
                AND reference_selling_price = #{referenceSellingPrice}
            </if>
            <if test="drugsStandardCode != null and drugsStandardCode != ''">
                AND drugs_standard_code = #{drugsStandardCode}
            </if>
            <if test="referencePurchasePrice != null">
                AND reference_purchase_price = #{referencePurchasePrice}
            </if>
            <if test="nmpn != null and nmpn != ''">
                AND  nmpn = #{nmpn}
            </if>
            <if test="pinyinCode != null and pinyinCode != ''">
                AND pinyin_code = #{pinyinCode}
            </if>
            <if test="drugsImg != null and drugsImg != ''">
                AND drugs_img = #{drugsImg}
            </if>
            <if test="drugsDetails != null and drugsDetails != ''">
                AND drugs_details = #{drugsDetails}
            </if>
            <if test="efficacyClassification != null">
                AND efficacy_classification = #{efficacyClassification}
            </if>
            <if test="prescriptionIdentification != null">
                AND prescription_identification = #{prescriptionIdentification}
            </if>
            <if test="drugsType != null">
                AND drugs_type = #{drugsType}
            </if>
            <if test="drugsDosageForm != null">
                AND drugs_dosage_form = #{drugsDosageForm}
            </if>
            <if test="drugsSpecification != null and drugsSpecification != ''">
                AND drugs_specification = #{drugsSpecification}
            </if>
            <if test="drugsPackagingUnit != null">
                AND drugs_packaging_unit = #{drugsPackagingUnit}
            </if>
            <if test="drugsUsage != null">
                AND drugs_usage = #{drugsUsage}
            </if>
            <if test="recommendedDosage != null and recommendedDosage != ''">
                AND recommended_dosage = #{recommendedDosage}
            </if>
            <if test="medicalInsuranceType != null">
                AND medical_insurance_type = #{medicalInsuranceType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="delFlag != null">
                AND del_flag = #{delFlag}
            </if>
            <if test="createBy != null">
                AND create_by = #{createBy}
            </if>
            <if test="createTime != null">
                AND create_time = #{createTime}
            </if>
            <if test="updateBy != null">
                AND update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                AND update_time = #{updateTime}
            </if>
            <if test="remark != null">
                AND remark = #{remark}
            </if>
            <if test="classifyId != null">
                AND classify_id=#{classifyId}
            </if>
            <if test="nationalDrugCode != null">
                AND national_drug_code=#{nationalDrugCode}
            </if>
            <if test="icdCode != null and icdCode != ''">
                AND icd_code=#{icdCode}
            </if>
            <if test="minPackNum != null">
                AND min_pack_num=#{minPackNum}
            </if>
            <if test="minMakeUnit != null and minMakeUnit != ''">
                AND min_make_unit=#{minMakeUnit}
            </if>
            <if test="minPackUnit != null and minPackUnit != ''">
                AND min_pack_unit=#{minPackUnit}
            </if>
            <if test=" origin != null ">
                AND origin = #{origin}
            </if>
            <if test="baseDose != null">
                AND base_dose = #{baseDose}
            </if>
            <if test="baseDoseUnit != null and baseDoseUnit != ''">
                AND base_dose_unit = #{baseDoseUnit}
            </if>
            <if test="hisDrugsId != null">
                AND his_drugs_id = #{hisDrugsId}
            </if>
            <if test="singleDose != null">
                AND single_dose = #{singleDose}
            </if>
            <if test="defaultFrequency != null and defaultFrequency != ''">
                AND default_frequency = #{defaultFrequency}
            </if>
            <if test="recommendUseDays != null">
                AND recommend_use_days = #{recommendUseDays}
            </if>
        </where>
    </select>

</mapper>

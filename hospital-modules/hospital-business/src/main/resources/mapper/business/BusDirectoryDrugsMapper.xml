<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.business.mapper.BusDirectoryDrugsMapper">
    <update id="updateBatchDirectoryStatus">
        UPDATE bus_directory_drugs
        SET directory_status = #{directoryStatus}
        WHERE hospital_id = #{hospitalId}
        AND directory_type = #{directoryType}
    </update>

    <select id="selectDirectoryDrugsList" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO"
            resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT
        <include refid="Drug_Detail_Fields"/>,
        bho.stock
        FROM bus_directory_drugs bdd
        inner join bus_drugs d on d.id = bdd.drugs_id
        left join bus_commodity_classify dd1 on dd1.id = d.drugs_type
        left join bus_hospital_officina bho on bho.drugs_id = bdd.drugs_id and bho.hospital_id = bdd.hospital_id
        left join sys_dict_data dd2 on dd2.dict_code = d.prescription_identification
        left join sys_dict_data dd3 on dd3.dict_code = d.medical_insurance_type
        <where>
            d.type = 0
            AND d.status = 1
            <if test="hospitalId != null">
                AND bdd.hospital_id = #{hospitalId}
            </if>
            <if test="directoryType != null">
                AND bdd.directory_type = #{directoryType}
            </if>
            <if test="drugsName != null and drugsName != ''">
                AND d.drugs_name like concat('%', trim(#{drugsName}), '%')
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                AND d.standard_common_name like concat('%', trim(#{standardCommonName}), '%')
            </if>
            <if test="drugsStandardCode != null and drugsStandardCode != ''">
                AND d.drugs_standard_code like concat('%', trim(#{drugsStandardCode}), '%')
            </if>
            <if test="nmpn != null and nmpn != ''">
                AND d.nmpn like concat('%', trim(#{nmpn}), '%')
            </if>
            <if test="drugsType != null">
                AND d.drugs_type =#{drugsType}
            </if>
            <if test="prescriptionIdentification != null">
                AND d.prescription_identification =#{prescriptionIdentification}
            </if>
            <if test="status != null">
                AND d.status =#{status}
            </if>
            <if test="drugsNumber != null and drugsNumber != ''">
                AND d.drugs_number = trim(#{drugsNumber})
            </if>
            <if test="drugsManufacturer != null and drugsManufacturer != ''">
                AND d.drugs_manufacturer like concat('%', trim(#{drugsManufacturer}), '%')
            </if>
            <if test="prescriptionIdentification != null">
                AND d.prescription_identification = #{prescriptionIdentification}
            </if>
            <if test="medicalInsuranceType != null">
                AND d.medical_insurance_type = #{medicalInsuranceType}
            </if>
            <choose>
                <when test="stockFilter != null and stockFilter.value == 'has_stock'">
                    and bho.stock > 0 and bho.status = 1
                </when>
                <when test="stockFilter != null and stockFilter.value == 'no_stock'">
                    and (bho.stock = 0 or bho.stock is null) and bho.status = 1
                </when>
            </choose>
        </where>
    </select>
    <sql id="Drug_Detail_Fields">
        bdd.id,
            bdd.drugs_id,
            bdd.directory_status,
            d.drugs_number,
            d.drugs_name,
            d.standard_common_name,
            d.drugs_specification,
            d.drugs_manufacturer,
            d.reference_selling_price,
            d.reference_purchase_price,
            d.nmpn,
            d.drugs_standard_code,
            dd1.classify_name as drugs_type,
            dd2.dict_label as prescription_identification,
            bdd.hospital_id,
            d.drugs_img,
            d.national_drug_code,
            bho.selling_price,
            dd2.dict_value pre_type,
            dd3.dict_label as medical_insurance_type
    </sql>
    <sql id="Drug_Detail_Select">
        SELECT
            <include refid="Drug_Detail_Fields"/>,
             begp.reference_purchase_price as enterprisePurchasePrice,
            (IFNULL(bho.stock, 0) + IFNULL(begp.stock, 0)) as stock
        FROM bus_directory_drugs bdd
                 INNER JOIN bus_hospital_officina bho ON bdd.id = bho.directory_id and bdd.drugs_id = bho.drugs_id and bho.hospital_id = bdd.hospital_id
                 LEFT JOIN bus_enterprise_drugs beg ON bho.enterprise_id = beg.enterprise_id and bho.drugs_id = beg.drugs_id
                 LEFT JOIN bus_enterprise_drugs_price begp ON beg.id = begp.drug_ref_id and begp.hospital_id = bho.hospital_id
            and begp.enterprise_id = bho.enterprise_id
                 INNER JOIN bus_drugs d ON d.id = bdd.drugs_id and d.type='0'
                 LEFT JOIN sys_dict_data dd2 ON d.prescription_identification = dd2.dict_code
                 LEFT JOIN sys_dict_data dd3 ON dd3.dict_code = d.medical_insurance_type
                 LEFT JOIN bus_commodity_classify dd1 ON dd1.id = d.drugs_type
    </sql>
    <select id="selectDrugsList" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO"
            resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        <include refid="Drug_Detail_Select"/>
        <where>
            and d.status = 1
            and bho.status = 1
            <if test="directoryType != null and directoryType != ''">
                and bdd.directory_type = #{directoryType}
            </if>
            <if test="drugsName != null and drugsName != ''">
                and d.drugs_name like concat('%', #{drugsName}, '%')
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                <!-- feat 1014705 修改为 通过拼音码、商品名或通用名搜索-->
                and (
                d.standard_common_name like concat('%', #{standardCommonName}, '%')
                or d.drugs_name like concat('%', #{standardCommonName}, '%')
                or d.pinyin_code like concat('%', #{standardCommonName}, '%')
                )
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and bho.hospital_id = #{hospitalId}
            </if>
            <if test="excludeHospitalIds != null and excludeHospitalIds.size() > 0">
                and bdd.hospital_id not in
                <foreach collection="excludeHospitalIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="stockFilter != null and stockFilter.value == 'has_stock'">
                    and (IFNULL(bho.stock, 0) + IFNULL(begp.stock, 0)) > 0 and bho.status = 1
                </when>
                <when test="stockFilter != null and stockFilter.value == 'no_stock'">
                    and (IFNULL(bho.stock, 0) + IFNULL(begp.stock, 0)) = 0 and bho.status = 1
                </when>
            </choose>
        </where>
        <!-- order by stock DESC 目前根据药品库存累加后排序会导致扫描全表，暂先把order by 移除 -->
    </select>

    <select id="selectNotAssociateList" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO"
            resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT
            d.id as drugs_id,
            d.drugs_number,
            d.drugs_name,
            d.standard_common_name,
            d.drugs_specification,
            d.drugs_manufacturer,
            d.reference_selling_price,
            d.reference_purchase_price,
            d.nmpn,
            d.drugs_standard_code,
            dd1.classify_name as drugs_type,
            dd2.dict_label as prescription_identification,
            dd3.dict_label as medical_insurance_type
        FROM bus_drugs d
        left join bus_commodity_classify dd1 on dd1.id = d.drugs_type
        left join sys_dict_data dd2 on dd2.dict_code = d.prescription_identification
        left join sys_dict_data dd3 on dd3.dict_code = d.medical_insurance_type
        <!-- 此处inner join调整为子查询效率会更高 -->
        LEFT JOIN (select drugs_id, `status` from bus_drug_audit where id in (select max(id) as id from bus_drug_audit group by drugs_id)) bda ON d.id = bda.drugs_id
        <where>
            d.id not in (select drugs_id from bus_directory_drugs where hospital_id = #{hospitalId} and directory_type = #{directoryType})
            and d.status = 1
            AND d.type = 0
            AND (bda.STATUS is null or bda.STATUS = 1)
            <if test="drugsName != null and drugsName != ''">
                AND d.drugs_name like concat('%', trim(#{drugsName}), '%')
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                AND d.standard_common_name like concat('%', trim(#{standardCommonName}), '%')
            </if>
            <if test="drugsStandardCode != null and drugsStandardCode != ''">
                AND d.drugs_standard_code like concat('%', trim(#{drugsStandardCode}), '%')
            </if>
            <if test="nmpn != null and nmpn != ''">
                AND d.nmpn like concat('%', trim(#{nmpn}), '%')
            </if>
            <if test="drugsType != null">
                AND d.drugs_type =#{drugsType}
            </if>
            <if test="prescriptionIdentification != null">
                AND d.prescription_identification =#{prescriptionIdentification}
            </if>
            <if test="status != null">
                AND d.status =#{status}
            </if>
            <if test="drugsNumber != null and drugsNumber != ''">
                AND d.drugs_number = trim(#{drugsNumber})
            </if>
            <if test="drugsManufacturer != null and drugsManufacturer != ''">
                AND d.drugs_manufacturer like concat('%', trim(#{drugsManufacturer}), '%')
            </if>
            <if test="medicalInsuranceType != null and medicalInsuranceType != ''">
                AND d.medical_insurance_type = #{medicalInsuranceType}
            </if>
            <if test="directoryType == 2">
                AND (d.national_drug_code is not null and d.national_drug_code != '')
            </if>
        </where>
    </select>

    <select id="tcmDirectoryDrugsList" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO"
            resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT
            bdd.id,
            bdd.drugs_id,
            bdd.directory_status,
            d.id drugs_id,
            d.drugs_name,
            d.drugs_manufacturer,
            dd1.dict_label decocting_method_value,
            dd2.dict_label drugs_dosage_form_value,
            dd3.dict_label medical_insurance_type_value,
            d.drugs_specification
        FROM bus_directory_drugs bdd
        LEFT JOIN bus_drugs d ON bdd.drugs_id = d.id
        left join sys_dict_data dd1 on dd1.dict_code = d.decocting_method
        left join sys_dict_data dd2 on dd2.dict_code = d.drugs_dosage_form
        left join sys_dict_data dd3 on dd3.dict_code = d.medical_insurance_type
        <where>
            bdd.hospital_id = #{hospitalId}
            AND d.type =1
            <if test="directoryType != null">
                AND bdd.directory_type = #{directoryType}
            </if>
            <if test="drugsName != null and drugsName != ''">
                AND d.drugs_name like concat('%', trim(#{drugsName}), '%')
            </if>
            <if test="decoctingMethod != null">
                AND d.decocting_method =#{decoctingMethod}
            </if>
            <if test="drugsDosageForm != null">
                AND d.drugs_dosage_form =#{drugsDosageForm}
            </if>
            <if test="medicalInsuranceType != null">
                AND d.medical_insurance_type = #{medicalInsuranceType}
            </if>
        </where>
    </select>

    <select id="tcmNotAssociateList" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO"
            resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT
           d.id drugs_id,
           d.drugs_name,
           d.drugs_manufacturer,
           d.drugs_specification,d.drugs_manufacturer,
           d.reference_purchase_price,
           dd1.dict_label decoctingMethodValue,
           dd2.dict_label drugsDosageFormValue,
           dd3.dict_label medicalInsuranceTypeValue
        FROM bus_drugs d
        left join sys_dict_data dd1 on dd1.dict_code = d.decocting_method
        left join sys_dict_data dd2 on dd2.dict_code = d.drugs_dosage_form
        left join sys_dict_data dd3 on dd3.dict_code = d.medical_insurance_type
        <where>
            d.id not in (select drugs_id from bus_directory_drugs where hospital_id = #{hospitalId} and directory_type = 3)
            and d.status = 1
            AND d.type =1
            <if test="drugsName != null and drugsName != ''">
                AND d.drugs_name like concat('%', trim(#{drugsName}), '%')
            </if>

            <if test="decoctingMethod != null">
                AND d.decocting_method =#{decoctingMethod}
            </if>
            <if test="drugsDosageForm != null">
                AND d.drugs_dosage_form =#{drugsDosageForm}
            </if>
            <if test="medicalInsuranceType != null">
                AND d.medical_insurance_type = #{medicalInsuranceType}
            </if>
        </where>
    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO bus_directory_drugs
        (directory_type, hospital_id,drugs_id,create_by,create_time)
        VALUES
        <foreach collection="list" item="i" separator=",">
            (#{i.directoryType}, #{i.hospitalId}, #{i.drugsId},#{i.createBy},#{i.createTime})
        </foreach>
    </insert>
    <insert id="drugsBindingDirectory">
        INSERT INTO bus_directory_drugs
        (directory_type, hospital_id, drugs_id, create_by, create_time, directory_status)
        SELECT
            #{directoryType} AS directory_type,
            #{hospitalId} AS hospital_id,
            id AS drugs_id,
            'zagly' AS create_by,
            NOW() AS create_time,
            1 AS directory_status
        FROM
            bus_drugs
        WHERE
            remark = #{remark};
    </insert>

    <select id="tcmDrugsList" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO"
            resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT
        bdd.id,
        <if test="enterpriseId != null and enterpriseId != ''">
            edp.reference_purchase_price,
        </if>
        bdd.drugs_id,
        bdd.directory_status,
        d.drugs_name,
        dd2.dict_label medicalInsuranceType,
        bdd.reference_selling_price,
        dd3.dict_label decoctingMethod,
        d.drugs_specification,
        dd4.dict_value tcm_type
        FROM
        bus_hospital_officina ho
        INNER JOIN bus_directory_drugs bdd ON ho.directory_id = bdd.id
        LEFT JOIN bus_drugs d ON d.id = ho.drugs_id
        LEFT JOIN sys_dict_data dd2 ON dd2.dict_code = d.medical_insurance_type
        LEFT JOIN sys_dict_data dd3 ON dd3.dict_code = d.decocting_method
        LEFT JOIN sys_dict_data dd4 ON dd4.dict_code = d.drugs_dosage_form
        <if test="enterpriseId != null and enterpriseId != ''">
            LEFT JOIN bus_enterprise_drugs ed on bdd.drugs_id = ed.drugs_id AND ed.enterprise_id = #{enterpriseId}
            LEFT JOIN bus_enterprise_drugs_price edp on ed.id = edp.drug_ref_id and edp.hospital_id = #{hospitalId}
        </if>
        <where>
            bdd.hospital_id = #{hospitalId}
            AND bdd.directory_type ='3'
            <if test="checkGrounding">
               and ho.status='1'
               and ho.stock>0
            </if>
            <if test="drugsName != null and drugsName != ''">
                AND d.drugs_name like concat('%', trim(#{drugsName}), '%')
            </if>
            <if test="drugsId != null ">
                AND bdd.drugs_id=#{drugsId}
            </if>
        </where>
        ORDER BY ho.create_time DESC
    </select>

    <select id="tcmDrugsById" parameterType="java.lang.Long" resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT
            bdd.id,
            bdd.drugs_id,
            bdd.reference_selling_price,
            bdd.directory_status
        FROM bus_directory_drugs bdd
        <where>
            bdd.hospital_id = #{hospitalId}
            and bdd.id = #{id}
            AND bdd.directory_type ='3'
        </where>
    </select>

    <select id="selectDirectoryDrugs" parameterType="java.lang.Long" resultType="com.puree.hospital.business.domain.BusDirectoryDrugs">
        SELECT
            bdd.id,
            bdd.directory_type,
            bdd.hospital_id,
            bdd.drugs_id,
            bdd.reference_selling_price,
            bdd.directory_status
        FROM
            bus_directory_drugs bdd
            INNER JOIN bus_drugs bd ON bdd.drugs_id = bdd.id
        WHERE
            bdd.hospital_id = #{hospitalId}
            AND bdd.drugs_id IN
            <foreach collection="drugsIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="directoryType != null and '' != directoryType">
                AND bdd.directory_type = #{directoryType}
            </if>
    </select>

    <select id="getUnSyncDrugIds" resultType="com.puree.hospital.business.domain.BusDrugs">
        select
            t1.id,
            t1.reference_purchase_price as referencePurchasePrice
        from bus_drugs t1
        left join bus_directory_drugs t2 ON t1.id = t2.drugs_id
        where t2.id is null
        and t2.hospital_id = #{hospitalId}
        and t2.directory_type = #{directoryType}
        and t1.id in
        <foreach collection="drugsIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
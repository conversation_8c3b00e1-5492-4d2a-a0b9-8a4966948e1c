<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.business.mapper.BusHospitalOfficinaMapper">

    <select id="selectListNotAssociatedDrugs" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO" resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT bdd.id,bdd.drugs_id,d.drugs_number,cc.classify_name as drugs_type,d.drugs_name,d.standard_common_name,
        dd2.dict_label as prescription_identification,d.drugs_specification,d.drugs_manufacturer,
        d.reference_selling_price,d.reference_purchase_price
        FROM bus_directory_drugs bdd
        inner join bus_drugs d on d.id = bdd.drugs_id
        left join bus_commodity_classify cc on cc.id = d.drugs_type
        left join bus_dict_drugs_classify ddc on ddc.id = d.classify_id
        left join sys_dict_data dd2 on dd2.dict_code = d.prescription_identification
        LEFT JOIN bus_hospital_officina t ON t.directory_id=bdd.id
        <where>
            bdd.directory_type=1 and t.directory_id is null
            and bdd.hospital_id = #{hospitalId}
            <if test="classifyId != null "> and find_in_set(#{classifyId},concat(ddc.ancestors,",",ddc.id)) </if>
            <if test="drugsName != null and drugsName != ''">
                AND d.drugs_name like concat('%', trim(#{drugsName}), '%')
            </if>
            <if test="standardCommonName != null and standardCommonName != ''">
                AND d.standard_common_name like concat('%', trim(#{standardCommonName}), '%')
            </if>
            <if test="drugsStandardCode != null and drugsStandardCode != ''">
                AND d.drugs_standard_code like concat('%', trim(#{drugsStandardCode}), '%')
            </if>
            <if test="nmpn != null and nmpn != ''">
                AND d.nmpn like concat('%', trim(#{nmpn}), '%')
            </if>
            <if test="drugsType != null">
                AND d.drugs_type =#{drugsType}
            </if>
            <if test="prescriptionIdentification != null">
                AND d.prescription_identification =#{prescriptionIdentification}
            </if>
        </where>
    </select>

    <insert id="batchInsert" parameterType="BusHospitalOfficina">
        INSERT INTO bus_hospital_officina
        (
            directory_id,
            drugs_id,
            hospital_id,
            selling_price,
            stock,
            status,
            enterprise_id,
            create_by,
            create_time
        )
        VALUES
        <foreach collection ="list" item="item" separator =",">
            (
            #{item.directoryId},
            #{item.drugsId},
            #{item.hospitalId},
            #{item.sellingPrice},
            #{item.stock},
            #{item.status},
            #{item.enterpriseId},
            #{item.createBy},
            #{item.createTime}
            )
        </foreach >
    </insert>

    <insert id="batchInsertMultiple" parameterType="BusHospitalOfficina">
        INSERT INTO bus_hospital_officina
        (
        directory_id,
        drugs_id,
        hospital_id,
        selling_price,
        status,
        stock,
        create_by,
        create_time
        )
        VALUES
        <foreach collection ="list" item="item" separator =",">
            (
            #{item.directoryId},
            #{item.drugsId},
            #{item.hospitalId},
            #{item.sellingPrice},
            #{item.status},
            #{item.stock},
            #{item.createBy},
            #{item.createTime}
            )
        </foreach >
    </insert>
    <insert id="drugsBindingHospitalPharmaciesBatchByDirectory">
        # 绑定到药房
        INSERT INTO bus_hospital_officina
        (directory_id, drugs_id, hospital_id, selling_price, stock, status, create_by, create_time)
        SELECT
            d.id AS directory_id,
            d.drugs_id AS drugs_id,
            d.hospital_id AS hospital_id,
            b.reference_selling_price AS selling_price,
            9999 AS stock,  -- 默认库存为9999，可以根据实际情况修改
            1 AS status,  -- 默认状态为启用
            'zagly' AS create_by,  -- 创建者设置为 'system'
            NOW() AS create_time
        FROM
            bus_directory_drugs d
                JOIN
            bus_drugs b ON d.drugs_id = b.id
        WHERE
            b.remark = #{remark}
          and d.hospital_id = #{hospitalId} and d.directory_type = #{directoryType};
    </insert>

    <select id="selectListAssociatedDrugs" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO"  resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT
            t1.id,
            t3.drugs_number,
            t4.classify_name AS drug_classification,
            t3.pinyin_code AS pinyin_code,
            t3.id AS drugs_id,
            t3.drugs_name,
            t3.drugs_img,
            t3.national_drug_code,
            t3.standard_common_name,
            t3.drugs_specification,
            t3.drugs_manufacturer,
            t3.reference_selling_price,
            t3.reference_purchase_price,
            t7.enterprise_name,
            t1.selling_price,
            t1.stock,
            t1.STATUS,
            t1.enterprise_id,
            t6.dict_label AS prescription_identification,
            t6.dict_value AS pre_type,
            t8.dict_label AS medical_insurance_type,
            h.name as drugs_usage_value
        FROM
            bus_hospital_officina t1
            LEFT JOIN bus_enterprise t7 ON t7.id = t1.enterprise_id
            LEFT JOIN bus_directory_drugs t2 ON t2.id = t1.directory_id
            INNER JOIN bus_drugs t3 ON t3.id = t2.drugs_id
            LEFT JOIN bus_dict_drugs_classify t4 ON t4.id = t3.classify_id
            LEFT JOIN bus_commodity_classify t5 ON t5.id = t3.drugs_type
            left join bus_dict_drugs_usage h on t3.drugs_usage = h.id
            LEFT JOIN sys_dict_data t6 ON t6.dict_code = t3.prescription_identification
            LEFT JOIN sys_dict_data t8 ON t8.dict_code = t3.medical_insurance_type
        <where>
            t2.directory_type != '3'
            AND t3.status = 1
            <if test="hospitalId !=null ">
                and t1.hospital_id = #{hospitalId}
            </if>
            <if test="drugsName !=null and drugsName != ''">
                and t3.drugs_name like concat('%',trim(#{drugsName}), '%')
            </if>
            <if test="drugsIds != null and drugsIds.size() > 0  ">
                and t3.id IN
                <foreach collection="drugsIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="standardCommonName !=null and standardCommonName != ''">
                and t3.standard_common_name like concat('%', trim(#{standardCommonName}), '%')
            </if>
            <if test="drugsStandardCode !=null and drugsStandardCode != ''">
                and t3.drugs_standard_code like concat('%', trim(#{drugsStandardCode}), '%')
            </if>
            <if test="nmpn !=null and nmpn != ''">
                and t3.nmpn like concat('%', trim(#{nmpn}), '%')
            </if>
            <if test="classifyId !=null">
                and find_in_set(#{classifyId},concat(t4.ancestors,",",t4.id))
            </if>
            <if test="status !=null">
                and t1.status=#{status}
            </if>
            <if test="drugsType !=null">
                and t3.drugs_type=#{drugsType}
            </if>
            <if test="prescriptionIdentification != null">
                AND t3.prescription_identification = #{prescriptionIdentification}
            </if>
            <if test="medicalInsuranceType != null">
                AND t3.medical_insurance_type = #{medicalInsuranceType}
            </if>
        </where>
    </select>


    <select id="tcmAssociatedDrugs" parameterType="com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO"  resultType="com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo">
        SELECT t1.id,
        t3.id  drugs_id,
        t3.drugs_name,
        t3.drugs_manufacturer,
        dd1.dict_label decoctingMethodValue,
        dd2.dict_label drugsDosageFormValue,
        dd3.dict_label medicalInsuranceTypeValue,
        t3.drugs_specification,t3.drugs_manufacturer,
        t3.drugs_manufacturer,
        t3.reference_purchase_price,
        t1.selling_price,t1.stock,t1.status,t1.enterprise_id,t7.enterprise_name
        FROM bus_hospital_officina t1
        LEFT JOIN bus_directory_drugs t2 ON t2.id = t1.directory_id
        INNER JOIN bus_drugs t3 ON t3.id = t2.drugs_id
        LEFT JOIN bus_enterprise t7 ON t7.id = t1.enterprise_id
        left join sys_dict_data dd1 on dd1.dict_code = t3.decocting_method
        left join sys_dict_data dd2 on dd2.dict_code = t3.drugs_dosage_form
        left join sys_dict_data dd3 on dd3.dict_code = t3.medical_insurance_type
        <where>
            t1.hospital_id=#{hospitalId}
            and  t2.directory_type='3'
            <if test="drugsName !=null and drugsName != ''">
                and t3.drugs_name like concat('%',trim(#{drugsName}), '%')
            </if>
            <if test="status !=null">
                and t1.status=#{status}
            </if>
            <if test="decoctingMethod != null">
                AND t3.decocting_method =#{decoctingMethod}
            </if>
            <if test="drugsDosageForm != null">
                AND t3.drugs_dosage_form =#{drugsDosageForm}
            </if>
            <if test="medicalInsuranceType != null">
                AND t3.medical_insurance_type = #{medicalInsuranceType}
            </if>
        </where>
    </select>


    <update id="updateHospitalOfficina" parameterType="BusHospitalOfficina">
        UPDATE bus_hospital_officina
        SET
        enterprise_id = #{enterpriseId},
        update_by = #{updateBy},
        update_time = #{updateTime}
        WHERE
        id = #{id}
        AND hospital_id = #{hospitalId}
    </update>

    <update id="updateEntIsNull">
        UPDATE bus_hospital_officina
        SET enterprise_id = null,
            update_time = now()
        WHERE enterprise_id = #{enterpriseId} AND hospital_id = #{hospitalId}
    </update>

    <select id="selectDrugsStock" resultType="com.puree.hospital.business.domain.BusHospitalOfficina">
        select t1.id,t1.enterprise_id,t1.hospital_id,
        t1.drugs_id,t1.status,t1.selling_price,t1.directory_id,
        t1.stock + IFNULL(t3.stock,0) as stock
        from bus_hospital_officina t1 left join bus_enterprise_drugs t2 on t1.enterprise_id = t2.enterprise_id and t1.drugs_id = t2.drugs_id
        left join bus_enterprise_drugs_price t3 on t2.id = t3.drug_ref_id and t3.hospital_id = #{hospitalId}
        where t1.hospital_id = #{hospitalId}
    </select>

    <update id="updateHospitalOfficinaInfo">
        update bus_hospital_officina
        set `status`=#{status} , update_by=#{updateBy}, update_time=#{updateTime}
        where drugs_id=#{drugsId} and hospital_id=#{hospitalId}
    </update>

    <update id="updateEntity" parameterType="com.puree.hospital.business.domain.BusHospitalOfficina">
        update bus_hospital_officina
        <set>
            <if test="sellingPrice != null">
                selling_price = #{sellingPrice,jdbcType=DECIMAL},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <if test="hospitalId != null">
                and hospital_id = #{hospitalId}
            </if>
            <if test="enterpriseId != null">
                and enterprise_id = #{enterpriseId}
            </if>
            <if test="drugsId != null">
                and drugs_id = #{drugsId}
            </if>
        </where>
    </update>

    <update id="updateSellingPriceBatch" parameterType="com.puree.hospital.business.domain.BusHospitalOfficina">
        <foreach collection="collect" item="entity" separator=";">
            update bus_hospital_officina
            <set>
                <if test="entity.sellingPrice != null">
                    selling_price = #{entity.sellingPrice,jdbcType=DECIMAL},
                </if>
                <if test="entity.updateTime != null">
                    update_time = #{entity.updateTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            <where>
                <if test="entity.hospitalId != null">
                    and hospital_id = #{entity.hospitalId}
                </if>
                <if test="entity.enterpriseId != null">
                    and enterprise_id = #{entity.enterpriseId}
                </if>
                <if test="entity.drugsId != null">
                    and drugs_id = #{entity.drugsId}
                </if>
            </where>
        </foreach>
    </update>
    <update id="updateBatchStatus">
        update bus_hospital_officina
        set `status`=#{status} , update_time= now()
        where hospital_id=#{hospitalId}
    </update>

</mapper>
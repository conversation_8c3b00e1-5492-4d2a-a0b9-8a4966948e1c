package com.puree.hospital.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.business.domain.BusTcmSyndrome;
import com.puree.hospital.business.domain.BusTcmSyndromeDiagnosis;
import com.puree.hospital.business.domain.vo.BusTcmSyndromeVo;
import com.puree.hospital.business.infrastructure.utils.SysNumberGenerator;
import com.puree.hospital.business.mapper.BusTcmSyndromeMapper;
import com.puree.hospital.business.service.IBusTcmSyndromeDiagnosisService;
import com.puree.hospital.business.service.IBusTcmSyndromeService;
import com.puree.hospital.common.core.constant.SysNumConstants;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 中医症型信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-29
 */
@Service
public class BusTcmSyndromeServiceImpl implements IBusTcmSyndromeService {
    private final BusTcmSyndromeMapper busTcmSyndromeMapper;
    private final IBusTcmSyndromeDiagnosisService tcmSyndromeDiagnosisService;
    private final SysNumberGenerator sysNumberGenerator;

    @Autowired
    public BusTcmSyndromeServiceImpl(BusTcmSyndromeMapper busTcmSyndromeMapper,
                                     IBusTcmSyndromeDiagnosisService tcmSyndromeDiagnosisService,
                                     SysNumberGenerator sysNumberGenerator) {
        this.busTcmSyndromeMapper = busTcmSyndromeMapper;
        this.tcmSyndromeDiagnosisService = tcmSyndromeDiagnosisService;
        this.sysNumberGenerator = sysNumberGenerator;
    }

    /**
     * 查询中医症型信息
     *
     * @param id 中医症型信息ID
     * @return 中医症型信息
     */
    @Override
    public BusTcmSyndrome selectBusTcmSyndromeById(Long id) {
        BusTcmSyndrome busTcmSyndrome = busTcmSyndromeMapper.selectById(id);
        List<Long> tcmDiagnosisId = tcmSyndromeDiagnosisService.selectTcmDiagnosisId(id);
        busTcmSyndrome.setTcmDiagnosisId(tcmDiagnosisId);
        return busTcmSyndrome;
    }

    /**
     * 查询中医症型信息列表
     *
     * @param busTcmSyndrome 中医症型信息
     * @return 中医症型信息
     */
    @Override
    public List<BusTcmSyndromeVo> selectBusTcmSyndromeList(BusTcmSyndrome busTcmSyndrome) {
        return busTcmSyndromeMapper.selectBusTcmSyndromeList(busTcmSyndrome);
    }

    /**
     * 新增中医症型信息
     *
     * @param busTcmSyndrome 中医症型信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBusTcmSyndrome(BusTcmSyndrome busTcmSyndrome) {
        busTcmSyndrome.setCreateTime(DateUtils.getNowDate());
        // 获取编号
        String number = sysNumberGenerator.get(SysNumConstants.ZX_NUM_KEY);
        busTcmSyndrome.setSyndromeNumber(number);
        busTcmSyndromeMapper.insert(busTcmSyndrome);
        // 插入数据到中间表
        BusTcmSyndromeDiagnosis tcmSyndromeDiagnosis = new BusTcmSyndromeDiagnosis();
        tcmSyndromeDiagnosis.setTcmSyndromeId(busTcmSyndrome.getId());
        tcmSyndromeDiagnosis.setTcmDiagnosisId(busTcmSyndrome.getTcmDiagnosisId());
        return tcmSyndromeDiagnosisService.insertBusTcmSyndromeDiagnosis(tcmSyndromeDiagnosis);
    }

    /**
     * 修改中医症型信息
     *
     * @param busTcmSyndrome 中医症型信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBusTcmSyndrome(BusTcmSyndrome busTcmSyndrome) {
        busTcmSyndrome.setUpdateTime(DateUtils.getNowDate());
        // 删除中间表旧的中医诊断数据
        Long id = busTcmSyndrome.getId();
        tcmSyndromeDiagnosisService.deleteBusTcmSyndromeDiagnosisById(id);
        // 添加新的中医诊断数据到中间表
        BusTcmSyndromeDiagnosis tcmSyndromeDiagnosis = new BusTcmSyndromeDiagnosis();
        tcmSyndromeDiagnosis.setTcmSyndromeId(id);
        tcmSyndromeDiagnosis.setTcmDiagnosisId(busTcmSyndrome.getTcmDiagnosisId());
        tcmSyndromeDiagnosisService.insertBusTcmSyndromeDiagnosis(tcmSyndromeDiagnosis);
        return busTcmSyndromeMapper.updateById(busTcmSyndrome);
    }

    /**
     * 批量删除中医症型信息
     *
     * @param ids 需要删除的中医症型信息ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBusTcmSyndromeByIds(Long[] ids) {
        // 删除中间表中中医诊断的数据
        tcmSyndromeDiagnosisService.deleteBusTcmSyndromeDiagnosisByIds(ids);
        return busTcmSyndromeMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除中医症型信息信息
     *
     * @param id 中医症型信息ID
     * @return 结果
     */
    @Override
    public int deleteBusTcmSyndromeById(Long id) {
        return busTcmSyndromeMapper.deleteById(id);
    }

    /**
     * 中医症型停用启用操作
     *
     * @param busTcmSyndrome
     * @return
     */
    @Override
    public int lock(BusTcmSyndrome busTcmSyndrome) {
        return busTcmSyndromeMapper.updateById(busTcmSyndrome);
    }

    /**
     * 校验病种名称是否重复
     *
     * @param id
     * @param tcmSyndrome
     * @return
     */
    @Override
    public boolean checkTcmDiagnosis(Long id, String tcmSyndrome) {
        QueryWrapper<BusTcmSyndrome> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(tcmSyndrome), "tcm_syndrome", tcmSyndrome);
        BusTcmSyndrome busTcmSyndrome = busTcmSyndromeMapper.selectOne(queryWrapper);
        return check(id, busTcmSyndrome);
    }

    /**
     * 校验诊断代码是否重复
     *
     * @param id
     * @param diagnosisCode
     * @return
     */
    @Override
    public boolean checkDiagnosisCode(Long id, String diagnosisCode) {
        QueryWrapper<BusTcmSyndrome> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(diagnosisCode), "diagnosis_code", diagnosisCode);
        BusTcmSyndrome busTcmSyndrome = busTcmSyndromeMapper.selectOne(queryWrapper);
        return check(id, busTcmSyndrome);
    }

    /**
     *  根据Ids查询症状
     * @param syndromesIds 症状id
     * @return  症状列表
     */
    @Override
    public List<BusTcmSyndrome> querySyndromeByIds(List<Long> syndromesIds) {
        LambdaQueryWrapper<BusTcmSyndrome> queryWrapper = Wrappers.<BusTcmSyndrome>lambdaQuery().in(BusTcmSyndrome::getId, syndromesIds);
        return busTcmSyndromeMapper.selectList(queryWrapper);
    }

    /**
     * 校验新增还是修改
     *
     * @param id
     * @param busTcmSyndrome
     * @return
     */
    private boolean check(Long id, BusTcmSyndrome busTcmSyndrome) {
        // 新增操作
        if (Objects.isNull(id) && Objects.nonNull(busTcmSyndrome)) {
            return true;
        }
        // 修改操作
        if (Objects.nonNull(id) && Objects.nonNull(busTcmSyndrome)) {
            return !id.equals(busTcmSyndrome.getId());
        }
        return false;
    }
}

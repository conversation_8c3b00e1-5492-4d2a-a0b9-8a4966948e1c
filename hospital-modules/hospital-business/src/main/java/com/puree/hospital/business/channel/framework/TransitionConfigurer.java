package com.puree.hospital.business.channel.framework;

import java.util.List;

/**
 * 状态转移配置器接口
 * @param <S> 状态类型 - 需要转的原始状态类型
 * @param <E> 事件类型 - 触发状态转移的事件类型
 */
public interface TransitionConfigurer<S, E, C> {
    /**
     * 从指定状态转移到目标状态
     * @return 状态转移配置器
     */
    TransitionConfigurer<S, E, C> from(S state);

    /**
     * 从任意状态转移到目标状态
     * @return 状态转移配置器
     */
    TransitionConfigurer<S, E, C> fromAny();

    /**
     * 转移到指定状态
     * @param state 目标状态
     * @return 状态转移配置器
     */
    TransitionConfigurer<S, E, C> to(S state);

    /**
     * 注册一个动作
     * @param action 动作名称
     * @return 状态转移配置器
     */
    TransitionConfigurer<S, E, C> action(Action<C> action);
    /**
     * 注册一个动作 - 使用类名注册动作
     * @param action 动作名称
     * @return 状态转移配置器
     */
    TransitionConfigurer<S, E, C> action(Class<? extends Action<C>> action);

    /**
     * 注册一个动作 - 使用类名注册动作
     * @param className 动作类名
     * @return 状态转移配置器
     */
    TransitionConfigurer<S, E, C> action(String className);
    /**
     * 结束当前状态转移配置 - 必须调用 - 注册到状态机中
     * @return 状态机构建器
     */
    StateMachineBuilder<S, E, C> end();
    /**
     * 检查是否可以从任意状态转移
     * @return true 如果可以从任意状态转移
     */
    boolean isFromAny();
    /**
     * 获取源状态
     * @return 源状态
     */
    S getSourceState();
    /**
     * 获取目标状态
     * @return 目标状态
     */
    S getTargetState();
    /**
     * 获取动作列表
     * @return 动作列表
     */
    List<Action<C>> getActions();
    /**
     * 获取事件类型
     * @return 事件类型
     */
    E getEvent();
}
package com.puree.hospital.business.channel.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.business.domain.BusChannelIdentity;
import com.puree.hospital.business.domain.BusChannelPartnerAgent;
import com.puree.hospital.business.domain.BusChannelPatientAgentRelation;
import com.puree.hospital.business.domain.dto.BusChannelOrderDTO;
import com.puree.hospital.business.domain.vo.BusChannelOrderVO;
import com.puree.hospital.business.domain.vo.ChannelInfoVO;
import com.puree.hospital.business.domain.vo.ChannelPartnerAgentVO;
import com.puree.hospital.business.domain.vo.TableDataInfoVO;
import com.puree.hospital.business.channel.mapper.BusChannelIdentityMapper;
import com.puree.hospital.business.channel.mapper.BusChannelOrderMapper;
import com.puree.hospital.business.channel.mapper.BusChannelPartnerAgentMapper;
import com.puree.hospital.business.channel.mapper.BusChannelPartnerMapper;
import com.puree.hospital.business.channel.mapper.BusChannelPatientAgentRelationMapper;
import com.puree.hospital.business.mapper.BusPatientHospitalMapper;
import com.puree.hospital.business.channel.service.IBusChannelPartnerAgentService;
import com.puree.hospital.common.api.enums.DelFlagEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 经纪人接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusChannelPartnerAgentServiceImpl extends ServiceImpl<BusChannelPartnerAgentMapper, BusChannelPartnerAgent> implements IBusChannelPartnerAgentService {
    private final BusChannelPartnerAgentMapper busChannelPartnerAgentMapper;
    private final BusPatientHospitalMapper busPatientHospitalMapper;
    private final BusChannelPartnerMapper channelPartnerMapper;
    private final BusChannelOrderMapper busChannelOrderMapper;
    private final BusChannelIdentityMapper busChannelIdentityMapper;
    private final BusChannelPatientAgentRelationMapper busChannelPatientAgentRelationMapper;

    @Override
    public List<BusChannelPartnerAgent> listByChannelPartner(Long id) {
        return busChannelPartnerAgentMapper.selectListByChannelPartnerId(id);
    }

    /**
     * 查询合作渠道经纪人列表
     *
     * @param agent 查询参数
     * @return List<BusChannelPartnerAgent>
     */
    @Override
    public List<BusChannelPartnerAgent> queryAgentList(BusChannelPartnerAgent agent) {
        List<String> dateList = agent.getDateList();
        if (CollUtil.isNotEmpty(dateList)) {
            agent.setStartTime(dateList.get(0) + " 00:00:00");
            agent.setEndTime(dateList.get(1) + " 23:59:59");
        }
        List<BusChannelPartnerAgent> channelPartnerAgents = busChannelPartnerAgentMapper.selectAgentList(agent);
        if (CollUtil.isNotEmpty(channelPartnerAgents)) {
            for (BusChannelPartnerAgent channelPartnerAgent : channelPartnerAgents) {
                // 查询客户数
                LambdaQueryWrapper<BusChannelIdentity> query = Wrappers.lambdaQuery();
                query.eq(BusChannelIdentity::getId, channelPartnerAgent.getChannelIdentityId());
                BusChannelIdentity channelIdentity = busChannelIdentityMapper.selectOne(query);
                if (Objects.nonNull(channelIdentity)) {
                    channelPartnerAgent.setPhonenumber(channelIdentity.getPhoneNumber());
                    channelPartnerAgent.setStatus(channelIdentity.getStatus());
                }
                LambdaQueryWrapper<BusChannelPatientAgentRelation> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusChannelPatientAgentRelation::getAgentId, channelPartnerAgent.getId());
                lambdaQuery.isNotNull(BusChannelPatientAgentRelation::getPatientId);
                if (CollUtil.isNotEmpty(dateList)) {
                    lambdaQuery
                            .le(BusChannelPatientAgentRelation::getCreateTime, agent.getStartTime())
                            .ge(BusChannelPatientAgentRelation::getCreateTime, agent.getEndTime());
                }
                Long agentNumber = busChannelPatientAgentRelationMapper.selectCount(lambdaQuery);
                if (ObjectUtil.isNull(agentNumber)) {
                    channelPartnerAgent.setClientNumber(0L);
                } else {
                    channelPartnerAgent.setClientNumber(agentNumber);
                }

                // 查询订单数和订单金额
                BusChannelOrderDTO dto = new BusChannelOrderDTO();
                dto.setAgentId(channelPartnerAgent.getId());
                if (CollUtil.isNotEmpty(dateList)) {
                    dto.setStartTime(dateList.get(0) + " 00:00:00");
                    dto.setEndTime(dateList.get(1) + " 23:59:59");
                }
                List<BusChannelOrderVO> channelOrderList = busChannelOrderMapper.selectChannelOrder(dto);
                if (CollUtil.isNotEmpty(channelOrderList)) {
                    channelPartnerAgent.setOrderNumber(channelOrderList.size());
                    BigDecimal amount = new BigDecimal(0);
                    for (BusChannelOrderVO vo : channelOrderList) {
                        amount = amount.add(BigDecimal.valueOf(vo.getOrderAmount()));
                    }
                    channelPartnerAgent.setAmount(amount);
                } else {
                    channelPartnerAgent.setOrderNumber(0);
                    channelPartnerAgent.setAmount(new BigDecimal(0));
                }
            }
        }
        return channelPartnerAgents;
    }

    @Override
    public void calcuSum(TableDataInfoVO dataInfoVO, List<BusChannelPartnerAgent> channelPartnerAgents) {
        Long agentNumber = 0L;
        long orderNumber = 0L;
        BigDecimal orderAmount = new BigDecimal("0");
        if (CollUtil.isNotEmpty(channelPartnerAgents)) {
            // 查询客户数
            LambdaQueryWrapper<BusChannelPatientAgentRelation> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.in(BusChannelPatientAgentRelation::getAgentId,
                    channelPartnerAgents.stream().map(BusChannelPartnerAgent::getId).collect(Collectors.toList()));
            lambdaQuery.isNotNull(BusChannelPatientAgentRelation::getPatientId);
            agentNumber = busChannelPatientAgentRelationMapper.selectCount(lambdaQuery);
            for (BusChannelPartnerAgent channelPartnerAgent : channelPartnerAgents) {
                // 查询订单数和订单金额
                BusChannelOrderDTO dto = new BusChannelOrderDTO();
                dto.setAgentId(channelPartnerAgent.getId());
                List<BusChannelOrderVO> channelOrderList = busChannelOrderMapper.selectChannelOrder(dto);
                if (CollUtil.isNotEmpty(channelOrderList)) {
                    orderNumber += channelOrderList.size();
                    for (BusChannelOrderVO vo : channelOrderList) {
                        orderAmount = orderAmount.add(BigDecimal.valueOf(vo.getOrderAmount()));
                    }
                }
            }
        }
        dataInfoVO.setAgentNumber(agentNumber);
        dataInfoVO.setOrderNumber(orderNumber);
        dataInfoVO.setAmount(orderAmount);
    }



    /**
     * 校验该手机号是否注册过合作渠道和经纪人
     * @param phoneNumber 手机号码
     * @param hospitalId  医院ID
     * @param ignoreId 忽略ID  例：修改时传入，忽略ID数据行
     * @return 校验结果
     */
    @Override
    public boolean checkPhoneNumber(String phoneNumber, Long ignoreId, Long hospitalId) {
        LambdaQueryWrapper<BusChannelIdentity> lambdaQueryWrapper = Wrappers.<BusChannelIdentity>lambdaQuery()
                .eq(BusChannelIdentity::getPhoneNumber, phoneNumber)
                .eq(Objects.nonNull(hospitalId), BusChannelIdentity::getHospitalId, hospitalId)
                .eq(BusChannelIdentity::getDelFlag, DelFlagEnum.NORMAL.getCode());
        return  busChannelIdentityMapper.selectCount(lambdaQueryWrapper) > 0;
    }

    @Override
    public List<BusChannelPartnerAgent> listChannelPartnerAgentByHospitalId(Long channelPartnerId , Long hospitalId) {
        if (null != channelPartnerId) {
            return this.listByChannelPartner(channelPartnerId) ;
        } else {
            if (null!=hospitalId) {
                return busChannelPartnerAgentMapper.listChannelPartnerAgentByHospitalId(hospitalId) ;
            }
        }
        throw new ServiceException("参数缺失") ;
    }

    /**
     * 根据id查询合作渠道经纪人姓名
     * @param ids id集合
     * @return 获取渠道姓名
     */
    @Override
    public Map<Long,String> getAgentName(List<Long> ids){
        if (CollectionUtils.isNotEmpty(ids)) {
            //查询出id和其对应的经纪人姓名
            LambdaQueryWrapper<BusChannelPartnerAgent> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(BusChannelPartnerAgent::getId, ids);
            wrapper.eq(BusChannelPartnerAgent::getDelFlag, YesNoEnum.NO.getCode());
            List<BusChannelPartnerAgent> agents = busChannelPartnerAgentMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(agents)) {
                return agents.stream().collect(Collectors.toMap(BusChannelPartnerAgent::getId, BusChannelPartnerAgent::getFullName));
            }
        }
        return Collections.emptyMap();
    }


    @Override
    public boolean checkExistInAgentRelation(Long patientId, Long hospitalId) {
        LambdaQueryWrapper<BusChannelPatientAgentRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusChannelPatientAgentRelation::getPatientId, patientId)
                .eq(BusChannelPatientAgentRelation::getHospitalId, hospitalId);
        return busChannelPatientAgentRelationMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public BusChannelPartnerAgent queryInfoByChannelIdentityId(Long channelIdentityId) {
        LambdaQueryWrapper<BusChannelPartnerAgent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusChannelPartnerAgent::getChannelIdentityId, channelIdentityId)
                .eq(BusChannelPartnerAgent::getDelFlag, DelFlagEnum.NORMAL.getCode())
                .orderByDesc(BusChannelPartnerAgent::getCreateTime)
                .last("limit 1");
        return busChannelPartnerAgentMapper.selectOne(queryWrapper);
    }

    @Override
    public ChannelPartnerAgentVO queryInfoById(Long id) {
        BusChannelPartnerAgent busChannelPartnerAgent = busChannelPartnerAgentMapper.selectById(id);
        if (ObjectUtil.isNull(busChannelPartnerAgent)) {
            return null;
        }
        ChannelPartnerAgentVO channelPartnerAgentVO = new ChannelPartnerAgentVO();
        BeanUtils.copyProperties(busChannelPartnerAgent, channelPartnerAgentVO);
        BusChannelIdentity busChannelIdentity = busChannelIdentityMapper.selectById(busChannelPartnerAgent.getChannelIdentityId());
        if (Objects.isNull(busChannelIdentity)) {
            return null;
        }
        channelPartnerAgentVO.setPhonenumber(busChannelIdentity.getPhoneNumber());
        channelPartnerAgentVO.setStatus(busChannelIdentity.getStatus());
        return channelPartnerAgentVO;
    }

    @Override
    public List<BusChannelPatientAgentRelation> getRelationByHospitalIdAndPatientId(List<Long> userId, Long hospitalId) {
        List<BusChannelPatientAgentRelation> busChannelPatientAgentRelation = busChannelPatientAgentRelationMapper.selectList(
        Wrappers.<BusChannelPatientAgentRelation>lambdaQuery()
                .in(BusChannelPatientAgentRelation::getPatientId, userId)
                .eq(BusChannelPatientAgentRelation::getHospitalId, hospitalId));
        if (ObjectUtil.isNotNull(busChannelPatientAgentRelation)) {
            return busChannelPatientAgentRelation;
        }
        return null;
    }

    @Override
    public List<ChannelInfoVO> getChannelInfoByAgentId(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyList();
        }
        return busChannelPartnerAgentMapper.getChannelInfoByAgentId(agentIds);
    }

    /**
     * 根据医院ID获取关联的用户ID
     * @param hospitalId 医院ID
     * @return 用户ID集合
     * 后续可以考虑加入缓存
     */
    @Override
    @SuppressWarnings("unchecked")
//    @Cacheable(value = CacheConstants.CHANNEL_PARTNER_AGENT_ID_LIST, key = "#hospitalId", unless = "#result == null")
    public List<Long> getRelationUserIdByHospitalId(Long hospitalId) {
        if (ObjectUtil.isNull(hospitalId)) {
            return new ArrayList<>();
        }
        List<BusChannelPatientAgentRelation> busChannelPatientAgentRelations = busChannelPatientAgentRelationMapper.selectList(Wrappers.<BusChannelPatientAgentRelation>lambdaQuery()
                .eq(BusChannelPatientAgentRelation::getHospitalId, hospitalId)
                .select(BusChannelPatientAgentRelation::getPatientId));
        if (CollectionUtils.isEmpty(busChannelPatientAgentRelations)) {
            return Collections.emptyList();
        }
        return busChannelPatientAgentRelations.stream().map(BusChannelPatientAgentRelation::getPatientId).collect(Collectors.toList());
    }

    @Override
    public List<BusChannelPatientAgentRelation> getRelationByAgentId(Long agentId) {
        LambdaQueryWrapper<BusChannelPatientAgentRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusChannelPatientAgentRelation::getAgentId, agentId);
        return busChannelPatientAgentRelationMapper.selectList(queryWrapper);
    }


}
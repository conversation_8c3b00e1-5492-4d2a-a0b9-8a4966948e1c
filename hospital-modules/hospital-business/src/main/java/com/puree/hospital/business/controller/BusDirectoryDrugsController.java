package com.puree.hospital.business.controller;

import com.github.pagehelper.PageHelper;
import com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO;
import com.puree.hospital.business.domain.BusDirectoryDrugs;
import com.puree.hospital.business.domain.BusDrugs;
import com.puree.hospital.business.domain.BusDrugsApply;
import com.puree.hospital.business.domain.dto.BusDirectoryDrugsInsertDto;
import com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo;
import com.puree.hospital.business.domain.vo.BusDrugsVo;
import com.puree.hospital.business.service.IBusDirectoryDrugsService;
import com.puree.hospital.business.service.IBusDrugsApplyService;
import com.puree.hospital.business.service.IBusDrugsService;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RequestMapping("directory/drugs")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDirectoryDrugsController extends BaseController {
    private final IBusDirectoryDrugsService busDirectoryDrugsService;
    private final IBusDrugsApplyService busDrugsApplyService;
    private final IBusDrugsService busDrugsService;

    /**
     * 查询目录药品列表
     *
     * @param dto
     * @return
     */
    @GetMapping("list")
    @Log(title = "查询目录药品列表", businessType = BusinessType.QUERY)
    public TableDataInfo list(BusDirectoryDrugsDTO dto) {
        startPage();
        return getDataTable(busDirectoryDrugsService.selectList(dto));
    }

    /**
     * 查询目录药品列表
     *
     * @param dto 请求参数
     * @return 目录药品列表
     */
    @PostMapping("feign/list")
    @Log(title = "查询目录药品列表", businessType = BusinessType.QUERY)
    public R<List<BusDirectoryDrugsVo>> feignList(@RequestBody BusDirectoryDrugsDTO dto) {
        dto.setStockFilter(BusDirectoryDrugsDTO.StockFilterEnum.HAS_STOCK);
        return R.ok(busDirectoryDrugsService.selectDrugsList(dto));
    }

    /**
     * 查询未被关联目录的药品列表
     *
     * @param dto
     * @return
     */
    @GetMapping("notAssociateList")
    @Log(title = "查询未被关联目录的药品列表", businessType = BusinessType.QUERY)
    public TableDataInfo notAssociateList(BusDirectoryDrugsDTO dto) {
        startPage();
        List<BusDirectoryDrugsVo> list = busDirectoryDrugsService.selectNotAssociateList(dto);
        return getDataTable(list);
    }


    /**
     * 查询药品列表
     *
     * @param dto
     * @return
     */
    @GetMapping("tcm/list")
    @Log(title = "查询药品列表", businessType = BusinessType.QUERY)
    public TableDataInfo tcmList(BusDirectoryDrugsDTO dto) {
        startPage();
        List<BusDirectoryDrugsVo> list = busDirectoryDrugsService.tcmDirectoryDrugsList(dto);
        return getDataTable(list);
    }


    /**
     * 查询中药未被关联目录的药品列表
     *
     * @param dto
     * @return
     */
    @GetMapping("tcmNotAssociateList")
    @Log(title = "查询中药未被关联目录的药品列表", businessType = BusinessType.QUERY)
    public TableDataInfo tcmNotAssociateList(BusDirectoryDrugsDTO dto) {
        startPage();
        List<BusDirectoryDrugsVo> list = busDirectoryDrugsService.tcmNotAssociateList(dto);
        return getDataTable(list);
    }


    /**
     * 新增
     *
     * @param insertDto
     * @return 返回影响行数
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:insert")
    @Log(title = "药品目录管理", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    @PostMapping("insert")
    public AjaxResult insert(@RequestBody BusDirectoryDrugsInsertDto insertDto) {
        if (null == insertDto) {
            return AjaxResult.error("参数缺失！");
        }
        List<BusDirectoryDrugs> list = insertDto.getList();
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.error("参数缺失！");
        }
        list.forEach(i -> {
            i.setCreateBy(SecurityUtils.getUsername());
            i.setCreateTime(DateUtils.getNowDate());
        });
        return toAjax(busDirectoryDrugsService.batchInsert(list));
    }

    /**
     * 患教下的药品同步至医院 远程调用
     *
     * @param hospitalId 医院id
     * @param ids 药品id集合
     * @return 返回影响行数
     */
    @Log(title = "新版患教关联的药品上架至医院", businessType = BusinessType.INSERT)
    @PostMapping("/sync")
    public AjaxResult syncDrugs(@RequestParam("hospitalId") Long hospitalId, @RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return AjaxResult.success(0);
        }
        return toAjax(busDirectoryDrugsService.syncDrugs(hospitalId, ids));
    }

    /**
     * 删除
     *
     * @param bdd
     * @return 返回影响行数
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:delete")
    @Log(title = "删除药品目录", businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    @PostMapping("delete")
    public AjaxResult delete(@RequestBody BusDirectoryDrugs bdd) {
        return toAjax(busDirectoryDrugsService.delete(bdd));
    }

    /**
     * 申请
     *
     * @param busDrugsApply
     * @return 返回影响行数
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:apply")
    @Log(title = "药品申请", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    @PostMapping("apply")
    public AjaxResult apply(@RequestBody BusDrugsApply busDrugsApply) {
        busDrugsApply.setCreateBy(SecurityUtils.getUsername());
        return toAjax(busDrugsApplyService.insert(busDrugsApply));
    }

    /**
     * 获取药品详细信息
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:detail")
    @GetMapping(value = "/{id}")
    @Log(title = "查询药品详细信息", businessType = BusinessType.QUERY)
    public AjaxResult drugsDetail(@PathVariable("id") Long id) {
        return AjaxResult.success(busDrugsService.selectBusDrugsById(id));
    }

    /**
     * 查询中药目录列表
     *
     * @param dto
     * @return
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:tcmDrugsList")
    @GetMapping("/tcmDrugsList")
    @Log(title = "查询中药目录列表", businessType = BusinessType.QUERY)
    public TableDataInfo tcmDrugsList(BusDirectoryDrugsDTO dto) {
        if (StringUtils.isNotEmpty(dto.getDrugsName())) {
            dto.setDrugsName(dto.getDrugsName().trim());
        }
        dto.setCheckGrounding(false);
        startPage();
        List<BusDirectoryDrugsVo> list = busDirectoryDrugsService.tcmDrugsList(dto);
        return getDataTable(list);
    }

    /**
     * 查詢中药详情
     *
     * @param id
     * @param hospitalId
     * @return
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:query")
    @GetMapping("/query")
    @Log(title = "查询中药详情", businessType = BusinessType.QUERY)
    public AjaxResult tcmDrugsById(@RequestParam("id") Long id, @RequestParam("hospitalId") Long hospitalId) {
        if (StringUtils.isNull(id) || StringUtils.isNull(hospitalId)) {
            return AjaxResult.error("参数缺失");
        }
        return AjaxResult.success(busDirectoryDrugsService.tcmDrugsById(id, hospitalId));
    }

    /**
     * 添加中药目录
     *
     * @param bdd
     * @return
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:addTcmDrugs")
    @PostMapping("/addTcmDrugs")
    @Log(title = "中药药品目录管理", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    public AjaxResult addTcmDrugs(@RequestBody List<BusDirectoryDrugs> bdd) {
        if (CollectionUtils.isEmpty(bdd)) {
            return AjaxResult.error("参数缺失！");
        }
        bdd.forEach(d->{
            d.setCreateBy(SecurityUtils.getUsername());
            d.setCreateTime(DateUtils.getNowDate());
        });
        return toAjax(busDirectoryDrugsService.tcmInsert(bdd));
    }

    /**
     * 更新中药目录
     *
     * @param bdd
     * @return
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:editTcmDrugs")
    @PostMapping("/editTcmDrugs")
    @Log(title = "中药药品目录管理", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    public AjaxResult editTcmDrugs(@RequestBody BusDirectoryDrugs bdd) {
        return toAjax(busDirectoryDrugsService.update(bdd));
    }

    /**
     * 删除中药目录
     *
     * @param id
     * @param hospitalId
     * @return
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:deleteTcmDrugs")
    @GetMapping("/deleteTcmDrugs")
    @Log(title = "中药药品目录管理", businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    public AjaxResult deleteTcmDrugs(@RequestParam("id") Long id, @RequestParam("hospitalId") Long hospitalId) {
        if (Objects.isNull(id) || Objects.isNull(hospitalId)) {
            return AjaxResult.error("参数缺失");
        }
        BusDirectoryDrugs bdd = new BusDirectoryDrugs();
        bdd.setId(id);
        bdd.setHospitalId(hospitalId);
        return toAjax(busDirectoryDrugsService.delete(bdd));
    }

    /**
     * 查询运营中药配送企业药品
     *
     * @param dto
     * @return
     */
    @GetMapping("/yytcmDrugsList")
    @Log(title = "查询运营中药配送企业药品", businessType = BusinessType.QUERY)
    public AjaxResult yytcmDrugsList(BusDrugs dto) {
        List<BusDrugsVo> list = busDirectoryDrugsService.yytcmDrugsList(dto);
        return AjaxResult.success(list);
    }

    /**
     * @return 返回是否可以删除
     */
    @Log(title = "删除药品目录前校验", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    @PostMapping("/check-before-delete")
    public AjaxResult checkBeforeDelete(@RequestBody BusDirectoryDrugs bdd) {
        return AjaxResult.success(busDirectoryDrugsService.checkBeforeDelete(bdd));
    }

    /**
     * 删除
     *
     * @param bdd 药品目录
     * @return 返回影响行数
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:delete")
    @Log(title = "删除药品目录", businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    @DeleteMapping("/delete-v2")
    public AjaxResult deleteV2(@RequestBody BusDirectoryDrugs bdd) {
        return toAjax(busDirectoryDrugsService.delete(bdd));
    }

    /**
     * 修改药品目录
     *
     * @param directoryDrugs 修改请求参数
     * @return 修改结果
     */
    @PreAuthorize(hasPermi = "business:directory:drugs:edit")
    @Log(title = "更新药品目录", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody BusDirectoryDrugs directoryDrugs) {
        return toAjax(busDirectoryDrugsService.update(directoryDrugs));
    }

}

package com.puree.hospital.business.channel.domain.service.impl;

import com.puree.hospital.business.channel.domain.command.AgentInfoUpdateCommand;
import com.puree.hospital.business.channel.domain.command.ChannelBindCommand;
import com.puree.hospital.business.channel.domain.command.ChannelInfoUpdateCommand;
import com.puree.hospital.business.channel.domain.model.aggregate.ChannelAgentAggregate;
import com.puree.hospital.business.channel.domain.model.aggregate.ChannelIdentityAggregate;
import com.puree.hospital.business.channel.domain.model.aggregate.ChannelPartnerAggregate;
import com.puree.hospital.business.channel.domain.model.aggregate.PatientAgentRelationAggregate;
import com.puree.hospital.business.channel.domain.model.bo.AgentInfoBO;
import com.puree.hospital.business.channel.domain.model.bo.AgentRef;
import com.puree.hospital.business.channel.domain.model.bo.ChannelIdentityRef;
import com.puree.hospital.business.channel.domain.model.bo.ChannelRef;
import com.puree.hospital.business.channel.domain.model.bo.HospitalRef;
import com.puree.hospital.business.channel.domain.model.bo.PhoneNumber;
import com.puree.hospital.business.channel.domain.model.bo.UserRef;
import com.puree.hospital.business.channel.domain.repository.ChannelManagementRepository;
import com.puree.hospital.business.channel.domain.service.IChannelDomainService;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/***
 * 渠道身份领域服务
 * <AUTHOR>
 * @date 2025/6/24
 * @version 1.0
 */
@Slf4j
@Service
public class ChannelDomainService implements IChannelDomainService {
    @Resource
    private final ChannelManagementRepository repository;

    public ChannelDomainService(ChannelManagementRepository channelManagementRepository) {
        this.repository = channelManagementRepository;
    }




    @Override
    public void channelBind(ChannelBindCommand bindCommand) {
        // 1. 参数校验
        validateChannelBindCommand(bindCommand);
        // 2. 查询当前用户的渠道身份信息
        Optional<ChannelIdentityAggregate> currentUserChannelIdentity = repository.findIdentityByHospitalIdAndPhoneNumber(
                HospitalRef.of(bindCommand.getHospitalId()),
                PhoneNumber.of(bindCommand.getPhoneNumber()));
        if (currentUserChannelIdentity.isPresent()) {
            // 如果当前用户已存在渠道身份信息，则不允许重复绑定
            log.warn("当前用户已存在渠道身份信息，无法重复绑定: {}", bindCommand.getPhoneNumber());
            throw new ServiceException("当前用户已存在渠道身份信息，无法重复绑定", 502);
        }
        // 3. 查询需要绑定的渠道身份信息，确定走锁客逻辑还是走成为合伙人（经纪人）的逻辑
        Optional<ChannelIdentityAggregate> identityById = repository.findIdentityById(bindCommand.getChannelIdentityId());
        if (!identityById.isPresent()) {
            // 绑定的渠道身份信息不存在，则不允许绑定
            log.warn("绑定的渠道身份信息不存在，无法绑定: {}", bindCommand.getChannelIdentityId());
            throw new ServiceException("绑定的渠道身份信息不存在", 502);
        }
        ChannelIdentityAggregate channelIdentityAggregate = identityById.get();
        if (channelIdentityAggregate.isDeleted()) {
            // 如果当前渠道身份信息已被删除，则不允许绑定
            log.warn("渠道身份已被删除，无法绑定: {}", channelIdentityAggregate);
            throw new ServiceException("渠道身份已被删除，无法绑定", 502);
        }
        // 根据身份类型的不同，绑定渠道
        if (channelIdentityAggregate.getIdentityType().isChannel()) {
            if (EnableStatusEnum.isDisabled(channelIdentityAggregate.getStatus().getStatus())) {
                // 如果当前渠道身份信息未激活，则不允许绑定
                log.warn("渠道身份未激活，无法绑定: {}", channelIdentityAggregate);
                throw new ServiceException("渠道身份未激活，无法绑定", 500);
            }
            // 绑定渠道 - 成为合伙人
            bindChannelPartner(UserRef.of(bindCommand.getUserId()),
                    ChannelIdentityRef.of(bindCommand.getChannelIdentityId()),
                    PhoneNumber.of(bindCommand.getPhoneNumber()),
                    AgentInfoBO.of(bindCommand.getAgentFullName(), bindCommand.getAgentShopName()),
                    HospitalRef.of(bindCommand.getHospitalId()));
            return;
        }
        if (channelIdentityAggregate.getIdentityType().isAgent()) {
            // 绑定经纪人 - 锁客
            // 不管合伙人身份的启用或者禁用状态
            bindChannelAgent(UserRef.of(bindCommand.getUserId()),
                    HospitalRef.of(channelIdentityAggregate.getHospitalRef().getValue()),
                    channelIdentityAggregate.getChannelIdentityRef());
            return;
        }
        throw new ServiceException("不支持的渠道身份类型");
    }

    @Override
    public int updateAgentInfoAndStatus(AgentInfoUpdateCommand command) {
        ChannelAgentAggregate agent = repository.findAgentById(AgentRef.of(command.getAgentId()));
        if (agent == null) {
            log.warn("未找到经纪人信息: {}", command.getAgentId());
            throw new ServiceException("未找到经纪人信息");
        }
        // 1. 更新经纪人信息
        agent.updateAgentInfo(command.getAgentFullName(), command.getAgentShopName(), false);
        // 2. 更新经纪人状态
        if (EnableStatusEnum.isDisabled(command.getAgentStatus().getStatus())) {
            agent.getChannelIdentityAggregate().disable();
        }
        if (EnableStatusEnum.isEnabled(command.getAgentStatus().getStatus())) {
            agent.getChannelIdentityAggregate().activate();
        }
        return repository.updateAgent(agent);
    }

    @Override
    public int addNewChannel(ChannelPartnerAggregate channel) {
        // 1. 校验手机号是否已经存在身份
        if (repository.existChannelIdentityByPhoneNumber(channel.getHospitalRef(),
                channel.getCurrentChannelIdentityAggregate().getPhoneNumber())) {
            log.warn("手机号已存在渠道身份，无法添加新的渠道: {}", channel.getCurrentChannelIdentityAggregate().getPhoneNumber());
            throw new ServiceException("手机号已存在渠道身份，无法添加新的渠道");
        }
        // 2. 创建新的渠道身份
        channel.addCheckChannelIdentity();
        return repository.addChannelPartner(channel);
    }

    @Override
    public int editChannelInfoAndStatus(ChannelInfoUpdateCommand command) {
        ChannelPartnerAggregate channel = repository.findChannelById(ChannelRef.of(command.getChannelId()));
        if (channel == null) {
            log.warn("未找到合作渠道信息: {}", command.getChannelId());
            throw new ServiceException("未找到经纪人信息");
        }
        // 1. 更新经纪人信息
        channel.updateChannelInfo(command.getChannelFullName(), command.getChannelContactName());
        // 2. 更新经纪人状态
        if (EnableStatusEnum.isDisabled(command.getChannelStatus().getStatus())) {
            channel.getCurrentChannelIdentityAggregate().disable();
        }
        if (EnableStatusEnum.isEnabled(command.getChannelStatus().getStatus())) {
            channel.getCurrentChannelIdentityAggregate().activate();
        }
        return repository.updateChannel(channel);
    }

    @Override
    public void unbindAgent(UserRef userRef, HospitalRef hospitalRef) {
        repository.deletePatientAgentRelationByUserIdAndHospitalId(userRef, hospitalRef);
    }

    @Override
    public int removeChannelById(ChannelRef id) {
        ChannelPartnerAggregate channel = repository.findChannelById(id);
        channel.delete();
        channel.getCurrentChannelIdentityAggregate().delete();
        return repository.deleteChannel(channel);
    }

    @Override
    public int updateChannelStatus(ChannelInfoUpdateCommand command) {
        Assert.notNull(command, "参数不能为空");
        Optional<ChannelIdentityAggregate> identity = repository.findIdentityByChannelId(command.getChannelId());
        if (!identity.isPresent()) {
            throw new ServiceException("渠道身份不存在");
        }
        ChannelIdentityAggregate channelIdentityAggregate = identity.get();
        channelIdentityAggregate.updateStatus(command.getChannelStatus());
        return repository.updateChannelIdentityById(channelIdentityAggregate);
    }

    @Override
    public int updateAgentStatus(AgentInfoUpdateCommand command) {
        Assert.notNull(command, "参数不能为空");
        Optional<ChannelIdentityAggregate> identity = repository.findIdentityByAgentId(AgentRef.of(command.getAgentId()));
        if (!identity.isPresent()) {
            return 0;
        }
        ChannelIdentityAggregate channelIdentityAggregate = identity.get();
        channelIdentityAggregate.updateStatus(command.getAgentStatus());
        return repository.updateChannelIdentityById(channelIdentityAggregate);
    }

    @Override
    public int updateBindingAgent(UserRef userRef, AgentRef agentRef, HospitalRef hospitalRef) {
        Optional<PatientAgentRelationAggregate> relation = repository.findPatientAgentRelationByUserIdAndHospitalId(userRef, hospitalRef);
        PatientAgentRelationAggregate agentRelation;
        if (relation.isPresent()) {
            agentRelation = relation.get();
            agentRelation.changeAgent(agentRef);
        } else {
            agentRelation = PatientAgentRelationAggregate.create(PatientAgentRelationAggregate.PatientRef.of(userRef.getValue()),
                    agentRef,
                    hospitalRef,
                    SecurityUtils.getUsername()
            );
        }
        return repository.insertOrUpdatePatientAgentRelation(agentRelation);
    }

    @Override
    public int deleteAgent(AgentRef agentRef, UserRef userRef) {
        ChannelAgentAggregate agent = repository.findAgentById(agentRef);
        if (Objects.isNull(agent) || Objects.isNull(agent.getChannelIdentityAggregate())) {
            return 0;
        }
        agent.delete();
        agent.getChannelIdentityAggregate().delete();
        int count = repository.deleteAgent(agent);
        // 3.删除用户和经纪人关系 - 如果用户还没注册，则不用删除关系（没绑定关系）
        if (Objects.nonNull(userRef)) {
            repository.deletePatientAgentRelationByUserIdAndHospitalId(userRef, agent.getHospitalRef());
        }
        return count;
    }

    @Override
    public int addNewAgent(ChannelAgentAggregate agent, UserRef userRef) {
        repository.saveNewChannelAgent(agent);
        if (userRef == null) {
            return 1;
        }
        // 2.保存经纪人关系表，关联自己
        PatientAgentRelationAggregate channelAgentRelation = PatientAgentRelationAggregate.create(
                PatientAgentRelationAggregate.PatientRef.of(userRef.getValue()),
                AgentRef.of(agent.getAgentRef().getValue()),
                agent.getHospitalRef(),
                SecurityUtils.getUsername()
        );
        repository.insertOrUpdatePatientAgentRelation(channelAgentRelation);
        return 1;
    }

    @Override
    public void updatePhone(PhoneNumber oldPhoneNumber, PhoneNumber phoneNumber) {
        if (repository.existChannelIdentityByPhoneNumber(phoneNumber)) {
            throw new ServiceException("手机号已存在渠道身份，无法修改");
        }
        List<ChannelIdentityAggregate> channelIdentityAggregateList = repository.findChannelIdentityByPhoneNumber(oldPhoneNumber);
        if (CollectionUtils.isEmpty(channelIdentityAggregateList)) {
            return;
        }
        channelIdentityAggregateList.forEach(
                identity -> identity.changePhoneNumber(phoneNumber)
        );
        repository.updateChannelIdentityListById(channelIdentityAggregateList);
    }


    /**
     * 绑定经纪人 - 锁客逻辑
     * @param userRef 用户ID
     * @param channelIdentityRef 渠道身份ID
     */
    private void bindChannelAgent(UserRef userRef, HospitalRef hospitalRef, ChannelIdentityRef channelIdentityRef) {
        Optional<PatientAgentRelationAggregate> patientAgentRelationByUserIdAndHospitalId = repository.findPatientAgentRelationByUserIdAndHospitalId(userRef, hospitalRef);
        if (patientAgentRelationByUserIdAndHospitalId.isPresent()) {
            // 如果当前用户已存在经纪人绑定关系，则不允许重复绑定
            log.warn("当前用户已存在经纪人绑定关系，无法重复绑定: {}", userRef);
            throw new ServiceException("当前用户已存在经纪人绑定关系，无法重复绑定", 502);
        }
        // 1.参数校验 - 创建的时候校验过了
        // 2.查询绑定渠道身份下是否存在经纪人信息
        Optional<ChannelAgentAggregate> agent = repository.findAgentEntityByChannelIdentityId(channelIdentityRef);
        if (!agent.isPresent()) {
            // 如果存在经纪人信息，则不允许重复绑定
            log.warn("当前渠道身份未找到经纪人信息，无法重复绑定: {}", channelIdentityRef);
            throw new ServiceException("当前渠道身份未找到经纪人信息，无法绑定", 502);
        }
        ChannelAgentAggregate channelAgentAggregate = agent.get();
        if (!Objects.equals(channelAgentAggregate.getHospitalRef().getValue(), hospitalRef.getValue())) {
            // 如果经纪人信息的医院ID和当前医院ID不一致，则不允许绑定
            log.warn("经纪人信息的医院ID和当前医院ID不一致，无法绑定: {}, {}", channelAgentAggregate.getHospitalRef(), hospitalRef.getValue());
            throw new ServiceException("经纪人信息的医院ID和当前医院ID不一致，无法绑定", 502);
        }
        // 2.需要新增业务对象
        PatientAgentRelationAggregate patientAgentRelation = PatientAgentRelationAggregate.create(
                PatientAgentRelationAggregate.PatientRef.of(userRef.getValue()),
                channelAgentAggregate.getAgentRef(),
                hospitalRef,
                SecurityUtils.getUsername()
        );
        // 3.保存绑定关系
        repository.insertOrUpdatePatientAgentRelation(patientAgentRelation);
    }

    /**
     * 绑定渠道代理人
     *
     * @param userId            用户ID
     * @param channelIdentityRef 渠道身份ID
     * @param userPhone         用户手机号
     * @param agentInfoBO         注册的经纪人信息
     */
    private void bindChannelPartner(UserRef userId, ChannelIdentityRef channelIdentityRef,
                                    PhoneNumber userPhone, AgentInfoBO agentInfoBO,
                                    HospitalRef hospitalRef) {
        // 1.查询绑定的渠道身份信息
        Optional<ChannelPartnerAggregate> channel = repository.findChannelEntityByChannelIdentityId(channelIdentityRef);
        if (!channel.isPresent()) {
            // 如果不存在渠道身份信息，则不允许重复绑定
            log.warn("不存在渠道身份信息，无法绑定: {}", userPhone);
            throw new ServiceException("不存在渠道身份信息，无法绑定", 502);
        }
        ChannelPartnerAggregate channelPartnerAggregate = channel.get();
        if (!Objects.equals(channelPartnerAggregate.getHospitalRef().getValue(), hospitalRef.getValue())) {
            // 如果当前渠道身份信息已被删除，则不允许绑定
            log.warn("渠道身份和用户不在一个医院，无法绑定: {}", channelPartnerAggregate);
            throw new ServiceException("渠道身份和用户不在一个医院，无法绑定", 502);
        }
        // 1.身份信息和经纪人信息保存
        ChannelAgentAggregate channelAgentAggregate = ChannelAgentAggregate.create(
                channelPartnerAggregate.getChannelRef(),
                agentInfoBO,
                hospitalRef,
                SecurityUtils.getUsername(),
                ChannelIdentityAggregate.create(
                        ChannelIdentityAggregate.IdentityType.AGENT,
                        hospitalRef,
                        userPhone
                )
        );
        repository.saveNewChannelAgent(channelAgentAggregate);
        // 2.保存经纪人关系表，关联自己
        PatientAgentRelationAggregate channelAgentRelation = PatientAgentRelationAggregate.create(
                PatientAgentRelationAggregate.PatientRef.of(userId.getValue()),
                AgentRef.of(channelAgentAggregate.getAgentRef().getValue()),
                hospitalRef,
                SecurityUtils.getUsername()
        );
        repository.insertOrUpdatePatientAgentRelation(channelAgentRelation);
    }

    /**
     * 校验渠道绑定命令
     * @param bindCommand 渠道绑定命令
     */
    private void validateChannelBindCommand(ChannelBindCommand bindCommand) {
        // 1. 校验参数是否为空
        if (bindCommand == null) {
            throw new ServiceException("参数不能为空");
        }
        // 2. 校验参数是否合法
        if (bindCommand.getUserId() == null || bindCommand.getHospitalId() == null || bindCommand.getPhoneNumber() == null) {
            throw new ServiceException("参数不合法");
        }
    }

    // …还可以添加 deactivateChannel、deleteChannel、refreshToken 等跨聚合或外部校验操作…
}

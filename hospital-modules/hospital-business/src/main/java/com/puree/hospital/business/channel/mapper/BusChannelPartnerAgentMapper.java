package com.puree.hospital.business.channel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.business.domain.BusChannelPartnerAgent;
import com.puree.hospital.business.domain.vo.ChannelInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合作渠道经纪人Mapeer
 * <AUTHOR>
 */
public interface BusChannelPartnerAgentMapper extends BaseMapper<BusChannelPartnerAgent> {

    /**
     * 查询合作渠道经纪人列表
     * @param agent 查询参数
     * @return List
     */
    List<BusChannelPartnerAgent> selectAgentList(BusChannelPartnerAgent agent);

    List<BusChannelPartnerAgent> listChannelPartnerAgentByHospitalId(@Param("hospitalId") Long hospitalId) ;

    /**
     * 根据经纪人ID查询渠道信息
     * @param agentIds 经纪人ID
     * @return 渠道信息
     */
    List<ChannelInfoVO> getChannelInfoByAgentId(@Param("agentIds") List<Long> agentIds);

    /**
     * 根据渠道合作商ID查询合作渠道经纪人列表
     * @param channelPartnerId 渠道合作商ID
     * @return 合作渠道经纪人列表
     */
    List<BusChannelPartnerAgent> selectListByChannelPartnerId(Long channelPartnerId);
}

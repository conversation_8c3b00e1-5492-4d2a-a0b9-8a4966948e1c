package com.puree.hospital.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.puree.hospital.business.domain.BusChannelPhysician;
import com.puree.hospital.business.domain.BusDoctor;
import com.puree.hospital.business.domain.BusPatientReportPhysician;
import com.puree.hospital.business.domain.dto.BusPatientReportPhysicianDTO;
import com.puree.hospital.business.channel.mapper.BusChannelPhysicianMapper;
import com.puree.hospital.business.mapper.BusDoctorMapper;
import com.puree.hospital.business.mapper.BusPatientReportPhysicianMapper;
import com.puree.hospital.business.service.IBusPatientReportPhysicianService;
import com.puree.hospital.common.core.enums.FollowUpStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 患者报告健管师绑定表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPatientReportPhysicianServiceImpl extends ServiceImpl<BusPatientReportPhysicianMapper, BusPatientReportPhysician> implements IBusPatientReportPhysicianService {
    private Logger logger = LoggerFactory.getLogger(BusPatientReportPhysicianServiceImpl.class);
    private final BusPatientReportPhysicianMapper busPatientReportPhysicianMapper;
    private final BusChannelPhysicianMapper channelPhysicianMapper;
    private final BusDoctorMapper doctorMapper;
    @Override
    public Long queryUnassignedCount(String[] measureTime, Long partnerId,Long hospitalId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        LambdaQueryWrapper<BusPatientReportPhysician> lambdaQuery = Wrappers.lambdaQuery();
        // 健管师id为空
        lambdaQuery.isNull(BusPatientReportPhysician::getDoctorId);
        //体检时间范围
        if (null != measureTime) {
            lambdaQuery.ge(BusPatientReportPhysician::getMeasureTime, sdf.format(measureTime[0]));
            lambdaQuery.le(BusPatientReportPhysician::getMeasureTime, sdf.format(measureTime[1]));
        }
        //渠道查询条件
        lambdaQuery.eq(null != partnerId, BusPatientReportPhysician::getPartnerId, partnerId);
        lambdaQuery.eq(BusPatientReportPhysician::getHospitalId,hospitalId);
        return busPatientReportPhysicianMapper.selectCount(lambdaQuery);
    }

    @Override
    public List<BusPatientReportPhysician> selectlist(BusPatientReportPhysicianDTO busPatientReportPhysicianDTO) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        LambdaQueryWrapper<BusPatientReportPhysician> lambdaQuery = Wrappers.lambdaQuery();
        //渠道id
        lambdaQuery.eq(null != busPatientReportPhysicianDTO.getPartnerId(), BusPatientReportPhysician::getPartnerId, busPatientReportPhysicianDTO.getPartnerId());
        //健管师id
        lambdaQuery.eq(null != busPatientReportPhysicianDTO.getDoctorId(), BusPatientReportPhysician::getDoctorId, busPatientReportPhysicianDTO.getDoctorId());
        //跟进状态
        lambdaQuery.eq(null != busPatientReportPhysicianDTO.getFollowUpStatus(), BusPatientReportPhysician::getFollowUpStatus, busPatientReportPhysicianDTO.getFollowUpStatus());
        lambdaQuery.eq(BusPatientReportPhysician::getHospitalId,busPatientReportPhysicianDTO.getHospitalId());
        lambdaQuery.orderByDesc(BusPatientReportPhysician::getMeasureTime);
        //体检时间范围
        if (null != busPatientReportPhysicianDTO.getPhysicalTime()) {
            lambdaQuery.ge(BusPatientReportPhysician::getMeasureTime, sdf.format(busPatientReportPhysicianDTO.getPhysicalTime().get(0)));
            lambdaQuery.le(BusPatientReportPhysician::getMeasureTime, sdf.format(busPatientReportPhysicianDTO.getPhysicalTime().get(1)));
        }
        List<BusPatientReportPhysician> list= busPatientReportPhysicianMapper.selectList(lambdaQuery);
        //按照体检时间降序
        return list;


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int reDistribute(BusPatientReportPhysicianDTO dto) {
        List<BusPatientReportPhysician> allReports = distinctPatientReport(dto);
        for (int i = 0; i < allReports.size(); i++) {
            BusPatientReportPhysician report = allReports.get(i);
            if (dto.getNewDoctorId().equals(report.getDoctorId())) {
                allReports.remove(report);
            }
        }
        // 健管师跟进上限
        BusChannelPhysician physician = channelPhysicianMapper.selectOne(new LambdaQueryWrapper<BusChannelPhysician>()
                .eq(BusChannelPhysician::getHospitalId, dto.getHospitalId())
                .eq(BusChannelPhysician::getDoctorId, dto.getNewDoctorId())
                .last("limit 1"));
        StringUtils.isNullThrowExp(physician, "健管师不存在");
        // 健管师目前跟进的报告数
        List<String> statusList = Lists.newArrayList(FollowUpStatusEnum.TO_BE_FOLLOWED_UP.getCode(), FollowUpStatusEnum.FOLLOW_UP.getCode());
        Long reportCount = busPatientReportPhysicianMapper.selectCount(new LambdaQueryWrapper<BusPatientReportPhysician>()
                .eq(BusPatientReportPhysician::getHospitalId, dto.getHospitalId())
                .eq(BusPatientReportPhysician::getDoctorId, dto.getNewDoctorId())
                .in(BusPatientReportPhysician::getFollowUpStatus, statusList));
        if (allReports.size() > physician.getUpperLimit() - reportCount) {
            throw new ServiceException("患者报告已超出该健管师的跟进上限，请选择其他健管师");
        }
        for (BusPatientReportPhysician allReport : allReports) {
            BusDoctor doctor = doctorMapper.selectById(dto.getNewDoctorId());
            StringUtils.isNullThrowExp(doctor,"新绑定健管师不存在");
            allReport.setUpdateTime(new Date());
            allReport.setDoctorId(dto.getNewDoctorId());
            allReport.setDoctorName(doctor.getFullName());
            busPatientReportPhysicianMapper.updateById(allReport);
        }
        return 1;
    }

    @Override
    public int selectPatientReport(BusPatientReportPhysicianDTO dto) {
        List<BusPatientReportPhysician> list = distinctPatientReport(dto);
        Map<String, List<BusPatientReportPhysician>> distinct = list.stream().collect(Collectors.groupingBy(BusPatientReportPhysician::getIdNumber));
        return distinct.size();
    }

    /**
     * 查询待跟进患者报告
     *
     * @param dto 筛选条件
     * @return 待跟进集合
     */
    private List<BusPatientReportPhysician> distinctPatientReport(BusPatientReportPhysicianDTO dto) {
        boolean haveDate = StringUtils.isNotEmpty(dto.getPhysicalTime()) && dto.getPhysicalTime().size() == 2;
        LambdaQueryWrapper<BusPatientReportPhysician> query = Wrappers.lambdaQuery();
        query.eq(StringUtils.isNotNull(dto.getPartnerId()), BusPatientReportPhysician::getPartnerId, dto.getPartnerId())
                .eq(StringUtils.isNotNull(dto.getDoctorId()), BusPatientReportPhysician::getDoctorId, dto.getDoctorId())
                .eq(BusPatientReportPhysician::getFollowUpStatus, FollowUpStatusEnum.TO_BE_FOLLOWED_UP.getCode())
                .eq(BusPatientReportPhysician::getHospitalId, dto.getHospitalId());
        if (haveDate) {
            query.ge(BusPatientReportPhysician::getMeasureTime, dto.getPhysicalTime().get(0))
                    .le(BusPatientReportPhysician::getMeasureTime, dto.getPhysicalTime().get(1));
        }
        List<BusPatientReportPhysician> list = busPatientReportPhysicianMapper.selectList(query);
        if (StringUtils.isEmpty(list)) {
            throw new ServiceException("无匹配当前筛选的患者");
        }
        return list;
    }
}

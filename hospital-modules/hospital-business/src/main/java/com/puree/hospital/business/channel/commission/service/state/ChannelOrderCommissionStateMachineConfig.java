package com.puree.hospital.business.channel.commission.service.state;

import com.puree.hospital.business.channel.commission.service.state.impl.VerifyOrderAfterSaleHasEndAction;
import com.puree.hospital.business.channel.commission.service.state.impl.balancelog.CreateClearingBalanceRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.balancelog.CreatePartOfRefundNoSettleBalanceRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.balancelog.CreateSettlementBalanceRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.eventlog.PlatFormCancelToSettleRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.eventlog.RefundToSettleRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.eventlog.AllRefundEventRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.eventlog.ClearingRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.eventlog.NoRefundSettledRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.eventlog.PartOfRefundRecordAction;
import com.puree.hospital.business.channel.commission.service.state.impl.snaphot.UpdateSnapshotPayTimeAction;
import com.puree.hospital.business.channel.commission.service.state.impl.summary.UpdateChannelCommissionSummaryAction;
import com.puree.hospital.business.channel.commission.service.state.impl.VerifyCurrentOrderCommissionStatusAction;
import com.puree.hospital.business.channel.commission.service.state.impl.VerifyOrderAfterSaleFinishAction;
import com.puree.hospital.business.channel.commission.service.state.impl.VerifyOrderStatusCancelAction;
import com.puree.hospital.business.channel.commission.service.state.impl.VerifyOrderStatusFinishAction;
import com.puree.hospital.business.channel.commission.service.state.impl.VerifyPaymentSuccessAction;
import com.puree.hospital.business.channel.commission.enums.ChannelOrderCommissionStateEnum;
import com.puree.hospital.business.channel.commission.service.state.impl.VerifyRefundSuccessAction;
import com.puree.hospital.business.channel.framework.StateMachineBuilder;
import com.puree.hospital.business.channel.framework.StateMachine;
import com.puree.hospital.business.channel.commission.enums.ChannelOrderCommissionEventEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ChannelOrderCommissionStateMachineConfig {

    /**
     * 渠道订单结算状态机配置
     * 定义状态机的初始状态、事件转换和对应的动作
     * @return 状态机实例
     */
    @Bean
    public StateMachine<ChannelOrderCommissionStateEnum, ChannelOrderCommissionEventEnum, ChannelOrderContext> channelOrderStateMachine() {
        // 将所有 Action 转换为 Map，便于通过名称查找
        return StateMachineBuilder
                .<ChannelOrderCommissionStateEnum, ChannelOrderCommissionEventEnum, ChannelOrderContext>create()
                // 支付：CREATED → CLEARING
                .onEvent(ChannelOrderCommissionEventEnum.PAYMENT)
                    .from(ChannelOrderCommissionStateEnum.CREATED)
                    .to(ChannelOrderCommissionStateEnum.CLEARING)
                    // 校验支付成功动作
                    .action(VerifyPaymentSuccessAction.class)
                    // 校验渠道订单状态
                    .action(VerifyCurrentOrderCommissionStatusAction.class)
                    // 更新快照的支付时间
                    .action(UpdateSnapshotPayTimeAction.class)
                    // 创建渠道转待结算明细记录
                    .action(ClearingRecordAction.class)
                    // 创建渠道身份佣金变动 - 产生待结算记录
                    .action(CreateClearingBalanceRecordAction.class)
                    // 更新渠道佣金汇总数据
                    .action(UpdateChannelCommissionSummaryAction.class)
                // 渠道订单状态转为清算状态 - 外层
                .end()

                // 取消订单：CREATED → CANCELED
                .onEvent(ChannelOrderCommissionEventEnum.CANCEL)
                    .from(ChannelOrderCommissionStateEnum.CREATED)
                    .to(ChannelOrderCommissionStateEnum.CANCELED)
                    .action(VerifyCurrentOrderCommissionStatusAction.class)
                // 渠道订单状态转为取消状态 - 外层
                .end()

                // 结算完成(后台取消订单全退款)：CLEARING → SETTLED
                // 全退款 - 无需变动账户余额-但是需要新增变更记录-变更金额都为0
                .onEvent(ChannelOrderCommissionEventEnum.NO_AFTER_SALE_ALL_REFUND_SETTLED)
                    .from(ChannelOrderCommissionStateEnum.CLEARING)
                    .to(ChannelOrderCommissionStateEnum.SETTLED)
                    // 校验订单已经退款
                    .action(VerifyRefundSuccessAction.class)
                    // 校验订单状态是已取消
                    .action(VerifyOrderStatusCancelAction.class)
                    .action(VerifyCurrentOrderCommissionStatusAction.class)
                    // 新增退款记录，全额退款
                    .action(AllRefundEventRecordAction.class)
                    // 新增变更记录
                    .action(CreateSettlementBalanceRecordAction.class)
                    // 更新渠道佣金汇总数据 - 待结算做扣减
                    .action(UpdateChannelCommissionSummaryAction.class)
                // 渠道订单状态转为结算状态 - 外层
                .end()

                // 结算完成(无退款-结算)
                .onEvent(ChannelOrderCommissionEventEnum.NO_REFUND_SETTLED)
                    .from(ChannelOrderCommissionStateEnum.CLEARING)
                    .to(ChannelOrderCommissionStateEnum.SETTLED)
                    // 校验订单状态，售后状态都是完结态
                    .action(VerifyOrderStatusFinishAction.class)
                    .action(VerifyOrderAfterSaleFinishAction.class)
                    .action(VerifyCurrentOrderCommissionStatusAction.class)
                    // 新增退款记录，全额退款
                    // 创建渠道转结算明细记录
                    .action(NoRefundSettledRecordAction.class)
                    // 新增变更记录
                    .action(CreateSettlementBalanceRecordAction.class)
                    // 更新渠道佣金汇总数据 - 待结算做扣减
                    .action(UpdateChannelCommissionSummaryAction.class)
                // 渠道订单状态转为结算状态 - 外层
                .end()

                // 未结算 - 订单部分退款同时订单状态未完结
                .onEvent(ChannelOrderCommissionEventEnum.PART_REFUND_NOT_SETTLED)
                    .from(ChannelOrderCommissionStateEnum.CLEARING)
                    .to(ChannelOrderCommissionStateEnum.CLEARING)
                    // 校验售后状态都是完结态
                    .action(VerifyRefundSuccessAction.class)
                    .action(VerifyCurrentOrderCommissionStatusAction.class)
                    .action(VerifyOrderAfterSaleHasEndAction.class)
                    // 新增退款记录，部分退款 - 只新增待结算的退款部分
                    // 创建渠道待结算结算退款明细记录
                    .action(PartOfRefundRecordAction.class)
                    // 账户待结算变更记录
                    .action(CreatePartOfRefundNoSettleBalanceRecordAction.class)
                    // 更新渠道佣金汇总数据 - 待结算做扣减
                    .action(UpdateChannelCommissionSummaryAction.class)
                // 渠道订单状态转为退款状态 - 外层
                .end()

                // 结算完成(订单部分退款后订单完成-结算)
                .onEvent(ChannelOrderCommissionEventEnum.FINISHED_SETTLED)
                    .from(ChannelOrderCommissionStateEnum.CLEARING)
                    .to(ChannelOrderCommissionStateEnum.SETTLED)
                    // 校验订单状态，售后状态都是完结态
                    .action(VerifyRefundSuccessAction.class)
                    .action(VerifyOrderAfterSaleFinishAction.class)
                    .action(VerifyOrderStatusFinishAction.class)
                    .action(VerifyCurrentOrderCommissionStatusAction.class)
                    // 新增退款记录，全额退款
                    // 创建渠道转结算明细记录
                    .action(RefundToSettleRecordAction.CLASS_NAME)
                    // 新增变更记录
                    .action(CreateSettlementBalanceRecordAction.class)
                    // 更新渠道佣金汇总数据 - 待结算做扣减
                    .action(UpdateChannelCommissionSummaryAction.class)
                // 渠道订单状态转为结算状态 - 外层
                .end()

                // 结算完成(订单部分退款后全额退款（都走售后流程）-结算)
                .onEvent(ChannelOrderCommissionEventEnum.AFTER_REFUND_CANCEL_SETTLED)
                    .from(ChannelOrderCommissionStateEnum.CLEARING)
                    .to(ChannelOrderCommissionStateEnum.SETTLED)
                    // 校验订单状态，售后状态都是完结态
                    .action(VerifyRefundSuccessAction.class)
                    .action(VerifyOrderAfterSaleFinishAction.class)
                    .action(VerifyOrderStatusFinishAction.class)
                    .action(VerifyCurrentOrderCommissionStatusAction.class)
                    // 新增退款记录，全额退款
                    // 创建渠道转结算明细记录
                    .action(PlatFormCancelToSettleRecordAction.CLASS_NAME)
                    // 新增变更记录
                    .action(CreateSettlementBalanceRecordAction.class)
                    // 更新渠道佣金汇总数据 - 待结算做扣减
                    .action(UpdateChannelCommissionSummaryAction.class)
                // 渠道订单状态转为结算状态 - 外层
                .end()

                // 结算完成(订单部分退款后-全额退款（最后一次全额退无售后）-结算)
                .onEvent(ChannelOrderCommissionEventEnum.AFTER_REFUND_PLATFORM_CANCEL_SETTLED)
                    .from(ChannelOrderCommissionStateEnum.CLEARING)
                    .to(ChannelOrderCommissionStateEnum.SETTLED)
                    // 校验订单状态，售后状态都是完结态
                    .action(VerifyOrderAfterSaleFinishAction.class)
                    .action(VerifyOrderStatusFinishAction.class)
                    .action(VerifyCurrentOrderCommissionStatusAction.class)
                    // 新增退款记录，全额退款
                    // 创建渠道转结算明细记录
                    .action(PlatFormCancelToSettleRecordAction.class)
                    // 新增变更记录
                    .action(CreateSettlementBalanceRecordAction.class)
                    // 更新渠道佣金汇总数据 - 待结算做扣减
                    .action(UpdateChannelCommissionSummaryAction.class)
                // 渠道订单状态转为结算状态 - 外层
                .end()
                .build();
    }




}
package com.puree.hospital.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO;
import com.puree.hospital.business.domain.BusDirectoryDrugs;
import com.puree.hospital.business.domain.BusHospitalOfficina;
import com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo;
import com.puree.hospital.business.importDrugs.domain.ImportDrugTaskParamDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusHospitalOfficinaMapper extends BaseMapper<BusHospitalOfficina> {
    /**
     * 查询未关联药品列表
     *
     * @param dto
     * @return
     */
    List<BusDirectoryDrugsVo> selectListNotAssociatedDrugs(BusDirectoryDrugsDTO dto);

    /**
     * 新增药房关联药品
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<BusHospitalOfficina> list);


    /**
     * 新增药房关联药品
     *
     * @param list
     * @return
     */
    int batchInsertMultiple(@Param("list") List<BusHospitalOfficina> list);

    /**
     * 查询已关联药品列表
     *
     * @param dto
     * @return
     */
    List<BusDirectoryDrugsVo> selectListAssociatedDrugs(BusDirectoryDrugsDTO dto);


    /**
     *查询已关联中药药品列表
     *
     * @param dto
     * @return
     */
    List<BusDirectoryDrugsVo> tcmAssociatedDrugs(BusDirectoryDrugsDTO dto);

    /**
     * 价格，库存，配送企业设置
     * @param hospitalOfficina
     * @return
     */
    int updateHospitalOfficina(BusHospitalOfficina hospitalOfficina);

    /**
     * 置空药品配送企业
     *
     * @param hospitalId 医院id
     * @param enterpriseId 配送企业id
     * @return 更新结果
     */
    int updateEntIsNull(@Param(value = "hospitalId") Long hospitalId,@Param(value = "enterpriseId") Long enterpriseId);

    /**
     * 查询药品药品库存
     *
     * @param hospitalId 医院id
     * @return 药房药品列表
     */
    List<BusHospitalOfficina> selectDrugsStock(Long hospitalId);

    int updateHospitalOfficinaInfo(BusHospitalOfficina req) ;

    /**
     * 更新销售价
     *
     * @param req 更新条件
     * @return
     */
    int updateEntity(BusHospitalOfficina req) ;

    /**
     * 批量更新药房药品价格  注意：有sql注入的风险
     *
     * @param hospitalOfficinaList 药房药品列表
     * @return
     */
    @Deprecated
    int updateSellingPriceBatch(@Param(value = "collect") List<BusHospitalOfficina> hospitalOfficinaList);

    /**
     * 批量插入药房
     *
     * @param paramDTO 药品导入参数DTO
     */
    void drugsBindingHospitalPharmaciesBatchByDirectory(ImportDrugTaskParamDTO paramDTO);

    void updateBatchStatus(@Param(value = "hospitalId") Long hospitalId, @Param(value = "status") Integer status);
}

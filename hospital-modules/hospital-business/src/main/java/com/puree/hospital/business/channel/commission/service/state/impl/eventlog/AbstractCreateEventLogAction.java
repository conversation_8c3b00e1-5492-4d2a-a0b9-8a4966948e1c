package com.puree.hospital.business.channel.commission.service.state.impl.eventlog;

import com.puree.hospital.business.channel.commission.domain.BusChannelOrderDetailSettlementEventLog;
import com.puree.hospital.business.channel.commission.domain.bo.OrderSnapshotBO;
import com.puree.hospital.business.channel.commission.mapper.BusChannelOrderDetailSettlementEventLogMapper;
import com.puree.hospital.business.channel.commission.service.state.ChannelOrderAction;
import com.puree.hospital.business.channel.commission.service.state.ChannelOrderContext;
import com.puree.hospital.business.domain.BusOrderAfterSales;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractCreateEventLogAction implements ChannelOrderAction<ChannelOrderContext> {
    protected final BusChannelOrderDetailSettlementEventLogMapper busChannelOrderDetailSettlementEventLogMapper;

    protected AbstractCreateEventLogAction(BusChannelOrderDetailSettlementEventLogMapper busChannelOrderDetailSettlementEventLogMapper){
        this.busChannelOrderDetailSettlementEventLogMapper = busChannelOrderDetailSettlementEventLogMapper;
    }

    /**
     * 获取待结算记录
     * @param eventLog 事件日志
     * @param snapshot 订单快照对象
     * @param afterSalesMap      售后单Map
     * @return 待结算记录
     */
    protected abstract BusChannelOrderDetailSettlementEventLog getClearingRecord(BusChannelOrderDetailSettlementEventLog eventLog, OrderSnapshotBO snapshot, Map<Long, BusOrderAfterSales> afterSalesMap);

    /**
     * 获取结算记录
     * @param eventLog 事件日志
     * @param snapshot 订单快照对象
     * @param afterSalesMap 售后单Map
     * @return 结算记录
     */
    protected abstract BusChannelOrderDetailSettlementEventLog getSettlementEventLog(BusChannelOrderDetailSettlementEventLog eventLog, OrderSnapshotBO snapshot, Map<Long, BusOrderAfterSales> afterSalesMap);

    /**
     * 设置上下文金额
     * @param context 渠道订单上下文对象
     * @param settledEventLogs 结算记录
     * @param unsettledEventLogs 待结算记录
     */
    protected abstract void setContextAmount(ChannelOrderContext context, List<BusChannelOrderDetailSettlementEventLog> unsettledEventLogs, List<BusChannelOrderDetailSettlementEventLog> settledEventLogs);

    /**
     * 冲正交易（结算扣减待结算明细需要） - 设置到unsettledEventLogs中，如果没有则默认不设置冲正交易
     * @param context 渠道订单上下文对象
     * @param unsettledEventLogs 待结算记录
     */
    protected void adjustUnsettledLogs(ChannelOrderContext context, List<BusChannelOrderDetailSettlementEventLog> unsettledEventLogs) {}
    /**
     * 执行动作
     *
     * @param context 渠道订单上下文对象
     */
    @Override
    public void execute(ChannelOrderContext context) {
        List<OrderSnapshotBO> orderSnapshotBOS = checkSnapshot(context);
        List<BusOrderAfterSales> afterSalesList = context.getAfterSalesList();
        Map<Long, BusOrderAfterSales> afterSalesMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(afterSalesList)) {
            // 只做有退款记录的售后订单
            afterSalesMap = afterSalesList.stream().filter(a -> OrderAfterSalesStatusEnum.isEnd(a.getAfterSalesStatus())).collect(Collectors.toMap(BusOrderAfterSales::getGoodsId, Function.identity()));
        }
        List<BusChannelOrderDetailSettlementEventLog> settledEventLogs = new ArrayList<>();
        List<BusChannelOrderDetailSettlementEventLog> unsettledEventLogs = new ArrayList<>();
        for (OrderSnapshotBO orderSnapshotBO : orderSnapshotBOS) {
            BusChannelOrderDetailSettlementEventLog eventLog = getBaseEventLogByContext(context, orderSnapshotBO);
            BusChannelOrderDetailSettlementEventLog clearingRecord = getClearingRecord(eventLog, orderSnapshotBO, afterSalesMap);
            if (Objects.nonNull(clearingRecord)) {
                unsettledEventLogs.add(clearingRecord);
                int count = busChannelOrderDetailSettlementEventLogMapper.insert(clearingRecord);
                if (count <= 0) {
                    log.error("插入渠道订单明细待结算事件日志失败,事件{}，消费者{}，订单号{}", context.getEvent(), context.getOriginalQueue(), context.getOrderNo());
                    throw new RuntimeException("插入渠道订单明细待结算事件日志失败");
                }
            }
            BusChannelOrderDetailSettlementEventLog settlementEventLog = getSettlementEventLog(eventLog, orderSnapshotBO, afterSalesMap);
            if (Objects.isNull(settlementEventLog)) {
                // 不需要新增结算记录
                continue;
            }
            settledEventLogs.add(settlementEventLog);
            int count2 = busChannelOrderDetailSettlementEventLogMapper.insert(settlementEventLog);
            if (count2 <= 0) {
                log.error("插入渠道订单明细结算事件日志失败,事件{}，消费者{}，订单号{}", context.getEvent(), context.getOriginalQueue(), context.getOrderNo());
                throw new RuntimeException("插入渠道订单明细结算事件日志失败");
            }
        }
        // 提供冲正交易（结算扣减待结算明细需要） - 设置到unsettledEventLogs中，如果没有则默认不设置冲正交易
        adjustUnsettledLogs(context, unsettledEventLogs);
        setContextAmount(context, unsettledEventLogs, settledEventLogs);
    }



    protected List<OrderSnapshotBO> checkSnapshot(ChannelOrderContext context) {
        List<OrderSnapshotBO> orderSnapshot = context.getOrderSnapshot();
        if (CollectionUtils.isEmpty(orderSnapshot)) {
            log.error("订单快照为空,事件{}，消费者{}，订单号{}", context.getEvent(), context.getOriginalQueue(), context.getOrderNo());
            throw new RuntimeException("订单快照查找不到");
        }
        return orderSnapshot;
    }



    /**
     * 获取渠道订单明细结算事件日志
     *
     * @param context 渠道订单上下文对象
     * @param snapshot 订单快照对象
     * @return 渠道订单明细结算事件日志
     */
    protected @NotNull BusChannelOrderDetailSettlementEventLog getBaseEventLogByContext(ChannelOrderContext context, OrderSnapshotBO snapshot) {
        BusChannelOrderDetailSettlementEventLog eventLog = new BusChannelOrderDetailSettlementEventLog();
        eventLog.setHospitalId(context.getHospitalId());
        eventLog.setEvenId(context.getEventId());
        eventLog.setChannelOrderId(snapshot.getChannelOrderId());
        eventLog.setChannelOrderDetailId(snapshot.getId());
        eventLog.setPaymentTime(context.getPaySuccessTime());
        eventLog.setEvenType(context.getEvent().name());
        eventLog.setRemark(context.getOriginalQueue());
        eventLog.setCreateTime(new Date());
        return eventLog;
    }



}

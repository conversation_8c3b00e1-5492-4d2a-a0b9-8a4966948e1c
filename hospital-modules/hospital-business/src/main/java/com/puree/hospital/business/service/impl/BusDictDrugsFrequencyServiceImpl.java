package com.puree.hospital.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.business.domain.BusDictDrugsFrequency;
import com.puree.hospital.business.mapper.BusDictDrugsFrequencyMapper;
import com.puree.hospital.business.service.IBusDictDrugsFrequencyService;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class BusDictDrugsFrequencyServiceImpl implements IBusDictDrugsFrequencyService {
    private final BusDictDrugsFrequencyMapper busDictDrugsFrequencyMapper;
    private final RemoteHospitalSettingApi remoteHospitalSettingApi;

    @Autowired
    public BusDictDrugsFrequencyServiceImpl(BusDictDrugsFrequencyMapper busDictDrugsFrequencyMapper, RemoteHospitalSettingApi remoteHospitalSettingApi) {
        this.busDictDrugsFrequencyMapper = busDictDrugsFrequencyMapper;
        this.remoteHospitalSettingApi = remoteHospitalSettingApi;
    }

    @Override
    public List<BusDictDrugsFrequency> selectList(BusDictDrugsFrequency drugsFrequency) {
        QueryWrapper<BusDictDrugsFrequency> queryWrapper = new QueryWrapper<>();
        return busDictDrugsFrequencyMapper.selectList(queryWrapper);
    }

    /**
     *  从配置项中获取用药频率
     * @return  用药频率
     */
    @Override
    public List<BusDictDrugsFrequency> selectListFromConfigurationItem(BusDictDrugsFrequency drugsFrequency) {
        Long hospitalId = SecurityUtils.getHospitalId();
        R<String> settingValue = remoteHospitalSettingApi.getSettingValue("drug-manager.data-dict.wm-drug-frequency", hospitalId);
        if (!settingValue.isSuccess() || StrUtil.isBlank(settingValue.getData())) {
            return Collections.emptyList();
        }
        return JSON.parseArray(settingValue.getData(), BusDictDrugsFrequency.class);
    }
}

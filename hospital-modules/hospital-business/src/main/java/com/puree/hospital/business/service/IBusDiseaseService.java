package com.puree.hospital.business.service;

import com.puree.hospital.business.api.model.DiagnosisVO;
import com.puree.hospital.business.api.model.dto.DiagnosisDTO;
import com.puree.hospital.business.domain.BusDisease;

import java.util.List;

/**
 * 西医病种信息Service接口
 * 
 * <AUTHOR>
 * @date 2021-10-15
 */
public interface IBusDiseaseService {
    /**
     * 查询病种信息
     *
     * @param id
     * @return
     */
    BusDisease selectBusDiseaseById(Long id);

    /**
     * 查询病种信息列表
     *
     * @param busDisease 病种信息
     * @return 病种信息集合
     */
    List<BusDisease> selectBusDiseaseList(BusDisease busDisease);

    /**
     * 新增病种信息
     *
     * @param busDisease 病种信息
     * @return 结果
     */
    int insertBusDisease(BusDisease busDisease);

    /**
     * 修改病种信息
     *
     * @param busDisease 病种信息
     * @return 结果
     */
    int updateBusDisease(BusDisease busDisease);

    /**
     * 批量删除病种信息
     *
     * @param ids 需要删除的病种信息ID
     * @return 结果
     */
    int deleteBusDiseaseByIds(Long[] ids);

    /**
     * 校验病种名称是否重复
     * @param id
     * @param diseaseName
     * @return
     */
    boolean checkDuplicateName(Long id, String diseaseName);

    /**
     * 校验ICD-10编码是否重复
     * @param id
     * @param icdCode
     * @return
     */
    boolean checkDuplicateIcd(Long id, String icdCode);

    /**
     * 西医病种启用停用操作
     * @param busDisease
     * @return
     */
    int lock(BusDisease busDisease);

    /**
     * 查询标准病种信息
     * @param icdCodes
     * @return
     */
    List<BusDisease> selectDiseaseList(List<String> icdCodes);

    /**
     *  查询不支持医保支付的诊断列表
     * @param diagnosisDTO  入参
     * @return  不支持医保支付的诊断列表
     */
    List<DiagnosisVO> getDiagnosisListByIds(DiagnosisDTO diagnosisDTO);

    /**
     *  根据Ids查询病种
     * @param diseaseIds 病种id
     * @return  病种列表
     */
    List<BusDisease> queryDiseaseByIds(List<Long> diseaseIds);
}

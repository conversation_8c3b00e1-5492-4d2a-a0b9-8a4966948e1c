package com.puree.hospital.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.puree.hospital.business.domain.BusBizDepartment;
import com.puree.hospital.business.domain.BusCommunicationMessage;
import com.puree.hospital.business.domain.BusConsultationOrder;
import com.puree.hospital.business.domain.BusConsultationSettings;
import com.puree.hospital.business.domain.BusDoctor;
import com.puree.hospital.business.domain.BusDoctorAudit;
import com.puree.hospital.business.domain.BusDoctorConsultation;
import com.puree.hospital.business.domain.BusDoctorDepartment;
import com.puree.hospital.business.domain.BusDoctorHospital;
import com.puree.hospital.business.domain.BusDoctorPatientGroup;
import com.puree.hospital.business.domain.BusDoctorPushSetup;
import com.puree.hospital.business.domain.BusDoctorScheduling;
import com.puree.hospital.business.domain.BusFiveAssistantDoctor;
import com.puree.hospital.business.domain.BusFiveGroupMember;
import com.puree.hospital.business.domain.BusFivePhysician;
import com.puree.hospital.business.domain.BusFiveServicePack;
import com.puree.hospital.business.domain.BusFiveServicePackOrder;
import com.puree.hospital.business.domain.BusHospital;
import com.puree.hospital.business.domain.BusHospitalPreorderDoctor;
import com.puree.hospital.business.domain.BusImGroupMember;
import com.puree.hospital.business.domain.BusOfficinaPharmacist;
import com.puree.hospital.business.domain.BusPreorderDoctor;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusSignature;
import com.puree.hospital.business.domain.BusTutorial;
import com.puree.hospital.business.domain.dto.BusDoctorDto;
import com.puree.hospital.business.domain.dto.BusDoctorRelDTO;
import com.puree.hospital.business.domain.dto.BusDoctorSchedulingDto;
import com.puree.hospital.business.domain.dto.BusPopularColumnDto;
import com.puree.hospital.business.domain.dto.DoctorDepartmentDTO;
import com.puree.hospital.business.domain.dto.DoctorPatientGroupDTO;
import com.puree.hospital.business.domain.dto.VerifyDoctorDTO;
import com.puree.hospital.business.domain.vo.BusDoctorDepartmentVo;
import com.puree.hospital.business.domain.vo.BusDoctorVo;
import com.puree.hospital.business.domain.vo.BusFiveGroupMemberVO;
import com.puree.hospital.business.domain.vo.BusFiveWorkGroupVO;
import com.puree.hospital.business.domain.vo.BusPreorderDoctorVo;
import com.puree.hospital.business.domain.vo.BusRecommendDoctorVo;
import com.puree.hospital.business.domain.vo.IsExistsPatientVO;
import com.puree.hospital.business.mapper.BusCommunicationMessageMapper;
import com.puree.hospital.business.mapper.BusConsultationOrderMapper;
import com.puree.hospital.business.mapper.BusConsultationSettingsMapper;
import com.puree.hospital.business.mapper.BusDoctorAuditMapper;
import com.puree.hospital.business.mapper.BusDoctorConsultationMapper;
import com.puree.hospital.business.mapper.BusDoctorDepartmentMapper;
import com.puree.hospital.business.mapper.BusDoctorHospitalMapper;
import com.puree.hospital.business.mapper.BusDoctorMapper;
import com.puree.hospital.business.mapper.BusDoctorPatientGroupMapper;
import com.puree.hospital.business.mapper.BusDoctorPushSetupMapper;
import com.puree.hospital.business.mapper.BusDoctorSchedulingMapper;
import com.puree.hospital.business.mapper.BusFiveAssistantDoctorMapper;
import com.puree.hospital.business.mapper.BusFiveGroupMemberMapper;
import com.puree.hospital.business.mapper.BusFivePhysicianMapper;
import com.puree.hospital.business.mapper.BusFiveServicePackMapper;
import com.puree.hospital.business.mapper.BusFiveServicePackOrderMapper;
import com.puree.hospital.business.mapper.BusHospitalMapper;
import com.puree.hospital.business.mapper.BusHospitalPreorderDoctorMapper;
import com.puree.hospital.business.mapper.BusImGroupMemberMapper;
import com.puree.hospital.business.mapper.BusOfficinaPharmacistMapper;
import com.puree.hospital.business.mapper.BusPreorderDoctorMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.mapper.BusSignatureMapper;
import com.puree.hospital.business.mapper.BusTutorialMapper;
import com.puree.hospital.business.service.IBusBizDepartmentService;
import com.puree.hospital.business.service.IBusConsultationOrderService;
import com.puree.hospital.business.service.IBusDoctorDepartmentService;
import com.puree.hospital.business.service.IBusDoctorPatientGroupService;
import com.puree.hospital.business.service.IBusDoctorSchedulingService;
import com.puree.hospital.business.service.IBusDoctorService;
import com.puree.hospital.business.service.IBusFiveGroupMemberService;
import com.puree.hospital.business.service.IBusFiveWorkGroupService;
import com.puree.hospital.business.service.IBusImGroupMemberService;
import com.puree.hospital.business.service.IBusPreorderDoctorService;
import com.puree.hospital.business.service.IBusRecommendDoctorService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AuditStatus;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ConsultationEnum;
import com.puree.hospital.common.core.enums.DoctorAuthStatusEnum;
import com.puree.hospital.common.core.enums.DoctorPracticeEnum;
import com.puree.hospital.common.core.enums.DoctorPushTypeEnum;
import com.puree.hospital.common.core.enums.FiveRoleEnum;
import com.puree.hospital.common.core.enums.ImGroupType;
import com.puree.hospital.common.core.enums.ImMemberEnum;
import com.puree.hospital.common.core.enums.ServicePackOrderStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.exception.base.BaseException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.core.utils.file.ImageUtils;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.five.api.RemoteServicePackOrderService;
import com.puree.hospital.five.api.RemoteWorkGroupService;
import com.puree.hospital.five.api.model.ServicePackOrderRequest;
import com.puree.hospital.five.api.model.ServicePackOrderResponse;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.im.api.model.ImGroupRequest;
import com.puree.hospital.im.api.model.ImPortraitRequest;
import com.puree.hospital.im.api.model.Member;
import com.puree.hospital.im.api.model.ProfileItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
@ConfigurationProperties
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorServiceImpl implements IBusDoctorService {
    private final BusDoctorMapper busDoctorMapper;
    private final BusDoctorConsultationMapper busDoctorConsultationMapper;
    private final BusDoctorDepartmentMapper busDoctorDepartmentMapper;
    private final BusDoctorHospitalMapper busDoctorHospitalMapper;
    private final BusDoctorPushSetupMapper busDoctorPushSetupMapper;
    private final BusDoctorAuditMapper busDoctorAuditMapper;
    private final BusConsultationSettingsMapper busConsultationSettingsMapper;
    private final RemoteImService remoteImService;
    private final IBusConsultationOrderService busConsultationOrderService;
    private final IBusBizDepartmentService busBizDepartmentService;
    private final IBusDoctorDepartmentService busDoctorDepartmentService;
    private final BusHospitalMapper busHospitalMapper;
    private final BusFiveServicePackOrderMapper busFiveServicePackOrderMapper;
    private final BusFiveServicePackMapper busFiveServicePackMapper;
    private final BusFiveGroupMemberMapper busFiveGroupMemberMapper;
    private final RemoteWorkGroupService remoteWorkGroupService;
    private final RemoteServicePackOrderService remoteServicePackOrderService;
    private final BusDoctorSchedulingMapper busDoctorSchedulingMapper;
    private final BusSignatureMapper busSignatureMapper;
    private final BusPreorderDoctorMapper busPreorderDoctorMapper;
    private final IBusPreorderDoctorService busPreorderDoctorService;
    private final IBusRecommendDoctorService busRecommendDoctorService;
    private final IBusDoctorSchedulingService busDoctorSchedulingService;
    private final BusConsultationOrderMapper consultationOrderMapper;
    private final IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final BusHospitalPreorderDoctorMapper hospitalPreorderDoctorMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final IBusFiveWorkGroupService busFiveWorkGroupService;
    private final IBusFiveGroupMemberService busFiveGroupMemberService;
    private final BusOfficinaPharmacistMapper busOfficinaPharmacistMapper;

    private final BusFivePhysicianMapper busFivePhysicianMapper;

    private final BusImGroupMemberMapper busImGroupMemberMapper;
    private final OSSUtil ossUtil;

    private final IBusImGroupMemberService busImGroupMemberService;
    private final BusCommunicationMessageMapper busCommunicationMessageMapper;
    private final BusDoctorPatientGroupMapper busDoctorPatientGroupMapper;
    private final BusFiveAssistantDoctorMapper assistantDoctorMapper;

    private final BusTutorialMapper tutorialMapper;
    @Value("${operate-platform.admin.mask-phone-number}")
    private boolean isMaskPhoneEnable;

    /**
     * 医生数据迁移
     */
//    @PostConstruct
//    @Transactional(rollbackFor = Exception.class)
    public void init() {
        //药师
        //只迁移审方药师
        List<BusOfficinaPharmacist> officinaPharmacists = busOfficinaPharmacistMapper.selectList(new LambdaQueryWrapper<BusOfficinaPharmacist>().eq(BusOfficinaPharmacist::getPharmacistCategory, CodeEnum.YES.getCode()));
        for (BusOfficinaPharmacist e : officinaPharmacists) {
            BusDoctor busDoctor = new BusDoctor();
            busDoctor.setPhoneNumber(e.getPhoneNumber());
            busDoctor.setIdCardNo(e.getIdNumber());
            busDoctor.setDoctorNumber(String.valueOf(System.currentTimeMillis()));
            busDoctor.setCreateBy("admin");
            busDoctor.setCreateTime(DateUtils.getNowDate());
            busDoctor.setFullName(e.getPharmacistName());
            busDoctor.setPhoto(e.getPharmacistAvatar());
            busDoctor.setIdCardNoImg(e.getPharmacistIdCard());
            busDoctor.setSex(Integer.valueOf(e.getPharmacistSex()));
            busDoctor.setQualificationCertificate(e.getPharmacistQualificationCertificate());
            busDoctor.setDescriptionDetails(e.getPharmacistIntroduction());
            busDoctorMapper.insert(busDoctor);
            BusDoctorHospital busDoctorHospital = new BusDoctorHospital();
            busDoctorHospital.setHospitalId(e.getHospitalId());
            busDoctorHospital.setDoctorId(busDoctor.getId());
            busDoctorHospital.setRole("1");
            busDoctorHospital.setIsThisCourt(DoctorPracticeEnum.OUR_HOSPITAL.getCode());
            busDoctorHospital.setIsAuthentication(3);
            busDoctorHospital.setHot("0");
            busDoctorHospital.setStatus(e.getStatus());
            busDoctorHospital.setReviewContent(e.getReviewContent());
            busDoctorHospital.setCreateTime(DateUtils.getNowDate());
            busDoctorHospitalMapper.insert(busDoctorHospital);
            //如果不存在问诊设置插入默认设置
            QueryWrapper<BusConsultationSettings> settingsQueryWrapper = new QueryWrapper<>();
            settingsQueryWrapper.eq("hospital_id", e.getHospitalId());
            BusConsultationSettings settings = busConsultationSettingsMapper.selectOne(settingsQueryWrapper);
            BusDoctorConsultation consultation = new BusDoctorConsultation();
            List<BusDoctorConsultation> consultationDefaultSetUp = consultation.getDefaultSetUp(busDoctor.getId()
                    , e.getHospitalId(), settings);
            consultationDefaultSetUp.forEach(c -> {
                c.setStatus(YesNoEnum.YES.getCode());
                c.setCreateTime(DateUtils.getNowDate());
                busDoctorConsultationMapper.insert(c);
            });
            //插入推送设置
            BusDoctorPushSetup busDoctorPushSetup = new BusDoctorPushSetup();
            busDoctorPushSetup.setPusherId(busDoctor.getId());
            busDoctorPushSetup.setIsPush(YesNoEnum.YES.getCode());
            busDoctorPushSetup.setPushType(2);//全部推送
            busDoctorPushSetup.setType("0");
            busDoctorPushSetup.setCreateBy("admin");
            busDoctorPushSetup.setCreateTime(DateUtils.getNowDate());
            busDoctorPushSetupMapper.insert(busDoctorPushSetup);
            //导入im账号
            R<Boolean> returnData = remoteImService.accountCheck(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
            if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) returnData.getData()) {
                remoteImService.accountImport(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId(),
                        busDoctor.getFullName(), busDoctor.getPhoto());
            } else {
                // 防止im已有账号更新昵称
                ImPortraitRequest imPortraitRequest = new ImPortraitRequest();
                imPortraitRequest.setFrom_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
                List<ProfileItem> profileItemList = new ArrayList<>();
                ProfileItem profileItem = new ProfileItem();
                profileItem.setTag("Tag_Profile_IM_Nick");
                profileItem.setValue(busDoctor.getFullName());
                profileItemList.add(profileItem);
                imPortraitRequest.setProfileItemList(profileItemList);
                remoteImService.portraitSet(imPortraitRequest);
            }
            //签名 0发货药师 1审方药师
            String objectType = "";
            BusSignature signature = new BusSignature();
            if ("0".equals(e.getPharmacistCategory())) {
                objectType = "6";
            } else if ("1".equals(e.getPharmacistCategory())) {
                objectType = "1";
            } else {
                objectType = "7";
            }
            BusSignature busSignature = busSignatureMapper.selectOne(new LambdaQueryWrapper<BusSignature>()
                    .eq(BusSignature::getObjectType, objectType)
                    .eq(BusSignature::getObjectId, e.getId()));
            if (!Objects.isNull(busSignature)) {
                signature.setId(busSignature.getId());
                signature.setObjectId(busDoctor.getId());
                busSignatureMapper.updateById(signature);
            }

            //处方
            BusPrescription updatePrescription = new BusPrescription();
            QueryWrapper<BusPrescription> queryWrapper = new QueryWrapper<>();
            if ("1".equals(e.getPharmacistCategory())) {
                queryWrapper.eq("review_pharmacist_id", e.getId());
                updatePrescription.setReviewPharmacistId(busDoctor.getId());
            }
            busPrescriptionMapper.update(updatePrescription, queryWrapper);
            //工作组人员改动
            BusFiveGroupMember updateFive = new BusFiveGroupMember();
            updateFive.setMemberId(busDoctor.getId());
            busFiveGroupMemberMapper.update(updateFive, new LambdaQueryWrapper<BusFiveGroupMember>()
                    .eq(BusFiveGroupMember::getType, FiveRoleEnum.PSYCHOLOGY.getCode())
                    .eq(BusFiveGroupMember::getMemberId, e.getId()));
            //im群组处理

            List<BusImGroupMember> groupMembers = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                    .eq(BusImGroupMember::getRole, "1")
                    .eq(BusImGroupMember::getPersonnelId, e.getId()));
            for (BusImGroupMember g : groupMembers) {
                remoteImService.exitGroup(g.getGroupId() + "", TencentyunImConstants.PHARMACIST_IM_ACCOUNT + g.getPersonnelId(), "");

                List<Member> assistantMemberList = new ArrayList<>();
                Member member = new Member();
                member.setRole("Member");
                member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
                assistantMemberList.add(member);
                ImGroupRequest groupRequest = new ImGroupRequest();
                groupRequest.setGroupId(g.getGroupId() + "");
                groupRequest.setMemberList(assistantMemberList);
                remoteImService.importGroupMember(groupRequest);
            }

            BusImGroupMember updateIm = new BusImGroupMember();
            updateIm.setPersonnelId(busDoctor.getId());
            busImGroupMemberMapper.update(updateIm, new LambdaQueryWrapper<BusImGroupMember>()
                    .eq(BusImGroupMember::getRole, "1")
                    .eq(BusImGroupMember::getPersonnelId, e.getId()));

            //患教资料更新
            BusTutorial updateTutorial = new BusTutorial();
            updateTutorial.setDoctorId(busDoctor.getId());
            tutorialMapper.update(updateTutorial, new LambdaQueryWrapper<BusTutorial>()
                    .eq(BusTutorial::getSource, "2")
                    .eq(BusTutorial::getPhysicianType, "1")
                    .eq(BusTutorial::getDoctorId, e.getId()));

            //消息处理
            BusCommunicationMessage updateMessage = new BusCommunicationMessage();
            updateMessage.setFrom(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
            updateMessage.setToFrom(TencentyunImConstants.PHARMACIST_IM_ACCOUNT + e.getId());
            busCommunicationMessageMapper.updateMessage(updateMessage);
        }

        //五师
        List<BusFivePhysician> fivePhysicianList = busFivePhysicianMapper.selectList(new LambdaQueryWrapper<BusFivePhysician>()
                .ne(BusFivePhysician::getPhysicianType, "3"));
        for (BusFivePhysician e : fivePhysicianList
        ) {
            BusDoctor busDoctor = new BusDoctor();
            busDoctor.setPhoneNumber(e.getPhoneNumber());
            busDoctor.setIdCardNo(e.getIdCardNo());
            busDoctor.setDoctorNumber(String.valueOf(System.currentTimeMillis()));
            busDoctor.setCreateBy("admin");
            busDoctor.setCreateTime(DateUtils.getNowDate());
            busDoctor.setFullName(e.getFullName());
            busDoctor.setBeGoodAt(e.getBeGoodAt());
            busDoctor.setDescriptionDetails(e.getDescriptionDetails());
            busDoctor.setIntroduce(e.getIntroduce());
            busDoctor.setTitleCertificate(e.getTitleCertificate());
            busDoctor.setQualificationCertificate(e.getPracticeCertificate());
            busDoctor.setPhoto(e.getPhoto());
            busDoctor.setIdCardNoImg(e.getIdCardNoImg());
            busDoctor.setSex(Integer.valueOf(e.getSex()));
            busDoctorMapper.insert(busDoctor);
            BusDoctorHospital busDoctorHospital = new BusDoctorHospital();
            busDoctorHospital.setHospitalId(e.getHospitalId());
            busDoctorHospital.setDoctorId(busDoctor.getId());
            busDoctorHospital.setRole(e.getPhysicianType());
            busDoctorHospital.setIsThisCourt(DoctorPracticeEnum.OUR_HOSPITAL.getCode());
            busDoctorHospital.setIsAuthentication(3);
            busDoctorHospital.setHot("0");
            busDoctorHospital.setCreateTime(DateUtils.getNowDate());
            busDoctorHospital.setStatus(Integer.valueOf(e.getStatus()));
            busDoctorHospitalMapper.insert(busDoctorHospital);

            //如果不存在问诊设置插入默认设置
            QueryWrapper<BusConsultationSettings> settingsQueryWrapper = new QueryWrapper<>();
            settingsQueryWrapper.eq("hospital_id", e.getHospitalId());
            BusConsultationSettings settings = busConsultationSettingsMapper.selectOne(settingsQueryWrapper.last(
                    "limit 1"));
            BusDoctorConsultation consultation = new BusDoctorConsultation();
            List<BusDoctorConsultation> consultationDefaultSetUp = consultation.getDefaultSetUp(busDoctor.getId()
                    , e.getHospitalId(), settings);
            consultationDefaultSetUp.forEach(c -> {
                c.setStatus(YesNoEnum.YES.getCode());
                c.setCreateTime(DateUtils.getNowDate());
                busDoctorConsultationMapper.insert(c);
            });

            BusSignature signature = new BusSignature();
            BusSignature busSignature = busSignatureMapper.selectOne(new LambdaQueryWrapper<BusSignature>()
                    .eq(BusSignature::getObjectType, e.getPhysicianType())
                    .eq(BusSignature::getObjectId, e.getId()));
            if (!Objects.isNull(busSignature)) {
                signature.setId(busSignature.getId());
                signature.setObjectId(busDoctor.getId());
                busSignatureMapper.updateById(signature);
            } else {
                signature.setObjectId(busDoctor.getId());
                signature.setObjectType(e.getPhysicianType());
                signature.setPassword(DESUtil.encrypt("123456"));
                signature.setCreateTime(DateUtils.getNowDate());
                busSignatureMapper.insert(signature);
            }

            //插入推送设置
            BusDoctorPushSetup busDoctorPushSetup = new BusDoctorPushSetup();
            busDoctorPushSetup.setPusherId(busDoctor.getId());
            busDoctorPushSetup.setIsPush(YesNoEnum.YES.getCode());
            busDoctorPushSetup.setPushType(2);//全部推送
            busDoctorPushSetup.setType("0");
            busDoctorPushSetup.setCreateBy("admin");
            busDoctorPushSetup.setCreateTime(DateUtils.getNowDate());
            busDoctorPushSetupMapper.insert(busDoctorPushSetup);
            //导入im账号
            R<Boolean> returnData = remoteImService.accountCheck(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
            if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) returnData.getData()) {
                remoteImService.accountImport(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId(),
                        busDoctor.getFullName(), busDoctor.getPhoto());
            } else {
                // 防止im已有账号更新昵称
                ImPortraitRequest imPortraitRequest = new ImPortraitRequest();
                imPortraitRequest.setFrom_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
                List<ProfileItem> profileItemList = new ArrayList<>();
                ProfileItem profileItem = new ProfileItem();
                profileItem.setTag("Tag_Profile_IM_Nick");
                profileItem.setValue(busDoctor.getFullName());
                profileItemList.add(profileItem);
                imPortraitRequest.setProfileItemList(profileItemList);
                remoteImService.portraitSet(imPortraitRequest);
            }
            //im群组处理 0医生 1药师 2护师 3医助 4健康管理师 5心理咨询师 15患者
            List<BusImGroupMember> groupMembers = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                    .eq(BusImGroupMember::getRole, e.getPhysicianType())
                    .eq(BusImGroupMember::getPersonnelId, e.getId()));

            BusImGroupMember updateIm = new BusImGroupMember();
            updateIm.setPersonnelId(busDoctor.getId());
            busImGroupMemberMapper.update(updateIm, new LambdaQueryWrapper<BusImGroupMember>()
                    .eq(BusImGroupMember::getRole, e.getPhysicianType())
                    .eq(BusImGroupMember::getPersonnelId, e.getId()));
            for (BusImGroupMember g : groupMembers
            ) {
                remoteImService.exitGroup(g.getGroupId() + "", TencentyunImConstants.PHYSICIAN_IM_ACCOUNT + g.getPersonnelId(), "");

                List<Member> assistantMemberList = new ArrayList<>();
                Member member = new Member();
                member.setRole("Member");
                member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
                assistantMemberList.add(member);
                ImGroupRequest groupRequest = new ImGroupRequest();
                groupRequest.setGroupId(g.getGroupId() + "");
                groupRequest.setMemberList(assistantMemberList);
                remoteImService.importGroupMember(groupRequest);
            }
            //工作组人员改动
            BusFiveGroupMember updateFive = new BusFiveGroupMember();
            updateFive.setMemberId(busDoctor.getId());
            busFiveGroupMemberMapper.update(updateFive, new LambdaQueryWrapper<BusFiveGroupMember>()
                    .eq(BusFiveGroupMember::getType, e.getPhysicianType())
                    .eq(BusFiveGroupMember::getMemberId, e.getId()));

            //患教资料更新
            BusTutorial updateTutorial = new BusTutorial();
            updateTutorial.setDoctorId(busDoctor.getId());
            tutorialMapper.update(updateTutorial, new LambdaQueryWrapper<BusTutorial>()
                    .eq(BusTutorial::getSource, "2")
                    .eq(BusTutorial::getPhysicianType, e.getPhysicianType())
                    .eq(BusTutorial::getDoctorId, e.getId()));

            //消息处理
            BusCommunicationMessage updateMessage = new BusCommunicationMessage();
            updateMessage.setFrom(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
            updateMessage.setToFrom(TencentyunImConstants.PHYSICIAN_IM_ACCOUNT + e.getId());
            busCommunicationMessageMapper.updateMessage(updateMessage);
        }

        // 医助数据清除
        List<BusDoctorPatientGroup> patientGroup = busDoctorPatientGroupMapper.selectList(new LambdaQueryWrapper<BusDoctorPatientGroup>()
                .eq(BusDoctorPatientGroup::getType, ImGroupType.ASSISTANT.getCode()));
        for (BusDoctorPatientGroup p : patientGroup) {
            busImGroupMemberMapper.delete(new LambdaQueryWrapper<BusImGroupMember>()
                    .eq(BusImGroupMember::getGroupId, p.getId()));
            busDoctorPatientGroupMapper.deleteById(p.getId());
            remoteImService.destroyGroup(p.getId() + "");
            busCommunicationMessageMapper.delete(new LambdaQueryWrapper<BusCommunicationMessage>()
                    .eq(BusCommunicationMessage::getGroupId, p.getId() + ""));
        }
        // 服务包或1v1群组
        List<BusFivePhysician> assistantList = busFivePhysicianMapper.selectList(new LambdaQueryWrapper<BusFivePhysician>()
                .eq(BusFivePhysician::getPhysicianType, ImMemberEnum.ASSISTANT.getCode()));
        for (BusFivePhysician a : assistantList) {
            List<BusImGroupMember> groupMembers = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                    .eq(BusImGroupMember::getPersonnelId, a.getId())
                    .eq(BusImGroupMember::getRole, ImMemberEnum.ASSISTANT.getCode()));
            List<Long> collect = groupMembers.stream().map(BusImGroupMember::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                busImGroupMemberMapper.deleteBatchIds(collect);
            }
            BusCommunicationMessage delete = new BusCommunicationMessage();
            delete.setFrom(TencentyunImConstants.PHYSICIAN_IM_ACCOUNT + a.getId());
            busCommunicationMessageMapper.deleteMessage(delete);
            // 删除工作组医助
            busFiveGroupMemberMapper.delete(new LambdaQueryWrapper<BusFiveGroupMember>()
                    .eq(BusFiveGroupMember::getMemberId, a.getId())
                    .eq(BusFiveGroupMember::getType, ImMemberEnum.ASSISTANT.getCode()));
            for (BusImGroupMember i : groupMembers) {
                Long groupId = i.getGroupId();
                remoteImService.exitGroup(groupId + "", TencentyunImConstants.PHYSICIAN_IM_ACCOUNT + i.getPersonnelId(), "");
            }
        }
    }

    @Override
    public List<BusDoctorVo> selectList(BusDoctorDto busDoctorDto, Boolean isAdmin) {
        List<BusDoctorVo> doctorVos = busDoctorMapper.selectDoctorList(busDoctorDto);
        //结果为空直接返回
        if (doctorVos == null) {
            log.error("未查询到医生信息,传递参数为{}", busDoctorDto);
            throw new ServiceException("未查询到医生信息");
        }
        doctorVos.forEach(d -> {
            String phoneNumber = DESUtil.decrypt(d.getPhoneNumber());
            d.setIdCardNo(DESUtil.decrypt(d.getIdCardNo()));
            //将此处脱敏作为一个开关，纳入到配置项中
            if (isMaskPhoneEnable) {
                phoneNumber = phoneNumber.replaceAll("(\\d{3})\\d*(\\d{4})", "$1****$2");
            }
            d.setPhoneNumber(phoneNumber);
        });
        if (!doctorVos.isEmpty()) {
            StringBuffer doctorIds = new StringBuffer();
            doctorVos.stream().forEach(d -> {
                doctorIds.append(d.getId() + ",");

            });
            String s = doctorIds.toString();
            String ids = s.substring(0, s.length() - 1);
            String[] split = ids.split(",");
            //医院信息
            QueryWrapper<BusDoctorHospital> doctorHospitalQueryWrapper = new QueryWrapper<>();
            doctorHospitalQueryWrapper.eq("hospital_id", busDoctorDto.getHospitalId());
            doctorHospitalQueryWrapper.in("doctor_id", Arrays.asList(split));
            List<BusDoctorHospital> hospitalList = busDoctorHospitalMapper.selectList(doctorHospitalQueryWrapper);
            //科室信息
            BusDoctorSchedulingDto busDoctorSchedulingDto = new BusDoctorSchedulingDto();
            busDoctorSchedulingDto.setHospitalId(busDoctorDto.getHospitalId());
            busDoctorSchedulingDto.setDoctorIds(ids);
            List<BusDoctorDepartmentVo> departmentList =
                    busDoctorDepartmentMapper.selectAndDepartmentAllNameList(busDoctorSchedulingDto);
            //图文问诊信息
            QueryWrapper<BusDoctorConsultation> consultationQueryWrapper = new QueryWrapper<>();
            consultationQueryWrapper.eq("hospital_id", busDoctorDto.getHospitalId());
            consultationQueryWrapper.in("doctor_id", Arrays.asList(split));
            List<BusDoctorConsultation> consultationList =
                    busDoctorConsultationMapper.selectList(consultationQueryWrapper);
            doctorVos.stream().forEach(d -> {
                BusSignature busSignature = busSignatureMapper.selectOne(new LambdaQueryWrapper<BusSignature>()
                        .eq(BusSignature::getObjectId, d.getId())
                        .eq(BusSignature::getObjectType, FiveRoleEnum.DOCTOR.getCode()));
                if (ObjectUtil.isNotNull(busSignature)) {
                    if (CharSequenceUtil.isEmpty(busSignature.getPassword())) {
                        d.setIsPassword(String.valueOf(YesNoEnum.NO.getCode()));
                    } else {
                        d.setIsPassword(String.valueOf(YesNoEnum.YES.getCode()));
                    }
                } else {
                    d.setIsPassword(String.valueOf(YesNoEnum.NO.getCode()));
                }
                List<BusDoctorDepartmentVo> dList = new ArrayList<>();
                List<BusDoctorConsultation> cList = new ArrayList<>();
                hospitalList.stream().forEach(h -> {
                    if (d.getId().equals(h.getDoctorId())) {
                        d.setIsThisCourt(h.getIsThisCourt());
                        d.setHot(h.getHot());
                        d.setOrderNum(h.getOrderNum());
                        d.setRole(h.getRole());
                        d.setOnlineStatus(h.getOnlineStatus());
                        BusHospital busHospital = busHospitalMapper.selectById(h.getHospitalId());
                        d.setStatus(h.getStatus());
                        d.setHospitalName(busHospital.getHospitalName());
                    }
                });
                departmentList.stream().forEach(dd -> {
                    if (d.getId().equals(dd.getDoctorId())) {
                        dList.add(dd);
                    }
                });
                consultationList.stream().forEach(c -> {
                    if (d.getId().equals(c.getDoctorId())) {
                        cList.add(c);
                    }
                });
                d.setDoctorDepartmentVoList(dList);
                d.setBusDoctorConsultationList(cList);
            });
        }
        return doctorVos;
    }

    @Override
    public Map<String, Object> getById(Long id, Long hospitalId) {
        Map<String, Object> map = new HashMap<>();
        BusDoctorVo busDoctorVo = busDoctorMapper.selectDoctorById(id, hospitalId);
        busDoctorVo.setIdCardNo(DESUtil.decrypt(busDoctorVo.getIdCardNo()));
        busDoctorVo.setPhoneNumber(DESUtil.decrypt(busDoctorVo.getPhoneNumber()));
        // 查询问诊信息
        List<BusDoctorConsultation> consultationList = busDoctorConsultationMapper.selectList(
                new LambdaQueryWrapper<BusDoctorConsultation>()
                        .eq(BusDoctorConsultation::getHospitalId, busDoctorVo.getHospitalId())
                        .eq(BusDoctorConsultation::getDoctorId, busDoctorVo.getId())
                        .orderByAsc(BusDoctorConsultation::getType));
        // 查询医助关联医生信息
        if (CodeEnum.YES.getCode().equals(busDoctorVo.getAssistantFlag())) {
            LambdaQueryWrapper<BusFiveAssistantDoctor> queryWrapper = new LambdaQueryWrapper<BusFiveAssistantDoctor>()
                    .eq(BusFiveAssistantDoctor::getHospitalId, hospitalId)
                    .eq(BusFiveAssistantDoctor::getAssistantId, id);
            busDoctorVo.setAssistantDoctors(assistantDoctorMapper.selectList(queryWrapper));
        }
        if (StringUtils.isNotEmpty(busDoctorVo.getPhoto())) {
            //图片转base64
            String urlImageToBase64 = "";
            try {

                urlImageToBase64 = ImageUtils.getUrlImageToBase64(ossUtil.getFullPath(busDoctorVo.getPhoto()), "jpeg");
            } catch (Exception e) {
                throw new ServiceException("头像base64转换失败" + e);
            }
            busDoctorVo.setPhotoBase64(urlImageToBase64);
        }
        map.put("doctor", busDoctorVo);
        map.put("consultationList", consultationList);
        return map;
    }

    @Override
    public int insert(BusDoctor busDoctor) {
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BusDoctorRelDTO busDoctor) {
        if (CodeEnum.YES.getCode().equals(busDoctor.getAssistantFlag())) {
            List<BusFiveAssistantDoctor> doctors = assistantDoctorMapper.selectList(new LambdaQueryWrapper<BusFiveAssistantDoctor>()
                    .eq(BusFiveAssistantDoctor::getDoctorId, busDoctor.getId()));
            if (CollUtil.isNotEmpty(doctors)) {
                throw new ServiceException("已有医助绑定该医生，该医生不能成为医助角色");
            }
        }
        // 修改医生信息
        busDoctor.setIdCardNo(DESUtil.encrypt(busDoctor.getIdCardNo()));
        busDoctor.setPhoneNumber(DESUtil.encrypt(busDoctor.getPhoneNumber()));
        busDoctor.setUpdateTime(DateUtils.getNowDate());
        busDoctorMapper.updateById(busDoctor);
        // 修改医生医院关联信息
        BusDoctorHospital dh = new BusDoctorHospital();
        dh.setReviewContent(busDoctor.getReviewContent());
        dh.setAssistantFlag(busDoctor.getAssistantFlag());
        dh.setHisDoctorId(busDoctor.getHisDoctorId());
        busDoctorHospitalMapper.update(dh, new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, busDoctor.getHospitalId())
                .eq(BusDoctorHospital::getDoctorId, busDoctor.getId()));
        // 修改前的医助医生集合
        List<BusFiveAssistantDoctor> oldData = assistantDoctorMapper.selectList(new LambdaQueryWrapper<BusFiveAssistantDoctor>()
                .eq(BusFiveAssistantDoctor::getAssistantId, busDoctor.getId()));
        // 修改后的医助医生集合
        List<BusFiveAssistantDoctor> newData = busDoctor.getAssistantDoctors();
        // 修改前有绑定医生，修改后有-->对应删除、新增操作
        Iterator<BusFiveAssistantDoctor> oldDataIterator = oldData.iterator();
        Iterator<BusFiveAssistantDoctor> newDataIterator = newData.iterator();

        while (oldDataIterator.hasNext()) {

            BusFiveAssistantDoctor oldFiveAssistantDoctor = oldDataIterator.next();
            boolean flag = false ;
            while (newDataIterator.hasNext()) {

                BusFiveAssistantDoctor newFiveAssistantDoctor = newDataIterator.next();
                if (oldFiveAssistantDoctor.getDoctorId().equals(newFiveAssistantDoctor.getDoctorId())) {

                    newDataIterator.remove();
                    flag = true;
                }
            }

            if (flag) {
                oldDataIterator.remove();
            }

        }

        if (!oldData.isEmpty()) {
            // 删除医助医生关系
            List<Long> oldDataIds = oldData.stream().map(BusFiveAssistantDoctor::getId).collect(Collectors.toList());
            assistantDoctorMapper.deleteBatchIds(oldDataIds);
            // 删除医助医生一对一群组
            busImGroupMemberService.updateGroupAssistant(oldData, CodeEnum.YES.getCode());
        }
        if (!newData.isEmpty()) {
            // 新增医助关联医生信息
            newData.forEach(item -> {
                item.setAssistantId(busDoctor.getId());
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                if (item.getDoctorId().equals(item.getAssistantId())) {
                    throw new ServiceException("医助不可绑定本人");
                }
                assistantDoctorMapper.insert(item);
            });
            // 添加医助医生一对一聊天
            busImGroupMemberService.updateGroupAssistant(newData, CodeEnum.NO.getCode());
        }
        return 1;
    }


    @Override
    public int enableDisable(BusDoctor busDoctor) {
        return busDoctorMapper.update(busDoctor);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int setConsultation(List<BusDoctorConsultation> busDoctorConsultation) {
        try {
            busDoctorConsultation.forEach(dc -> {
                if (ObjectUtil.isNotNull(dc.getId())) {
                    // 修改
                    if (ConsultationEnum.FURTHER.getCode().equals(dc.getType()) && ObjectUtil.isNull(dc.getCost())) {
                        busDoctorConsultationMapper.updateFurtherCost(dc.getId());
                    } else {
                        dc.setUpdateTime(DateUtils.getNowDate());
                        busDoctorConsultationMapper.updateById(dc);
                    }

                } else {
                    dc.setCreateTime(DateUtils.getNowDate());
                    busDoctorConsultationMapper.insert(dc);
                }
            });
        } catch (Exception e) {
            log.warn("医生问诊设置信息异常", e);
            throw new BaseException("操作失败");
        }
        return 1;
    }

    @Override
    public int delete(Long ids) {
        return 0;
    }

    @Override
    public List<BusDoctorConsultation> getDoctorConsultationInfo(BusDoctorConsultation doctorConsultation) {
        QueryWrapper<BusDoctorConsultation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("doctor_id", doctorConsultation.getDoctorId());
        queryWrapper.eq("hospital_id", doctorConsultation.getHospitalId());
        return busDoctorConsultationMapper.selectList(queryWrapper);
    }

    /**
     * 根据手机号查询医生信息
     *
     * @param phoneNumber 手机号
     * @return 医生信息
     */
    @Override
    public BusDoctor selectDoctorInfo(String phoneNumber) {
        return busDoctorMapper.selectDoctorInfo(DESUtil.encrypt(phoneNumber));
    }

    /**
     * 设置热门医生
     *
     * @param dto
     * @return
     */
    @Override
    public int settingHotOrNum(BusDoctorDto dto) {
        BusDoctorHospital busDoctorHospital = new BusDoctorHospital();
        busDoctorHospital.setId(dto.getId());
        if (StringUtils.isNotEmpty(dto.getHot())) {
            busDoctorHospital.setHot(dto.getHot());
        }
        if (ObjectUtil.isNotEmpty(dto.getOrderNum())) {
            busDoctorHospital.setOrderNum(dto.getOrderNum());
        }
        return busDoctorHospitalMapper.updateById(busDoctorHospital);
    }

    @Override
    public BusDoctorVo getDoctorInfo(Long id, Long hospitalId) {
        return busDoctorMapper.selectDoctorById(id, hospitalId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addDoctor(BusDoctorRelDTO dto) {
        VerifyDoctorDTO verifyDTO = new VerifyDoctorDTO(null, dto.getPhoneNumber(), dto.getIdCardNo(), CodeEnum.NO.getCode());
        Boolean isExist = this.verifyDoctorIsExist(verifyDTO);
        if (isExist) {
            throw new ServiceException("身份证号或手机号已被注册！");
        }
        dto.setCreateTime(new Date());
        dto.setPhoneNumber(DESUtil.encrypt(dto.getPhoneNumber()));
        dto.setIdCardNo(DESUtil.encrypt(dto.getIdCardNo()));
        dto.setCreateBy(SecurityUtils.getUsername());
        dto.setDoctorNumber(String.valueOf(System.currentTimeMillis()));
        busDoctorMapper.insert(dto);
        // 插入默认问诊设置
        BusConsultationSettings settings = busConsultationSettingsMapper.selectOne(
                new LambdaQueryWrapper<BusConsultationSettings>()
                        .eq(BusConsultationSettings::getHospitalId, dto.getHospitalId()));
        BusDoctorConsultation consultation = new BusDoctorConsultation();
        List<BusDoctorConsultation> consultationDefaultSetUp = consultation.getDefaultSetUp(dto.getId(), dto.getHospitalId(), settings);
        consultationDefaultSetUp.forEach(busDoctorConsultationMapper::insert);
        // 插入医生科室表
        List<List<Long>> depts = dto.getDepts();
        depts.forEach(d -> {
            BusDoctorDepartment busDoctorDepartment = new BusDoctorDepartment();
            busDoctorDepartment.setHospitalId(dto.getHospitalId());
            busDoctorDepartment.setDoctorId(dto.getId());
            busDoctorDepartment.setDepartmentId(d.get(1));
            busDoctorDepartment.setCreateBy(SecurityUtils.getUsername());
            busDoctorDepartment.setCreateTime(DateUtils.getNowDate());
            busDoctorDepartmentMapper.insert(busDoctorDepartment);
        });
        // 医生医院关联
        BusDoctorHospital busDoctorHospital = new BusDoctorHospital();
        busDoctorHospital.setDoctorId(dto.getId());
        busDoctorHospital.setHospitalId(dto.getHospitalId());
        busDoctorHospital.setIsThisCourt(dto.getIsThisCourt());
        busDoctorHospital.setIsAuthentication(DoctorAuthStatusEnum.AUTH_SUCCESS.getCode());
        busDoctorHospital.setJobNumber(System.currentTimeMillis() + "");
        busDoctorHospital.setRole(dto.getRole());
        busDoctorHospital.setCreateBy(SecurityUtils.getUsername());
        busDoctorHospital.setCreateTime(DateUtils.getNowDate());
        busDoctorHospital.setStatus(dto.getStatus());
        busDoctorHospital.setAssistantFlag(dto.getAssistantFlag());
        busDoctorHospital.setReviewContent(dto.getReviewContent());
        busDoctorHospital.setHisDoctorId(dto.getHisDoctorId());
        busDoctorHospitalMapper.insert(busDoctorHospital);
        // 医助关联医生
        if (CodeEnum.YES.getCode().equals(dto.getAssistantFlag())) {
            // 新增医助关联医生信息
            List<BusFiveAssistantDoctor> assistantDoctors = dto.getAssistantDoctors();
            if (CollUtil.isNotEmpty(assistantDoctors)) {
                assistantDoctors.forEach(a -> {
                    a.setCreateBy(SecurityUtils.getUsername());
                    a.setCreateTime(DateUtils.getNowDate());
                    a.setAssistantId(dto.getId());
                    assistantDoctorMapper.insert(a);
                });
                // 添加医助医生一对一聊天
                busImGroupMemberService.createDoctorAndAssistantGroup(assistantDoctors);
            }
        }
        // 插入推送设置
        BusDoctorPushSetup busDoctorPushSetup = new BusDoctorPushSetup();
        busDoctorPushSetup.setPusherId(dto.getId());
        busDoctorPushSetup.setIsPush(YesNoEnum.YES.getCode());
        busDoctorPushSetup.setPushType(DoctorPushTypeEnum.ALL_PUSH.getCode());
        busDoctorPushSetup.setType(FiveRoleEnum.DOCTOR.getCode());
        busDoctorPushSetup.setCreateBy(SecurityUtils.getUsername());
        busDoctorPushSetup.setCreateTime(DateUtils.getNowDate());
        busDoctorPushSetupMapper.insert(busDoctorPushSetup);
        // 审核记录
        if (FiveRoleEnum.DOCTOR.getCode().equals(dto.getRole())) {
            BusDoctorAudit audit = OrikaUtils.convert(dto, BusDoctorAudit.class);
            audit.setId(null);
            audit.setDoctorId(dto.getId());
            audit.setAuditStatus(2);
            audit.setJobNumber(busDoctorHospital.getJobNumber());
            audit.setCreateBy(SecurityUtils.getUsername());
            audit.setCreateTime(DateUtils.getNowDate());
            audit.setAffiliatedHospital(audit.getHospitalId());
            busDoctorAuditMapper.insert(audit);
        }
        // 导入im账号
        R<Boolean> returnData = remoteImService.accountCheck(TencentyunImConstants.DOCTOR_IM_ACCOUNT + dto.getId());
        if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) returnData.getData()) {
            remoteImService.accountImport(TencentyunImConstants.DOCTOR_IM_ACCOUNT + dto.getId(),
                    dto.getFullName(), dto.getPhoto());
        } else {
            // 防止im已有账号更新昵称
            ImPortraitRequest imPortraitRequest = new ImPortraitRequest();
            imPortraitRequest.setFrom_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + dto.getId());
            List<ProfileItem> profileItemList = new ArrayList<>();
            ProfileItem profileItem = new ProfileItem();
            profileItem.setTag("Tag_Profile_IM_Nick");
            profileItem.setValue(dto.getFullName());
            profileItemList.add(profileItem);
            imPortraitRequest.setProfileItemList(profileItemList);
            remoteImService.portraitSet(imPortraitRequest);
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDoctorDepartment(List<BusDoctorDepartment> list) {
        if (list.isEmpty()) {
            throw new ServiceException("更新科室为空");
        }
        QueryWrapper<BusDoctorAudit> auditQueryWrapper = new QueryWrapper<>();
        auditQueryWrapper.eq("hospital_id", list.get(0).getHospitalId());
        auditQueryWrapper.eq("doctor_id", list.get(0).getDoctorId());
        auditQueryWrapper.eq("audit_status", YesNoEnum.NO.getCode());
        auditQueryWrapper.orderByDesc("create_time");
        BusDoctorAudit doctorAudit = busDoctorAuditMapper.selectOne(auditQueryWrapper.last("limit 1"));
        if (ObjectUtil.isNotNull(doctorAudit)) {
            throw new ServiceException("该医生资料正在审核,无法修改科室");
        }

        QueryWrapper<BusDoctorDepartment> wrapper = new QueryWrapper<>();
        wrapper.eq("hospital_id", list.get(0).getHospitalId());
        wrapper.eq("doctor_id", list.get(0).getDoctorId());
        List<BusDoctorDepartment> departments = busDoctorDepartmentMapper.selectList(wrapper);
        List<BusDoctorDepartment> departmentList = contrastList(departments, list);
        for (BusDoctorDepartment d : departmentList) {
            boolean flag = false;
            boolean b = busConsultationOrderService.checkBizDepartmentIsConsultationOrderStatus(d.getDepartmentId(),
                    d.getHospitalId(), d.getDoctorId());
            if (b) {
                flag = true;
            } else {
                // 判断医生所在业务科室是否关联工作组
                R<Boolean> listR = remoteWorkGroupService.queryWorkGroupInfo(d.getHospitalId(), d.getDoctorId(),
                        d.getDepartmentId(), SecurityConstants.INNER);
                Boolean data = listR.getData();
                if (data) {
                    flag = true;
                }
                log.info("远程调用工作组code={},结果={}", listR.getCode(), data);
            }
            //判断是否有排班
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
            String format = sdf.format(DateUtils.getNowDate());
            BusDoctorScheduling schedulings = busDoctorSchedulingMapper.selectOne(new LambdaQueryWrapper<BusDoctorScheduling>()
                    .eq(BusDoctorScheduling::getDepartmentId, d.getDepartmentId())
                    .eq(BusDoctorScheduling::getHospitalId, d.getHospitalId())
                    .eq(BusDoctorScheduling::getDoctorId, d.getDoctorId())
                    .ge(BusDoctorScheduling::getSchedulingDate, format));
            if (ObjectUtil.isNotNull(schedulings)) {
                flag = true;
            }
            if (flag) {
                BusBizDepartment bizDepartment =
                        busBizDepartmentService.selectBusBizDepartmentById(d.getDepartmentId());
                throw new ServiceException(bizDepartment.getDepartmentName() + "存在业务数据无法修改");
            }
        }

        //删除原有插入新关联科室
        QueryWrapper<BusDoctorDepartment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hospital_id", list.get(0).getHospitalId());
        queryWrapper.eq("doctor_id", list.get(0).getDoctorId());
        busDoctorDepartmentMapper.delete(queryWrapper);
        busDoctorDepartmentMapper.batchInsert(list);
        return 1;
    }

    /**
     * @Param dto
     * @Return List<IsExistsPatientVO>
     * @Description 基础数据是否存在患者
     * <AUTHOR>
     * @Date 2024/7/23 17:33
     **/
    @Override
    public List<IsExistsPatientVO> isBaseInfoExistsPatient(DoctorPatientGroupDTO dto) {
        Map<Long, IsExistsPatientVO> map = getBaseInfoExistsPatientMap(dto, null);


        return CollUtil.newArrayList(map.values());
    }


    /**
     * @Param dto
     * @Return Map<Long, IsExistsPatientVO>
     * @Description 获取 基础数据 map
     * <AUTHOR>
     * @Date 2024/7/30 14:09
     **/
    private Map<Long, IsExistsPatientVO> getBaseInfoExistsPatientMap(DoctorPatientGroupDTO dto, List<IsExistsPatientVO> list) {
        Assert.notNull(dto, "参数不能为空");
        Assert.notEmpty(dto.getDepartmentIds(), "科室ID不能为空");
        Assert.notNull(dto.getHospitalId(), "医院ID不能为空");
        Assert.notNull(dto.getDoctorId(), "医生ID不能为空");

        List<BusPreorderDoctorVo> preorderDoctorVos = busPreorderDoctorService.getList(dto.getDepartmentIds(), dto.getHospitalId(), dto.getDoctorId());
        List<BusRecommendDoctorVo> recommendDoctorVos = busRecommendDoctorService.getList(dto.getDepartmentIds(), dto.getHospitalId(), dto.getDoctorId());
        List<BusFiveWorkGroupVO> fiveWorkGroupVOS = busFiveWorkGroupService.getList(dto.getDepartmentIds(), dto.getHospitalId(), dto.getDoctorId());
        List<BusFiveGroupMemberVO> busFiveGroupMemberVOS = busFiveGroupMemberService.getList(dto.getDepartmentIds(), dto.getHospitalId(), dto.getDoctorId());
        Map<Long, IsExistsPatientVO> map = new HashMap<>();
        preorderDoctorVos.forEach(item -> isBaseInfoExistsPatientHandler(map, list, item.getBizDepartmentId(), item.getDepartmentName(), 1));
        recommendDoctorVos.forEach(item -> isBaseInfoExistsPatientHandler(map, list, item.getDepartmentId(), item.getDepartmentName(), 2));
        fiveWorkGroupVOS.forEach(item -> isBaseInfoExistsPatientHandler(map, list, item.getDepartmentId(), item.getDepartmentName(), 3));
        busFiveGroupMemberVOS.forEach(item -> isBaseInfoExistsPatientHandler(map, list, item.getDepartmentId(), item.getDepartmentName(), 4));
        return map;
    }

    /**
     * @Param map
     * @Param departmentId
     * @Param departmentName
     * @Return void
     * @Description 处理封装
     * <AUTHOR>
     * @Date 2024/7/25 17:36
     **/
    private void isBaseInfoExistsPatientHandler(Map<Long, IsExistsPatientVO> map, List<IsExistsPatientVO> list, Long departmentId, String departmentName, Integer type) {
        IsExistsPatientVO entity = buildIsExistsPatientVO(departmentId, departmentName);
        entity.setType(type);
        if (ObjectUtil.isNotNull(list)) {
            list.add(entity);
        }
        IsExistsPatientVO vo = map.get(departmentId);

        if (ObjectUtil.isNull(vo)) {
            vo = entity;
            map.put(departmentId, vo);
        }
    }


    /**
     * @Param departmentId
     * @Param departmentName
     * @Return IsExistsPatientVO
     * @Description 生成IsExistsPatientVO
     * <AUTHOR>
     * @Date 2024/7/25 17:31
     **/
    private IsExistsPatientVO buildIsExistsPatientVO(Long departmentId, String departmentName) {
        IsExistsPatientVO vo = new IsExistsPatientVO();
        vo.setDepartmentId(departmentId);
        vo.setDepartmentName(departmentName);
        return vo;
    }

    /**
     * @Param dto
     * @Return List<IsExistsPatientVO>
     * @Description 群组是否存在患者
     * <AUTHOR>
     * @Date 2024/7/23 17:33
     **/
    @Override
    public List<IsExistsPatientVO> isGroupExistsPatient(DoctorPatientGroupDTO dto) {
        List<BusDoctorPatientGroup> list = busDoctorPatientGroupService.getListByDepartmentIds(dto);

        Map<Long, IsExistsPatientVO> map = new HashMap<>();

        list.stream().forEach(item -> {
            Long departmentId = item.getDepartmentId();
            IsExistsPatientVO vo = map.get(departmentId);

            if (ObjectUtil.isNull(vo)) {
                vo = buildIsExistsPatientVO(departmentId, item.getDepartmentName());
                map.put(departmentId, vo);
            }

            Set<String> names = vo.getPatientNames();
            if (CollUtil.isEmpty(names)) {
                names = new HashSet<>();
                vo.setPatientNames(names);
            }
            names.add(item.getFamilyName());
        });
        return CollUtil.newArrayList(map.values());

    }

    /**
     * @Param dto
     * @Return List<IsExistsPatientVO>
     * @Description 是否存在患者
     * <AUTHOR>
     * @Date 2024/7/19 11:24
     **/
    @Override
    public List<IsExistsPatientVO> isExistsPatient(DoctorDepartmentDTO dto) {
        //问诊订单表
        List<BusConsultationOrder> orders = busConsultationOrderService.isExistsPatient(dto);
        //为空返回空集合
        if (CollUtil.isEmpty(orders)) {
            return CollUtil.newArrayList();
        }

        Map<Long, IsExistsPatientVO> map = new HashMap<>();

        orders.stream().forEach(item -> {
            Long departmentId = item.getDepartmentId();
            IsExistsPatientVO vo = map.get(departmentId);

            if (ObjectUtil.isNull(vo)) {
                vo = buildIsExistsPatientVO(departmentId, item.getDepartmentName());
                map.put(departmentId, vo);
            }

            Set<String> names = vo.getPatientNames();
            if (CollUtil.isEmpty(names)) {
                names = new HashSet<>();
                vo.setPatientNames(names);
            }
            names.add(item.getFamilyName());
        });
        return CollUtil.newArrayList(map.values());
    }

    /**
     * @Param dto
     * @Return Boolean
     * @Description 删除医生科室
     * <AUTHOR>
     * @Date 2024/7/19 18:50
     **/
    @Override
    public Boolean deleteDoctorByDepartmentId(DoctorDepartmentDTO dto) {
        Assert.notNull(dto, "参数不能为空");
        Assert.notEmpty(dto.getDepartmentIds(), "科室ID不能为空");
        Assert.notNull(dto.getHospitalId(), "医院ID不能为空");
        Assert.notNull(dto.getDoctorId(), "医生ID不能为空");

        Long hospitalId = dto.getHospitalId();
        Long doctorId = dto.getDoctorId();

        List<Long> collect = getSourceDepartmentIds(hospitalId, doctorId);
        //目标科室
        List<Long> departmentIds = dto.getDepartmentIds();

        if (departmentIds.size() == collect.size()) {
            return true;
        } else if (departmentIds.size() > collect.size()) {
            throw new IllegalArgumentException("非删除操作");
        }

        //交集
        Collection<Long> intersection = CollUtil.intersection(collect, departmentIds);
        Assert.notEmpty(intersection, "目标科室不为源科室交集");

        if (departmentIds.size() != intersection.size()) {
            throw new IllegalArgumentException("非删除操作");
        }

        //差集
        List<Long> disjunction = (List<Long>) CollUtil.disjunction(collect, departmentIds);

        busDoctorSchedulingService.cleanDoctorScheduling(hospitalId, doctorId, new Date(), disjunction);
        return busDoctorDepartmentService.deleteByHospitalIdAndDoctorId(hospitalId, doctorId, disjunction);
    }

    /**
     * @Param dto
     * @Return Boolean
     * @Description 新增或修改医生科室
     * 能进来代表四种情况
     * 1.被新增修改的医生科室下无患者，直接变更
     * 2.单纯新增的科室不用管，直接新增
     * 3.被修改的科室下有患者，则修改科室只能修改了一个科室，且允许变更科室
     * 4.无患者的情况下可以同时新增+修改
     * <AUTHOR>
     * @Date 2024/7/22 11:20
     **/
    @Override
    @Transactional
    public Boolean addModifyDepartment(DoctorDepartmentDTO dto) {
        Assert.notNull(dto, "参数不能为空");
        Assert.notEmpty(dto.getDepartmentIds(), "科室ID不能为空");
        Assert.notNull(dto.getHospitalId(), "医院ID不能为空");
        Assert.notNull(dto.getIsChange(), "是否允许变更科室不能为空");
        Assert.notNull(dto.getDoctorId(), "医生ID不能为空");


        Long doctorId = dto.getDoctorId();
        Long hospitalId = dto.getHospitalId();
        //源数据
        List<Long> sourceList = getSourceDepartmentIds(hospitalId, doctorId);
        //目标数据
        List<Long> targetList = dto.getDepartmentIds();
        //取交集
        Collection<Long> intersection = CollUtil.intersection(targetList, sourceList);

        //交集为空，判断源数据是否全部不存在患者，如果是则直接变更
        if (CollUtil.isEmpty(intersection) && sourceList.size() != targetList.size()) {
            Assert.isTrue(!checkHaveConsultationOrder(sourceList, hospitalId, doctorId), "目标科室不为源科室交集");
            //无患者的情况下，直接修改
            //删除源科室
            if (CollUtil.isNotEmpty(sourceList)) {
                busDoctorDepartmentService.deleteByHospitalIdAndDoctorId(hospitalId, doctorId, sourceList);
                busDoctorSchedulingService.cleanDoctorScheduling(hospitalId, doctorId, new Date(), sourceList);
            }
            //新增目标科室
            busDoctorDepartmentService.batchInsertDoctorDepartment(hospitalId, doctorId, targetList);
            return true;
        }

        int targetSize = targetList.size();
        int sourceSize = sourceList.size();
        int intersectionSize = intersection.size();

        if (targetSize > sourceSize) {
            //意味着在原有的基础上新增科室
            if (sourceSize == intersectionSize) {
                //意味着新增科室，可以直接操作
                List<Long> targetDisjunction = CollUtil.newArrayList(CollUtil.disjunction(targetList, intersection));
                //新增目标科室和交集的差集
                busDoctorDepartmentService.batchInsertDoctorDepartment(hospitalId, doctorId, targetDisjunction);
            } else {
                throw new IllegalArgumentException("非法操作");
            }
        } else {
            if (sourceSize == intersectionSize) {
                //表示无修改-
                return true;
            }
            //取源数据和交集数据的差集，差集即为修改的科室
            List<Long> sourceDisjunction = CollUtil.newArrayList(CollUtil.disjunction(sourceList, intersection));
            Boolean b = checkHaveConsultationOrder(sourceDisjunction, hospitalId, doctorId);
            if (Boolean.TRUE.equals(b)) {
                List<Long> targetDisjunction = CollUtil.newArrayList(CollUtil.disjunction(targetList, intersection));
                //新增科室数量
                int targetDisjunctionSize = targetDisjunction.size();
                //有患者的情况下，判断是否为单个科室变更，判断是否允许变更科室
                if (targetDisjunctionSize != 1) {
                    throw new IllegalArgumentException("医生原科室下已有患者，单次仅支持变更一个科室，请先变更为某一科室再增加科室");
                } else {
                    //无患者的情况下，直接修改
                    //删除源科室和交集的差集
                    busDoctorDepartmentService.deleteByHospitalIdAndDoctorId(hospitalId, doctorId, sourceDisjunction);
                    busDoctorSchedulingService.cleanDoctorScheduling(hospitalId, doctorId, new Date(), sourceDisjunction);
                    //新增目标科室和交集的差集
                    busDoctorDepartmentService.batchInsertDoctorDepartment(hospitalId, doctorId, targetDisjunction);
                    //必须迁移的数据
                    migrateData(sourceDisjunction, targetDisjunction.get(0), hospitalId, doctorId);
                    if (Boolean.TRUE.equals(dto.getIsChange())) {
                        //允许变更
                        changeData(sourceDisjunction, targetDisjunction.get(0), hospitalId, doctorId, null);
                    }
                }
            } else {
                //无患者的情况下，直接修改
                //删除源科室和交集的差集
                busDoctorDepartmentService.deleteByHospitalIdAndDoctorId(hospitalId, doctorId, sourceDisjunction);
                busDoctorSchedulingService.cleanDoctorScheduling(hospitalId, doctorId, new Date(), sourceDisjunction);
                List<Long> targetDisjunction = CollUtil.newArrayList(CollUtil.disjunction(targetList, intersection));
                migrateData(sourceDisjunction, targetDisjunction.get(0), hospitalId, doctorId);
                //新增目标科室和交集的差集
                busDoctorDepartmentService.batchInsertDoctorDepartment(hospitalId, doctorId, targetDisjunction);
            }
        }
        return true;
    }

    /**
     * @Param doctorId
     * @Param hospitalId
     * @Param departmentId
     * @Return Boolean
     * @Description 根据医生ID和医院ID和科室ID查询是否存在
     * <AUTHOR>
     * @Date 2024/7/29 14:29
     **/
    @Override
    public Boolean isDepartmentExistsDoctor(Long doctorId, Long hospitalId, Long departmentId) {
        BusDoctorDepartment department = new BusDoctorDepartment();
        department.setDoctorId(doctorId);
        department.setHospitalId(hospitalId);
        department.setDepartmentId(departmentId);
        List<BusDoctorDepartment> list = getList(department);

        if (CollUtil.isNotEmpty(list)) {
            return true;
        }
        return false;
    }

    @Override
    public List<IsExistsPatientVO> isDelBaseInfoExistsPatient(DoctorPatientGroupDTO dto) {
        List<IsExistsPatientVO> list = new ArrayList<>();
        getBaseInfoExistsPatientMap(dto, list);
        return list;
    }

    /**
     * @Param sourceList 源数据列表
     * @Param target 目标
     * @Param hospitalId 医院ID
     * @Param doctorId 医生ID
     * @Return Boolean
     * @Description 将数据从 sourceList 迁移到 target
     * <AUTHOR>
     * @Date 2024/7/24 17:01
     **/
    private Boolean migrateData(List<Long> sourceList, Long target, Long hospitalId, Long doctorId) {
        //1.迁移 快速开方医生 医生预订单
        busPreorderDoctorService.migrateData(sourceList, target, hospitalId, doctorId);
        //2.迁移 热门专栏
        busRecommendDoctorService.migrateData(sourceList, target, hospitalId, doctorId);
        //3.迁移 合作机构医生数据
        //这里实际上修改的是 bus_doctor_department 表的数据，在前置就已经做了
        //4.迁移 工作组列表数据
        busFiveWorkGroupService.migrateData(sourceList, target, hospitalId, doctorId);
        //5.迁移 组长组员科室数据
        busFiveGroupMemberService.migrateData(sourceList, target, hospitalId, doctorId);

        return true;
    }

    /**
     * @Param dto
     * @Return Boolean
     * @Description 合并数据
     * <AUTHOR>
     * @Date 2024/7/26 9:56
     **/
    @Override
    @Transactional
    public Boolean mergeData(DoctorDepartmentDTO dto) {
        Assert.notNull(dto, "参数不能为空");
        Assert.notEmpty(dto.getDepartmentIds(), "科室ID不能为空");
        Assert.notNull(dto.getHospitalId(), "医院ID不能为空");
        Assert.notNull(dto.getIsChange(), "是否允许变更科室不能为空");
        Assert.notNull(dto.getDoctorId(), "医生ID不能为空");


        Long doctorId = dto.getDoctorId();
        Long hospitalId = dto.getHospitalId();
        //源数据
        List<Long> sourceList = getSourceDepartmentIds(hospitalId, doctorId);
        //目标数据
        List<Long> targetList = dto.getDepartmentIds();
        //取交集
        Collection<Long> intersection = CollUtil.intersection(targetList, sourceList);
        if (CollUtil.isNotEmpty(intersection)) {
            log.error("合并科室时科室重复，intersection:{}", intersection);
            throw new IllegalArgumentException("科室重复");
        }

        //无患者的情况下，直接修改
        //删除源科室和交集的差集
        busDoctorDepartmentService.deleteByHospitalIdAndDoctorId(hospitalId, doctorId, sourceList);
        busDoctorSchedulingService.cleanDoctorScheduling(hospitalId, doctorId, new Date(), sourceList);
        List<Long> targetDisjunction = CollUtil.newArrayList(CollUtil.disjunction(targetList, intersection));
        //新增目标科室和交集的差集
        busDoctorDepartmentService.batchInsertDoctorDepartment(hospitalId, doctorId, targetDisjunction);
        //必须迁移的数据
        migrateData(sourceList, targetDisjunction.get(0), hospitalId, doctorId);

        if (Boolean.TRUE.equals(dto.getIsChange())) {
            //允许变更
            changeData(sourceList, targetDisjunction.get(0), hospitalId, doctorId, null);
        }

        return true;

    }

    /**
     * @Param sourceList
     * @Param target
     * @Param hospitalId
     * @Param doctorId
     * @Param familyIdExtra
     * @Return Boolean
     * @Description 医生手动合并数据，手动传输源科室
     * <AUTHOR>
     * @Date 2024/7/26 16:04
     **/
    @Override
    public Boolean doctorChangeData(DoctorDepartmentDTO dto) {
        Assert.notNull(dto, "参数不能为空");
        Assert.notEmpty(dto.getDepartmentIds(), "目标科室ID不能为空");
        Assert.notEmpty(dto.getSourceList(), "源科室ID不能为空");
        Assert.notNull(dto.getHospitalId(), "医院ID不能为空");
        Assert.notNull(dto.getDoctorId(), "医生ID不能为空");

        return changeData(dto.getSourceList(), dto.getDepartmentIds().get(0), dto.getHospitalId(), dto.getDoctorId(), dto.getFamilyIdExtra());
    }

    /**
     * @Param sourceList 源数据列表
     * @Param target 目标
     * @Param hospitalId 医院ID
     * @Param doctorId 医生ID
     * @Return Boolean
     * @Description 合并变更数据 将数据从 sourceList 合并变更到 target
     * <AUTHOR>
     * @Date 2024/7/24 18:13
     **/
    private Boolean changeData(List<Long> sourceList, Long target, Long hospitalId, Long doctorId, List<Long> familyIdExtra) {
        // 获取需要变更的数据
        List<BusDoctorPatientGroup> list = selectChangeData(sourceList, hospitalId, doctorId, familyIdExtra);

        //根据患者分组
        Map<Long, List<BusDoctorPatientGroup>> map = new HashMap<>();
        for (BusDoctorPatientGroup item : list) {
            Long familyId = item.getFamilyId();
            BusCommunicationMessage lastByGroupId = busCommunicationMessageMapper.getLastByGroupId(item.getId());
            item.setCreateTime(item.getCreateTime());
            if (ObjectUtil.isNotNull(lastByGroupId)) {
                item.setCreateTime(lastByGroupId.getCreateTime());
            }
            // 构建为 map
            map.computeIfAbsent(familyId, k -> new ArrayList<>()).add(item);
        }
        //保留患者最新一条数据
        for (List<BusDoctorPatientGroup> groups : map.values()) {
            if (groups.size() > 1) {
                // 按创建时间排序
                List<BusDoctorPatientGroup> collect = groups.stream()
                        .sorted(Comparator.comparing(BusDoctorPatientGroup::getCreateTime))
                        .collect(Collectors.toList());
                // 标记所有为变更
                collect.forEach(item -> item.setMarkChanges(true));
                // 将最新的一条数据标记为不变更
                collect.get(collect.size() - 1).setMarkChanges(false);
                // 更新标记变更状态
                collect.forEach(busDoctorPatientGroupService::updateMarkChanges);
            }
        }

        //1.变更医生患者工作组数据
        busDoctorPatientGroupService.changeData(sourceList, target, hospitalId, doctorId, familyIdExtra);
        return true;
    }

    /**
     * @Param sourceList
     * @Param hospitalId
     * @Param doctorId
     * @Return Boolean
     * @Description 查询是否存在不同群组同患者
     * <AUTHOR>
     * @Date 2024/7/26 12:05
     **/
    private List<BusDoctorPatientGroup> selectChangeData(List<Long> sourceList, Long hospitalId, Long doctorId, List<Long> familyIdExtra) {
        //1.变更医生患者工作组数据
        return busDoctorPatientGroupService.selectChangeData(sourceList, hospitalId, doctorId, familyIdExtra);
    }


    /**
     * @Param collect
     * @Param hospitalId
     * @Param doctorId
     * @Return Boolean
     * @Description 判断被修改的科室下是否存在患者
     * <AUTHOR>
     * @Date 2024/7/22 18:28
     **/
    private Boolean checkHaveConsultationOrder(List<Long> collect, Long hospitalId, Long doctorId) {
        DoctorPatientGroupDTO dto = new DoctorPatientGroupDTO();
        dto.setDepartmentIds(collect);
        dto.setHospitalId(hospitalId);
        dto.setDoctorId(doctorId);
        dto.setType(0);
        //判断被修改的科室下是否存在患者
        List<IsExistsPatientVO> existsPatient = isGroupExistsPatient(dto);
        if (CollUtil.isNotEmpty(existsPatient)) {
            return true;
        }
        return false;
    }


    /**
     * @Param hospitalId 医院ID
     * @Param doctorId 医生ID
     * @Return List<Long> 源科室ID列表
     * @Description 获取源科室
     * <AUTHOR>
     * @Date 2024/7/22 11:33
     **/
    public List<Long> getSourceDepartmentIds(Long hospitalId, Long doctorId) {
        List<BusDoctorDepartment> list = getListByHospitalIdDoctorId(hospitalId, doctorId);
//        Assert.notEmpty(list, "医生源科室不能为空");
        //源科室
        return list.stream().map(item -> item.getDepartmentId()).collect(Collectors.toList());
    }

    /**
     * @Param hospitalId
     * @Param doctorId
     * @Return List<BusDoctorDepartment>
     * @Description 通过医院ID和医生ID查询医生所有科室
     * <AUTHOR>
     * @Date 2024/7/19 18:25
     **/
    @Override
    public List<BusDoctorDepartment> getListByHospitalIdDoctorId(Long hospitalId, Long doctorId) {
        BusDoctorDepartment department = new BusDoctorDepartment();
        department.setHospitalId(hospitalId);
        department.setDoctorId(doctorId);
        return getList(department);
    }

    /**
     * @Param department
     * @Return List<BusDoctorDepartment>
     * @Description 批量查询
     * <AUTHOR>
     * @Date 2024/7/19 18:24
     **/
    @Override
    public List<BusDoctorDepartment> getList(BusDoctorDepartment department) {
        return busDoctorDepartmentMapper.getList(department);
    }


    @Override
    public int modifyState(BusDoctorHospital busDoctorHospital) {
        /*查询当前医生所绑定工作组的信息*/
        LambdaQueryWrapper<BusFiveGroupMember> qw = new LambdaQueryWrapper();
        qw.eq(BusFiveGroupMember::getMemberId, busDoctorHospital.getDoctorId());
        qw.eq(BusFiveGroupMember::getType, FiveRoleEnum.DOCTOR.getCode());
        qw.eq(BusFiveGroupMember::getHospitalId, busDoctorHospital.getHospitalId());
        qw.eq(BusFiveGroupMember::getStatus, YesNoEnum.YES.getCode());
        List<BusFiveGroupMember> groupMemberList = busFiveGroupMemberMapper.selectList(qw);
        /*获取当前医生所绑定工作组的工作组id集合*/
        List<Long> workGroupIds = groupMemberList.stream().map(BusFiveGroupMember::getWorkGroupId).collect(Collectors.toList());
        /*如果当前医生绑定有工作组，则判断当前医生所在的工作组是否绑定有在服务中服务包订单*/
        if (workGroupIds.size() > 0) {
            /*当服务包订单为待支付、待激活、已激活状态时候不可以禁用*/
            List<String> statusList = new ArrayList<>();
            statusList.add(ServicePackOrderStatusEnum.TO_BE_PAID.getCode());
            statusList.add(ServicePackOrderStatusEnum.ACTIVATED.getCode());
            statusList.add(ServicePackOrderStatusEnum.TO_BE_ACTIVATED.getCode());
            LambdaQueryWrapper<BusFiveServicePackOrder> lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.in(BusFiveServicePackOrder::getWorkGroupId, workGroupIds);
            lambdaQueryWrapper.in(BusFiveServicePackOrder::getStatus, statusList);
            List<BusFiveServicePackOrder> busFiveServicePackOrders = busFiveServicePackOrderMapper.selectList(lambdaQueryWrapper);
            if (busFiveServicePackOrders.size() > 0) {
                throw new ServiceException("该账号有服务包订单正在进行中，不可以禁用");
            }
            /*判断当前医生所在的工作组是否绑定有在服务中服务包订单*/
            LambdaQueryWrapper<BusFiveServicePack> busFiveServicePackLQW = new LambdaQueryWrapper();
            busFiveServicePackLQW.in(BusFiveServicePack::getWorkingGroupId, workGroupIds);
            busFiveServicePackLQW.eq(BusFiveServicePack::getStatus, String.valueOf(YesNoEnum.YES.getCode()));
            busFiveServicePackLQW.eq(BusFiveServicePack::getWhetherDelete, String.valueOf((YesNoEnum.NO.getCode())));
            List<BusFiveServicePack> busFiveServicePacks = busFiveServicePackMapper.selectList(busFiveServicePackLQW);
            if (busFiveServicePacks.size() > 0) {
                throw new ServiceException("该账号有服务包已经上架，不可以禁用");
            }
        }
        boolean check = busConsultationOrderService.checkBizDepartmentIsConsultationOrderStatus(null,
                busDoctorHospital.getHospitalId(), busDoctorHospital.getDoctorId());
        if (YesNoEnum.NO.getCode().equals(busDoctorHospital.getStatus()) && check) {
            throw new ServiceException("禁用失败,医生存在进行中订单");
        }
        BusDoctorHospital doctorHospital = busDoctorHospitalMapper.selectOne(new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, busDoctorHospital.getHospitalId())
                .eq(BusDoctorHospital::getDoctorId, busDoctorHospital.getDoctorId()));
        if (ObjectUtil.isNull(doctorHospital)) {
            throw new ServiceException("查询医生医院信息失败");
        }
        LambdaUpdateWrapper<BusDoctorHospital> hospitalLambdaUpdateWrapper = new LambdaUpdateWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, busDoctorHospital.getHospitalId())
                .eq(BusDoctorHospital::getDoctorId, busDoctorHospital.getDoctorId());
        busDoctorHospital.setOrderNum(doctorHospital.getOrderNum());
        return busDoctorHospitalMapper.update(busDoctorHospital, hospitalLambdaUpdateWrapper);
    }

    /**
     * 修改在线状态
     *
     * @param busDoctorHospital
     * @return
     */
    @Override
    public int modifyOnlineStatus(BusDoctorHospital busDoctorHospital) {
        LambdaQueryWrapper<BusDoctorHospital> queryWrapper = new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, busDoctorHospital.getHospitalId())
                .eq(BusDoctorHospital::getDoctorId, busDoctorHospital.getDoctorId());
        return busDoctorHospitalMapper.update(busDoctorHospital, queryWrapper);
    }

    /**
     * 查询修改科室
     *
     * @param list1
     * @param list2
     * @return
     */
    private List<BusDoctorDepartment> contrastList(List<BusDoctorDepartment> list1, List<BusDoctorDepartment> list2) {
        List<BusDoctorDepartment> departmentlist = new ArrayList<>();
        for (BusDoctorDepartment d : list1) {
            boolean iscz = false;
            for (BusDoctorDepartment dd : list2) {
                if (d.getDepartmentId().equals(dd.getDepartmentId())) {
                    iscz = true;
                }
            }
            if (!iscz) {
                departmentlist.add(d);
            }
        }
        return departmentlist;
    }

    private void updateWorkGroupInfo(Long hospitalId, Long doctorId) {
        ServicePackOrderRequest request = new ServicePackOrderRequest();
        request.setHospitalId(hospitalId);
        request.setDoctorId(doctorId);
        request.setStatus(ServicePackOrderStatusEnum.ACTIVATED.getCode());
        R<List<ServicePackOrderResponse>> listR = remoteServicePackOrderService.repeatServicePack(request,
                SecurityConstants.INNER);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCourtWay(BusDoctorHospital doctorHospital) {
        Long doctorId = doctorHospital.getDoctorId();
        Long hospitalId = doctorHospital.getHospitalId();
        Integer isThisCourt = doctorHospital.getIsThisCourt();
        BusDoctorAudit audit = busDoctorAuditMapper.selectOne(new LambdaQueryWrapper<BusDoctorAudit>()
                .eq(BusDoctorAudit::getDoctorId, doctorId)
                .eq(BusDoctorAudit::getHospitalId, hospitalId)
                .eq(BusDoctorAudit::getAuditStatus, AuditStatus.NOTAPPROVED.getCode()));
        if (ObjectUtil.isNotNull(audit)) {
            throw new ServiceException("该医生存在待审核的记录，请审核完成后再修改");
        }
        BusDoctorHospital busDoctorHospital = busDoctorHospitalMapper.selectOne(new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getDoctorId, doctorId)
                .eq(BusDoctorHospital::getHospitalId, hospitalId));
        //判断是否有(审核拒绝或审核中的处方) 0:未审核；1：审核未通过；2：审核通过; 3:作废 4 已使用 5已失效 6未签名
        List<BusPrescription> prescriptionList = busPrescriptionMapper.selectList(new LambdaQueryWrapper<BusPrescription>()
                .inSql(BusPrescription::getStatus, "0,1")
                .eq(BusPrescription::getHospitalId, hospitalId)
                .eq(BusPrescription::getDoctorId, doctorId));

        if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(isThisCourt)) {
            BusPreorderDoctor busPreorderDoctor = busPreorderDoctorMapper.selectOne(new LambdaQueryWrapper<BusPreorderDoctor>()
                    .eq(BusPreorderDoctor::getHospitalId, hospitalId)
                    .eq(BusPreorderDoctor::getDoctorId, doctorId));
            if (ObjectUtil.isNotNull(busPreorderDoctor)) {
                throw new ServiceException("该医生已经是用药医生，无法成为特聘专家");
            }
            BusHospitalPreorderDoctor preorderDoctor = hospitalPreorderDoctorMapper.selectOne(new LambdaQueryWrapper<BusHospitalPreorderDoctor>()
                    .eq(BusHospitalPreorderDoctor::getDoctorId, doctorId)
                    .eq(BusHospitalPreorderDoctor::getHospitalId, hospitalId));
            if (!Objects.isNull(preorderDoctor)) {
                throw new ServiceException("该医生已经是开方医生，无法成为特聘专家");
            }
            if (!DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(busDoctorHospital.getIsThisCourt())) {
                if (CollUtil.isNotEmpty(prescriptionList)) {
                    throw new ServiceException("该医生有处方未处理，无法切换执业方式");
                }
            }
        } else if (DoctorPracticeEnum.OUR_HOSPITAL.getCode().equals(isThisCourt)) {
            List<BusDoctorHospital> list = busDoctorHospitalMapper.selectList(new LambdaQueryWrapper<BusDoctorHospital>()
                    .eq(BusDoctorHospital::getDoctorId, doctorId)
                    .eq(BusDoctorHospital::getIsThisCourt, DoctorPracticeEnum.OUR_HOSPITAL.getCode())
                    .ne(BusDoctorHospital::getHospitalId, hospitalId));
            if (CollUtil.isNotEmpty(list)) {
                throw new ServiceException("该医生在其他医院已是本院医生");
            }
            if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(busDoctorHospital.getIsThisCourt())) {
                if (CollUtil.isNotEmpty(prescriptionList)) {
                    throw new ServiceException("该医生有处方未处理，无法切换执业方式");
                }
            }
        } else {
            if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(busDoctorHospital.getIsThisCourt())) {
                if (CollUtil.isNotEmpty(prescriptionList)) {
                    throw new ServiceException("该医生有处方未处理，无法切换执业方式");
                }
            }
        }
        // 更新医生第一执业医院
        BusDoctor doctor = new BusDoctor();
        doctor.setId(doctorId);
        doctor.setFirstHospital(doctorHospital.getFirstHospital());
        doctor.setFirstHospitalUnifiedCreditCode(doctorHospital.getFirstHospitalUnifiedCreditCode());
        busDoctorMapper.update(doctor);

        return busDoctorHospitalMapper.update(doctorHospital, new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getDoctorId, doctorId)
                .eq(BusDoctorHospital::getHospitalId, hospitalId));
    }

    /**
     * 转特聘专家检查是否有在问诊中的订单
     *
     * @param hospitalId 医院id
     * @param doctorId   医生id
     * @return true-存在 false-不存在
     */
    private boolean checkHaveConsultationOrder(Long hospitalId, Long doctorId) {
        ArrayList<Integer> statusList = Lists.newArrayList(2, 3);
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusConsultationOrder::getHospitalId, hospitalId)
                .eq(BusConsultationOrder::getDoctorId, doctorId).and(
                        i -> i.in(BusConsultationOrder::getStatus, statusList)
                                .or().in(BusConsultationOrder::getVideoStatus, statusList));
        List<BusConsultationOrder> consultationOrders = consultationOrderMapper.selectList(queryWrapper);
        log.info("医院id:{},医生id:{}查询到的问诊中订单为:{}条", hospitalId, doctorId, consultationOrders.size());
        return !consultationOrders.isEmpty();
    }

    @Override
    public Boolean verifyDoctorIsExist(VerifyDoctorDTO dto) {
        dto.setPhoneNumber(DESUtil.encrypt(dto.getPhoneNumber()));
        dto.setIdCard(DESUtil.encrypt(dto.getIdCard()));
        long count = busDoctorMapper.verifyDoctorIsExist(dto);
        return count > 0;
    }

    @Override
    public List<BusDoctor> listHospPhy(Long hospitalId) {
        List<String> list = Lists.newArrayList(FiveRoleEnum.PSYCHOLOGY.getCode(), FiveRoleEnum.NURSE.getCode(),
                FiveRoleEnum.HEALTH_MANAGE.getCode(), FiveRoleEnum.PSYCHOLOGY_CONSULT.getCode(),
                FiveRoleEnum.REHABILITATION_SPECIALIST.getCode(), FiveRoleEnum.DIETITIAN.getCode());
        List<BusDoctorHospital> dhList = busDoctorHospitalMapper.selectList(new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, hospitalId)
                .eq(BusDoctorHospital::getStatus, YesNoEnum.YES.getCode())
                .in(BusDoctorHospital::getRole, list));
        List<Long> docIds = new ArrayList<>();
        dhList.forEach(item -> docIds.add(item.getDoctorId()));
        if (CollUtil.isEmpty(docIds)) {
            return new ArrayList<>();
        }
        return busDoctorMapper.selectBatchIds(docIds);
    }

    /**
     * 根据科室查询机构医生
     *
     * @param dto
     * @return
     */
    @Override
    public List<BusDoctor> selectDoctorByDepId(BusPopularColumnDto dto) {
        return busDoctorMapper.selectDoctorByDepId(dto);
    }
}

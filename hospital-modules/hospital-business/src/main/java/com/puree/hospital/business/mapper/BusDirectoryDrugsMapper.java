package com.puree.hospital.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO;
import com.puree.hospital.business.domain.BusDirectoryDrugs;
import com.puree.hospital.business.domain.BusDrugs;
import com.puree.hospital.business.domain.dto.BusDirectoryDrugQueryDTO;
import com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo;
import com.puree.hospital.business.importDrugs.domain.ImportDrugTaskParamDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusDirectoryDrugsMapper extends BaseMapper<BusDirectoryDrugs> {

    /**
     * 查询目录药品列表
     *
     * @param bo    查询条件
     * @return 目录药品列表
     */
    List<BusDirectoryDrugsVo> selectDirectoryDrugsList(BusDirectoryDrugsDTO bo);

    /**
     * 查询未关联目录的药品列表
     *
     * @param dto 查询条件
     * @return 未关联目录的药品列表
     */
    List<BusDirectoryDrugsVo> selectNotAssociateList(BusDirectoryDrugsDTO dto);

    /**
     * 查询中药目录
     *
     * @param dto 查询条件
     * @return 中药目录
     */
    List<BusDirectoryDrugsVo> tcmDirectoryDrugsList (BusDirectoryDrugsDTO dto);

    /**
     * 查询中药【未关联目录】的药品列表
     *
     * @param dto 查询条件
     * @return 中药药品列表
     */
    List<BusDirectoryDrugsVo> tcmNotAssociateList(BusDirectoryDrugsDTO dto);

    /**
     * 批量插入
     *
     * @param list 药品目录
     * @return 插入行数
     */
    int batchInsert(List<BusDirectoryDrugs> list);

    /**
     * 查询中药药品列表
     *
     * @param dto 查询条件
     * @return 中药药品目录
     */
    List<BusDirectoryDrugsVo> tcmDrugsList(BusDirectoryDrugsDTO dto);

    /**
     * 查询中药目录药品信息
     *
     * @param id          中药id
     * @param hospitalId  医院id
     * @return 中药药品信息
     */
    BusDirectoryDrugsVo tcmDrugsById(@Param("id") Long id,@Param("hospitalId") Long hospitalId);

    /**
     * 查询药品是否存在目录中
     *
     * @param hospitalId    医院id
     * @param directoryType 目录类型
     * @param drugsIds      药品id列表
     * @return 已存在的药品列表
     */
    List<BusDirectoryDrugs> selectDirectoryDrugs(@Param("hospitalId") Long hospitalId,
                                                 @Param("directoryType") String directoryType,
                                                 @Param("drugsIds") List<Long> drugsIds);

    /**
     * 过滤出未同步医院的药品id列表
     *
     * @param hospitalId    医院id列表
     * @param directoryType 目录类型
     * @param drugsIds      药品id列表
     * @return 过滤出的药品id列表
     */
    List<BusDrugs> getUnSyncDrugIds(@Param("hospitalId") Long hospitalId,
                                    @Param("directoryType") String directoryType,
                                    @Param("drugsIds") List<Long> drugsIds);

    /**
     * 批量绑定药品目录
     *
     * @param paramDTO 药品目录绑定参数
     */
    void drugsBindingDirectory(ImportDrugTaskParamDTO paramDTO);

    /**
     * 根据医院id更新药品目录状态
     * @param hospitalId 医院id
     */
    void updateBatchDirectoryStatus(@Param("hospitalId") Long hospitalId, @Param("directoryStatus") Integer directoryStatus,
                                    @Param("directoryType") String directoryType);

    /**
     *  查询药品列表
     * @param dto 查询参数
     * @return 药品信息
     */
    List<BusDirectoryDrugsVo> selectDrugsList(BusDirectoryDrugsDTO dto);
}

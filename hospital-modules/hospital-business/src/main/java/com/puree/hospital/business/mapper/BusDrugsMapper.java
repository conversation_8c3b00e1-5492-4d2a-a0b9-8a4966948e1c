package com.puree.hospital.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.business.domain.BusDrugs;
import com.puree.hospital.business.domain.ESDrugs;
import com.puree.hospital.business.domain.dto.BusChineseDrugDTO;
import com.puree.hospital.business.domain.vo.BusDrugsVo;
import com.puree.hospital.business.domain.vo.BusEnterpriseDrugsVO;
import com.puree.hospital.business.domain.vo.DepartmentInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 药品Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
public interface BusDrugsMapper extends BaseMapper<BusDrugs> {
    /**
     * 查询药品
     *
     * @param id 药品ID
     * @return 药品
     */
    BusDrugsVo selectBusDrugsById(Long id);

    /**
     * 查询药品列表
     *
     * @param busDrugs 药品
     * @return 药品集合
     */
    List<BusDrugsVo> selectBusDrugsList(BusDrugs busDrugs);

    /**
     * 查询药品列表
     *
     * @param busDrugs 药品
     * @return 药品集合
     */
    List<BusDrugsVo> selBusDrugsConnectMultiTableList(BusDrugs busDrugs);

    /**
     * 查询医院药品列表
     *
     * @param busDrugs 药品
     * @return 药品集合
     */
    List<BusDrugsVo> selBusDrugsWithHospitalId(BusDrugs busDrugs);

    /**
     * 查询厂商药品列表
     *
     * @param busDrugs 药品
     * @return 药品集合
     */
    List<BusEnterpriseDrugsVO> selectBusDrugsAndEnterpriseList(BusDrugs busDrugs);

    /**
     * 新增药品
     *
     * @param busDrugs 药品
     * @return 结果
     */
    Integer insertBusDrugs(BusDrugs busDrugs);

    /**
     * 修改药品
     *
     * @param busDrugs 药品
     * @return 结果
     */
    int updateBusDrugs(BusDrugs busDrugs);

    /**
     * 删除药品
     *
     * @param id 药品ID
     * @return 结果
     */
    int deleteBusDrugsById(Long id);

    /**
     * 批量删除药品
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteBusDrugsByIds(Long[] ids);

    /**
     * 批量新增
     *
     * @param drugs 药品
     * @return 新增结果
     */
    int batchInsertDrugs(List<BusDrugs> drugs);

    /**
     * 通过批准文号查询es药品
     *
     * @param nmpn 批准文号
     * @return es药品
     */
    ESDrugs selectEsDrugs(String nmpn);

    /**
     * 通过药品名查询es药品
     *
     * @param productName 产品名
     * @return es药品集合
     */
    List<ESDrugs> selectEsDrugsByProductName(String productName);

    /**
     * 中药饮片列表
     *
     * @param dto 筛选条件
     * @return 中药饮片集合
     */
    List<BusDrugsVo> listChineseDrugs(BusChineseDrugDTO dto);

    /**
     * 根据药品id查询关联科室信息
     *
     * @param drugsId 药品id
     * @return 药品关联科室集合
     */
    List<DepartmentInfoVO> selectDepartmentByDrugId(@Param("drugsId") Long drugsId) ;

    /**
     * 药品草稿根据药品id查询
     *
     * @param drugsId 药品id
     * @param count   查询的数量
     * @return 药品集合
     */
    List<BusDrugsVo> selectDraftDrugsByDrugsId(@Param("drugsId") Long drugsId, @Param("count") Integer count);

    /**
     * 查询药品关联的科室信息
     *
     * @param departmentIdList 科室id集合
     * @return 药品关联的科室信息
     */
    List<DepartmentInfoVO> selectDepartmentForDraftDrug(@Param("departmentIdList") List<Long> departmentIdList) ;

    /**
     * 根据药品条件查询药品id列表
     * @param busDrug 药品条件
     * @return 药品id列表
     */
    List<Long> selectIdsByBusDrug(BusDrugs busDrug);
}

package com.puree.hospital.business.task.his;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.business.domain.BusDepartment;
import com.puree.hospital.business.domain.BusDepartmentDrugs;
import com.puree.hospital.business.domain.BusDirectoryDrugs;
import com.puree.hospital.business.domain.BusDrugs;
import com.puree.hospital.business.domain.BusHospitalOfficina;
import com.puree.hospital.business.domain.MiDrugsDirectory;
import com.puree.hospital.business.mapper.BusDepartmentDrugsMapper;
import com.puree.hospital.business.mapper.BusDepartmentMapper;
import com.puree.hospital.business.mapper.BusDirectoryDrugsMapper;
import com.puree.hospital.business.mapper.BusDrugsMapper;
import com.puree.hospital.business.mapper.BusHospitalOfficinaMapper;
import com.puree.hospital.business.mapper.MiDrugsDirectoryMapper;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.his.api.constant.BaseHisEnum;
import com.puree.hospital.his.api.constant.enums.HisDosageFormEnum;
import com.puree.hospital.his.api.constant.enums.HisDrugClassifyEnum;
import com.puree.hospital.his.api.constant.enums.HisDrugPackUnitEnum;
import com.puree.hospital.his.api.constant.enums.HisDrugTypeEnum;
import com.puree.hospital.his.api.constant.enums.HisEfficacyClassifyEnum;
import com.puree.hospital.his.api.constant.enums.HisInsuranceTypeEnum;
import com.puree.hospital.his.api.constant.enums.HisPrescriptionTypeEnum;
import com.puree.hospital.his.api.entity.HisDrugInfo;
import com.puree.hospital.his.api.entity.query.HisDrugInfoQuery;
import com.puree.hospital.his.api.feign.RemoteHisBaseInfoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * HIS 药品同步
 * <AUTHOR>
 * @date 2025/2/20 14:52
 */

@RefreshScope
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DrugsSyncTask {

    private final BusDrugsMapper busDrugsMapper;
    private final BusDirectoryDrugsMapper busDirectoryDrugsMapper;
    private final BusHospitalOfficinaMapper busHospitalOfficinaMapper;
    private final RemoteHisBaseInfoService remoteHisBaseInfoService;
    private final BusDepartmentDrugsMapper busDepartmentDrugsMapper;
    private final BusDepartmentMapper busDepartmentMapper;
    private final MiDrugsDirectoryMapper miDrugsDirectoryMapper;
    private static final int PAGE_SIZE = 300;
    private static final String OPERATOR = "task";
    @Value("${private-deployment.hospital-id:-1}")
    private Long hospitalId;

    @XxlJob("drugsSyncTask")
    public void drugsSync() {
        int pageNum = 1;
        HisDrugInfoQuery query = new HisDrugInfoQuery();
        query.setPageNumber(pageNum);
        query.setPageSize(PAGE_SIZE);

        List<Long> bizIds = bizList();
        AjaxResult<List<HisDrugInfo>> result;
        while(null != (result = remoteHisBaseInfoService.getDrugList(query))){
            if(!result.isSuccess()){
                XxlJobHelper.log("HIS药品同步失败，请求参数：{}， 错误信息：{}", query, result.getMsg());
                return;
            }
            List<HisDrugInfo> drugs = result.getData();
            XxlJobHelper.log("HIS药品同步，页码：{}，每页条数：{}，药品数量：{}", query.getPageNumber(), PAGE_SIZE, drugs.size());
            log.info("请求参数：{}，药品列表：{}", query, drugs);
            if(CollectionUtils.isEmpty(drugs)){
                return;
            }
            assembleDrugs(drugs, bizIds);
            query.setPageNumber(pageNum++);
        }
    }

    /**
     * 药品数据组装
     * @param drugs his药品列表
     */
    private void assembleDrugs(List<HisDrugInfo> drugs, List<Long> bizIds){
        List<BusDrugs> drugsList = new ArrayList<>();
        // 待修改的集合，待移除限制的医保编码集合
        List<MiDrugsDirectory> drugsDirectoryAddOrUpdateList = new ArrayList<>();
        List<String> removeLimitUseMiCodes = new ArrayList<>();
        for (HisDrugInfo drug : drugs) {
            BusDrugs busDrugs = new BusDrugs();
            busDrugs.setStandardCommonName(drug.getStandardCommonName());
            busDrugs.setDrugsManufacturer(drug.getDrugsManufacturer());
            busDrugs.setDrugsName(drug.getDrugsName());
            busDrugs.setReferenceSellingPrice(new BigDecimal(drug.getReferenceSellingPrice()));
            busDrugs.setDrugsStandardCode(drug.getDrugsStandardCode());
            busDrugs.setReferencePurchasePrice(new BigDecimal(drug.getReferencePurchasePrice()));
            busDrugs.setNmpn(drug.getNmpn());
            busDrugs.setPinyinCode(drug.getPinyinCode());
            busDrugs.setDrugsImg(drug.getDrugsImg());
            busDrugs.setDrugsDetails(drug.getDrugsDetails());
            //药理/功效分类-字典映射
            String code = getMappingCode(HisEfficacyClassifyEnum.class, drug.getEfficacyClassification(), null, drug);
            if(StringUtils.isNotEmpty(code)){
                busDrugs.setEfficacyClassification(Long.valueOf(code));
            }

            //处方标识-字典映射
            code = getMappingCode(HisPrescriptionTypeEnum.class, drug.getPrescriptionIdentification(), null, drug);
            if(StringUtils.isNotEmpty(code)){
                busDrugs.setPrescriptionIdentification(Long.valueOf(code));
            }
            //药品类型-字典映射
            code = getMappingCode(HisDrugTypeEnum.class, null, drug.getDrugsType(), drug);
            if(StringUtils.isEmpty(code)){
                continue;
            }
            busDrugs.setDrugsType(Long.valueOf(code));
            //药品剂型-字典映射
            code = getMappingCode(HisDosageFormEnum.class, drug.getDrugsDosageForm(), null, drug);
            if(StringUtils.isNotEmpty(code)){
                busDrugs.setDrugsDosageForm(Long.valueOf(code));
            }
            busDrugs.setDrugsSpecification(drug.getDrugsSpecification());
            //药品包装单位-字典映射
            code = getMappingCode(HisDrugPackUnitEnum.class, null, drug.getDrugsPackagingUnit(), drug);
            if(StringUtils.isNotEmpty(code)){
                busDrugs.setDrugsPackagingUnit(Long.valueOf(code));
            }
            //药品用法-字典映射
//            code = getMappingCode(HisUsageEnum.class, drug.getDrugsUsage(), null, drug);
//            if(StringUtils.isNotEmpty(code)){
//                busDrugs.setDrugsUsage(Long.valueOf(code));
//            }
            busDrugs.setDrugsUsage(StrUtil.isNotBlank(drug.getDrugsUsage()) ? Long.valueOf(drug.getDrugsUsage()) : null);
            busDrugs.setRecommendedDosage(drug.getRecommendedDosage());
            //医保类型-字典映射
            code = getMappingCode(HisInsuranceTypeEnum.class, drug.getMedicalInsuranceType(), null, drug);
            if(StringUtils.isNotEmpty(code)){
                busDrugs.setMedicalInsuranceType(Long.valueOf(code));
            }
            // 2025-07-10 药品状态同步his的药品状态
//            busDrugs.setStatus(YesNoEnum.YES.getCode());
            String status = drug.getStatus();
            busDrugs.setStatus(StrUtil.isNotBlank(status) ? Integer.valueOf(status) : YesNoEnum.NO.getCode());
            busDrugs.setCreateBy(OPERATOR);
            busDrugs.setCreateTime(new Date());
            //药品分类-字典映射
            code = getMappingCode(HisDrugClassifyEnum.class, null, drug.getClassifyId(), drug);
            if(StringUtils.isNotEmpty(code)){
                busDrugs.setClassifyId(Long.valueOf(code));
            }
            busDrugs.setType(YesNoEnum.NO.getCode());
            if(drug.getMinPackNum() != null){
                busDrugs.setMinPackNum(new BigDecimal(drug.getMinPackNum()).intValue());
            }
            busDrugs.setMinMakeUnit(drug.getMinMakeUnit());
            busDrugs.setMinPackUnit(drug.getMinPackUnit());
            busDrugs.setNationalDrugCode(drug.getNationalDrugCode());
            busDrugs.setOrigin(YesNoEnum.NO.getCode());
            busDrugs.setBaseDoseUnit(drug.getDoseUnit());
            if(drug.getBaseDose() != null){
                busDrugs.setBaseDose(new BigDecimal(drug.getBaseDose()));
            }
            busDrugs.setHisDrugsId(drug.getDrugsStandardCode());
            drugsList.add(busDrugs);

            // 处理医保药品目录表 当医保限制用药时，更新进医保药品目录表
            if (Objects.nonNull(drug.getLimitFlag())) {
                int intValue = drug.getLimitFlag().intValue();
                // 添加限制
                if (Objects.equals(intValue, 1) && StringUtils.isNotBlank(drug.getLimitExplan())) {
                    MiDrugsDirectory miDrugsDirectory = new MiDrugsDirectory();
                    miDrugsDirectory.setMiCode(drug.getNationalDrugCode());
                    miDrugsDirectory.setLimitUse(drug.getLimitExplan());
                    drugsDirectoryAddOrUpdateList.add(miDrugsDirectory);
                // 去除限制
                } else if (Objects.equals(intValue, 0)) {
                    removeLimitUseMiCodes.add(drug.getNationalDrugCode());
                }
            }
        }
        saveDrugs(drugs, drugsList, bizIds);

        // 处理医保药品目录
        saveMiDrugsDirectory(drugsDirectoryAddOrUpdateList, removeLimitUseMiCodes);
    }

    /**
     *  同步医保药品目录
     * @param drugsDirectoryList  组装的医保药品目录数据
     */
    private void saveMiDrugsDirectory(List<MiDrugsDirectory> drugsDirectoryList, List<String> removeLimitUseMiCodes) {
        try {
            if (!CollectionUtils.isEmpty(drugsDirectoryList)) {
                // 新增或修改进目录数据
                int count = miDrugsDirectoryMapper.insertOrUpdateBatch(drugsDirectoryList);
                log.info("同步医保药品限制用药插入结果：{}，插入条数：{}", count, drugsDirectoryList.size());
            }
            if (!CollectionUtils.isEmpty(removeLimitUseMiCodes)) {
                // 移除的限制
                int i = miDrugsDirectoryMapper.removeLimitUseByMiCodes(removeLimitUseMiCodes);
                log.info("同步医保药品限制用药移除限制更新条数：{}", i);
            }
        } catch (Exception e) {
            log.info("处理医保药品限制用药异常", e);
        }
    }

    /**
     * 药品信息同步
     * @param drugs HIS药品列表
     * @param drugsList 药品信息组装列表
     * @param bizIds 科室id列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDrugs(List<HisDrugInfo> drugs, List<BusDrugs> drugsList, List<Long> bizIds){
        int count = busDrugsMapper.insertOrUpdateBatch(drugsList);
        log.info("药品批量插入结果：{}, 插入条数：{}", count, drugsList.size());

        //查询药品主键和drugsId的映射关系
        LambdaQueryWrapper<BusDrugs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BusDrugs::getId, BusDrugs::getHisDrugsId);
        queryWrapper.in(BusDrugs::getHisDrugsId, drugsList.stream().map(BusDrugs::getHisDrugsId).collect(Collectors.toList()));
        List<BusDrugs> busDrugsList = busDrugsMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(busDrugsList)){
            return;
        }
        List<BusDirectoryDrugs> directoryDrugs = new ArrayList<>();
        for (BusDrugs busDrugs : busDrugsList) {
            BusDirectoryDrugs directoryDrug = new BusDirectoryDrugs();
            directoryDrug.setDirectoryType(CodeEnum.YES.getCode());
            directoryDrug.setDrugsId(busDrugs.getId());
            directoryDrug.setHospitalId(hospitalId);
            directoryDrug.setCreateTime(DateUtils.getNowDate());
            directoryDrug.setCreateBy(OPERATOR);
            directoryDrugs.add(directoryDrug);
        }
        //院内目录批量插入
        count = busDirectoryDrugsMapper.insertOrUpdateBatch(directoryDrugs);
        log.info("院内目录批量插入结果：{}, 插入条数：{}", count, directoryDrugs.size());

        List<BusDirectoryDrugs> directoryDrugsList = busDirectoryDrugsMapper.selectList(new LambdaQueryWrapper<BusDirectoryDrugs>()
                .eq(BusDirectoryDrugs::getDirectoryType, CodeEnum.YES.getCode())
                .eq(BusDirectoryDrugs::getHospitalId, hospitalId)
                .in(BusDirectoryDrugs::getDrugsId, busDrugsList.stream().map(BusDrugs::getId).collect(Collectors.toList())));
        if(CollectionUtils.isEmpty(directoryDrugsList)){
            return;
        }
        //我方药品id与HIS药品记录映射关系转换
        Map<String, HisDrugInfo> drugsStandardCodeMap = drugs.stream().collect(Collectors.toMap(HisDrugInfo::getDrugsStandardCode, Function.identity()));
        Map<Long, HisDrugInfo> drugsIdMap = busDrugsList.stream().collect(Collectors.toMap(BusDrugs::getId, item -> drugsStandardCodeMap.get(item.getHisDrugsId())));
        List<BusHospitalOfficina> hospitalOfficinas = new ArrayList<>();
        for (BusDirectoryDrugs directoryDrug : directoryDrugsList) {
            BusHospitalOfficina officina = new BusHospitalOfficina();
            officina.setStatus(YesNoEnum.YES.getCode());
            HisDrugInfo hisDrugInfo = drugsIdMap.get(directoryDrug.getDrugsId());
            if(hisDrugInfo != null && hisDrugInfo.getSellingPrice() != null && hisDrugInfo.getStock() != null){
                officina.setSellingPrice(new BigDecimal(hisDrugInfo.getSellingPrice()));
                officina.setStock(new BigDecimal(hisDrugInfo.getStock()).intValue());
            }else{
                officina.setSellingPrice(new BigDecimal(0));
                officina.setStock(0);
            }
            officina.setDrugsId(directoryDrug.getDrugsId());
            officina.setDirectoryId(directoryDrug.getId());
            officina.setHospitalId(hospitalId);
            officina.setCreateTime(DateUtils.getNowDate());
            officina.setCreateBy(OPERATOR);
            hospitalOfficinas.add(officina);
        }
        //绑定药房批量插入
        count = busHospitalOfficinaMapper.insertOrUpdateBatch(hospitalOfficinas);
        log.info("绑定药房批量插入结果：{}, 插入条数：{}", count, hospitalOfficinas.size());

        //科室绑定
        busDrugsList.forEach(item -> addDepartment(item.getId(), bizIds));
    }

    /**
     * code获取
     * @param enumClass 枚举类
     * @param mapping HIS方字典code
     * @param name HIS方字典名称
     * @param drug HIS药品信息
     * @return code
     */
    private String getMappingCode(Class enumClass, String mapping, String name, HisDrugInfo drug){
        if(StringUtils.isEmpty(mapping) && StringUtils.isEmpty(name)){
            log.error("没有对应的映射：{}，{}", enumClass.getName(), drug);
            return null;
        }
        String code = null;
        try{
            if(StringUtils.isNotEmpty(mapping)){
                code = BaseHisEnum.mappingToCode(enumClass, mapping);
            }else {
                code = BaseHisEnum.nameToCode(enumClass, name);;
            }
        }catch (Exception e){
            log.error("没有对应的映射：{}，{}，{}，异常信息：", enumClass.getName(), mapping, name, e);
        } finally {
            return code;
        }

    }

    /**
     * 获取科室id列表
     * @return 科室id列表
     */
    private List<Long> bizList(){
        List<BusDepartment> bizList = busDepartmentMapper.selectList(Wrappers.lambdaQuery());
        if(CollectionUtils.isEmpty(bizList)){
            return null;
        }
        return bizList.stream().map(BusDepartment::getId).collect(Collectors.toList());
    }

    /**
     * 药品-科室绑定
     * @param drugsId 药品id
     * @param departmentIds 科室id列表
     * @return 影响行数
     */
    private int addDepartment(Long drugsId, List<Long> departmentIds) {
        if(CollectionUtils.isEmpty(departmentIds)){
            return 0;
        }
        busDepartmentDrugsMapper.deleteBusDepartmentDrugsByDrugsId(drugsId);
        List<BusDepartmentDrugs> list = new ArrayList<>();
        for (Long departmentId : departmentIds) {
            BusDepartmentDrugs departmentDrugs = new BusDepartmentDrugs();
            departmentDrugs.setDrugsId(drugsId);
            departmentDrugs.setDepartmentId(departmentId);
            list.add(departmentDrugs);
        }
        return busDepartmentDrugsMapper.batchInsert(list);
    }
}

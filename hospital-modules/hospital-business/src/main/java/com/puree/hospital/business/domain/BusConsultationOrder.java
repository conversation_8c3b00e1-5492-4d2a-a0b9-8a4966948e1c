package com.puree.hospital.business.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.utils.excel.converter.order.ConsultationOrderSexConverter;
import com.puree.hospital.common.core.utils.excel.converter.order.OrderStatusConverter;
import com.puree.hospital.common.core.utils.excel.converter.order.OrderTypeConverter;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 问诊订单表
 */
@ExcelIgnoreUnannotated
@Data
public class BusConsultationOrder extends Entity {

    /** 问诊ID */
    private Long consultationId;
    /** 订单编号 */
    @ColumnWidth(28)
    @ExcelProperty(value = "订单编号",index = 0)
    private String orderNo;
    /** 订单状态（0待支付,1待接诊 未使用,2问诊中 使用中, 3问诊中 已开处方,4已完成 已使用,5已失效,6已退号,7已退款,8已取消,9退款中） */
    @ColumnWidth(18)
    @ExcelProperty(value = "订单状态",index = 9,converter = OrderStatusConverter.class)
    private String status;
    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(28)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "下单时间",index = 2)
    private Date orderTime;
    /** 订单金额 */
    @ColumnWidth(10)
    @ExcelProperty(value = "订单金额",index = 8)
    private String amount;
    /** 订单类型（0:图文;1:视频） */
    @ColumnWidth(18)
    @ExcelProperty(value = "订单类型",index = 1,converter = OrderTypeConverter.class)
    private String orderType;
    /** 问诊人ID */
    private Long familyId;
    /** 问诊人姓名 */
    @ColumnWidth(18)
    @ExcelProperty(value = "问诊人姓名",index = 5)
    private String familyName;
    /** 问诊人性别 */
    @ColumnWidth(18)
    @ExcelProperty(value = "问诊人性别",index = 6,converter = ConsultationOrderSexConverter.class)
    private String familySex;
    /** 问诊人年龄 */
    @ColumnWidth(18)
    @ExcelProperty(value = "问诊人年龄",index = 7)
    private String familyAge;
    /** 医生ID */
    private Long doctorId;
    /** 医生照片 */
    private String photo;
    /** 医生姓名 */
    @ColumnWidth(18)
    @ExcelProperty(value = "医生姓名",index = 3)
    private String doctorName;
    /** 医生职称 */
    private String title;
    /** 科室名称 */
    @ColumnWidth(18)
    @ExcelProperty(value = "科室名称",index = 4)
    private String departmentName;
    /** 问诊回合 */
    private Integer round;
    /** 患者ID */
    private Long patientId;
    /** 科室ID */
    private Long departmentId;
    /** 医院ID */
    private Long hospitalId;
    /** 退号原因 */
    private String reason;
    /** 付款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;
    /**
     * 购买类型（0诊室内购买 1诊室外购买）
     */
    private String buyType;
    /**
     * 问诊类型（0挂科室 1挂医生）
     */
    private String consultationType;
    /**
     * 待接诊状态
     */
    private String preStatus;
    /**
     * 关注订单
     */
    private String follow;
    /**
     * 接诊/报到时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;
    /**
     * 视频问诊预约开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 视频问诊预约结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 视频问诊订单状态
     * 0待支付,1待接诊 2问诊中(未开处方), 3问诊中(已开处方),4已完成,5已失效,6已退号,7已退款,8已取消,9退款中,10已预约(未报到),11已预约(已报到),12已过号
     */
    private String videoStatus;
    /**
     * 机构code
     */
    private String partnersCode;
    /**
     * 机构名称
     */
    @TableField(exist = false)
    @ExcelProperty(value = "合作机构",index = 12)
    private String partnersName;
    /**
     * 叫号是否过号
     */
    private Integer isGuoHao;
    /**
     * 取消时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;
    /**
     * 1表示通过预审
     */
    private Integer pass;
    /**
     * 是否删除（0否 1是）
     */
    private String delStatus;
    /**
     * 支付方式 1-微信支付 2-通联支付
     */
    private String payWay;
    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    /**
     * 退号时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawalTime;
    /**
     * 被复诊的订单id
     */
    private Long furtherConsultationId;
    /**
     * 是否新订单（0否 1是）
     */
    private Integer newOrderOrNot;
    /**
     * 经纪人id
     */
    @TableField(exist = false)
    private Long agentId;
    /**
     * 经纪人名称
     */
    @ColumnWidth(18)
    @ExcelProperty(value = "经纪人",index = 10)
    @TableField(exist = false)
    private String agentName;
    /**
     * 合作渠道id
     */
    @TableField(exist = false)
    private Long partnerId;
    /**
     * 合作渠道名称
     */
    @ColumnWidth(18)
    @ExcelProperty(value = "合作渠道",index = 11)
    @TableField(exist = false)
    private String partnerName;
    /**
     * 页码
     */
    @TableField(exist = false)
    private Integer startNum;
    /**
     * 多少个
     */
    @TableField(exist = false)
    private Integer pageSize;
    /**
     * 1-下单时间 2支付时间  3完成时间
     */
    @TableField(exist = false)
    private Integer timeType;
    /**
     *下单时间
     */
    @TableField(exist = false)
    private List<String> orderTimeList;
    /**
     * 完成时间
     */
    @TableField(exist = false)
    private List<String> completeTimeList;
    /**
     * 支付时间
     */
    @TableField(exist = false)
    private List<String> paymentTimeList;
    /**
     * 开始时间
     */
    @TableField(exist = false)
    private String timeStart;
    /**
     * 结束时间
     */
    @TableField(exist = false)
    private String timeEnd;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 发票状态, 参考 InvoiceStatusEnum.java
     */
    private Integer invoiceStatus;

    /**
     * 发票类型 1 税控盘 2 全电  参考 InvoiceTypeEnum.java
     */
    private Integer invoiceType ;

    /**
     * 发票代码,   发蓝色发票返回此字段，用于开具红色发票
     */
    @TableField(exist = false)
    private String invoiceCode ;

    /**
     * 发票号码,   发蓝色发票返回此字段，用于开具红色发票
     */
    @TableField(exist = false)
    private String invoiceNo ;

    /**
     * 发票下载地址
     */
    @TableField(exist = false)
    private String downloadUrl ;

    /**
     * 发票预览地址
     */
    @TableField(exist = false)
    private String picUrl ;

    /**
     * 开具发票失败错误码
     */
    @TableField(exist = false)
    private String invoiceErrorCode ;

    /**
     * 开具发票失败错误信息
     */
    @TableField(exist = false)
    private String invoiceErrorMsg ;

    private Long invoiceHeaderId;

    /**
     * 1-患者支付 ，2-医生赠送， 3-健康解读报告
     */
    private Integer payType ;

    /**
     * 客户端类型
     * @see ClientTypeEnum
     * */
    @TableField(exist = false)
    private String clientType;

}

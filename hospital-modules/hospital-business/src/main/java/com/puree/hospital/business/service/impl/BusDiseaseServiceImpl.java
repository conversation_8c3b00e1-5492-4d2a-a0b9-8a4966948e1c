package com.puree.hospital.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.business.api.model.DiagnosisVO;
import com.puree.hospital.business.api.model.dto.DiagnosisDTO;
import com.puree.hospital.business.domain.BusDisease;
import com.puree.hospital.business.infrastructure.utils.SysNumberGenerator;
import com.puree.hospital.business.mapper.BusDiseaseMapper;
import com.puree.hospital.business.service.IBusDiseaseService;
import com.puree.hospital.common.core.constant.SysNumConstants;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 西医病种信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-15
 */
@Service
public class BusDiseaseServiceImpl implements IBusDiseaseService {
    private final BusDiseaseMapper busDiseaseMapper;
    private final SysNumberGenerator sysNumberGenerator;

    @Autowired
    public BusDiseaseServiceImpl(BusDiseaseMapper busDiseaseMapper, SysNumberGenerator sysNumberGenerator) {
        this.busDiseaseMapper = busDiseaseMapper;
        this.sysNumberGenerator = sysNumberGenerator;
    }

    /**
     * 查询病种信息
     *
     * @param id 西医病种ID
     * @return
     */
    @Override
    public BusDisease selectBusDiseaseById(Long id) {
        return busDiseaseMapper.selectById(id);
    }

    /**
     * 查询西医病种信息列表
     *
     * @param busDisease 病种信息
     * @return 病种信息
     */
    @Override
    public List<BusDisease> selectBusDiseaseList(BusDisease busDisease) {
        QueryWrapper<BusDisease> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .like(StringUtils.isNotEmpty(busDisease.getDiseaseName()), "disease_name", busDisease.getDiseaseName())
                .eq(Objects.nonNull(busDisease.getStatus()), "status", busDisease.getStatus())
                .eq(Objects.nonNull(busDisease.getSupportMiPayment()), "support_mi_payment", busDisease.getSupportMiPayment());
        return busDiseaseMapper.selectList(queryWrapper);
    }

    /**
     * 新增病种信息
     *
     * @param busDisease 病种信息
     * @return 结果
     */
    @Override
    public int insertBusDisease(BusDisease busDisease) {
        // 校验限定参数
        checkSupportMiPaymentAndUnSupportReason(busDisease);
        busDisease.setCreateTime(DateUtils.getNowDate());
        String number = sysNumberGenerator.get(SysNumConstants.BZ_NUM_KEY);
        busDisease.setDiseaseNumber(number);
        return busDiseaseMapper.insert(busDisease);
    }


    /**
     *  校验是否支持医保和不支持医保原因
     * @param busDisease   参数
     */
    private void checkSupportMiPaymentAndUnSupportReason(BusDisease busDisease) {
        if (Objects.isNull(busDisease.getSupportMiPayment())) {
            throw new ServiceException("是否支持医保参数不能为空");
        } else if (Objects.equals(busDisease.getSupportMiPayment(), 0) && StrUtil.isBlank(busDisease.getUnsupportedReason())) {
            throw new ServiceException("不支持医保原因不能为空");
        }
    }

    /**
     * 修改病种信息
     *
     * @param busDisease 病种信息
     * @return 结果
     */
    @Override
    public int updateBusDisease(BusDisease busDisease) {
        // 校验限定参数
        checkSupportMiPaymentAndUnSupportReason(busDisease);
        busDisease.setUpdateTime(DateUtils.getNowDate());
        return busDiseaseMapper.updateById(busDisease);
    }

    /**
     * 批量删除病种信息
     *
     * @param ids 需要删除的病种信息ID
     * @return 结果
     */
    @Override
    public int deleteBusDiseaseByIds(Long[] ids) {
        return busDiseaseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 校验病种名称是否重复
     *
     * @param id
     * @param diseaseName
     * @return
     */
    @Override
    public boolean checkDuplicateName(Long id, String diseaseName) {
        QueryWrapper<BusDisease> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(diseaseName), "disease_name", diseaseName);
        BusDisease busDisease = busDiseaseMapper.selectOne(queryWrapper);
        return check(id, busDisease);
    }

    /**
     * 校验ICD-10编码是否重复
     *
     * @param id
     * @param icdCode
     * @return
     */
    @Override
    public boolean checkDuplicateIcd(Long id, String icdCode) {
        QueryWrapper<BusDisease> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(icdCode), "icd_code", icdCode);
        BusDisease busDisease = busDiseaseMapper.selectOne(queryWrapper);
        return check(id, busDisease);
    }

    /**
     * 西医病种停用启用操作
     *
     * @param busDisease
     * @return
     */
    @Override
    public int lock(BusDisease busDisease) {
        busDisease.setUpdateTime(DateUtils.getNowDate());
        return busDiseaseMapper.updateById(busDisease);
    }

    /**
     * 查询标准病种信息
     *
     * @param icdCodes
     * @return
     */
    @Override
    public List<BusDisease> selectDiseaseList(List<String> icdCodes) {
        return busDiseaseMapper.selectDiseaseList(icdCodes);
    }

    /**
     *  查询不支持医保支付的诊断列表
     * @param diagnosisDTO  入参
     * @return  不支持医保支付的诊断列表
     */
    @Override
    public List<DiagnosisVO> getDiagnosisListByIds(DiagnosisDTO diagnosisDTO) {
        LambdaQueryWrapper<BusDisease> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BusDisease::getStatus, EnableStatusEnum.ENABLED.getStatus())
                .eq(BusDisease::getSupportMiPayment, diagnosisDTO.getType())
                .in(BusDisease::getId, diagnosisDTO.getDiagnosisIds());
        List<BusDisease> busDiseases = busDiseaseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(busDiseases)) {
            return new ArrayList<>();
        }
        return busDiseases.stream().map(item -> {
            DiagnosisVO diagnosisListVO = new DiagnosisVO();
            diagnosisListVO.setId(item.getId())
                    .setNumber(item.getDiseaseNumber())
                    .setName(item.getDiseaseName())
                    .setPinYinCode(item.getPinYinCode())
                    .setIcd10Code(item.getIcdCode())
                    .setSupportMiPayment(item.getSupportMiPayment())
                    .setUnsupportedReason(item.getUnsupportedReason())
                    .setPrescriptionType(diagnosisDTO.getPrescriptionType());
            return diagnosisListVO;
        }).collect(Collectors.toList());
    }


    /**
     *  根据Ids查询病种
     * @param diseaseIds 病种id
     * @return  病种列表
     */
    @Override
    public List<BusDisease> queryDiseaseByIds(List<Long> diseaseIds) {
        // 查询疾病数据
        LambdaQueryWrapper<BusDisease> queryWrapper = Wrappers.<BusDisease>lambdaQuery().in(BusDisease::getId, diseaseIds);
        return busDiseaseMapper.selectList(queryWrapper);
    }

    /**
     * 校验新增还是修改
     *
     * @param id
     * @param busDisease
     * @return
     */
    private boolean check(Long id, BusDisease busDisease) {
        // 新增操作
        if (Objects.isNull(id) && Objects.nonNull(busDisease)) {
            return true;
        }
        // 修改操作
        if (Objects.nonNull(id) && Objects.nonNull(busDisease)) {
            return !Objects.equals(id, busDisease.getId());
        }
        return false;
    }
}

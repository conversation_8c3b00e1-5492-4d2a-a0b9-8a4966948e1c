package com.puree.hospital.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.business.domain.BusHospital;
import com.puree.hospital.business.domain.vo.BusHospitalVo;
import com.puree.hospital.business.domain.vo.HospitalComboboxVo;
import com.puree.hospital.business.domain.vo.HospitalVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusHospitalMapper extends BaseMapper<BusHospital> {

    int updateBusHospital(BusHospital busHospital);

    HospitalVo selectHospitalTel(Long hospitalId);

    List<BusHospitalVo> selectHospitalList(BusHospital busHospital);

    List<HospitalComboboxVo> selectHospitalCombobox();

    /**
     * 更新医院开启监管状态
     *
     * @param id     主键
     * @param status 状态 0-关 1-开
     * @return 更新结果
     */
    int updateRegulator(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 查询医院列表
     * @param busHospital
     * @return
     */
    List<BusHospital> selectBusHospitalList(BusHospital busHospital);
}

package com.puree.hospital.business.channel.commission.service.impl;

import com.puree.hospital.business.channel.commission.domain.bo.OrderSnapshotBO;
import com.puree.hospital.business.channel.commission.domain.vo.ChannelCommissionResult;
import com.puree.hospital.business.channel.commission.service.IChannelCommissionCalculationService;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 渠道佣金计算领域服务实现类 - 用于计算渠道佣金
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/07/24
 */
@Service
public class ChannelCommissionCalculationServiceImpl implements IChannelCommissionCalculationService {
    /**
     * 计算渠道佣金
     *
     * @param orderSnapshotBO 订单快照
     * @return 计算结果
     */
    @Override
    public ChannelCommissionResult calculateOriginalCommission(OrderSnapshotBO orderSnapshotBO) {
        ChannelCommissionResult channelCommissionResult = new ChannelCommissionResult();
        // 计算经纪人佣金
        channelCommissionResult.setAgentCommission(calculateAgentCommission(orderSnapshotBO));
        // 计算渠道商佣金
        channelCommissionResult.setChannelCommission(calculateChannelCommission(orderSnapshotBO));
        return channelCommissionResult;
    }

    /**
     * 计算差额退款渠道佣金
     *
     * @param orderSnapshotBO 订单快照
     * @return 计算结果
     */
    @Override
    public ChannelCommissionResult calculateAfterRefundCommission(OrderSnapshotBO orderSnapshotBO) {
        if (orderSnapshotBO == null) {
            throw new RuntimeException("订单快照不能为空");
        }
        ChannelCommissionResult channelCommissionResult = new ChannelCommissionResult();
        if (orderSnapshotBO.getRefundAmount() == null || orderSnapshotBO.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
            orderSnapshotBO.setRefundAmount(BigDecimal.ZERO);
        }
        // 计算经纪人原始佣金
        BigDecimal agentOriginalCommission = calculateAgentCommission(orderSnapshotBO);
        BigDecimal agentRefundCommission = calculateRefundCommission(orderSnapshotBO, agentOriginalCommission);
        // 计算经纪人佣金
        channelCommissionResult.setAgentCommission(agentOriginalCommission.subtract(agentRefundCommission).setScale(2, RoundingMode.HALF_UP));
        // 计算渠道商佣金
        BigDecimal channelOriginalCommission = calculateChannelCommission(orderSnapshotBO);
        BigDecimal channelRefundCommission = calculateRefundCommission(orderSnapshotBO, channelOriginalCommission);
        channelCommissionResult.setChannelCommission(channelOriginalCommission.subtract(channelRefundCommission).setScale(2, RoundingMode.HALF_UP));
        return channelCommissionResult;
    }

    /**
     * 计算退款的渠道佣金
     *
     * @param orderSnapshotBO 订单快照
     * @return 计算结果
     */
    @Override
    public ChannelCommissionResult calculateRefundCommission(OrderSnapshotBO orderSnapshotBO) {
        if (orderSnapshotBO == null) {
            throw new RuntimeException("订单快照不能为空");
        }
        ChannelCommissionResult channelCommissionResult = new ChannelCommissionResult();
        if (orderSnapshotBO.getRefundAmount() == null || orderSnapshotBO.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
            orderSnapshotBO.setRefundAmount(BigDecimal.ZERO);
        }
        // 计算经纪人佣金
        channelCommissionResult.setAgentCommission(calculateRefundCommission(orderSnapshotBO, calculateAgentCommission(orderSnapshotBO)));
        // 计算渠道商佣金
        channelCommissionResult.setChannelCommission(calculateRefundCommission(orderSnapshotBO, calculateChannelCommission(orderSnapshotBO)));
        return channelCommissionResult;
    }

    /**
     * 计算渠道商佣金
     * @param orderSnapshotBO 订单快照
     * @return 渠道商佣金
     */
    private BigDecimal calculateChannelCommission(OrderSnapshotBO orderSnapshotBO) {
        if (orderSnapshotBO == null || EnableStatusEnum.isDisabled(orderSnapshotBO.getPartnerStatusSnapshot()) ||
                orderSnapshotBO.getChannelCommissionRate() == null || BigDecimal.ZERO.compareTo(orderSnapshotBO.getChannelCommissionRate()) == 0) {
            return BigDecimal.ZERO;
        }
        // 计算渠道商佣金 = (渠道商佣金率 * 单类原始金额) / 100 然后四舍五入保留两位小数
        return orderSnapshotBO.getChannelCommissionRate()
                .multiply(orderSnapshotBO.getOriginalAmount())
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算渠道商佣金
     * @param orderSnapshotBO 订单快照
     * @return 渠道商佣金
     */
    private BigDecimal calculateAgentCommission(OrderSnapshotBO orderSnapshotBO) {
        if (orderSnapshotBO == null || EnableStatusEnum.isDisabled(orderSnapshotBO.getAgentStatusSnapshot()) ||
                orderSnapshotBO.getAgentCommissionRate() == null || BigDecimal.ZERO.compareTo(orderSnapshotBO.getAgentCommissionRate()) == 0) {
            return BigDecimal.ZERO;
        }
        // 计算渠道商佣金 = (渠道商佣金率 * 单类原始金额) / 100 然后四舍五入保留两位小数
        return orderSnapshotBO.getAgentCommissionRate()
                .multiply(orderSnapshotBO.getOriginalAmount())
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算退款佣金
     * @param orderSnapshotBO 订单快照
     * @param originalCommission 原始佣金
     * @return 退款的扣减佣金 - 正数
     */
    private BigDecimal calculateRefundCommission(OrderSnapshotBO orderSnapshotBO, BigDecimal originalCommission) {
        if (BigDecimal.ZERO.compareTo(orderSnapshotBO.getRefundAmount()) >= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal actualRefundAmount = orderSnapshotBO.getRefundAmount();
        if (orderSnapshotBO.getRefundAmount().compareTo(orderSnapshotBO.getOriginalAmount()) >= 0) {
            actualRefundAmount = orderSnapshotBO.getOriginalAmount();
        }
        // 计算渠道商佣金 = (原始返现佣金 * 退款金额) / 原价  然后四舍五入保留两位小数
        return originalCommission
                .multiply(actualRefundAmount)
                .divide(orderSnapshotBO.getOriginalAmount(), 2, RoundingMode.HALF_UP);
    }
}

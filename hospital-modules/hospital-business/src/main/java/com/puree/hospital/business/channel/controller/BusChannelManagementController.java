package com.puree.hospital.business.channel.controller;


import com.puree.hospital.business.domain.dto.BusPatientDto;
import com.puree.hospital.business.domain.dto.ChannelBindDTO;
import com.puree.hospital.business.domain.dto.QrCodeCreateDTO;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.business.channel.service.IBusChannelManagementApplicationService;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/***
 * 渠道医院用户控制器
 * 渠道身份相关公用接口
 * <AUTHOR>
 * @date 2025/06/23 16:56
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping("/channel")
public class BusChannelManagementController extends BaseController {
    @Resource
    public IBusChannelManagementApplicationService channelManagementApplicationService;

    /**
     * 获取渠道小程序码
     * 包含渠道推广码，客户邀约码
     *
     * @param qrCodeCreateDTO 二维码参数信息
     * @return 生成的二维码链接点至
     */
    @PostMapping("/getQrCode")
    @Log(title = "获取经纪人名片小程序码", businessType = BusinessType.OTHER)
    public R<?> getQrCode(@RequestBody QrCodeCreateDTO qrCodeCreateDTO) {
        return R.ok(channelManagementApplicationService.getQrCode(qrCodeCreateDTO));
    }

    /**
     * 解析小程序码参数
     * @param scene 二维码参数信息
     * @return 解析后的二维码对应人的身份信息
     */
    @GetMapping("/identityInfo-by-scene")
    @Log(title = "获取经纪人名片小程序码", businessType = BusinessType.OTHER)
    public R<?> identityInfoByScene(@RequestParam("scene") String scene) {
        return R.ok(channelManagementApplicationService.getIdentityInfoByScene(scene));
    }

    /**
     * 医院用户绑定渠道
     * @param channelBindDTO 渠道医院用户
     * @return 绑定结果
     */
    @PutMapping("/bind")
    @Log(title = "渠道/经纪人绑定", businessType = BusinessType.OTHER)
    public R<?> bindChannel(@RequestBody ChannelBindDTO channelBindDTO) {
        try {
            channelManagementApplicationService.bindChannel(channelBindDTO);
        } catch (ServiceException e) {
            log.warn("渠道/经纪人绑定失败", e);
            return R.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("渠道/经纪人绑定失败", e);
            return R.fail(e.getMessage());
        }
        return R.ok();
    }

    /**
     * 校验手机号唯一
     * @param hospitalId 医院ID
     * @param phoneNumber 手机号
     * @return 校验结果
     */
    @GetMapping("/check/identity-by-phone")
    @Log(title = "校验手机号唯一", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public AjaxResult<Boolean> checkPhone(@RequestParam("hospitalId") Long hospitalId,
                                          @RequestParam("phoneNumber") String phoneNumber) {
        return AjaxResult.success(channelManagementApplicationService.checkPhoneNumberIdentity(phoneNumber, hospitalId));
    }

    /**
     * 解绑经纪人和患者绑定关系
     * @param hospitalId 医院ID
     * @param phoneNumber 手机号
     * @return 解绑结果
     */
    @GetMapping("/unbind/agent")
    @PreAuthorize(hasPermi = "business:channel:add")
    public R<?> unBindAgent(@RequestParam("hospitalId") Long hospitalId,
                            @RequestParam("phoneNumber") String phoneNumber){
        return R.ok(channelManagementApplicationService.unbindAgent(hospitalId, phoneNumber));
    }

    @PutMapping("/update/binding")
    @Log(title = "修改患者绑定合伙人", businessType = BusinessType.UPDATE)
    @PreAuthorize(hasPermi = "business:binding:agent")
    public AjaxResult<?> updateBindingAgent(@RequestBody BusPatientDto dto) {
        return toAjax(channelManagementApplicationService.updateBindingAgent(dto));
    }

}

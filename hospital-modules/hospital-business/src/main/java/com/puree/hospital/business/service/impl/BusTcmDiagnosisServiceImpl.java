package com.puree.hospital.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.business.api.model.DiagnosisVO;
import com.puree.hospital.business.api.model.dto.DiagnosisDTO;
import com.puree.hospital.business.domain.BusTcmDiagnosis;
import com.puree.hospital.business.domain.dto.BusIcdTcmDiagnosisDto;
import com.puree.hospital.business.infrastructure.utils.SysNumberGenerator;
import com.puree.hospital.business.mapper.BusTcmDiagnosisMapper;
import com.puree.hospital.business.service.IBusTcmDiagnosisService;
import com.puree.hospital.common.core.constant.SysNumConstants;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 中医诊断信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-26
 */
@Service
public class BusTcmDiagnosisServiceImpl implements IBusTcmDiagnosisService {
    private final BusTcmDiagnosisMapper tcmDiagnosisMapper;
    private final SysNumberGenerator sysNumberGenerator;

    @Autowired
    public BusTcmDiagnosisServiceImpl(BusTcmDiagnosisMapper tcmDiagnosisMapper, SysNumberGenerator sysNumberGenerator) {
        this.tcmDiagnosisMapper = tcmDiagnosisMapper;
        this.sysNumberGenerator = sysNumberGenerator;
    }

    /**
     * 查询中医诊断信息
     *
     * @param id 中医诊断信息ID
     * @return 中医诊断信息
     */
    @Override
    public BusTcmDiagnosis selectBusTcmDiagnosisById(Long id) {
        return tcmDiagnosisMapper.selectById(id);
    }

    /**
     * 查询中医诊断信息列表
     *
     * @param busTcmDiagnosis 中医诊断信息
     * @return 中医诊断信息
     */
    @Override
    public List<BusTcmDiagnosis> selectBusTcmDiagnosisList(BusTcmDiagnosis busTcmDiagnosis) {
        QueryWrapper<BusTcmDiagnosis> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .like(StringUtils.isNotEmpty(busTcmDiagnosis.getTcmDiagnosis()), "tcm_diagnosis",
                        busTcmDiagnosis.getTcmDiagnosis())
                .eq(Objects.nonNull(busTcmDiagnosis.getStatus()), "status", busTcmDiagnosis.getStatus())
                .eq(Objects.nonNull(busTcmDiagnosis.getSupportMiPayment()), "support_mi_payment", busTcmDiagnosis.getSupportMiPayment());
        return tcmDiagnosisMapper.selectList(queryWrapper);
    }

    /**
     * 新增中医诊断信息
     *
     * @param busTcmDiagnosis 中医诊断信息
     * @return 结果
     */
    @Override
    public int insertBusTcmDiagnosis(BusTcmDiagnosis busTcmDiagnosis) {
        // 校验限定参数
        checkSupportMiPaymentAndUnSupportReason(busTcmDiagnosis);
        busTcmDiagnosis.setCreateTime(DateUtils.getNowDate());
        String number = sysNumberGenerator.get(SysNumConstants.ZY_NUM_KEY);
        busTcmDiagnosis.setTcmNumber(number);
        return tcmDiagnosisMapper.insert(busTcmDiagnosis);
    }

    /**
     *  校验是否支持医保和不支持医保原因
     * @param busTcmDiagnosis   参数
     */
    private void checkSupportMiPaymentAndUnSupportReason(BusTcmDiagnosis busTcmDiagnosis) {
        if (Objects.isNull(busTcmDiagnosis.getSupportMiPayment())) {
            throw new ServiceException("是否支持医保参数不能为空");
        } else if (Objects.equals(busTcmDiagnosis.getSupportMiPayment(), 0) && StrUtil.isBlank((busTcmDiagnosis.getUnsupportedReason()))) {
            throw new ServiceException("不支持医保原因不能为空");
        }
    }

    /**
     * 修改中医诊断信息
     *
     * @param busTcmDiagnosis 中医诊断信息
     * @return 结果
     */
    @Override
    public int updateBusTcmDiagnosis(BusTcmDiagnosis busTcmDiagnosis) {
        // 校验限定参数
        checkSupportMiPaymentAndUnSupportReason(busTcmDiagnosis);
        busTcmDiagnosis.setUpdateTime(DateUtils.getNowDate());
        return tcmDiagnosisMapper.updateById(busTcmDiagnosis);
    }

    /**
     * 批量删除中医诊断信息
     *
     * @param ids 需要删除的中医诊断信息ID
     * @return 结果
     */
    @Override
    public int deleteBusTcmDiagnosisByIds(Long[] ids) {
        return tcmDiagnosisMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 中医诊断停用启用操作
     *
     * @param busTcmDiagnosis
     * @return
     */
    @Override
    public int lock(BusTcmDiagnosis busTcmDiagnosis) {
        busTcmDiagnosis.setUpdateTime(DateUtils.getNowDate());
        return tcmDiagnosisMapper.updateById(busTcmDiagnosis);
    }

    /**
     * 校验病种名称是否重复
     *
     * @param id
     * @param tcmDiagnosis
     * @return
     */
    @Override
    public boolean checkTcmDiagnosis(Long id, String tcmDiagnosis) {
        QueryWrapper<BusTcmDiagnosis> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(tcmDiagnosis), "tcm_diagnosis", tcmDiagnosis);
        BusTcmDiagnosis busTcmDiagnosis = tcmDiagnosisMapper.selectOne(queryWrapper);
        return check(id, busTcmDiagnosis);
    }

    /**
     * 校验诊断代码是否重复
     *
     * @param id
     * @param diagnosisCode
     * @return
     */
    @Override
    public boolean checkDiagnosisCode(Long id, String diagnosisCode) {
        QueryWrapper<BusTcmDiagnosis> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(diagnosisCode), "diagnosis_code", diagnosisCode);
        BusTcmDiagnosis busTcmDiagnosis = tcmDiagnosisMapper.selectOne(queryWrapper);
        return check(id, busTcmDiagnosis);
    }

    /**
     * 获取ICD未关联的中医诊断列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<BusTcmDiagnosis> selectListNotAssociatedIcd(BusIcdTcmDiagnosisDto dto) {
        return tcmDiagnosisMapper.selectListNotAssociatedIcd(dto);
    }

    /**
     *  查询不支持医保支付的诊断列表
     * @param diagnosisDTO  入参
     * @return  不支持医保支付的诊断列表
     */
    @Override
    public List<DiagnosisVO> getDiagnosisListByIds(DiagnosisDTO diagnosisDTO) {
        LambdaQueryWrapper<BusTcmDiagnosis> queryWrapper = Wrappers.<BusTcmDiagnosis>lambdaQuery()
                .eq(BusTcmDiagnosis::getStatus, EnableStatusEnum.ENABLED.getStatus())
                .eq(BusTcmDiagnosis::getSupportMiPayment, diagnosisDTO.getType())
                .in(BusTcmDiagnosis::getId, diagnosisDTO.getDiagnosisIds());
        List<BusTcmDiagnosis> busTcmDiagnoses = tcmDiagnosisMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(busTcmDiagnoses)) {
            return Lists.newArrayList();
        }
        return busTcmDiagnoses.stream().map(item -> {
            DiagnosisVO diagnosisListVO = new DiagnosisVO();
            diagnosisListVO.setId(item.getId())
                    .setNumber(item.getTcmNumber())
                    .setName(item.getTcmDiagnosis())
                    .setPinYinCode(item.getPinYinCode())
//                    .setIcd10Code(item.getDiagnosisCode())
                    .setSupportMiPayment(item.getSupportMiPayment())
                    .setUnsupportedReason(item.getUnsupportedReason())
                    .setPrescriptionType(diagnosisDTO.getPrescriptionType());
            return diagnosisListVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据Ids查询诊断
     * @param diagnosisIds  诊断Id
     * @return  诊断列表
     */
    @Override
    public List<BusTcmDiagnosis> queryDiagnosisByIds(List<Long> diagnosisIds) {
        // 查询诊断数据
        LambdaQueryWrapper<BusTcmDiagnosis> queryWrapper = Wrappers.<BusTcmDiagnosis>lambdaQuery().in(BusTcmDiagnosis::getId, diagnosisIds);
        return tcmDiagnosisMapper.selectList(queryWrapper);
    }

    /**
     * 校验新增还是修改
     *
     * @param id
     * @param busTcmDiagnosis
     * @return
     */
    private boolean check(Long id, BusTcmDiagnosis busTcmDiagnosis) {
        // 新增操作
        if (Objects.isNull(id) && Objects.nonNull(busTcmDiagnosis)) {
            return true;
        }
        // 修改操作
        if (Objects.nonNull(id) && Objects.nonNull(busTcmDiagnosis)) {
            return !Objects.equals(id, busTcmDiagnosis.getId());
        }
        return false;
    }
}

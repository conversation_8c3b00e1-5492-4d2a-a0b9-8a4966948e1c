package com.puree.hospital.business.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.puree.hospital.common.api.annotation.Excel;
import com.puree.hospital.common.api.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;


/**
 * 药品对象 bus_drugs
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentRowHeight(20)
@HeadRowHeight(20)
@HeadStyle(shrinkToFit = BooleanEnum.TRUE, fillForegroundColor = 44)
public class BusDrugs extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ExcelProperty("药品ID")
    private Long id;

    /**
     * 药品编号
     */
    @Excel(name = "药品编号")
    private String drugsNumber;

    /**
     * 标准通用名
     */
    @Excel(name = "标准通用名")
    @ExcelProperty("通用名")
    @ColumnWidth(20)
    private String standardCommonName;

    /**
     * 药品制造商
     */
    @Excel(name = "药品制造商")
    @ExcelProperty("厂商")
    @ColumnWidth(25)
    private String drugsManufacturer;

    /**
     * 药品名称
     */
    @Excel(name = "药品名称")
    @ExcelProperty("商品名")
    @ColumnWidth(20)
    private String drugsName;

    /**
     * 参考售价
     */
    @Excel(name = "参考售价")
    @ExcelProperty("参考售价")
    @ColumnWidth(15)
    private BigDecimal referenceSellingPrice;

    /**
     * 药品本位码
     */
    @Excel(name = "药品本位码")
    @ExcelProperty("本位码")
    @ColumnWidth(17)
    private String drugsStandardCode;

    /**
     * 参考进价
     */
    @Excel(name = "参考进价")
    @ExcelProperty("供货价")
    @ColumnWidth(10)
    private BigDecimal referencePurchasePrice;

    /**
     * 国药准字(英文简写)
     */
    @Excel(name = "国药准字(英文简写)")
    @ExcelProperty("批准文号")
    @ColumnWidth(20)
    private String nmpn;

    /**
     * 拼音码
     */
    @Excel(name = "拼音码")
    @ExcelProperty("拼音码")
    @ColumnWidth(20)
    private String pinyinCode;

    /**
     * 药品图片
     */
    @Excel(name = "药品图片")
    private String drugsImg;

    /**
     * 药品详情
     */
    @Excel(name = "药品详情")
    private String drugsDetails;

    /**
     * 药理/功效分类(字典表关联)
     */
    @Excel(name = "药理/功效分类(字典表关联)")
    private Long efficacyClassification;

    /**
     * 处方标识(字典表关联)
     */
    @Excel(name = "处方标识(字典表关联)")
    private Long prescriptionIdentification;

    /**
     * 商品类型
     */
    @Excel(name = "商品类型")
    private Long drugsType;

    /**
     * 药品剂型(字典表关联)
     */
    @Excel(name = "药品剂型(字典表关联)")
    private Long drugsDosageForm;

    /**
     * 药品规格
     */
    @Excel(name = "药品规格")
    @ExcelProperty("规格")
    @ColumnWidth(15)
    private String drugsSpecification;

    /**
     * 药品包装单位(字典表关联)
     */
    @Excel(name = "药品包装单位(字典表关联)")
    private Long drugsPackagingUnit;

    /**
     * 药品用法(字典表关联)
     */
    @Excel(name = "药品用法 0 口服 1外服")
    private Long drugsUsage;

    /**
     * 建议用量
     */
    @Excel(name = "建议用量")
    @ExcelProperty("建议用量")
    @ColumnWidth(20)
    private String recommendedDosage;

    /**
     * 医保类型(字典表关联)
     */
    @Excel(name = "医保类型(字典表关联)")
    @ExcelProperty("医保类型")
    @ColumnWidth(15)
    private Long medicalInsuranceType;

    /**
     * 状态（0停用 1启用）
     */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private Integer status;

    /**
     * 是否删除（0否 1是）
     */
    private Integer delFlag;

    /**
     * 商品分类
     */
    @Excel(name = "药品分类")
    @ExcelProperty("药品分类")
    @ColumnWidth(15)
    private Long classifyId;

    /**
     * 药品类型 0西药 1中药
     */
    private Integer type;

    /**
     * 运营药品编号
     */
    private String yySeq;

    /**
     * 煎煮方法
     */
    private Long decoctingMethod;

    private String ypid;
    /**
     * 国标药品代码
     */
    @Excel(name = "医保编码")
    @ExcelProperty("医保编码")
    @ColumnWidth(25)
    private String nationalDrugCode;
    /**
     * 详细规格
     */
    private String detailedSpecifications;
    /**
     * 药品详情图
     */
    private String drugsImgDetail;
    /**
     * 药品主图
     */
    private String mainImg;
    /**
     * 最小包装数量
     */
    private Integer minPackNum;
    /**
     * 最小制剂单位
     */
    private String minMakeUnit;
    /**
     * 最小包装单位
     */
    private String minPackUnit;
    /**
     * icd-10编码
     */
    @ExcelProperty("icd编码")
    @ColumnWidth(10)
    private String icdCode;
    /**
     * 药房销售价
     */
    @TableField(exist = false)
    private BigDecimal sellingPrice;
    /**
     * 药房库存
     */
    @TableField(exist = false)
    private Integer stock;
    /**
     * 医院id
     */
    @TableField(exist = false)
    private Long hospitalId;

    /**
     * 批量药品ID
     */
    @TableField(exist = false)
    private List<Long> drugsIds;

    /**
     * classification 0是运行平台，1是医院后台导入药品
     */
    @TableField(exist = false)
    private String classification;

    /**
     * 科室id
     */
    @TableField(exist = false)
    private Long[] departmentIds ;

    @TableField(exist = false)
    private String enterpriseDrugsId ;

    /**
     * 药品来源 0-运营 1-医院
     */
    private Integer origin;

    /**
     * 基本剂量
     */
    private BigDecimal baseDose;

    /**
     * 基本剂量单位
     */
    private String baseDoseUnit;

    /**
     * HIS药品id
     */
    private String hisDrugsId;

    /**
     * 每次用量
     */
    private BigDecimal singleDose;

    /**
     * 默认频次，参考频次字典表
     */
    private String defaultFrequency;

    /**
     * 推荐使用天数
     */
    private Integer recommendUseDays;
    /**
     * 备注
     */
    private String remark;

}

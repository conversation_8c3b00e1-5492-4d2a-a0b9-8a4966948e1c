package com.puree.hospital.business.channel.commission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.business.channel.commission.domain.BusChannelOrderDetailSettlementEventLog;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 经纪人银行卡表 Mapper
 * <AUTHOR>
 * @since 2025-07-29
 */
@Mapper
public interface BusChannelOrderDetailSettlementEventLogMapper extends BaseMapper<BusChannelOrderDetailSettlementEventLog> {

    /**
     * 根据售后单ID查询结算记录
     * @param id 售后单ID
     * @return 结算记录
     */
    BusChannelOrderDetailSettlementEventLog selectClearingRecordByAfterSaleId(Long id);

    /**
     * 根据渠道订单ID查询结算记录
     * @param channelOrderId 渠道订单ID
     * @return 结算记录
     */
    List<BusChannelOrderDetailSettlementEventLog> selectClearingRecordByChannelOrderId(Long channelOrderId);
}

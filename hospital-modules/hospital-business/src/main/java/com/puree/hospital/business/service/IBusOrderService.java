package com.puree.hospital.business.service;

import com.puree.hospital.app.api.model.BusStockDrugs;
import com.puree.hospital.business.api.model.dto.BusHospitalOrderDTO;
import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.BusOrderAfterSales;
import com.puree.hospital.business.domain.BusOrderShop;
import com.puree.hospital.business.domain.dto.BusOrderDrugTraceCodeQueryDTO;
import com.puree.hospital.business.domain.dto.BusOrderDto;
import com.puree.hospital.business.domain.vo.BusOrderDetailVo;
import com.puree.hospital.business.domain.vo.BusOrderDrugTraceCodeVO;
import com.puree.hospital.business.domain.vo.BusOrderVo;
import com.puree.hospital.business.domain.vo.OrderVO;
import com.puree.hospital.order.api.model.BusDrugsOrderRequest;
import com.puree.hospital.order.api.model.BusShopOrder;
import com.puree.hospital.order.api.model.BusSubOrderDTO;
import com.puree.hospital.order.api.model.vo.BusDrugsOrderVO;
import com.puree.hospital.shop.api.model.BusShopGoods;

import java.io.OutputStream;
import java.util.List;

public interface IBusOrderService {
    /**
     * 查询订单列表
     * @param dto
     * @return
     */
    List<BusOrderVo> selectBusOrderList(BusOrderDto dto);

    /**
     * 查看订单详情
     * @param order
     * @return
     */
    BusOrderDetailVo selectBusOrderDetail(BusOrder order);

    /**
     * 订单审核
     * @param order
     * @return
     */
    int examine(BusOrder order);

    /**
     * 发货
     * @param order
     * @return
     */
    int delivery(BusOrder order);

    /**
     * 查询订单详细
     * @param orderNo
     * @return
     */
    OrderVO selectOrderInfo(String orderNo);

    /**
     * 更新订单地址
     * @param busHospitalOrderDto
     * @return
     */
    int updateOrderAddress(BusHospitalOrderDTO busHospitalOrderDto);


    /**
     * 更新药品订单信息
     * @param
     * @return
     */
    int updateDrugsOrderById(BusDrugsOrderRequest busDrugsOrderRequest);

    /**
     * 更新商品订单信息
     * @param busShopOrder
     * @return
     */
    int  updateShopOrderById(BusShopOrder busShopOrder);

    /**
     * 获取总订单信息
     * @param orderNo
     * @return
     */
    List<BusOrder> getOrderList(String orderNo);

    /**
     * 更新订单运费
     * @param busHospitalOrderDto
     * @return
     */
    int updateOrderAmount(BusHospitalOrderDTO busHospitalOrderDto);

    /**
     * 获取药品订单详情
     * @param orderNo
     * @return
     */
    BusDrugsOrderVO getDrugsOrdeByOrderNo(String  orderNo);

    /**
     * 获取商品订单详情
     * @param orderNo
     * @return
     */
    BusShopOrder getShopOrdeByOrderNo(String  orderNo);

    /**
     * 获取商品订单详情
     * @param id
     * @return
     */
    BusShopOrder getShopOrdeById(Long id);

    /**
     * 获取药品订单详情
     * @param id
     * @return
     */
    BusDrugsOrder getDrugsOrdeById(Long id);

    /**
     * 取消订单
     * @param busHospitalOrderDto
     * @return
     */
    int updateCancelOrder(BusHospitalOrderDTO busHospitalOrderDto);

    /**
     * 更新配送企业库存
     * @param  busStockDrugs
     */
    void updateReleaseStock(BusStockDrugs busStockDrugs);

    /**
     * 根据商品订单id查询商品规格信息
     */
    List<BusOrderShop> listBusOrderShopByOrderId(Long orderId);

    /**
     * 获取商品信息
     * @param id
     * @return
     */
    BusShopGoods getBusShopGoodsById(Long id);

    /**
     * 统计待发货订单数量
     * @param order
     * @return
     */
    int countNewOrder(BusOrder order);

    /**
     * 修改华润订单状态为已复核
     * @param orderNo
     * @return
     */
    int updateOrderStatus(String orderNo);

    /**
     * 校验华润订单是否可以退款
     * @param orderNo 子订单编号
     * @return
     */
    boolean checkHrOrderStatus(String orderNo);

    void splitOrder(String orderNo, BusOrderAfterSales orderAfterSales);

    /**
     * 查询发货商品/药品信息
     * @param orderNo
     * @return
     */
    OrderVO selectDeliveryList(String orderNo);

    /**
     * 文件导出
     * @param dto
     * @param outputStream
     */
    void exportOrder(BusSubOrderDTO dto, OutputStream outputStream);

    void pushOrderToYF(String subOrderNo, Long hospitalId) ;

    /**
     * 查询已经发货的医保订单 - 已经过滤条件
     * @param query  查询条件
     * @return 订单列表
     */
    List<BusOrderDrugTraceCodeVO> selectDeliveredInsuranceDrugs(BusOrderDrugTraceCodeQueryDTO query);
}

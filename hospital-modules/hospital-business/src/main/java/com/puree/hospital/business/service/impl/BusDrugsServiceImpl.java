package com.puree.hospital.business.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.business.domain.*;
import com.puree.hospital.business.domain.vo.BusDrugsVo;
import com.puree.hospital.business.domain.vo.DepartmentInfoVO;
import com.puree.hospital.business.infrastructure.utils.SysNumberGenerator;
import com.puree.hospital.business.mapper.*;
import com.puree.hospital.business.service.IBusDrugAuditService;
import com.puree.hospital.business.service.IBusDrugsService;
import com.puree.hospital.common.core.constant.SysNumConstants;
import com.puree.hospital.common.core.enums.BusDrugAuditEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.exception.base.BaseException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 药品Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class BusDrugsServiceImpl extends ServiceImpl<BusDrugsMapper, BusDrugs> implements IBusDrugsService {
    private final BusDrugsMapper busDrugsMapper;
    private final SysNumberGenerator sysNumberGenerator;
    private final BusDepartmentDrugsMapper busDepartmentDrugsMapper;
    private final BusDiseaseMapper busDiseaseMapper;
    private final BusDirectoryDrugsMapper directoryDrugsMapper;
    private final BusHospitalOfficinaMapper officinaMapper;
    private final IBusDrugAuditService iBusDrugAuditService;
    private final BusDrugsInstructionMapper instructionMapper;
    private final BusDrugsDraftMapper drugsDraftMapper ;
    private final BusDrugAuditMapper drugAuditMapper ;


    /**
     * 查询药品
     *
     * @param id 药品ID
     * @return 药品
     */
    @Override
    public BusDrugs selectBusDrugsById(Long id) {
        BusDrugsVo drugsVo = busDrugsMapper.selectBusDrugsById(id);
        if (StringUtils.isNotEmpty(drugsVo.getIcdCode())) {
            List<String> list = Arrays.asList(drugsVo.getIcdCode().split(","));
            LambdaQueryWrapper<BusDisease> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.in(BusDisease::getIcdCode, list);
            List<BusDisease> diseaseList = busDiseaseMapper.selectList(lambdaQuery);
            String diseaseName = diseaseList.stream().map(BusDisease::getDiseaseName).collect(Collectors.joining(","));
            drugsVo.setIcdValue(diseaseName);
        }

        List<DepartmentInfoVO> departmentInfoList = busDrugsMapper.selectDepartmentByDrugId(id);
        StringBuilder sb = new StringBuilder();
        List<BusDepartment> departmentList = new ArrayList<>() ;
        if ( null!=departmentInfoList && !departmentInfoList.isEmpty() ) {
            departmentInfoList.forEach(e->{
                if (StrUtil.isNotBlank(e.getFatherDepartmentName())) {
                    sb.append(e.getFatherDepartmentName()).append("-").append(e.getSonDepartmentName()).append(" ") ;
                }else {
                    sb.append(e.getSonDepartmentName()).append(" ") ;
                }
                BusDepartment busDepartment = new BusDepartment();
                busDepartment.setId(e.getId());
                busDepartment.setParentId(e.getParentId());
                departmentList.add(busDepartment) ;
            });
            drugsVo.setDepartmentIds(departmentInfoList.stream().map(DepartmentInfoVO::getId).toArray(Long[]::new));
            drugsVo.setDepartmentList(departmentList);
        }
        drugsVo.setDepartmentName(sb.toString());
        drugsVo.setShield(this.handleDrugShield(id));
        return drugsVo;
    }

    /**
     * 查询药品列表
     *
     * @param busDrugs 药品
     * @return 药品
     */
    @Override
    public List<BusDrugsVo> selectBusDrugsList(BusDrugs busDrugs) {
        return busDrugsMapper.selectBusDrugsList(busDrugs);
    }

    @Override
    public List<BusDrugsVo> selBusDrugsConnectMultiTableList(BusDrugs busDrugs) {
        return busDrugsMapper.selBusDrugsConnectMultiTableList(busDrugs);
    }


    @Override
    public List<BusDrugsVo> selBusDrugsWithHospitalId(BusDrugs busDrugs) {
        return busDrugsMapper.selBusDrugsWithHospitalId(busDrugs);
    }
    /**
     * 新增药品
     *
     * @param busDrugs 药品
     * @return 结果
     */
    @Override
    public int insertBusDrugs(BusDrugs busDrugs) {
        this.checkDrugs(busDrugs, null);
        busDrugs.setCreateBy(SecurityUtils.getUsername());
        busDrugs.setCreateTime(DateUtils.getNowDate());
        busDrugs.setType(YesNoEnum.NO.getCode());
        String ypNum = sysNumberGenerator.get(SysNumConstants.YP_NUM_KEY);
        busDrugs.setDrugsNumber(ypNum);
        log.info("新增药品 classification: " + busDrugs.getClassification());
        Integer idNum = 0 ;
        //如果等于1是医院后台导入药品，需要生成审核记录
        if (null != busDrugs.getClassification() && BusDrugAuditEnum.AUDIT_COMPLETED.getCode().equals(busDrugs.getClassification())) {
            busDrugs.setOrigin(YesNoEnum.YES.getCode());
            idNum = busDrugsMapper.insertBusDrugs(busDrugs);
            log.info("新增药品主键id busDrugs.getId(): " + busDrugs.getId());
            BusDrugAudit busDrugAudit = new BusDrugAudit();
            //药品id
            busDrugAudit.setDrugsId(busDrugs.getId());
            busDrugAudit.setHospitalId(busDrugs.getHospitalId());
            busDrugAudit.setStatus(BusDrugAuditEnum.TO_BE_EXAMINED.getCode());
            busDrugAudit.setSellingPrice(busDrugs.getSellingPrice());
            busDrugAudit.setStock(busDrugs.getStock());
            iBusDrugAuditService.save(busDrugAudit);
            // 医院后台-新增药品时候，添加 departmentIds 科室
            this.addDepartment(busDrugs.getId(), busDrugs.getDepartmentIds(), SecurityUtils.getUsername()) ;
            return idNum;
        } else {
            busDrugs.setOrigin(YesNoEnum.NO.getCode());
            idNum = busDrugsMapper.insertBusDrugs(busDrugs);
            // 运营后台-新增药品时候，添加 departmentIds 科室
            this.addDepartment(busDrugs.getId(), busDrugs.getDepartmentIds(), SecurityUtils.getUsername()) ;
            return idNum;
        }

    }

    @Override
    public void checkDrugs(BusDrugs busDrugs, Long drugsId) {
        //国药准字+规格
        BusDrugs drugs = busDrugsMapper.selectOne(new LambdaQueryWrapper<BusDrugs>()
                .eq(BusDrugs::getNmpn, busDrugs.getNmpn())
                .ne(StringUtils.isNotNull(drugsId), BusDrugs::getId, drugsId)
                .eq(BusDrugs::getDrugsSpecification, busDrugs.getDrugsSpecification()).last("limit 1"));
        //本位码+规格
        BusDrugs drugsTow = busDrugsMapper.selectOne(new LambdaQueryWrapper<BusDrugs>()
                .eq(BusDrugs::getDrugsSpecification, busDrugs.getDrugsSpecification())
                .ne(StringUtils.isNotNull(drugsId), BusDrugs::getId, drugsId)
                .eq(BusDrugs::getDrugsStandardCode, busDrugs.getDrugsStandardCode()).last("limit 1"));
        if (StringUtils.isNotNull(drugs)) {
            if (StringUtils.isNull(drugsId) || !drugsId.equals(drugs.getId())) {
                throw new ServiceException("已存在同一国药准字号及规格的药品，请核实后再添加！");
            }
        }
        if (StringUtils.isNotNull(drugsTow)) {
            if (StringUtils.isNull(drugsId) || !drugsId.equals(drugsTow.getId())) {
                throw new ServiceException("已存在同一本位码及规格的药品，请核实后再添加！");
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addDepartment(Long drugsId, Long[] departmentIds, String userName) {
        busDepartmentDrugsMapper.deleteBusDepartmentDrugsByDrugsId(drugsId);
        if (null!=departmentIds) {
            List<BusDepartmentDrugs> list = new ArrayList<>();
            for (Long departmentId : departmentIds) {
                BusDepartmentDrugs departmentDrugs = new BusDepartmentDrugs();
                departmentDrugs.setDrugsId(drugsId);
                departmentDrugs.setDepartmentId(departmentId);
                departmentDrugs.setCreateBy(userName);
                list.add(departmentDrugs);
            }
            return busDepartmentDrugsMapper.batchInsert(list);
        }
        return 0 ;
    }

    /**
     * 修改药品
     *
     * @param busDrugs 药品
     * @return 结果
     */
    @Override
    public int updateBusDrugs(BusDrugs busDrugs) {
        this.checkDrugs(busDrugs, busDrugs.getId());
        //如果等于1是医院后台修改药品信息，需要更新审核记录状态为待审核
        if (null != busDrugs.getClassification() && BusDrugAuditEnum.AUDIT_COMPLETED.getCode().equals(busDrugs.getClassification())) {
            return hospitalUpdateDrug(busDrugs);
        }
        busDrugs.setUpdateTime(DateUtils.getNowDate());
        busDrugs.setUpdateBy(SecurityUtils.getUsername());
        // 药品至少关联一个科室
        if (null!=busDrugs.getDepartmentIds() && busDrugs.getDepartmentIds().length>0) {
            this.addDepartment(busDrugs.getId(), busDrugs.getDepartmentIds(), SecurityUtils.getUsername()) ;
        }
        return busDrugsMapper.updateBusDrugs(busDrugs);
    }

    private int hospitalUpdateDrug(BusDrugs req){

        BusDrugsDraft drugsDraft = new BusDrugsDraft();
        BeanUtils.copyProperties(req, drugsDraft);
        if ( null!= req.getDepartmentIds() && req.getDepartmentIds().length>0 ) {
            drugsDraft.setDepartmentIdList(Arrays.asList(req.getDepartmentIds()));
        }
        drugsDraft.setId(null);
        drugsDraft.setDrugsId(req.getId());
        Date now = new Date();
        drugsDraft.setCreateTime(now);
        drugsDraft.setUpdateTime(now);
        drugsDraft.setCreateBy(SecurityUtils.getUsername());
        drugsDraft.setUpdateBy(SecurityUtils.getUsername());
        drugsDraftMapper.insertDrugsDraft(drugsDraft);

        BusDrugAudit drugAuditDTO = new BusDrugAudit();
        drugAuditDTO.setDrugsId(req.getId());
        drugAuditDTO.setHospitalId(req.getHospitalId());
        drugAuditDTO.setStatus(BusDrugAuditEnum.TO_BE_EXAMINED.getCode());
        drugAuditDTO.setSellingPrice(req.getSellingPrice());
        drugAuditDTO.setStock(req.getStock());
        drugAuditDTO.setCreateTime(now) ;
        drugAuditDTO.setUpdateTime(now) ;
        drugAuditDTO.setCreateBy(SecurityUtils.getUsername());
        drugAuditDTO.setUpdateBy(SecurityUtils.getUsername());
        iBusDrugAuditService.save(drugAuditDTO);

        return 1 ;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int disableStart(BusDrugs busDrugs) {
        try {
            // updateBusDrugs 方式改为了能为空的字段每次调用都会更新，这里换为mybatis-plus的写法，只改状态
//            int updateBusDrugs = busDrugsMapper.updateBusDrugs(busDrugs);
            LambdaUpdateWrapper<BusDrugs> updateWrapper = new LambdaUpdateWrapper<BusDrugs>()
                    .eq(BusDrugs::getId, busDrugs.getId())
                    .set(BusDrugs::getStatus, busDrugs.getStatus())
                    .set(BusDrugs::getUpdateTime, DateUtils.getNowDate());
            int updateBusDrugs = busDrugsMapper.update(null, updateWrapper);
            //修改后续操作
            LambdaQueryWrapper<BusHospitalOfficina> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusHospitalOfficina::getDrugsId, busDrugs.getId());
            BusHospitalOfficina hospitalOfficina = new BusHospitalOfficina();
            hospitalOfficina.setStatus(busDrugs.getStatus());
            officinaMapper.update(hospitalOfficina, lambdaQuery);
            return updateBusDrugs;
        } catch (Exception e) {
            throw new BaseException("更新状态失败");
        }
    }

    /**
     * 批量删除药品
     *
     * @param ids 需要删除的药品ID
     * @return 结果
     */
    @Override
    public int deleteBusDrugsByIds(Long[] ids) {
        return busDrugsMapper.deleteBusDrugsByIds(ids);
    }

    /**
     * 删除药品信息
     *
     * @param id 药品ID
     * @return 结果
     */
    @Override
    public int deleteBusDrugsById(Long id) {
        return busDrugsMapper.deleteBusDrugsById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addMultSpecDrugs(BusDrugs drugs) {
        BusDrugs existDrug = busDrugsMapper.selectById(drugs.getId());
        StringUtils.isNullThrowExp(existDrug, "添加其它规格药品不存在");
        existDrug.setId(null);
        existDrug.setNmpn(drugs.getNmpn());
        existDrug.setDrugsStandardCode(drugs.getDrugsStandardCode());
        existDrug.setReferenceSellingPrice(drugs.getReferenceSellingPrice());
        existDrug.setReferencePurchasePrice(drugs.getReferencePurchasePrice());
        existDrug.setDrugsSpecification(drugs.getDrugsSpecification());
        existDrug.setNationalDrugCode(drugs.getNationalDrugCode());
        existDrug.setCreateBy(SecurityUtils.getUsername());
        existDrug.setCreateTime(DateUtils.getNowDate());
        existDrug.setType(YesNoEnum.NO.getCode());
        String ypNum = sysNumberGenerator.get(SysNumConstants.YP_NUM_KEY);
        existDrug.setDrugsNumber(ypNum);
        // 添加到运营平台
        this.checkDrugs(existDrug, null);
        busDrugsMapper.insert(existDrug);
        // 添加到目录
        BusDirectoryDrugs directoryDrugs = new BusDirectoryDrugs();
        directoryDrugs.setCreateBy(SecurityUtils.getUsername());
        directoryDrugs.setCreateTime(new Date());
        directoryDrugs.setDrugsId(existDrug.getId());
        directoryDrugs.setDirectoryType(CodeEnum.YES.getCode());
        directoryDrugs.setHospitalId(drugs.getHospitalId());
        directoryDrugsMapper.insert(directoryDrugs);
        //添加药房
        BusHospitalOfficina officina = new BusHospitalOfficina();
        officina.setDirectoryId(directoryDrugs.getId());
        officina.setDrugsId(directoryDrugs.getDrugsId());
        officina.setHospitalId(directoryDrugs.getHospitalId());
        officina.setSellingPrice(drugs.getSellingPrice());
        officina.setStock(drugs.getStock());
        officina.setStatus(YesNoEnum.YES.getCode());
        officina.setCreateBy(SecurityUtils.getUsername());
        officina.setCreateTime(DateUtils.getNowDate());
        return officinaMapper.insert(officina);
    }

    @Override
    public void export(HttpServletResponse resp) {
        try {
            List<BusDrugs> drugs = busDrugsMapper.selectList(new LambdaQueryWrapper<BusDrugs>()
                    .eq(BusDrugs::getType, 0)
                    .eq(BusDrugs::getDelFlag, 0));
            EasyExcel.write(resp.getOutputStream(), BusDrugs.class).sheet("运营库药品").doWrite(drugs);
        } catch (Exception e) {
            log.error("药品库数据导出失败", e);
            throw new ServiceException("药品库数据导出失败，" + e.getMessage());
        }
    }

    @Override
    public BusDrugsInstruction getInstruction(Long drugId) {
        return instructionMapper.selectOne(new LambdaQueryWrapper<BusDrugsInstruction>()
                .eq(BusDrugsInstruction::getDrugsId, drugId));
    }

    @Override
    public int editInstruction(BusDrugsInstruction instruction) {
        BusDrugsInstruction drugsInstruction = instructionMapper.selectOne(new LambdaQueryWrapper<BusDrugsInstruction>()
                .eq(BusDrugsInstruction::getDrugsId, instruction.getDrugsId()));
        if (null == drugsInstruction) {
            return instructionMapper.insert(instruction);
        }
        instruction.setId(drugsInstruction.getId());
        return instructionMapper.updateById(instruction);
    }

    /**
     * @Param busDrugs
     * @Return java.util.List<com.puree.hospital.business.domain.vo.BusDrugsVo>
     * @Description 批量查询
     * <AUTHOR>
     * @Date 2024/3/7 17:47
     **/
    @Override
    public List<BusDrugsVo> listByIds(List<Long> drugsIdList, Long hospitalId) {
        BusDrugs busDrugs = new BusDrugs();
        busDrugs.setDrugsIds(drugsIdList);
        List<BusDrugsVo> list = new ArrayList<>();
        if(hospitalId == null){
            list = selBusDrugsConnectMultiTableList(busDrugs);
        }else {
            busDrugs.setHospitalId(hospitalId);
            list = selBusDrugsWithHospitalId(busDrugs);
        }


        List<BusDrugsVo> vos = new ArrayList<>(10);
        //排序
        for (Long id : drugsIdList) {
            for (BusDrugsVo drugsVo : list) {
                if (NumberUtil.equals(id, drugsVo.getId())) {
                    vos.add(drugsVo);
                }
            }
        }
        return vos;
    }

    @Override
    public BusDrugs selectBusDrugsByIdV2(Long id) {

        BusDrugAudit drugAuditVO = drugAuditMapper.selectOne( new LambdaQueryWrapper<BusDrugAudit>()
                .eq(BusDrugAudit::getDrugsId, id)
                .orderByDesc(BusDrugAudit::getId).last("limit 1") ) ;

        if ( null==drugAuditVO || !BusDrugAuditEnum.AUDIT_COMPLETED.getCode().equals(drugAuditVO.getStatus()) ) {
            List<BusDrugsVo> drugsVoList = busDrugsMapper.selectDraftDrugsByDrugsId(id, 1);
            if ( null!=drugsVoList && !drugsVoList.isEmpty() ) {
                return selectDraftDrugsInfo(drugsVoList.get(0)) ;
            }
        }

        return selectBusDrugsById(id) ;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(List<BusDrugs> drugsList) {
        if (CollectionUtils.isEmpty(drugsList)) {
            return;
        }
        drugsList.forEach(busDrug -> {
            busDrug.setCreateBy(SecurityUtils.getUsername());
            busDrug.setCreateTime(DateUtils.getNowDate());
            busDrug.setType(YesNoEnum.NO.getCode());
            String ypNum = sysNumberGenerator.get(SysNumConstants.YP_NUM_KEY);
            busDrug.setDrugsNumber(ypNum);
            busDrug.setOrigin(YesNoEnum.NO.getCode());
        });
        saveBatch(drugsList);
    }

    @Override
    public List<Long> selectIdsByBusDrug(BusDrugs busDrug) {
        return busDrugsMapper.selectIdsByBusDrug(busDrug);
    }

    @Override
    public void drugsBindingDepartment(Long drugMinId, String remark) {
        busDepartmentDrugsMapper.batchBindingInsert(drugMinId, remark);
    }

    @Override
    public List<BusDrugs> selectBusDrugsListByIds(List<Long> bachIds) {
        LambdaQueryWrapper<BusDrugs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BusDrugs::getId, bachIds);
        return busDrugsMapper.selectList(queryWrapper);
    }

    private BusDrugs selectDraftDrugsInfo(BusDrugsVo drugsVo) {

        this.handleIcdCodeAndDepartmentInfo(drugsVo);

        List<BusDrugsVo> oldDrugsVoList = busDrugsMapper.selectDraftDrugsByDrugsId(drugsVo.getDrugsId(), 2);
        if ( null!=oldDrugsVoList && 2==oldDrugsVoList.size() ) {
            // 上一条修改的药品信息
            BusDrugsVo tmpDrugsVo = oldDrugsVoList.get(1);
            this.handleIcdCodeAndDepartmentInfo(tmpDrugsVo);
            drugsVo.setOldDrugsInfo(tmpDrugsVo) ;
        }else {
            //BusDrugs oldDrugs = this.selectBusDrugsById(drugsVo.getDrugsId());
            //drugsVo.setOldDrugsInfo((BusDrugsVo) oldDrugs);
        }

        drugsVo.setShield(this.handleDrugShield(drugsVo.getDrugsId()));
        drugsVo.setId(drugsVo.getDrugsId());
        return drugsVo;
    }

    private Integer handleDrugShield(Long drugsId) {

        List<BusDrugAudit> drugAuditList = drugAuditMapper.selectList( new LambdaQueryWrapper<BusDrugAudit>().eq(BusDrugAudit::getDrugsId, drugsId) ) ;
        if ( null==drugAuditList || drugAuditList.isEmpty() ) {
            return YesNoEnum.YES.getCode() ;
        }

        BusDrugs drugsDO = busDrugsMapper.selectById(drugsId);
        // 存量数据
        if ( null==drugsDO.getOrigin() || YesNoEnum.NO.getCode().equals(drugsDO.getOrigin()) ) {
            return YesNoEnum.YES.getCode() ;
        }

        Optional<BusDrugAudit> optional = drugAuditList.stream().filter(drugAudit -> BusDrugAuditEnum.AUDIT_COMPLETED.getCode().equals(drugAudit.getStatus())).findFirst();
        if (optional.isPresent()) {
            return YesNoEnum.YES.getCode() ;
        }else {
            return YesNoEnum.NO.getCode() ;
        }

    }

    private void handleIcdCodeAndDepartmentInfo(BusDrugsVo drugsVo){

        BusDrugsDraft busDrugsDraft = drugsDraftMapper.selectById(drugsVo.getId());
        drugsVo.setDepartmentIdList(busDrugsDraft.getDepartmentIdList());

        if (StrUtil.isNotBlank(drugsVo.getIcdCode())) {
            List<String> list = Arrays.asList(drugsVo.getIcdCode().split(","));
            LambdaQueryWrapper<BusDisease> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.in(BusDisease::getIcdCode, list);
            List<BusDisease> diseaseList = busDiseaseMapper.selectList(lambdaQuery);
            String diseaseName = diseaseList.stream().map(BusDisease::getDiseaseName).collect(Collectors.joining(","));
            drugsVo.setIcdValue(diseaseName);
        }

        if ( null==drugsVo.getDepartmentIdList() || drugsVo.getDepartmentIdList().isEmpty() ) {
            return ;
        }

        List<DepartmentInfoVO> departmentInfoList = busDrugsMapper.selectDepartmentForDraftDrug(drugsVo.getDepartmentIdList());
        StringBuilder sb = new StringBuilder();
        List<BusDepartment> departmentList = new ArrayList<>() ;
        if ( null!=departmentInfoList && !departmentInfoList.isEmpty() ) {
            departmentInfoList.forEach(e->{
                if (StrUtil.isNotBlank(e.getFatherDepartmentName())) {
                    sb.append(e.getFatherDepartmentName()).append("-").append(e.getSonDepartmentName()).append(" ") ;
                }else {
                    sb.append(e.getSonDepartmentName()).append(" ") ;
                }
                BusDepartment busDepartment = new BusDepartment();
                busDepartment.setId(e.getId());
                busDepartment.setParentId(e.getParentId());
                departmentList.add(busDepartment) ;
            });
            drugsVo.setDepartmentIds(departmentInfoList.stream().map(DepartmentInfoVO::getId).toArray(Long[]::new));
            drugsVo.setDepartmentList(departmentList);
        }
        drugsVo.setDepartmentName(sb.toString());

    }


}

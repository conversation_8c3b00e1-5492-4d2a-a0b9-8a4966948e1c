package com.puree.hospital.business.service;

import com.puree.hospital.business.domain.BusTcmSyndrome;
import com.puree.hospital.business.domain.vo.BusTcmSyndromeVo;

import java.util.List;

/**
 * 中医症型信息Service接口
 * 
 * <AUTHOR>
 * @date 2021-10-29
 */
public interface IBusTcmSyndromeService {
    /**
     * 查询中医症型信息
     * 
     * @param id 中医症型信息ID
     * @return 中医症型信息
     */
    BusTcmSyndrome selectBusTcmSyndromeById(Long id);

    /**
     * 查询中医症型信息列表
     * 
     * @param busTcmSyndrome 中医症型信息
     * @return 中医症型信息集合
     */
    List<BusTcmSyndromeVo> selectBusTcmSyndromeList(BusTcmSyndrome busTcmSyndrome);

    /**
     * 新增中医症型信息
     * 
     * @param busTcmSyndrome 中医症型信息
     * @return 结果
     */
    int insertBusTcmSyndrome(BusTcmSyndrome busTcmSyndrome);

    /**
     * 修改中医症型信息
     * 
     * @param busTcmSyndrome 中医症型信息
     * @return 结果
     */
    int updateBusTcmSyndrome(BusTcmSyndrome busTcmSyndrome);

    /**
     * 批量删除中医症型信息
     * 
     * @param ids 需要删除的中医症型信息ID
     * @return 结果
     */
    int deleteBusTcmSyndromeByIds(Long[] ids);

    /**
     * 删除中医症型信息信息
     * 
     * @param id 中医症型信息ID
     * @return 结果
     */
    int deleteBusTcmSyndromeById(Long id);

    /**
     * 中医症型停用启用操作
     * @param busTcmSyndrome
     * @return
     */
    int lock(BusTcmSyndrome busTcmSyndrome);

    /**
     * 校验病种名称是否重复
     * @param id
     * @param tcmSyndrome
     * @return
     */
    boolean checkTcmDiagnosis(Long id, String tcmSyndrome);

    /**
     * 校验诊断代码是否重复
     * @param id
     * @param diagnosisCode
     * @return
     */
    boolean checkDiagnosisCode(Long id, String diagnosisCode);

    /**
     *  根据Ids查询症状
     * @param syndromesIds 症状id
     * @return  症状列表
     */
    List<BusTcmSyndrome> querySyndromeByIds(List<Long> syndromesIds);
}

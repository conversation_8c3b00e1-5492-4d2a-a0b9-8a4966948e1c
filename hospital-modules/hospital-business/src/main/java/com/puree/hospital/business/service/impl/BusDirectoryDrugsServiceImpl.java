package com.puree.hospital.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.puree.hospital.business.api.model.dto.BusDirectoryDrugsDTO;
import com.puree.hospital.business.domain.BusConsultationSettings;
import com.puree.hospital.business.domain.BusDirectoryDrugs;
import com.puree.hospital.business.domain.BusDoctorCommonlyUsed;
import com.puree.hospital.business.domain.BusDrugs;
import com.puree.hospital.business.domain.BusHospital;
import com.puree.hospital.business.domain.BusHospitalOfficina;
import com.puree.hospital.business.domain.BusHospitalPaDrugs;
import com.puree.hospital.business.domain.BusHospitalPharmacyDrugs;
import com.puree.hospital.business.domain.BusOtcDrugs;
import com.puree.hospital.business.domain.BusPrescriptionDrugs;
import com.puree.hospital.business.domain.BusShopCart;
import com.puree.hospital.business.domain.dto.BusDirectoryDrugQueryDTO;
import com.puree.hospital.business.domain.vo.BusDirectoryDrugsVo;
import com.puree.hospital.business.domain.vo.BusDrugsVo;
import com.puree.hospital.business.importDrugs.domain.ImportDrugTaskParamDTO;
import com.puree.hospital.business.mapper.BusDirectoryDrugsMapper;
import com.puree.hospital.business.mapper.BusDoctorCommonlyUsedMapper;
import com.puree.hospital.business.mapper.BusDrugsMapper;
import com.puree.hospital.business.mapper.BusHospitalMapper;
import com.puree.hospital.business.mapper.BusHospitalOfficinaMapper;
import com.puree.hospital.business.mapper.BusHospitalPaDrugsMapper;
import com.puree.hospital.business.mapper.BusHospitalPharmacyDrugsMapper;
import com.puree.hospital.business.mapper.BusOtcDrugsMapper;
import com.puree.hospital.business.mapper.BusPreorderDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionTemplateDrugsMapper;
import com.puree.hospital.business.mapper.BusShopCartMapper;
import com.puree.hospital.business.service.IBusConsultationSettingsService;
import com.puree.hospital.business.service.IBusDirectoryDrugsService;
import com.puree.hospital.common.core.enums.DirectoryTypeEnum;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.core.enums.ShopCartTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDirectoryDrugsServiceImpl implements IBusDirectoryDrugsService {

    private final BusDirectoryDrugsMapper busDirectoryDrugsMapper;
    private final BusDrugsMapper busDrugsMapper;
    private final BusHospitalOfficinaMapper busHospitalOfficinaMapper;
    private final BusHospitalPharmacyDrugsMapper busHospitalPharmacyDrugsMapper;
    private final BusDoctorCommonlyUsedMapper commonlyUsedMapper;
    private final BusHospitalPaDrugsMapper paDrugsMapper;
    private final BusOtcDrugsMapper otcDrugsMapper;
    private final BusPrescriptionDrugsMapper prescriptionDrugsMapper;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    private final BusShopCartMapper shopCartMapper;
    private final BusPrescriptionTemplateDrugsMapper busPrescriptionTemplateDrugsMapper;
    private final BusPreorderDrugsMapper busPreorderDrugsMapper;
    private final BusHospitalMapper busHospitalMapper;

    @Override
    public List<BusDirectoryDrugsVo> selectList(BusDirectoryDrugsDTO dto) {
        List<BusDirectoryDrugsVo> list = busDirectoryDrugsMapper.selectDirectoryDrugsList(dto);
        assembleHospitalName(list);
        return list;
    }

    private void assembleHospitalName(List<BusDirectoryDrugsVo> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            List<Long> hospitalIdList = list.stream().map(BusDirectoryDrugsVo::getHospitalId).distinct().collect(Collectors.toList());
            List<BusHospital> hospitalList = busHospitalMapper.selectBatchIds(hospitalIdList);
            Map<Long, BusHospital> hospitalMap = new HashMap<>(list.size());
            if (CollectionUtil.isNotEmpty(hospitalList)) {
                hospitalMap.putAll(hospitalList.stream().collect(Collectors.toMap(BusHospital::getId, Function.identity())));
            }
            list.forEach(item -> {
                BusHospital hospital = hospitalMap.getOrDefault(item.getHospitalId(),new BusHospital());
                item.setHospitalName(hospital.getHospitalName());
            });
        }
    }

    @Override
    public List<BusDirectoryDrugsVo> selectNotAssociateList(BusDirectoryDrugsDTO dto) {
        return busDirectoryDrugsMapper.selectNotAssociateList(dto);
    }

    @Override
    public List<BusDirectoryDrugsVo> tcmDirectoryDrugsList(BusDirectoryDrugsDTO dto) {
        return busDirectoryDrugsMapper.tcmDirectoryDrugsList(dto);
    }

    @Override
    public List<BusDirectoryDrugsVo> tcmNotAssociateList(BusDirectoryDrugsDTO dto) {
        return busDirectoryDrugsMapper.tcmNotAssociateList(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int tcmInsert(List<BusDirectoryDrugs> list) {
        this.checkDirectoryDrugs(list);
        busDirectoryDrugsMapper.batchInsert(list);
        addHospitalOfficinas(list, false);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<BusDirectoryDrugs> list) {
        this.checkDirectoryDrugs(list);
        busDirectoryDrugsMapper.batchInsert(list);
        addHospitalOfficinas(list, false);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(BusDirectoryDrugs bdd) {
        BusDirectoryDrugs directoryDrugs = busDirectoryDrugsMapper.selectById(bdd.getId());
        if (Objects.isNull(directoryDrugs)) {
            throw new ServiceException("药品目录中不存在此药品");
        }
        checkDelete(directoryDrugs);
        //双通道目录的药品来源标记为-1
        Long drugsSourceId = -1L;
        //院内目录增加
        if (DirectoryTypeEnum.isInner(directoryDrugs.getDirectoryType())) {
            BusHospitalOfficina officina = busHospitalOfficinaMapper.selectOne(new LambdaQueryWrapper<BusHospitalOfficina>()
                    .eq(BusHospitalOfficina::getDirectoryId, directoryDrugs.getId()));
            if (Objects.nonNull(officina)) {
                //删除药方信息
                busHospitalOfficinaMapper.delete(new LambdaQueryWrapper<BusHospitalOfficina>()
                        .eq(BusHospitalOfficina::getDirectoryId, directoryDrugs.getId())
                        .eq(BusHospitalOfficina::getHospitalId, directoryDrugs.getHospitalId())
                        .eq(BusHospitalOfficina::getDrugsId, directoryDrugs.getDrugsId()));
                drugsSourceId = officina.getId();
            }
            // 删除购物车关联药品
            LambdaQueryWrapper<BusShopCart> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusShopCart::getHospitalId, directoryDrugs.getHospitalId())
                    .eq(BusShopCart::getBusinessId, directoryDrugs.getDrugsId())
                    .ne(BusShopCart::getType, ShopCartTypeEnum.GOODS.getCode());
            shopCartMapper.delete(lambdaQuery);
        }

        //删除医生常用药
        commonlyUsedMapper.delete( new LambdaQueryWrapper<BusDoctorCommonlyUsed>()
                .eq(BusDoctorCommonlyUsed::getHospitalId, directoryDrugs.getHospitalId())
                .eq(BusDoctorCommonlyUsed::getDrugsId, directoryDrugs.getDrugsId())
                .eq(BusDoctorCommonlyUsed::getDrugsSourceId, drugsSourceId));

        //查询该药品有是否有关联多个药品目录
        List<BusDirectoryDrugs> list = busDirectoryDrugsMapper.selectDirectoryDrugs(directoryDrugs.getHospitalId(), null, Lists.newArrayList(directoryDrugs.getDrugsId()));
        //如果该药品只有一个关联目录，则删除医生的处方模板中的药品
        if (list.size() == 1) {
            busPrescriptionTemplateDrugsMapper.deleteByDrugsId(directoryDrugs.getHospitalId(), directoryDrugs.getDrugsId());
        }
        //直接删除药店关联的药品目录
        LambdaQueryWrapper<BusHospitalPharmacyDrugs> pharmacyDrugsQueryWrapper = new LambdaQueryWrapper<>();
        pharmacyDrugsQueryWrapper.eq(BusHospitalPharmacyDrugs::getDirectoryId, directoryDrugs.getId());
        busHospitalPharmacyDrugsMapper.delete(pharmacyDrugsQueryWrapper);
        //删除药品目录
        return busDirectoryDrugsMapper.deleteById(directoryDrugs.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BusDirectoryDrugs busDirectoryDrugs) {
        return busDirectoryDrugsMapper.updateById(busDirectoryDrugs);
    }

    @Override
    public List<BusDirectoryDrugsVo> tcmDrugsList(BusDirectoryDrugsDTO dto) {
        return busDirectoryDrugsMapper.tcmDrugsList(dto);
    }

    @Override
    public BusDirectoryDrugsVo tcmDrugsById(Long id, Long hospitalId) {
        return busDirectoryDrugsMapper.tcmDrugsById(id, hospitalId);
    }


    @Override
    public List<BusDrugsVo> yytcmDrugsList(BusDrugs busDrugs) {
        return busDrugsMapper.selectBusDrugsList(busDrugs);
    }

    /**
     * 患教下的药品同步至医院
     *
     * @param hospitalId 医院id
     * @param ids 药品id集合
     * @return 返回影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncDrugs(Long hospitalId, List<Long> ids) {
        //如果传入的id为空，则不做数据同步
        if (CollectionUtil.isEmpty(ids)) {
            return 0;
        }
        //药品目录类型
        String directoryType = DirectoryTypeEnum.MM_INNER.getType();
        //过滤出未同步医院的药品id列表
        List<BusDrugs> busDrugsList = busDirectoryDrugsMapper.getUnSyncDrugIds(hospitalId, directoryType,ids);
        if(CollectionUtils.isEmpty(busDrugsList)){
            return 0;
        }
        //创建药品集合对象
        List<BusDirectoryDrugs> drugList = new ArrayList<>();
        busDrugsList.forEach(item -> {
            BusDirectoryDrugs drug = new BusDirectoryDrugs();
            drug.setDrugsId(item.getId());
            drug.setHospitalId(hospitalId);
            drug.setDirectoryType(directoryType);
            drug.setCreateBy(SecurityUtils.getUsername());
            drug.setCreateTime(new Date());
            drug.setReferencePurchasePrice(item.getReferencePurchasePrice());
            drugList.add(drug);
        });
        //批量插入药品
        busDirectoryDrugsMapper.batchInsert(drugList);
        //添加药房
        addHospitalOfficinas(drugList, true);
        return drugList.size();
    }

    @Override
    public Boolean checkBeforeDelete(BusDirectoryDrugs bdd) {
        BusDirectoryDrugs directoryDrugs = busDirectoryDrugsMapper.selectById(bdd.getId());
        if (Objects.isNull(directoryDrugs)) {
            throw new ServiceException("药品目录中不存在此药品");
        }
        checkDelete(directoryDrugs);
        return true ;
    }

    @Override
    public Long selectCountByQueryDTO(BusDirectoryDrugQueryDTO queryDTO) {
        LambdaQueryWrapper<BusDirectoryDrugs> eq = new LambdaQueryWrapper<BusDirectoryDrugs>()
                .eq(Objects.nonNull(queryDTO.getHospitalId()),BusDirectoryDrugs::getHospitalId, queryDTO.getHospitalId())
                .eq(StringUtils.isNotEmpty(queryDTO.getDirectoryType()),BusDirectoryDrugs::getDirectoryType, queryDTO.getDirectoryType())
                .eq(Objects.nonNull(queryDTO.getId()),BusDirectoryDrugs::getId, queryDTO.getId());
        if (CollectionUtil.isNotEmpty(queryDTO.getDrugIds())) {
            eq.in(BusDirectoryDrugs::getDrugsId, queryDTO.getDrugIds());
        }
        return busDirectoryDrugsMapper.selectCount(eq);
    }

    @Override
    public List<BusDirectoryDrugs> selectListByQueryDTO(BusDirectoryDrugQueryDTO queryDTO) {
        LambdaQueryWrapper<BusDirectoryDrugs> queryWrapper = new LambdaQueryWrapper<BusDirectoryDrugs>()
                .eq(Objects.nonNull(queryDTO.getHospitalId()),BusDirectoryDrugs::getHospitalId, queryDTO.getHospitalId())
                .eq(StringUtils.isNotEmpty(queryDTO.getDirectoryType()),BusDirectoryDrugs::getDirectoryType, queryDTO.getDirectoryType())
                .gt(Objects.nonNull(queryDTO.getIndexId()),BusDirectoryDrugs::getId, queryDTO.getIndexId());
        if (Objects.nonNull(queryDTO.getPageSize())) {
            queryWrapper.last("limit " + queryDTO.getPageSize());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getDrugIds())) {
            queryWrapper.in(BusDirectoryDrugs::getDrugsId, queryDTO.getDrugIds());
        }
        return busDirectoryDrugsMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional
    public void drugsBindingDirectory(ImportDrugTaskParamDTO paramDTO) {
        busDirectoryDrugsMapper.drugsBindingDirectory(paramDTO);
    }

    @Override
    @Transactional
    public void updateBatchDirectoryStatus(Long hospitalId, Integer directoryStatus, String directoryType) {
        busDirectoryDrugsMapper.updateBatchDirectoryStatus(hospitalId, directoryStatus, directoryType);
    }

    @Override
    public List<BusDirectoryDrugsVo> selectDrugsList(BusDirectoryDrugsDTO dto) {
        // 过滤掉测试医院的数据
        BusHospital busHospital = new BusHospital();
        busHospital.setIsTest(1);
        List<BusHospital> busHospitals = busHospitalMapper.selectBusHospitalList(busHospital);
        dto.setExcludeHospitalIds(busHospitals.stream().map(BusHospital::getId).collect(Collectors.toList()));
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<BusDirectoryDrugsVo> list = busDirectoryDrugsMapper.selectDrugsList(dto);
        assembleHospitalName(list);
        return list;
    }

    /**
     * 校验待维护的药品目录数据
     *
     * @param list 待插入的药品集合
     */
    private void checkDirectoryDrugs(List<BusDirectoryDrugs> list) {
        if (CollectionUtil.isEmpty(list)) {
            throw new ServiceException("待维护的药品目录数据不能为空");
        }
        List<Long> hospitalIds = list.stream().map(BusDirectoryDrugs::getHospitalId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(hospitalIds) || hospitalIds.size() > 1) {
            throw new ServiceException("医药药品目录维护只能单个医院进行维护");
        }
        Long hospitalId = hospitalIds.get(0);
        List<String> directoryTypes = list.stream().map(BusDirectoryDrugs::getDirectoryType).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(directoryTypes) || directoryTypes.size() > 1) {
            throw new ServiceException("医院药品目录维护只能单个目录进行维护");
        }
        String directoryType = directoryTypes.get(0);
        // 如果目录中存在医院+药品已经录入，不再插入
        List<Long> drugsIds = list.stream().map(BusDirectoryDrugs::getDrugsId).collect(Collectors.toList());
        List<BusDirectoryDrugs> directoryDrugs = busDirectoryDrugsMapper.selectDirectoryDrugs(hospitalId, directoryType, drugsIds);
        if (CollectionUtil.isNotEmpty(directoryDrugs)) {
            String drugsName = directoryDrugs.stream().map(BusDirectoryDrugs::getDrugsName).collect(Collectors.joining(","));
            throw new ServiceException(drugsName + "已存在目录中,请重新勾选!");
        }
        //双通道药品目录维护，药品必须有医保编码
        if (DirectoryTypeEnum.isDualChannel(directoryType)) {
            LambdaQueryWrapper<BusDrugs> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(BusDrugs::getId, drugsIds);
            queryWrapper.eq(BusDrugs::getStatus, EnableStatusEnum.ENABLED.getStatus());
            List<BusDrugs> drugsList = busDrugsMapper.selectList(queryWrapper);
            Map<Long, BusDrugs> drugsMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(drugsList)) {
                drugsMap.putAll(drugsList.stream().filter(Objects::nonNull).collect(Collectors.toMap(BusDrugs::getId, Function.identity())));
            }
            drugsIds.forEach(drugsId -> {
                BusDrugs busDrugs = drugsMap.get(drugsId);
                if (Objects.isNull(busDrugs)) {
                    throw new ServiceException(String.format("药品id%s对应的药品已下架或不存在", drugsId));
                }
                if (Objects.isNull(busDrugs.getNationalDrugCode())) {
                    throw new ServiceException(String.format("药品%s未录入医保编码", busDrugs.getDrugsName()));
                }
            });
        }

    }

    /**
     * 药品加入到药房
     *
     * @param list 药品列表
     * @param shelf 是否上架
     */
    private void addHospitalOfficinas(List<BusDirectoryDrugs> list, boolean shelf) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String directoryType = list.stream().filter(Objects::nonNull).map(BusDirectoryDrugs::getDirectoryType).filter(StringUtils::isNotBlank).findFirst().orElse(null);
        //双通道药品不加入药房
        if (DirectoryTypeEnum.isDualChannel(directoryType)) {
            return;
        }
        // 查询医院是否配置销售价
        BigDecimal configureSalesPrice = null;
        BusConsultationSettings settings = busConsultationSettingsService.selectOne(new BusConsultationSettings().setHospitalId(list.get(0).getHospitalId()));
        if (Objects.nonNull(settings) && Objects.nonNull(settings.getConfigureSalesPrice())) {
            configureSalesPrice = settings.getConfigureSalesPrice();
        }
        //添加药房
        List<BusHospitalOfficina> hospitalOfficinaList = new ArrayList<>();
        for (BusDirectoryDrugs directoryDrugs : list) {
            // 保存到药房
            BusHospitalOfficina busHospitalOfficina = new BusHospitalOfficina();
            busHospitalOfficina.setDirectoryId(directoryDrugs.getId());
            busHospitalOfficina.setDrugsId(directoryDrugs.getDrugsId());
            busHospitalOfficina.setHospitalId(directoryDrugs.getHospitalId());
            busHospitalOfficina.setCreateBy(SecurityUtils.getUsername());
            busHospitalOfficina.setCreateTime(DateUtils.getNowDate());
            busHospitalOfficina.setStock(0);
            busHospitalOfficina.setStatus(shelf ? EnableStatusEnum.ENABLED.getStatus() : EnableStatusEnum.DISABLED.getStatus());
            BigDecimal referencePurchasePrice = directoryDrugs.getReferencePurchasePrice();
            //如果没有参考成本价，数据则不合规范
            if (referencePurchasePrice == null) {
                throw new ServiceException("参考成本价不能为空！");
            }
            BigDecimal sellingPrice;
            if (Objects.nonNull(configureSalesPrice)) {
                // 设置药房销售价 = 药品参考成本价 * 上浮百分比
                BigDecimal priceRate = configureSalesPrice.divide(new BigDecimal(100), 6, RoundingMode.HALF_UP);
                sellingPrice = referencePurchasePrice.multiply(priceRate.add(BigDecimal.ONE));
            } else {
                sellingPrice = referencePurchasePrice.multiply(new BigDecimal("1.15"));
            }
            busHospitalOfficina.setSellingPrice(sellingPrice);
            hospitalOfficinaList.add(busHospitalOfficina);
        }
        busHospitalOfficinaMapper.batchInsertMultiple(hospitalOfficinaList);
    }

    /**
     * 校验删除目录药品
     *
     * @param directoryDrugs 药品目录参数
     */
    private void checkDelete(BusDirectoryDrugs directoryDrugs) {
        //院内目录增加校验
        if (DirectoryTypeEnum.isInner(directoryDrugs.getDirectoryType())) {
            BusHospitalOfficina officina = busHospitalOfficinaMapper.selectOne(new LambdaQueryWrapper<BusHospitalOfficina>()
                    .eq(BusHospitalOfficina::getDirectoryId, directoryDrugs.getId()));
            if (Objects.nonNull(officina)) {
                if (EnableStatusEnum.isEnabled(officina.getStatus())){
                    throw new ServiceException("药品已在药房上架，暂不能删除！");
                }
            }
            List<BusHospitalPaDrugs> paDrugs = paDrugsMapper.selectList(new LambdaQueryWrapper<BusHospitalPaDrugs>()
                    .eq(BusHospitalPaDrugs::getDirectoryDrugsId, directoryDrugs.getId()));
            if (CollectionUtil.isNotEmpty(paDrugs)){
                throw new ServiceException("中药协定方中已绑定该药材，暂不能删除！");
            }
            //otc药品
            List<BusOtcDrugs> otcDrugs = otcDrugsMapper.selectList(new LambdaQueryWrapper<BusOtcDrugs>()
                    .eq(BusOtcDrugs::getDrugsId, directoryDrugs.getDrugsId())
                    .eq(BusOtcDrugs::getHospitalId, directoryDrugs.getHospitalId()));
            //用药预订单
            long preOrderCount = busPreorderDrugsMapper.countByDrugsId(directoryDrugs.getHospitalId(), directoryDrugs.getDrugsId());
            if (CollectionUtil.isNotEmpty(otcDrugs) || preOrderCount > 0){
                throw new ServiceException("该药品已生成订单，暂不能删除！");
            }
        }
        List<BusPrescriptionDrugs> prescriptionDrugs = prescriptionDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
                .eq(BusPrescriptionDrugs::getDrugsId, directoryDrugs.getDrugsId())
                .eq(BusPrescriptionDrugs::getHospitalId, directoryDrugs.getHospitalId()));
        if (CollectionUtil.isNotEmpty(prescriptionDrugs)){
            throw new ServiceException("该药品已生成处方，暂不能删除！");
        }
    }

}

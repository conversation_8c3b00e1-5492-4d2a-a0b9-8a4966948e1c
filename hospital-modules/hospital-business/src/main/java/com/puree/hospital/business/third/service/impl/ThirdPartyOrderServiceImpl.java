package com.puree.hospital.business.third.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.model.event.order.DrugsOrderShippedEvent;
import com.puree.hospital.app.api.model.event.order.ShopOrderShippedEvent;
import com.puree.hospital.business.api.model.dto.BusDrugTraceCodeDTO;
import com.puree.hospital.business.api.model.dto.ThirdPartyDeliverDTO;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.BusOfficinaPharmacist;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.BusOrderDrugTraceCode;
import com.puree.hospital.business.domain.BusPharmacistSchedule;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusSignature;
import com.puree.hospital.business.domain.vo.BusDrugsOrderVo;
import com.puree.hospital.business.domain.vo.OrderSendOutResultVO;
import com.puree.hospital.business.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.business.mapper.BusDrugsOrderMapper;
import com.puree.hospital.business.mapper.BusOfficinaPharmacistMapper;
import com.puree.hospital.business.mapper.BusOrderDrugTraceCodeMapper;
import com.puree.hospital.business.mapper.BusOrderMapper;
import com.puree.hospital.business.mapper.BusPharmacistScheduleMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.mapper.BusSignatureMapper;
import com.puree.hospital.business.service.IBusOrderService;
import com.puree.hospital.business.third.service.ThirdPartyOrderService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DrugTraceCodeStatusEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.NemberEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.SignatureRole;
import com.puree.hospital.common.core.enums.SignatureStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.order.api.model.BusShopOrder;
import com.puree.hospital.order.api.model.vo.BusDrugsOrderVO;
import com.puree.hospital.tool.api.RemoteKdnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @ClassName ThirdPartyOrderServiceImpl
 * <AUTHOR>
 * @Description 第三方订单服务实现类
 * @Date 2024/11/18 16:10
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ThirdPartyOrderServiceImpl implements ThirdPartyOrderService {

    private final IBusOrderService iBusOrderService;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPharmacistScheduleMapper busPharmacistScheduleMapper;
    private final BusOfficinaPharmacistMapper busOfficinaPharmacistMapper;
    private final BusOrderMapper busOrderMapper;
    private final BusSignatureMapper signatureMapper;
    private final BusOrderDrugTraceCodeMapper busOrderDrugTraceCodeMapper;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final RemoteKdnService remoteKdnService;
    private final BusDrugsOrderMapper busDrugsOrderMapper;


    @Resource
    @Lazy
    private ApplicationEventPublisher publisher;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean thirdPartyDeliver(List<ThirdPartyDeliverDTO> list) {
        //通过订单号获取商品药品ID，
        OrderSendOutResultVO resultVO = new OrderSendOutResultVO();
        BusOfficinaPharmacist pharmacist = new BusOfficinaPharmacist();
        BusOrder busOrder = null;
        // 无法区分是商品订单还是药品订单，是否有多个，采取Set去重
        Set<Long> drugOrderIds = new HashSet<>();
        Set<Long> shopOrderIds = new HashSet<>();
        Date nowDate = DateUtils.getNowDate();
        String selfPickupCode = String.valueOf(RandomUtils.nextInt(100000, 999999));
        for (ThirdPartyDeliverDTO dto : list) {
            //药品
            //中药处方不需要药品ID，西药处方需要药品ID
            //会按照处方发货
            //填充DTO
            fillThirdPartyDeliverDTO(dto);
            // 子订单编号
            String orderNo = dto.getOrderNo();
            BusDrugOrderPackage busDrugOrderPackageDTO = new BusDrugOrderPackage();
            if (OrderTypeConstant.DRUGS_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
                BusDrugsOrderVO drugsOrder = iBusOrderService.getDrugsOrdeByOrderNo(orderNo);
                // 添加到待推送事件集合
                drugOrderIds.add(drugsOrder.getId());
                String prescriptionType = "";
                if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
                    BusPrescription busPrescription = busPrescriptionMapper.selectById(drugsOrder.getPrescriptionId());
                    prescriptionType = busPrescription.getPrescriptionType();
                    // 检查当天发货药师或调剂药师有无排班
                    List<BusPharmacistSchedule> busPharmacistScheduleList = busPharmacistScheduleMapper.selectList(new LambdaQueryWrapper<BusPharmacistSchedule>()
                            .ge(BusPharmacistSchedule::getScheduleDate, DateUtils.getDate() + " 00:00:00")
                            .le(BusPharmacistSchedule::getScheduleDate, DateUtils.getDate() + " 23:59:59")
                            .eq(BusPharmacistSchedule::getScheduleStatus, "1"));
                    log.info("查询是否当天有排班：{}", busPharmacistScheduleList);
                    if (schedulingHandler(busPharmacistScheduleList, busPrescription)) {
                        return true;
                    }
                }
                // 根据子订单ID查询总订单信息
                busOrder = selectOrderInfo(CodeEnum.NO.getCode(), drugsOrder.getId());
                log.info("发货busOrder:{}", JSON.toJSONString(busOrder));
                if (Objects.isNull(busOrder)) {
                    throw new ServiceException("订单信息不存在");
                }
                //医保西药处理
                westernMedicine(busOrder, prescriptionType, dto, drugsOrder, resultVO);

                //查询包裹是否发货
                BusDrugOrderPackage drugOrderPackage = getBusDrugOrderPackage(drugsOrder);
                log.info("包裹查出信息：{}", drugOrderPackage);
                if (Objects.nonNull(drugOrderPackage)) {
                    if (Objects.nonNull(drugOrderPackage.getDeliveryTime())) {
                        throw new ServiceException("该订单商品已经发货，请刷新页面");
                    }
                    /*配送企业订单，自提发货需要做更新操作*/
                    if (CodeEnum.NO.getCode().equals(busOrder.getDeliveryType())) {
                        fulfillmentHandler(busDrugOrderPackageDTO, selfPickupCode, busOrder, nowDate, drugOrderPackage, dto);
                        continue;
                    }
                }
                drugAfterHandler(busDrugOrderPackageDTO, busOrder, drugsOrder, dto);
            } else if (OrderTypeConstant.GOODS_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
                //商品
                BusShopOrder shopOrder = iBusOrderService.getShopOrdeByOrderNo(orderNo);
                // 添加到待推送事件集合
                shopOrderIds.add(shopOrder.getId());
                // 查询总订单信息
                busOrder = selectOrderInfo(CodeEnum.YES.getCode(), shopOrder.getId());
                /*查询包裹是否发货*/
                BusDrugOrderPackage shopOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                        .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.YES.getCode())
                        .eq(BusDrugOrderPackage::getDrugsOrderId, shopOrder.getId())
                        .eq(BusDrugOrderPackage::getDrugsGoodsId, dto.getDrugsId())
                        .last("limit 1")
                );
                if (Objects.nonNull(shopOrderPackage) && Objects.nonNull(shopOrderPackage.getDeliveryTime())) {
                    throw new ServiceException("该订单已经发货");
                }
                if (Objects.nonNull(busOrder.getAfterSaleId())) {
                    busDrugOrderPackageDTO.setBarterOrNot(YesNoEnum.YES.getCode());
                }
                //商品处理
                shopAfterHandler(busDrugOrderPackageDTO, busOrder, shopOrder, dto);
            }
            if (YesNoEnum.YES.getCode().equals(busDrugOrderPackageDTO.getDeliveryType())) {
                /*查询快递鸟*/
                JSONArray shippers = this.getKDNDelivery(dto.getExpressNo());
                if (CollectionUtil.isNotEmpty(shippers)) {
                    JSONObject shipper = (JSONObject) shippers.get(0);
                    busDrugOrderPackageDTO.setLogisticsCompany(shipper.getString("ShipperName"));
                    busDrugOrderPackageDTO.setExpressCode(shipper.getString("ShipperCode"));
                } else {
                    throw new ServiceException("请输入正确的快递单号");
                }
            } else if (YesNoEnum.NO.getCode().equals(busDrugOrderPackageDTO.getDeliveryType())) {
                /*设置提货码*/
                busDrugOrderPackageDTO.setCode(selfPickupCode);
            } else {
                // 设置商品无需物流
                busDrugOrderPackageDTO.setCode("2");
            }
            busDrugOrderPackageDTO.setDeliveryNo(dto.getExpressNo());
            busDrugOrderPackageDTO.setDeliveryTime(nowDate);
            busDrugOrderPackageDTO.setCreateTime(DateUtils.getNowDate());
            busDrugOrderPackageDTO.setCreateBy(SecurityUtils.getUsername());
            log.info("发货新增包裹:{}",JSON.toJSONString(busDrugOrderPackageDTO));
            // 保存非配送企业发货包裹信息
            busDrugOrderPackageDTO.setId(null);
            busDrugOrderPackageMapper.insert(busDrugOrderPackageDTO);

            // 修改订单状态
            int result = this.updateBusOrderStatus(busOrder);
            if (result > 0) {
                pharmacist.setSignatureStatus(SignatureStatusEnum.NORMAL.getCode());
            }
        }
        resultVO.setPharmacist(pharmacist);
        // 发送发货事件通知
        sendOrderShippedEventNotification(drugOrderIds, shopOrderIds);
        return true;
    }

    /**
     * 发送发货事件通知
     * @param drugOrderIds  药品订单ID
     * @param shopOrderIds  商品订单ID
     */
    private void sendOrderShippedEventNotification(Set<Long> drugOrderIds, Set<Long> shopOrderIds) {
        // 发送药品订单发货事件
        if (CollUtil.isNotEmpty(drugOrderIds)) {
            drugOrderIds.forEach(item -> {
                DrugsOrderShippedEvent drugsOrderShippedEvent = new DrugsOrderShippedEvent();
                drugsOrderShippedEvent.setSubOrderId(item);
                publisher.publishEvent(drugsOrderShippedEvent);
            });
        }
        // 发送商品订单发货事件
        if (CollUtil.isNotEmpty(shopOrderIds)) {
            shopOrderIds.forEach(item -> {
                ShopOrderShippedEvent shopOrderShippedEvent = new ShopOrderShippedEvent();
                shopOrderShippedEvent.setSubOrderId(item);
                publisher.publishEvent(shopOrderShippedEvent);
            });
        }
    }

    /**
     * 商品处理
     * @param busDrugOrderPackageDTO
     * @param busOrder
     * @param shopOrder
     * @param dto
     */
    private static void shopAfterHandler(BusDrugOrderPackage busDrugOrderPackageDTO, BusOrder busOrder, BusShopOrder shopOrder, ThirdPartyDeliverDTO dto) {
        busDrugOrderPackageDTO.setHospitalId(busOrder.getHospitalId());
        busDrugOrderPackageDTO.setDrugsOrderId(shopOrder.getId());
        busDrugOrderPackageDTO.setDrugsGoodsId(dto.getDrugsId());
        busDrugOrderPackageDTO.setPackageType(YesNoEnum.YES.getCode());
        busDrugOrderPackageDTO.setDeliveryType(Integer.valueOf(busOrder.getDeliveryType()));
        busDrugOrderPackageDTO.setEnterpriseName("药房");
        busDrugOrderPackageDTO.setOrderNo(busOrder.getOrderNo());
    }

    /**
     * 医保和西药处理
     * @param busOrder
     * @param prescriptionType
     * @param dto
     * @param drugsOrder
     * @param resultVO
     */
    private void westernMedicine(BusOrder busOrder, String prescriptionType, ThirdPartyDeliverDTO dto, BusDrugsOrderVO drugsOrder, OrderSendOutResultVO resultVO) {
        // 如果是医保支付订单，而且是西药处方
        if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay())
                && String.valueOf(PrescriptionTypeEnum.MM.getCode()).equals(prescriptionType)) {
            List<BusDrugTraceCodeDTO> drugTraceCodeList = dto.getDrugTraceCodeList();
            if (CollectionUtil.isNotEmpty(drugTraceCodeList)) {
                //溯源码处理
                drugTraceCodeHandler(drugTraceCodeList, busOrder, drugsOrder, dto);
                resultVO.setNeedUploadTraceCode(true);
            }
            resultVO.setTotalOrderNo(busOrder.getOrderNo());
            resultVO.setHospitalId(busOrder.getHospitalId());
        }
    }

    /**
     * 药品后置处理
     * @param busDrugOrderPackageDTO
     * @param busOrder
     * @param drugsOrder
     * @param dto
     */
    private static void drugAfterHandler(BusDrugOrderPackage busDrugOrderPackageDTO, BusOrder busOrder, BusDrugsOrderVO drugsOrder, ThirdPartyDeliverDTO dto) {
        busDrugOrderPackageDTO.setHospitalId(busOrder.getHospitalId());
        busDrugOrderPackageDTO.setPackageDrugType(YesNoEnum.YES.getCode());
        busDrugOrderPackageDTO.setPrescriptionId(drugsOrder.getPrescriptionId());
        busDrugOrderPackageDTO.setDrugsOrderId(drugsOrder.getId());
        busDrugOrderPackageDTO.setDrugsGoodsId(dto.getDrugsId());
        busDrugOrderPackageDTO.setPackageType(YesNoEnum.NO.getCode());
        busDrugOrderPackageDTO.setDeliveryType(Integer.valueOf(busOrder.getDeliveryType()));
        busDrugOrderPackageDTO.setOrderNo(busOrder.getOrderNo());
        busDrugOrderPackageDTO.setEnterpriseName("药房");
        if (Objects.nonNull(busOrder.getAfterSaleId())) {
            busDrugOrderPackageDTO.setBarterOrNot(YesNoEnum.YES.getCode());
        }
    }

    /**
     * 配送企业处理
     * @param busDrugOrderPackageDTO
     * @param selfPickupCode
     * @param busOrder
     * @param nowDate
     * @param drugOrderPackage
     * @param dto
     */
    private void fulfillmentHandler(BusDrugOrderPackage busDrugOrderPackageDTO, String selfPickupCode, BusOrder busOrder, Date nowDate, BusDrugOrderPackage drugOrderPackage, ThirdPartyDeliverDTO dto) {
        /*设置提货码*/
        busDrugOrderPackageDTO.setCode(selfPickupCode);
        busDrugOrderPackageDTO.setOrderNo(busOrder.getOrderNo());
        busDrugOrderPackageDTO.setDeliveryTime(nowDate);
        busDrugOrderPackageDTO.setId(drugOrderPackage.getId());
        busDrugOrderPackageDTO.setDrugsGoodsId(dto.getDrugsId());
        busDrugOrderPackageDTO.setCreateTime(DateUtils.getNowDate());
        busDrugOrderPackageDTO.setCreateBy(SecurityUtils.getUsername());
        log.info("自提发货相关参数：{}", busDrugOrderPackageDTO);
        busDrugOrderPackageMapper.updateById(busDrugOrderPackageDTO);
        // 更新订单状态
        this.updateBusOrderStatus(busOrder);
    }

    /**
     * 排版
     * @param busPharmacistScheduleList
     * @param busPrescription
     * @return 签名有问题会直接跳过
     */
    private boolean schedulingHandler(List<BusPharmacistSchedule> busPharmacistScheduleList, BusPrescription busPrescription) {
        for(BusPharmacistSchedule busPharmacistSchedule : busPharmacistScheduleList) {
            long deliveryPharmacistId = busPharmacistSchedule.getPharmacistId();
            BusOfficinaPharmacist busOfficinaPharmacist = busOfficinaPharmacistMapper.selectById(deliveryPharmacistId);
            if (Objects.isNull(busOfficinaPharmacist)) {
                throw new ServiceException("药师不存在！");
            }
            LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
            if("0".equals(busPharmacistSchedule.getPharmacistType())){
                lambdaQuery.eq(BusSignature::getObjectType, SignatureRole.DELIVERY_PHARMACIST.getValue());
                // 修改处方发货药师
                busPrescription.setDeliveryPharmacistId(busOfficinaPharmacist.getId());
                busPrescription.setDeliveryPharmacistName(busOfficinaPharmacist.getPharmacistName());
                busPrescriptionMapper.updateById(busPrescription);
                log.info("修改处方发货药师：{}", JSON.toJSONString(busPrescription));
            }else if("2".equals(busPharmacistSchedule.getPharmacistType())){
                lambdaQuery.eq(BusSignature::getObjectType, SignatureRole.CHEMIST.getValue());
                // 修改处方调剂药师
                busPrescription.setDispensingPharmacistId(busOfficinaPharmacist.getId());
                busPrescription.setDispensingPharmacistName(busOfficinaPharmacist.getPharmacistName());
                busPrescriptionMapper.updateById(busPrescription);
                log.info("修改处方调剂药师：{}", JSON.toJSONString(busPrescription));
            }
            lambdaQuery.eq(BusSignature::getObjectId, busOfficinaPharmacist.getId());
            BusSignature signature = signatureMapper.selectOne(lambdaQuery);
            if (null == signature || null == signature.getEndDate() || DateUtils.getNowDate().compareTo(signature.getEndDate()) >= 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询包裹是否发货
     * @param drugsOrder
     * @return
     */
    private BusDrugOrderPackage getBusDrugOrderPackage(BusDrugsOrderVO drugsOrder) {
        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode());
        lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrder.getId());
        lambdaQuery.last("limit 1");
        return busDrugOrderPackageMapper.selectOne(lambdaQuery);
    }

    /**
     * 药品溯源码处理
     * @param drugTraceCodeList
     * @param busOrder
     * @param drugsOrder
     * @param dto
     */
    private void drugTraceCodeHandler(List<BusDrugTraceCodeDTO> drugTraceCodeList, BusOrder busOrder, BusDrugsOrderVO drugsOrder, ThirdPartyDeliverDTO dto) {
        //将溯源码信息保存到数据库中
        for (BusDrugTraceCodeDTO input : drugTraceCodeList) {
            if (Objects.isNull(input)
                    || StringUtils.isBlank(input.getProductionBatchNo())
                    || StringUtils.isBlank(input.getProductionDate())
                    || StringUtils.isBlank(input.getDrugTraceCode())) {
                throw new ServiceException("请填写完整的溯源相关信息");
            }
            BusOrderDrugTraceCode busOrderDrugTraceCode = new BusOrderDrugTraceCode();
            busOrderDrugTraceCode.setHospitalId(busOrder.getHospitalId());
            busOrderDrugTraceCode.setOrderNo(busOrder.getOrderNo());
            busOrderDrugTraceCode.setPrescriptionId(drugsOrder.getPrescriptionId());
            busOrderDrugTraceCode.setDrugId(dto.getDrugsId());
            busOrderDrugTraceCode.setProductionBatchNo(input.getProductionBatchNo());
            busOrderDrugTraceCode.setProductionDate(input.getProductionDate());
            busOrderDrugTraceCode.setDrugTraceCode(input.getDrugTraceCode());
            busOrderDrugTraceCode.setStatus(DrugTraceCodeStatusEnum.PENDING_UPLOAD.getStatus());
            busOrderDrugTraceCodeMapper.insert(busOrderDrugTraceCode);
        }
    }


    /**
     * @param dto
     * @return ThirdPartyDeliverDTO
     * @description 填充DTO
     * <AUTHOR>
     * @date 2024/11/25 18:11
     **/
    private ThirdPartyDeliverDTO fillThirdPartyDeliverDTO(ThirdPartyDeliverDTO dto) {
        //填充处方信息
        BusPrescription busPrescription = busPrescriptionMapper.selectByNumber(dto.getPrescriptionNo());
        if (ObjectUtil.isNull(busPrescription)){
            throw new ServiceException("未查询到该处方");
        }
        dto.setPrescriptionId(busPrescription.getId());
        //理论上当遇到拆单情况时同一个处方会存在两个子订单，但是开发区医院的HIS场景不存在这种拆单的情况，所以这里取第一个即可
        List<BusDrugsOrderVo> orderVos = busDrugsOrderMapper.selectByPrescriptionId(dto.getPrescriptionId());
        if (CollUtil.isEmpty(orderVos)){
            throw new ServiceException("未查询到该订单");
        }
        dto.setOrderNo(orderVos.get(0).getOrderNo());
        return dto;
    }


     /**
     * 查询总订单信息
     * @param code 0药品 1商品
     * @param id 子订单ID
     * @return
     */
    private BusOrder selectOrderInfo(String code, Long id) {
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getSubOrderType, code);
        lambdaQuery.eq(BusOrder::getSubOrderId, id);
        return busOrderMapper.selectOne(lambdaQuery);
    }


    /**
     * 更新总订单表状态
     */
    public int updateBusOrderStatus(BusOrder busOrder){
        BusOrder order = new BusOrder();
        order.setDeliveryTime(DateUtils.getNowDate());
        if (CodeEnum.NO.getCode().equals(busOrder.getDeliveryType())) {
            order.setOrderStatus(DrugsOrderEnum.TOBEPICKEDUP.getCode());
        } else {
            order.setOrderStatus(DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getCode());
        }
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getOrderNo, busOrder.getOrderNo());
        return busOrderMapper.update(order, lambdaQuery);
    }

    /**
     * 快递鸟查询
     */
    public JSONArray getKDNDelivery(String deliveryNo){
        /*查询快递鸟*/
        if (deliveryNo.contains("JT")){
            throw new ServiceException("暂不支持极兔快递");
        }
        R<String> objectR = remoteKdnService.searchKdInfo(deliveryNo);
        if (!Constants.SUCCESS.equals(objectR.getCode())) {
            throw new ServiceException(objectR.getMsg());
        }
        log.info("发货快递鸟信息objectR ={}",objectR.getData());
        JSONObject jsonObject = JSONObject.parseObject((String)objectR.getData());
        JSONArray shippers = jsonObject.getJSONArray("Shippers");
        if (null == shippers ||shippers.isEmpty()){
            throw new ServiceException("您的快递单号无物流信息，请检查单号是否有误");
        }
        return  shippers;
    }

}

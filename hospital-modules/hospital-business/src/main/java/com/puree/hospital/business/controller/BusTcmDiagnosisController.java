package com.puree.hospital.business.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.puree.hospital.business.api.model.DiagnosisVO;
import com.puree.hospital.business.api.model.dto.DiagnosisDTO;
import com.puree.hospital.business.domain.BusTcmDiagnosis;
import com.puree.hospital.business.domain.dto.DiagnosisQueryDTO;
import com.puree.hospital.business.domain.vo.DiagnosisQueryVO;
import com.puree.hospital.business.service.IBusDiseaseService;
import com.puree.hospital.business.service.IBusTcmDiagnosisService;
import com.puree.hospital.business.service.IBusTcmSyndromeService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 中医诊断信息Controller
 *
 * <AUTHOR>
 * @date 2021-10-26
 */
@RestController
@RequestMapping("/tcmDiagnosis")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusTcmDiagnosisController extends BaseController {
    private final IBusTcmDiagnosisService busTcmDiagnosisService;
    private final IBusDiseaseService busDiseaseService;
    private final IBusTcmSyndromeService busTcmSyndromeService;

    /**
     * 查询中医诊断信息列表
     */
    @PreAuthorize(hasPermi = "business:diagnosis:list")
    @GetMapping("/list")
    @Log(title = "查询中医诊断信息列表", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public TableDataInfo list(BusTcmDiagnosis busTcmDiagnosis) {
        startPage();
        List<BusTcmDiagnosis> list = busTcmDiagnosisService.selectBusTcmDiagnosisList(busTcmDiagnosis);
        return getDataTable(list);
    }

    /**
     * 获取中医诊断信息详细信息
     */
    @PreAuthorize(hasPermi = "business:diagnosis:query")
    @GetMapping(value = "/{id}")
    @Log(title = "获取中医诊断信息详细信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(busTcmDiagnosisService.selectBusTcmDiagnosisById(id));
    }

    /**
     * 新增中医诊断信息
     */
    @PreAuthorize(hasPermi = "business:diagnosis:add")
    @Log(title = "中医诊断信息", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    @PostMapping
    public AjaxResult add(@RequestBody BusTcmDiagnosis busTcmDiagnosis) {
        busTcmDiagnosis.setCreateBy(SecurityUtils.getUsername());
        return toAjax(busTcmDiagnosisService.insertBusTcmDiagnosis(busTcmDiagnosis));
    }

    /**
     * 修改中医诊断信息
     */
    @PreAuthorize(hasPermi = "business:diagnosis:edit")
    @Log(title = "中医诊断信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PutMapping
    public AjaxResult edit(@RequestBody BusTcmDiagnosis busTcmDiagnosis) {
        busTcmDiagnosis.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(busTcmDiagnosisService.updateBusTcmDiagnosis(busTcmDiagnosis));
    }

    /**
     * 删除中医诊断信息
     */
    @PreAuthorize(hasPermi = "business:diagnosis:remove")
    @Log(title = "中医诊断信息", businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(busTcmDiagnosisService.deleteBusTcmDiagnosisByIds(ids));
    }

    /**
     * 中医诊断停用启用操作
     *
     * @param busTcmDiagnosis   入参
     * @return  限制集
     */
    @PreAuthorize(hasPermi = "business:diagnosis:lock")
    @Log(title = "中医诊断信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PostMapping("/lock")
    public AjaxResult lock(@RequestBody BusTcmDiagnosis busTcmDiagnosis) {
        busTcmDiagnosis.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(busTcmDiagnosisService.lock(busTcmDiagnosis));
    }

    /**
     * 校验病种名称是否重复
     *
     * @param id    id
     * @param tcmDiagnosis  诊断名称
     * @return  是否重复
     */
    @GetMapping(value = "/checkDuplicateName")
    @Log(title = "中医诊断信息", businessType = BusinessType.OTHER,operatorType = OperatorType.MANAGE)
    public AjaxResult checkDuplicateName(Long id, String tcmDiagnosis) {
        return AjaxResult.success(busTcmDiagnosisService.checkTcmDiagnosis(id, tcmDiagnosis));
    }

    /**
     * 校验诊断代码是否重复
     *
     * @param id
     * @param diagnosisCode
     * @return
     */
    @GetMapping(value = "/checkDuplicateCode")
    @Log(title = "中医诊断信息", businessType = BusinessType.OTHER,operatorType = OperatorType.MANAGE)
    public AjaxResult checkDuplicateCode(Long id, String diagnosisCode) {
        return AjaxResult.success(busTcmDiagnosisService.checkDiagnosisCode(id, diagnosisCode));
    }

    /**
     *  校验诊断是否支持医保支付
     * @param diagnosisDTO  入参
     * @return  是否校验通过
     */
    @PostMapping("/listByIds")
    @Log(title = "校验诊断是否支持医保支付", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public R<List<DiagnosisVO>> getDiagnosisListByIds(@RequestBody @Valid DiagnosisDTO diagnosisDTO) {
        if (CollectionUtil.isEmpty(diagnosisDTO.getDiagnosisIds())) {
            return R.ok();
        }
        List<DiagnosisVO> diagnosisList = null;
        // 西医诊断
        if (PrescriptionTypeEnum.isMm(diagnosisDTO.getPrescriptionType())) {
            diagnosisList = busDiseaseService.getDiagnosisListByIds(diagnosisDTO);
        // 中医诊断
        } else if (PrescriptionTypeEnum.isTcmOrZyxdf(diagnosisDTO.getPrescriptionType())) {
            diagnosisList = busTcmDiagnosisService.getDiagnosisListByIds(diagnosisDTO);
        }
        return R.ok(diagnosisList);
    }

    /**
     * 根据ids查询所有的诊断信息，包括启用和禁用的数据
     * @param diagnosisDTO    入参
     * @return  数据集合
     */
    @PostMapping("/allByIds")
    @Log(title = "根据Ids查询诊断", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public R<DiagnosisQueryVO> getDiagnosisByIds(@RequestBody DiagnosisQueryDTO diagnosisDTO) {
        DiagnosisQueryVO diagnosisQueryVO = new DiagnosisQueryVO();
        if (Objects.isNull(diagnosisDTO)) {
            return R.ok(diagnosisQueryVO);
        }
        // 校验西医诊断状态
        if (CollectionUtil.isNotEmpty(diagnosisDTO.getDiseaseId())) {
            diagnosisQueryVO.setDisease(busDiseaseService.queryDiseaseByIds(diagnosisDTO.getDiseaseId()));
        }
        // 校验中医诊断状态和中医疾病
        if (CollectionUtil.isNotEmpty(diagnosisDTO.getDiagnosisId())){
            diagnosisQueryVO.setDiagnosis(busTcmDiagnosisService.queryDiagnosisByIds(diagnosisDTO.getDiagnosisId()));
        }
        if (CollectionUtil.isNotEmpty(diagnosisDTO.getSyndromeId())) {
            diagnosisQueryVO.setSyndrome(busTcmSyndromeService.querySyndromeByIds(diagnosisDTO.getSyndromeId()));
        }
        return R.ok(diagnosisQueryVO);
    }
}

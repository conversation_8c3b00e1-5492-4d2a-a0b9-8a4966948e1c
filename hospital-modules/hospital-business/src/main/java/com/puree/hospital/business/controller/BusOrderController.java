package com.puree.hospital.business.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.api.RemoteWxPayService;
import com.puree.hospital.business.api.model.dto.BusHospitalOrderDTO;
import com.puree.hospital.business.domain.BusConsultationOrder;
import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.BusFiveServicePackOrder;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.mapper.BusConsultationOrderMapper;
import com.puree.hospital.business.mapper.BusFiveServicePackOrderMapper;
import com.puree.hospital.business.service.IBusDrugsOrderService;
import com.puree.hospital.business.service.IBusOrderService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.order.api.RemoteBusOrderService;
import com.puree.hospital.order.api.model.BusSubOrderDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;

@RestController
@RequestMapping("order")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOrderController extends BaseController {
    private final RemoteWxPayService remoteWxPayService;
    private final BusConsultationOrderMapper busConsultationOrderMapper;
    private final BusFiveServicePackOrderMapper busFiveServicePackOrderMapper;
    private final IBusOrderService busOrderService;
    private final RemoteBusOrderService remoteBusOrderService;
    private final IBusDrugsOrderService drugsOrderService;

    @PreAuthorize(hasPermi = "business:order:query")
    @GetMapping("/query")
    @Log(title = "订单信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public AjaxResult queryOrderInfo(@RequestParam("hospitalId") Long hospitalId,
                                     @RequestParam("mchType") String mchType,
                                     @RequestParam("orderNo") String orderNo) {
        String flag = orderNo.substring(0, 2);
        R<String> r = null;
        // 查询订单信息
        switch (flag) {
            case OrderTypeConstant.DRUGS_ORDER:
                BusDrugsOrder busDrugsOrder = drugsOrderService.queryDrugsOrderByNo(orderNo);
                String doStatus = busDrugsOrder.getStatus();
                if (DrugsOrderEnum.PENDING_PAYMENT.getCode().equals(doStatus) || DrugsOrderEnum.REFUNDED_ZHONG.getCode().equals(doStatus)) {
                    r = remoteWxPayService.queryOrder(hospitalId, mchType, orderNo);
                }
                break;
            case OrderTypeConstant.CONSULATION_ORDER:
                LambdaQueryWrapper<BusConsultationOrder> coWrapper = new LambdaQueryWrapper<BusConsultationOrder>()
                        .eq(BusConsultationOrder::getOrderNo, orderNo);
                BusConsultationOrder busConsultationOrder = busConsultationOrderMapper.selectOne(coWrapper);
                if (CodeEnum.NO.getCode().equals(busConsultationOrder.getOrderType())) {
                    String coStatus = busConsultationOrder.getStatus();
                    if ("0".equals(coStatus) || "9".equals(coStatus)) {
                        r = remoteWxPayService.queryOrder(hospitalId, mchType, orderNo);
                    }
                } else {
                    String coVideoStatus = busConsultationOrder.getVideoStatus();
                    if ("0".equals(coVideoStatus) || "9".equals(coVideoStatus)) {
                        r = remoteWxPayService.queryOrder(hospitalId, mchType, orderNo);
                    }
                }
                break;
            case OrderTypeConstant.SERVICE_PACK_ORDER:
                LambdaQueryWrapper<BusFiveServicePackOrder> soWrapper = new LambdaQueryWrapper<BusFiveServicePackOrder>()
                        .eq(BusFiveServicePackOrder::getOrderNo, orderNo);
                BusFiveServicePackOrder busFiveServicePackOrder = busFiveServicePackOrderMapper.selectOne(soWrapper);
                String soStatus = busFiveServicePackOrder.getStatus();
                if ("0".equals(soStatus) || "7".equals(soStatus)) {
                    r = remoteWxPayService.queryOrder(hospitalId, mchType, orderNo);
                }
                break;
            default:
        }
        if (StringUtils.isNotNull(r)) {
            int code = r.getCode();
            String msg = r.getMsg();
            String data = (String) r.getData();
            logger.info("远程调用订单支付查询code={}，查询结果={}", code, data);
            if (HttpStatus.SUCCESS == code) {
                return AjaxResult.success(data);
            } else {
                return AjaxResult.error(msg);
            }
        } else {
            return AjaxResult.error("该订单无需补单！");
        }
    }

    /**
     * 查询订单详细
     * @param orderNo 订单编号
     * @return
     */
    @GetMapping("/info")
    @PreAuthorize(hasPermi = "business:order:detail")
    @Log(title = "查询订单详细", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public AjaxResult selectOrderInfo(@RequestParam("orderNo") String orderNo) {
        return AjaxResult.success(busOrderService.selectOrderInfo(orderNo));
    }

    /**
     * 更新订单地址
     * @param busHospitalOrderDto
     * @return
     */
    @PutMapping("/update/address")
    @PreAuthorize(hasPermi = "business:order:updateAddress")
    @Log(title = "更新订单地址", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public AjaxResult updateOrderAddress(@RequestBody BusHospitalOrderDTO busHospitalOrderDto) {
        logger.info("更新订单地址busHospitalOrderDto={}", JSONObject.toJSONString(busHospitalOrderDto));
        return AjaxResult.success(busOrderService.updateOrderAddress(busHospitalOrderDto));
    }

    /**
     * 更新订单运费
     * @param busHospitalOrderDto
     * @return
     */
    @PutMapping("/update/freight")
    @PreAuthorize(hasPermi = "business:order:updateFreight")
    @Log(title = "更新订单运费", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public AjaxResult updateOrderAmount(@RequestBody BusHospitalOrderDTO busHospitalOrderDto) {
        return AjaxResult.success(busOrderService.updateOrderAmount(busHospitalOrderDto));
    }

    /**
     *  取消订单
     * @param busHospitalOrderDto
     * @return
     */
    @GetMapping("/update/cancel")
    @PreAuthorize(hasPermi = "business:order:cancel")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public AjaxResult updateCancelOrder(BusHospitalOrderDTO busHospitalOrderDto) {
        return AjaxResult.success(busOrderService.updateCancelOrder(busHospitalOrderDto));
    }


    /**
     * 医院后台-交易订单列表查询
     */
    @GetMapping("subOrder/list")
    @PreAuthorize(hasPermi = "business:order:list")
    public TableDataInfo listBusSubOrder(BusSubOrderDTO busSubOrderDTO){
        R<TableDataInfo> tableDataInfoR = remoteBusOrderService.listBusSubOrder(busSubOrderDTO);
        if (!Constants.SUCCESS.equals(tableDataInfoR.getCode())) {
            throw new ServiceException("交易订单列表查询失败");
        }
        return tableDataInfoR.getData();
    }

    /**
     * 医院后台交易订单导出-交易订单信息查询
     */
    @PostMapping("/export")
    @PreAuthorize(hasPermi = "business:order:export")
    public void listExportOrder(BusSubOrderDTO dto, HttpServletResponse response) throws IOException {
        OutputStream outputStream = response.getOutputStream();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件以附件形式下载
        response.setHeader("Content-disposition",
                "attachment;filename=down_" + URLEncoder.encode(System.currentTimeMillis() + ".xlsx", "utf-8"));
        busOrderService.exportOrder(dto, outputStream);
        outputStream.flush();
        response.getOutputStream().close();
    }

    /**
     * 统计待发货订单数量
     * @param order
     * @return
     */
    @GetMapping("/query/new")
    public AjaxResult countNewOrder(BusOrder order) {
        return AjaxResult.success(busOrderService.countNewOrder(order));
    }

    @GetMapping("/feign/drugsOrder")
    public R queryDrugsOrderByNo(@RequestParam("orderNo") String orderNo) {
        return R.ok(drugsOrderService.queryDrugsOrderByNo(orderNo));
    }

    /**
     * 查询发货商品/药品信息
     * @param orderNo
     * @return
     */
    @GetMapping("/delivery-list")
    public AjaxResult selectDeliveryList(@RequestParam("orderNo") String orderNo) {
        return AjaxResult.success(busOrderService.selectDeliveryList(orderNo));
    }

}

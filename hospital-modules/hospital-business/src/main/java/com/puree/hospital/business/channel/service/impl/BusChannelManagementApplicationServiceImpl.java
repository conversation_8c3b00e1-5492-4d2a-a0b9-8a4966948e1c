package com.puree.hospital.business.channel.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.puree.hospital.app.api.RemotePatientService;
import com.puree.hospital.app.api.RemoteUserService;
import com.puree.hospital.app.api.model.SMSLoginUser;
import com.puree.hospital.business.api.model.enums.ChannelIdentityTypeEnum;
import com.puree.hospital.business.api.model.event.ChannelIdentityCreatedEvent;
import com.puree.hospital.business.channel.domain.model.bo.AgentRef;
import com.puree.hospital.business.channel.domain.model.bo.ChannelRef;
import com.puree.hospital.business.channel.domain.model.bo.HospitalRef;
import com.puree.hospital.business.channel.domain.model.bo.UserRef;
import com.puree.hospital.business.channel.queue.producer.ChannelIdentityCreatedEventProducer;
import com.puree.hospital.business.domain.BusChannelIdentity;
import com.puree.hospital.business.domain.BusChannelPartner;
import com.puree.hospital.business.domain.BusChannelPartnerAgent;
import com.puree.hospital.business.domain.BusPatient;
import com.puree.hospital.business.domain.BusPatientReportPhysician;
import com.puree.hospital.business.channel.domain.command.AgentInfoUpdateCommand;
import com.puree.hospital.business.channel.domain.command.ChannelBindCommand;
import com.puree.hospital.business.channel.domain.command.ChannelInfoUpdateCommand;
import com.puree.hospital.business.channel.domain.model.aggregate.ChannelAgentAggregate;
import com.puree.hospital.business.channel.domain.model.aggregate.ChannelIdentityAggregate;
import com.puree.hospital.business.channel.domain.model.aggregate.ChannelPartnerAggregate;
import com.puree.hospital.business.channel.domain.model.bo.AgentInfoBO;
import com.puree.hospital.business.channel.domain.model.bo.AuditInfoBO;
import com.puree.hospital.business.channel.domain.model.bo.PhoneNumber;
import com.puree.hospital.business.channel.domain.service.IChannelDomainService;
import com.puree.hospital.business.domain.dto.BusPatientDto;
import com.puree.hospital.business.domain.dto.ChannelBindDTO;
import com.puree.hospital.business.domain.dto.QrCodeCreateDTO;
import com.puree.hospital.business.domain.vo.BusChannelIdentityVO;
import com.puree.hospital.business.domain.vo.BusPatientVO;
import com.puree.hospital.business.domain.vo.ChannelPartnerAgentVO;
import com.puree.hospital.business.domain.vo.ChannelPartnerVO;
import com.puree.hospital.business.channel.helper.ChannelBindSerializerHelper;
import com.puree.hospital.business.channel.helper.PatientChannelPartnerHelper;
import com.puree.hospital.business.service.IBusPatientReportPhysicianService;
import com.puree.hospital.business.service.IBusPatientService;
import com.puree.hospital.business.channel.service.IBusChannelManagementApplicationService;
import com.puree.hospital.business.channel.service.IBusChannelIdentityService;
import com.puree.hospital.business.channel.service.IBusChannelPartnerAgentService;
import com.puree.hospital.business.channel.service.IBusChannelPartnerService;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.DelFlagEnum;
import com.puree.hospital.common.core.enums.EnableStatusEnum;
import com.puree.hospital.common.core.enums.FollowUpStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.tool.api.RemoteBaseWxService;
import com.puree.hospital.tool.api.model.dto.QrCodeDTO;
import com.puree.hospital.tool.api.model.enums.QrCodeWxInterfaceEnum;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 渠道管理应用服务实现类
 * <AUTHOR>
 * @date 2025/06/23 16:56
 */
@Slf4j
@Service
public class BusChannelManagementApplicationServiceImpl implements IBusChannelManagementApplicationService {
    @Resource
    private RemoteBaseWxService remoteBaseWxService;
    @Resource
    private IBusChannelIdentityService busChannelIdentityService;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private RemotePatientService remotePatientService;
    @Resource
    private IChannelDomainService channelDomainService;
    @Resource
    private PatientChannelPartnerHelper patientChannelPartnerHelper;
    @Resource
    @Lazy
    private IBusPatientService busPatientService;
    @Resource
    private IBusChannelPartnerAgentService busChannelPartnerAgentService;
    @Resource
    private IBusChannelPartnerService busChannelPartnerService;
    @Resource
    private IBusPatientReportPhysicianService busPatientReportPhysicianService;
    @Resource
    @Lazy
    private ChannelIdentityCreatedEventProducer channelIdentityCreatedEventProducer;
    /**
     * 渠道推广小程序码路径
     */
    private static final String CHANNEL_PARTNER_QR_CODE_PATH = "pages/user/index";
    /**
     * 客户邀约小程序码路径
     */
    private static final String AGENT_QR_CODE_PATH = "pages/home/<USER>";

    /**
     * 获取渠道小程序码
     * 包含渠道推广码，客户邀约码
     *
     * @param qrCodeCreateDTO 二维码参数信息
     * @return 生成的二维码链接点至
     */
    @Override
    public String getQrCode(QrCodeCreateDTO qrCodeCreateDTO) {
        if (qrCodeCreateDTO == null || qrCodeCreateDTO.getHospitalId() == null) {
            throw new IllegalArgumentException("医院ID不能为空");
        }
        // 校验身份类型
        BusChannelIdentity hasChannelIdentity =  busChannelIdentityService.queryChannelIdentityByDto(qrCodeCreateDTO);
        if (Objects.isNull(hasChannelIdentity)) {
            throw new ServiceException("渠道身份不存在,请联系管理员");
        }
        // 路径
        String path;
        ChannelBindDTO channelBindDTO = new ChannelBindDTO();
        channelBindDTO.setChannelIdentityId(hasChannelIdentity.getId());
        channelBindDTO.setIdentityType(hasChannelIdentity.getIdentityType());
        channelBindDTO.setVersion(qrCodeCreateDTO.getVersion());
        // 参数
        String params = new ChannelBindSerializerHelper().serialize(channelBindDTO);
        if (ChannelIdentityTypeEnum.CHANNEL.name().equalsIgnoreCase(qrCodeCreateDTO.getIdType())) {
            // 生成渠道推广码
            path = CHANNEL_PARTNER_QR_CODE_PATH;
        } else if (ChannelIdentityTypeEnum.AGENT.name().equals(qrCodeCreateDTO.getIdType())) {
            // 生成经纪人名片小程序码
            path = AGENT_QR_CODE_PATH;
        } else {
            log.error("不支持的渠道身份类型: {}", qrCodeCreateDTO.getIdType());
            throw new ServiceException("不支持的渠道身份");
        }
        QrCodeDTO qrCodeDTO = new QrCodeDTO()
                .setHospitalId(qrCodeCreateDTO.getHospitalId())
                .setPath(path)
                .setParam(params)
                .setWxInterfaceDesc(QrCodeWxInterfaceEnum.GET_UNLIMITED_QRCODE.name());
        R<String> qrCode = remoteBaseWxService.getQrCode(qrCodeDTO
                , SecurityConstants.INNER);
        if (Objects.isNull(qrCode) || !qrCode.isSuccess()) {
            log.error("获取二维码失败,原因: {}", qrCode.getMsg());
            throw new ServiceException("获取二维码失败,请联系管理员");
        }
        return "data:image/jpeg;base64," + qrCode.getData();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindChannel(ChannelBindDTO channelBindDTO) {
        // 1.远程调配 - 登录用户信息
        SMSLoginUser user = getUser();
        String phoneNum = getUserPhoneByUserId(user.getUserid());
        // 2.构建调配参数
        ChannelBindCommand bindCommand = ChannelBindCommand.builder()
                .userId(user.getUserid())
                .hospitalId(user.getHospitalId())
                .phoneNumber(phoneNum)
                .channelIdentityId(channelBindDTO.getChannelIdentityId())
                .agentFullName(channelBindDTO.getAgentFullName())
                .agentShopName(channelBindDTO.getAgentShopName())
                .build();
        channelDomainService.channelBind(bindCommand);
        // 3.删除患者渠道身份缓存
        patientChannelPartnerHelper.deletePatientRelationCache(phoneNum, user.getHospitalId());
        // 4.发送渠道身份创建事件
        ChannelIdentityCreatedEvent event = new ChannelIdentityCreatedEvent();
        event.setHospitalId(user.getHospitalId());
        event.setPhoneNumber(phoneNum);
        channelIdentityCreatedEventProducer.send(event);
    }

    @Override
    public BusChannelIdentityVO getIdentityInfoByScene(String scene) {
        ChannelBindDTO channelBindDTO;
        try {
            channelBindDTO = new ChannelBindSerializerHelper().deserialize(scene);
        } catch (Exception e) {
            log.error("解析二维码参数失败: {}", e.getMessage());
            return null;
        }
        BusChannelIdentity busChannelIdentity = busChannelIdentityService.queryChannelIdentityByChannelBindDTO(channelBindDTO);
        if (busChannelIdentity == null) {
            return null;
        }
        BusChannelIdentityVO busChannelIdentityVO = new BusChannelIdentityVO();
        if (ChannelIdentityTypeEnum.isChannel(busChannelIdentity.getIdentityType())) {
            if (EnableStatusEnum.DISABLED.getStatus().equals(busChannelIdentity.getStatus())) {
                return null;
            }
            BusChannelPartner busChannelPartner = busChannelPartnerService.queryInfoByChannelId(busChannelIdentity.getId());
            busChannelIdentityVO.setChannelName(busChannelPartner.getFullName());
        }
        if (ChannelIdentityTypeEnum.isAgent(busChannelIdentity.getIdentityType())) {
            BusChannelPartnerAgent busChannelPartnerAgent = busChannelPartnerAgentService.queryInfoByChannelIdentityId(busChannelIdentity.getId());
            busChannelIdentityVO.setChannelName(busChannelPartnerAgent.getShopName());
        }
        BeanUtils.copyProperties(busChannelIdentity, busChannelIdentityVO);
        busChannelIdentityVO.setVersion(channelBindDTO.getVersion());
        return busChannelIdentityVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateAgentInfoAndStatus(BusChannelPartnerAgent channelPartnerAgent) {
        if (Objects.isNull(channelPartnerAgent) || Objects.isNull(channelPartnerAgent.getId())) {
            throw new ServiceException("经纪人信息不能为空");
        }
        // 1.远程调配 - 登录用户信息
        BusChannelPartnerAgent channelPartnerAgentById = busChannelPartnerAgentService.getById(channelPartnerAgent.getId());
        if (Objects.isNull(channelPartnerAgentById)) {
            throw new ServiceException("经纪人信息不存在");
        }
        BusChannelIdentity channelIdentity = busChannelIdentityService.getById(channelPartnerAgentById.getChannelIdentityId());
        if (Objects.isNull(channelIdentity) || !ChannelIdentityTypeEnum.AGENT.name().equalsIgnoreCase(channelIdentity.getIdentityType())) {
            throw new ServiceException("经纪人身份信息不存在");
        }
        AgentInfoUpdateCommand agentInfoUpdateCommand = AgentInfoUpdateCommand.builder()
                .agentId(channelPartnerAgent.getId())
                .agentFullName(channelPartnerAgent.getFullName())
                .agentShopName(channelPartnerAgent.getShopName())
                .hospitalId(channelPartnerAgent.getHospitalId())
                .agentStatus(EnableStatusEnum.isDisabled(channelPartnerAgent.getStatus())?
                        EnableStatusEnum.DISABLED : EnableStatusEnum.ENABLED)
                .build();
        // 更新经纪人姓名和店铺名称
        int count = channelDomainService.updateAgentInfoAndStatus(agentInfoUpdateCommand);
        if (count > 0) {
            // 删除缓存
            patientChannelPartnerHelper.deletePatientRelationCache(channelIdentity.getPhoneNumber(), channelPartnerAgent.getHospitalId());
        }
        return count;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addNewChannel(BusChannelPartner busChannelPartner) {
        if (Objects.isNull(busChannelPartner) || Objects.isNull(busChannelPartner.getHospitalId())) {
            throw new ServiceException("合作渠道信息不能为空");
        }
        // 1.校验逻辑
        // 1.1 校验手机号是否已存在渠道身份
        if (checkPhoneNumberIdentity(busChannelPartner.getPhonenumber(), busChannelPartner.getHospitalId())) {
            throw new ServiceException("该手机号已存在渠道身份，请勿重复添加");
        }
        // 1.2 校验手机号是否绑定过经纪人 - 需要调用外部服务协助
        BusPatientVO byPhoneNumber = busPatientService.getByPhoneNumber(busChannelPartner.getPhonenumber());
        if (Objects.nonNull(byPhoneNumber) && busChannelPartnerAgentService.checkExistInAgentRelation(byPhoneNumber.getId(), busChannelPartner.getHospitalId())) {
            throw new ServiceException("该手机号已绑定经纪人，请勿重复添加", 502);
        }
        // 调用领域服务添加新合作渠道
        ChannelPartnerAggregate channel = ChannelPartnerAggregate.create(
                HospitalRef.of(busChannelPartner.getHospitalId()),
                ChannelPartnerAggregate.ChannelInfo.of(
                        busChannelPartner.getFullName(),
                        busChannelPartner.getContacts()),
                AuditInfoBO.createdBy(SecurityUtils.getUsername()),
                ChannelIdentityAggregate.create(
                        ChannelIdentityAggregate.IdentityType.CHANNEL,
                        HospitalRef.of(busChannelPartner.getHospitalId()),
                        PhoneNumber.of(busChannelPartner.getPhonenumber())
                ));
        if (EnableStatusEnum.DISABLED.getStatus().equals(busChannelPartner.getStatus())) {
            // 需要设置为禁用状态
            channel.getCurrentChannelIdentityAggregate().disable();
        }
        int count = channelDomainService.addNewChannel(channel);
        return createIdentityToDeleteCacheAndSendEvent(count, busChannelPartner.getPhonenumber(), busChannelPartner.getHospitalId());
    }

    /**
     * 创建渠道身份并删除缓存，发送事件
     *
     * @param count              创建的数量
     * @param busChannelPartner  渠道手机号
     * @param busChannelPartner1 医院ID
     * @return 创建的数量
     */
    private int createIdentityToDeleteCacheAndSendEvent(int count, String busChannelPartner, Long busChannelPartner1) {
        if (count > 0) {
            // 删除患者渠道身份缓存
            patientChannelPartnerHelper.deletePatientRelationCache(busChannelPartner, busChannelPartner1);
            ChannelIdentityCreatedEvent event = new ChannelIdentityCreatedEvent();
            event.setHospitalId(busChannelPartner1);
            event.setPhoneNumber(busChannelPartner);
            channelIdentityCreatedEventProducer.send(event);
        }
        return count;
    }

    @Override
    public boolean checkPhoneNumberIdentity(String phoneNumber, Long hospitalId) {
        if (StringUtils.isEmpty(phoneNumber) || Objects.isNull(hospitalId)) {
            throw new ServiceException("手机号或医院ID不能为空");
        }
        LambdaQueryWrapper<BusChannelIdentity> queryWrapper = new LambdaQueryWrapper<BusChannelIdentity>()
                .eq(BusChannelIdentity::getPhoneNumber, phoneNumber)
                .eq(BusChannelIdentity::getHospitalId, hospitalId)
                .eq(BusChannelIdentity::getDelFlag, DelFlagEnum.NORMAL.getCode());
        return busChannelIdentityService.count(queryWrapper) > 0;
    }

    @Override
    @Transactional
    public int editChannelInfoAndStatus(BusChannelPartner busChannelPartner) {
        if (Objects.isNull(busChannelPartner) || Objects.isNull(busChannelPartner.getId())) {
            throw new ServiceException("合作渠道信息不能为空");
        }
        // 1.校验逻辑
        ChannelInfoUpdateCommand command = ChannelInfoUpdateCommand.builder()
                .channelStatus(EnableStatusEnum.DISABLED.getStatus().equals(busChannelPartner.getStatus())?
                        EnableStatusEnum.DISABLED : EnableStatusEnum.ENABLED)
                .channelFullName(busChannelPartner.getFullName())
                .channelContactName(busChannelPartner.getContacts())
                .channelId(busChannelPartner.getId())
                .build();
        int count = channelDomainService.editChannelInfoAndStatus(command);
        if (count > 0) {
            // 删除患者渠道身份缓存
            patientChannelPartnerHelper.deletePatientRelationCache(busChannelPartner.getPhonenumber(), busChannelPartner.getHospitalId());
        }
        return count;
    }

    @Override
    @Transactional
    public boolean unbindAgent(Long hospitalId, String phoneNumber) {
        if (Objects.isNull(hospitalId) || StringUtils.isEmpty(phoneNumber)) {
            throw new ServiceException("医院ID或经纪人手机号不能为空");
        }
        BusPatientVO byPhoneNumber = busPatientService.getByPhoneNumber(phoneNumber);
        channelDomainService.unbindAgent(
                UserRef.of(byPhoneNumber.getId()),
                HospitalRef.of(hospitalId));
        return true;
    }

    @Override
    @Transactional
    public int removeChannelById(Long id) {
        LambdaQueryWrapper<BusChannelPartnerAgent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusChannelPartnerAgent::getChannelPartnerId, id);
        wrapper.eq(BusChannelPartnerAgent::getDelFlag, DelFlagEnum.NORMAL.getCode());
        long list = busChannelPartnerAgentService.count(wrapper);
        if (list > 0){
            throw new ServiceException("渠道商存在经纪人，无法删除");
        }
        ChannelPartnerVO busChannelPartner = busChannelPartnerService.queryInfo(id);
        int delete = channelDomainService.removeChannelById(ChannelRef.of(id));
        if (delete > 0) {
            patientChannelPartnerHelper.deletePatientRelationCache(busChannelPartner.getPhonenumber(), busChannelPartner.getHospitalId());
        }
        return delete;
    }

    @Override
    @Transactional
    public int lockChannel(BusChannelPartner busChannelPartner) {
        ChannelPartnerVO info = busChannelPartnerService.queryInfo(busChannelPartner.getId());
        if (Objects.isNull(info) || StringUtils.isEmpty(info.getPhonenumber())) {
            return 0;
        }
        // 注释经纪人登录token的删除 - 经纪人（医伴经纪人）小程序不用了
//      redisService.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + info.getToken());
        patientChannelPartnerHelper.deletePatientRelationCache(info.getPhonenumber(), info.getHospitalId());

        //渠道禁用，患者报告的健管师需要重新分配
        BusPatientReportPhysician busPatientReportPhysician = new BusPatientReportPhysician();
        LambdaUpdateWrapper<BusPatientReportPhysician> updateWrapper = new LambdaUpdateWrapper<>();
        //渠道id
        updateWrapper.set(BusPatientReportPhysician::getDoctorId, null).set(BusPatientReportPhysician::getDoctorName, null)
                .eq(BusPatientReportPhysician::getPartnerId, busChannelPartner.getId())
                .eq(BusPatientReportPhysician::getFollowUpStatus, FollowUpStatusEnum.TO_BE_FOLLOWED_UP.getCode());
        busPatientReportPhysicianService.update(busPatientReportPhysician, updateWrapper);
        ChannelInfoUpdateCommand command = ChannelInfoUpdateCommand.builder()
                .channelId(busChannelPartner.getId())
                .channelStatus(EnableStatusEnum.isDisabled(busChannelPartner.getStatus())? EnableStatusEnum.DISABLED : EnableStatusEnum.ENABLED)
                .build();
        return channelDomainService.updateChannelStatus(command);
    }

    @Transactional
    @Override
    public int agentLock(BusChannelPartnerAgent busChannelPartnerAgent) {
        ChannelPartnerAgentVO info = busChannelPartnerAgentService.queryInfoById(busChannelPartnerAgent.getId());
        if (ObjectUtil.isNull(info)){
            // 没有经纪人
            return 0;
        }
        patientChannelPartnerHelper.deletePatientRelationCache(info.getPhonenumber(), info.getHospitalId());
        AgentInfoUpdateCommand command = AgentInfoUpdateCommand.builder()
                .agentId(info.getId())
                .agentStatus(EnableStatusEnum.isDisabled(busChannelPartnerAgent.getStatus())? EnableStatusEnum.DISABLED : EnableStatusEnum.ENABLED)
                .build();
        return channelDomainService.updateAgentStatus(command);
    }

    @Override
    @Transactional
    public int updateBindingAgent(BusPatientDto dto) {
        //1.校验逻辑
        if (Objects.isNull(dto) || Objects.isNull(dto.getPatientId()) ||
                Objects.isNull(dto.getAgentId()) || Objects.isNull(dto.getHospitalId())) {
            throw new ServiceException("绑定参数错误，请联系管理员");
        }
        BusPatient busPatient = busPatientService.selectPatientInfo(dto.getPatientId());
        if (Objects.isNull(busPatient)) {
            throw new ServiceException("未找到该用户");
        }
        BusChannelIdentity busChannelIdentity = busChannelIdentityService.getByPhoneAndHospitalId(busPatient.getPhoneNumber(), dto.getHospitalId());
        if (Objects.nonNull(busChannelIdentity)) {
            throw new ServiceException("已经是合伙人/合作渠道,无法绑定");
        }
        return channelDomainService.updateBindingAgent(UserRef.of(dto.getPatientId()), AgentRef.of(dto.getAgentId())
                , HospitalRef.of(dto.getHospitalId()));
    }

    @Override
    @Transactional
    public int distributionChannel(String ids, Long agentId, Long hospitalId) {
        String[] idsList = ids.split(",");
        Set<Long> userIds = new HashSet<>();
        for (String id : idsList) {
            userIds.add(Long.valueOf(id));
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return 0;
        }
        List<BusPatient> busPatient = busPatientService.selectPatientInfoByIds(new ArrayList<>(userIds));
        if (CollectionUtils.isEmpty(busPatient)) {
            throw new ServiceException("未查询到用户");
        }
        if (userIds.size() != busPatient.size()) {
            log.warn("入参用户{}，查询用户{}", userIds,busPatient);
            throw new ServiceException("未查询到用户");
        }
        List<String> phoneList = busPatient.stream().map(BusPatient::getPhoneNumber).collect(Collectors.toList());
        if (busChannelIdentityService.existsChannelIdentity(phoneList, hospitalId)) {
            List<BusChannelIdentity> busChannelIdentity = busChannelIdentityService.selectChannelIdentityByPhoneList(phoneList, hospitalId);
            String phones = busChannelIdentity.stream().map(BusChannelIdentity::getPhoneNumber).collect(Collectors.joining(","));
            throw new ServiceException(String.format("%s是渠道/店主身份，不允许分配渠道",phones));
        }
        for (Long userId : userIds) {
            try {
                channelDomainService.updateBindingAgent(UserRef.of(userId), AgentRef.of(agentId)
                        , HospitalRef.of(hospitalId));
            } catch (Exception e) {
                log.error("渠道绑定错误: {}", e.getMessage(),e);
                throw new ServiceException("绑定错误，请联系管理员");
            }
        }
        return 1;
    }

    @Override
    @Transactional
    public int deleteAgent(Long id) {
        BusChannelPartnerAgent agent = busChannelPartnerAgentService.getById(id);
        if (Objects.isNull(agent) || DelFlagEnum.DELETE.getCode() == agent.getDelFlag()) {
            return 0;
        }
        BusChannelIdentity channelIdentity = busChannelIdentityService.getById(agent.getChannelIdentityId());
        if (Objects.isNull(channelIdentity)) {
            return 0;
        }
        BusPatientVO patientVO = busPatientService.getByPhoneNumber(channelIdentity.getPhoneNumber());
        int count = channelDomainService.deleteAgent(AgentRef.of(id), Objects.isNull(patientVO) ? null : UserRef.of(patientVO.getId()));
        patientChannelPartnerHelper.deletePatientRelationCache(channelIdentity.getPhoneNumber(), agent.getHospitalId());
        return count;

    }

    @Override
    @Transactional
    public int addNewAgent(BusChannelPartnerAgent channelPartnerAgent) {
        if (Objects.isNull(channelPartnerAgent) || Objects.isNull(channelPartnerAgent.getHospitalId())) {
            throw new ServiceException("合伙人信息不能为空");
        }
        // 1.校验逻辑
        // 1.1 校验手机号是否已存在渠道身份
        if (checkPhoneNumberIdentity(channelPartnerAgent.getPhonenumber(), channelPartnerAgent.getHospitalId())) {
            throw new ServiceException("该手机号已存在渠道身份，请勿重复添加");
        }
        BusPatientVO patientVO = busPatientService.getByPhoneNumber(channelPartnerAgent.getPhonenumber());
        ChannelAgentAggregate agent = ChannelAgentAggregate.create(
                ChannelRef.of(channelPartnerAgent.getChannelPartnerId()),
                AgentInfoBO.of(
                        channelPartnerAgent.getFullName(),
                        channelPartnerAgent.getShopName()),
                HospitalRef.of(channelPartnerAgent.getHospitalId()),
                SecurityUtils.getUsername(),
                ChannelIdentityAggregate.create(
                        ChannelIdentityAggregate.IdentityType.AGENT,
                        HospitalRef.of(channelPartnerAgent.getHospitalId()),
                        PhoneNumber.of(channelPartnerAgent.getPhonenumber())
                ));
        int count = channelDomainService.addNewAgent(agent, Objects.isNull(patientVO) ? null : UserRef.of(patientVO.getId()));
        return createIdentityToDeleteCacheAndSendEvent(count, channelPartnerAgent.getPhonenumber(), channelPartnerAgent.getHospitalId());
    }

    @Override
    public void updateAgentSelfRelation(String phoneNumber, Long hospitalId, Long agentId) {
        BusPatientVO patientVO = busPatientService.getByPhoneNumber(phoneNumber);
        if (patientVO == null){
            throw new RuntimeException("用户不存在");
        }
        channelDomainService.updateBindingAgent(UserRef.of(patientVO.getId()), AgentRef.of(agentId)
                , HospitalRef.of(hospitalId));
    }

    @Transactional
    @Override
    public void changePhone(String oldPhoneNumber, String phoneNumber) {
        List<BusChannelIdentity> oldIdentity = busChannelIdentityService.selectListByPhone(oldPhoneNumber);
        if (CollectionUtils.isEmpty(oldIdentity)) {
            return;
        }
        //查询新手机号是否已经关联经纪人了
        boolean result = busChannelPartnerAgentService.checkPhoneNumber(phoneNumber, null, null);
        if (result) {
            throw new ServiceException("修改的新手机号已经关联经纪人信息，无法进行修改");
        }
        channelDomainService.updatePhone(PhoneNumber.of(oldPhoneNumber), PhoneNumber.of(phoneNumber));
        // 清空原来所有身份数据的缓存
        oldIdentity.forEach(
                i -> patientChannelPartnerHelper.deletePatientRelationCache(oldPhoneNumber, i.getHospitalId())
        );
    }

    private @NotNull String getUserPhoneByUserId(Long userId) {
        R<String> phoneNumR = remotePatientService.findPatientPhone(userId);
        if (phoneNumR == null || !phoneNumR.isSuccess() || StringUtils.isEmpty(phoneNumR.getData())) {
            throw new ServiceException("账号获取失败，请联系管理员", 401);
        }
        return phoneNumR.getData();
    }

    /**
     * 获取当前登录用户信息
     * @return  登录用户信息
     */
    private @NotNull SMSLoginUser getUser() {
        R<SMSLoginUser> smsLoginUserR = remoteUserService.queryPatientInfo(SecurityConstants.INNER);
        if (smsLoginUserR == null || !smsLoginUserR.isSuccess() || Objects.isNull(smsLoginUserR.getData())) {
            throw new ServiceException("登录失效", 401);
        }
        return smsLoginUserR.getData();
    }

}

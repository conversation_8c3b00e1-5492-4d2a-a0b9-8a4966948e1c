package com.puree.hospital.business.channel.framework;

import com.puree.hospital.common.core.utils.SpringUtils;
import lombok.Getter;
import org.springframework.boot.SpringApplication;

import java.util.ArrayList;
import java.util.List;

/**
 * 简单的 Transition 配置器实现
 * @param <S> 状态类型
 * @param <E> 事件类型
 * @param <C> 上下文类型
 */
@Getter
public class SimpleTransitionConfigurer<S, E, C> implements TransitionConfigurer<S, E, C> {
    /**
     * 事件类型 - 触发状态转移的事件类型
     */
    private final E event;
    /**
     * 状态机构建器 - 用于注册本次 Transition 配置
     */
    private final StateMachineBuilder<S, E, C> builder;
    /**
     * 状态机的源状态 - 需要转入的状态类型
     * 如果 fromAny() 被调用，则表示可以从任意状态转入
     */
    private S fromState;
    /**
     * 是否可以从任意状态转入
     */
    private boolean fromAny = false;
    /**
     * 状态机的目标状态 - 需要转入的状态类型
     */
    private S toState;

    /**
     * 动作列表 - 在状态转移时需要执行的动作
     */
    private final List<Action<C>> actions = new ArrayList<>();

    public SimpleTransitionConfigurer(E event, StateMachineBuilder<S, E, C> builder) {
        this.event = event;
        this.builder = builder;
    }

    @Override
    public TransitionConfigurer<S, E, C> from(S state) {
        this.fromState = state;
        this.fromAny = false;
        return this;
    }

    @Override
    public TransitionConfigurer<S, E, C> fromAny() {
        this.fromAny = true;
        return this;
    }

    @Override
    public TransitionConfigurer<S, E, C> to(S state) {
        this.toState = state;
        return this;
    }

    @Override
    public TransitionConfigurer<S, E, C> action(Action<C> action) {
        this.actions.add(action);
        return this;
    }

    @Override
    public TransitionConfigurer<S, E, C> action(Class<? extends Action<C>> actionClass) {
        Action<C> action = SpringUtils.getBean(actionClass);
        return action(action);
    }

    @Override
    public TransitionConfigurer<S, E, C> action(String className) {
        Action<C> action = SpringUtils.getBean(className);
        return action(action);
    }

    /**
     * 结束本次 Transition 配置，返回到 Builder 继续下一个 onEvent(...)
     */
    @Override
    public StateMachineBuilder<S, E, C> end() {
        // 校验必要字段
        if (toState == null) {
            throw new IllegalStateException("Transition must define a 'to' state");
        }
        // 将本次配置注册到 Builder
        builder.registerTransition(this);
        return builder;
    }

    @Override
    public S getSourceState() {
        return fromAny ? null : fromState;
    }

    @Override
    public S getTargetState() {
        return toState;
    }


}
package com.puree.hospital.business.channel.commission.queue.consumer;

import com.puree.hospital.app.api.model.event.order.OrderCancelEvent;
import com.puree.hospital.business.channel.commission.enums.ChannelOrderCommissionEventEnum;
import com.puree.hospital.business.channel.commission.enums.ChannelOrderCommissionStateEnum;
import com.puree.hospital.business.channel.commission.helper.BusOrderHelper;
import com.puree.hospital.business.channel.commission.service.IBusChannelOrderCommissionService;
import com.puree.hospital.business.channel.service.IBusChannelOrderService;
import com.puree.hospital.business.domain.BusChannelOrder;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.BusOrderAfterSales;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.enums.OrderStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import com.puree.hospital.common.redis.mq.annotation.RedisConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@RedisConsumer(topic = OrderCancelEvent.TOPIC, group = BusOrderCreatedToSnapshotConsumer.GROUP)
public class BusOrderCancelConsumer extends RedisStreamConsumer<OrderCancelEvent> {

    public static final String GROUP = "business_channel_commission_order_cancel_consumer_group";


    @Resource
    private IBusChannelOrderCommissionService busChannelOrderCommissionService;

    @Resource
    private IBusChannelOrderService busChannelOrderService;

    @Resource
    private BusOrderHelper busOrderHelper;

    /**
     * 业务实现的方法
     *
     * @param message 消息内容
     */
    @Override
    public void onMessage(RedisMessage<OrderCancelEvent> message) {
        log.info("Received order cancel event: {}", message);
        OrderCancelEvent event = message.getBody();
        if (Objects.isNull(event) || Objects.isNull(event.getHospitalId()) ||
                StringUtils.isEmpty(event.getTotalOrderNo())) {
            log.warn("Received invalid order cancel event: {}", message);
            return;
        }
        // 获取总订单
        BusOrder busOrder = busOrderHelper.getShopBusOrder(event.getTotalOrderNo(), event.getHospitalId());
        if (busOrder == null) {
            return;
        }
        // 验证订单状态改为的已取消
        if (!OrderStatusEnum.CANCEL.getCode().equalsIgnoreCase(busOrder.getOrderStatus())) {
            log.error("Order not cancelled: {}", busOrder.getId());
            throw new RuntimeException("Order not cancelled: " + busOrder.getId());
        }
        List<BusChannelOrder> busChannelOrders = busChannelOrderService.selectChannelOrderByOrderNo(busOrder.getOrderNo(), event.getHospitalId());
        if (CollectionUtils.isEmpty(busChannelOrders)) {
            log.warn("No channel orders found for order no: {}", busOrder.getOrderNo());
            return;
        }
        // 要区别有没有退款
        // 没有退款，视为用户主动取消或者自动关单，则直接将需要进入结算的订单状态改为已经取消
        if (!event.isHasRefund()) {
            // 获取渠道订单
            BusChannelOrder channelOrder = busChannelOrders.stream().filter(o -> ChannelOrderCommissionStateEnum.isCreated(o.getCommissionStatus())).findFirst().orElse(null);
            if (Objects.isNull(channelOrder)) {
                log.warn("No channel order found with CREATED status for order no: {}", busOrder.getOrderNo());
                return;
            }
            busChannelOrderCommissionService.commissionBusOrderSettle(
                    ChannelOrderCommissionEventEnum.CANCEL,
                    channelOrder.getId(),
                    event.getHospitalId(),
                    message.getId(),
                    GROUP
            );
            return;
        }
        BusChannelOrder channelOrder = busChannelOrders.stream()
                .filter(o -> ChannelOrderCommissionStateEnum.isClearing(o.getCommissionStatus()))
                .findFirst().orElse(null);
        if (Objects.isNull(channelOrder)) {
            log.warn("No channel order found with CLEARING status for order no: {}", busOrder.getOrderNo());
            return;
        }
        // 有退款（订单已经取消） - 那么走的逻辑是 - 转结算 - 此时的
        // 分支2 - 有售后结束的订单 - 走的是Refund转结算的逻辑 - 医院后台取消订单
        List<BusOrderAfterSales> orderAfterSalesList = busOrderHelper.getOrderAfterSalesList(event.getTotalOrderNo());
        if (!CollectionUtils.isEmpty(orderAfterSalesList) && orderAfterSalesList.stream().anyMatch(o -> OrderAfterSalesStatusEnum.isEnd(o.getAfterSalesStatus()))) {
            busChannelOrderCommissionService.commissionBusOrderSettle(
                    ChannelOrderCommissionEventEnum.AFTER_REFUND_PLATFORM_CANCEL_SETTLED,
                    channelOrder.getId(),
                    event.getHospitalId(),
                    message.getId(),
                    GROUP
            );
            return;
        }
        // 分支1 - 没有售后结束的订单 - 直接结算
        busChannelOrderCommissionService.commissionBusOrderSettle(
                ChannelOrderCommissionEventEnum.NO_AFTER_SALE_ALL_REFUND_SETTLED,
                channelOrder.getId(),
                event.getHospitalId(),
                message.getId(),
                GROUP
        );


    }

}

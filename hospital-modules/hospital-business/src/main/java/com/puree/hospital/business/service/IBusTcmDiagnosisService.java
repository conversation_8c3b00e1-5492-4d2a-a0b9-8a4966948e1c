package com.puree.hospital.business.service;

import com.puree.hospital.business.api.model.DiagnosisVO;
import com.puree.hospital.business.api.model.dto.DiagnosisDTO;
import com.puree.hospital.business.domain.BusTcmDiagnosis;
import com.puree.hospital.business.domain.dto.BusIcdTcmDiagnosisDto;

import java.util.List;

/**
 * 中医诊断信息Service接口
 * 
 * <AUTHOR>
 * @date 2021-10-26
 */
public interface IBusTcmDiagnosisService {
    /**
     * 查询中医诊断信息
     * 
     * @param id 中医诊断信息ID
     * @return 中医诊断信息
     */
    BusTcmDiagnosis selectBusTcmDiagnosisById(Long id);

    /**
     * 查询中医诊断信息列表
     * 
     * @param busTcmDiagnosis 中医诊断信息
     * @return 中医诊断信息集合
     */
    List<BusTcmDiagnosis> selectBusTcmDiagnosisList(BusTcmDiagnosis busTcmDiagnosis);

    /**
     * 新增中医诊断信息
     * 
     * @param busTcmDiagnosis 中医诊断信息
     * @return 结果
     */
    int insertBusTcmDiagnosis(BusTcmDiagnosis busTcmDiagnosis);

    /**
     * 修改中医诊断信息
     * 
     * @param busTcmDiagnosis 中医诊断信息
     * @return 结果
     */
    int updateBusTcmDiagnosis(BusTcmDiagnosis busTcmDiagnosis);

    /**
     * 批量删除中医诊断信息
     * 
     * @param ids 需要删除的中医诊断信息ID
     * @return 结果
     */
    int deleteBusTcmDiagnosisByIds(Long[] ids);

    /**
     * 中医诊断停用启用操作
     * @param busTcmDiagnosis
     * @return
     */
    int lock(BusTcmDiagnosis busTcmDiagnosis);

    /**
     * 校验病种名称是否重复
     * @param id
     * @param tcmDiagnosis
     * @return
     */
    boolean checkTcmDiagnosis(Long id, String tcmDiagnosis);

    /**
     * 校验诊断代码是否重复
     * @param id
     * @param diagnosisCode
     * @return
     */
    boolean checkDiagnosisCode(Long id, String diagnosisCode);

    /**
     * 获取ICD未关联的中医诊断列表
     * @param dto
     * @return
     */
    List<BusTcmDiagnosis> selectListNotAssociatedIcd(BusIcdTcmDiagnosisDto dto);

    /**
     *  查询不支持医保支付的诊断列表
     * @param diagnosisDTO  入参
     * @return  不支持医保支付的诊断列表
     */
    List<DiagnosisVO> getDiagnosisListByIds(DiagnosisDTO diagnosisDTO);

    /**
     * 根据Ids查询诊断
     * @param diagnosisIds  诊断Id
     * @return  诊断列表
     */
    List<BusTcmDiagnosis> queryDiagnosisByIds(List<Long> diagnosisIds);
}

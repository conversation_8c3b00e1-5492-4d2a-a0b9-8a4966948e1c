package com.puree.hospital.business.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.core.utils.excel.converter.order.DrugsOrderDeliveryTypeConverter;
import com.puree.hospital.common.core.utils.excel.converter.order.DrugsOrderDrugsTypeConverter;
import com.puree.hospital.common.core.utils.excel.converter.order.DrugsOrderStatusConverter;
import com.puree.hospital.common.core.utils.excel.converter.order.DrugsOrderTypeConverter;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ExcelIgnoreUnannotated
public class BusDrugsOrder extends Entity {
    private static final long serialVersionUID = 1L;
    @ColumnWidth(18)
    @ExcelProperty(value = "订单编号",index = 0)
    private String orderNo;
    private Long prescriptionId;
    @ColumnWidth(18)
    @ExcelProperty(value = "配送方式",index = 8,converter = DrugsOrderDeliveryTypeConverter.class)
    private String deliveryType;
    @ColumnWidth(18)
    @ExcelProperty(value = "订单状态",index = 10,converter = DrugsOrderStatusConverter.class)
    private String status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(28)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "下单时间",index = 1)
    private Date orderTime;
    @ColumnWidth(18)
    @ExcelProperty(value = "收货人",index = 3)
    private String receivingUser;
    @ColumnWidth(18)
    @ExcelProperty(value = "联系电话",index = 5)
    private String receivingTel;
    @ColumnWidth(30)
    @ExcelProperty(value = "收货地址",index = 4)
    private String receivingAddress;
    @ColumnWidth(15)
    @ExcelProperty(value = "订单金额",index = 7)
    private String amount;
    @ColumnWidth(18)
    @ExcelProperty(value = "物流公司",index = 6)
    private String logisticsCompany;
    private String deliveryNo;
    @ColumnWidth(18)
    @ExcelProperty(value = "药品类型",index = 2,converter = DrugsOrderDrugsTypeConverter.class)
    private String orderDrugsType;
    private Long drugsStoreId;
    private String drugsStoreName;
    private Long hospitalId;
    private String hospitalName;
    private String pickUpTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    /**
     * 是否删除（0否 1是）
     */
    private String delStatus;

    /**
     * 来源 0药房 1药店
     */
    private Long source;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区/县
     */
    private String area;
    /**
     * 详细地址
     */
    private String detailedAddress;

    /**物流编码*/
    private String expressCode;
    /**
     * 0药房订单 1配送企业订单
     */
    private String orderType;
    /**
     * 机构code
     */
    private String partnersCode;
    /**
     * 药品订单类别（0普通订单 1用药订单）
     */
    @ColumnWidth(18)
    @ExcelProperty(value = "订单类型",index = 9,converter = DrugsOrderTypeConverter.class)
    private String orderClassify;
    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;
    /**
     * 香雪返回的订单号
     */
    private String xxOrderNumber;
    /**
     * 至信返回的订单号
     */
    private String zxOrderNumber;
    /** 患者ID */
    private Long patientId;
    /**
     * 运费
     */
    private String freight;
    /**
     * 补充描述
     */
    private String orderDescription;
    /**
     * 强制退款原因
     */
    private String agreeReason;
    /**
     * 支付方式  1-微信  2-通联
     */
    private String payWay;
    /**
     * 配送企业ID
     */
    @TableField(exist = false)
    private Long enterpriseId;
    /**
     * 是否推送成功 0失败 1成功
     */
    private Integer isSendSuccess;
    /**
     * 华润订单状态（30已复核）
     */
    private Integer hrStatus;
    /**
     * 是否发货药品/商品（0否 1是）
     */
    @TableField(exist = false)
    private Integer delivery;

    /**
     * 诊查费
     */
    private BigDecimal examinationFee;

    /**
     * 诊查费名称
     */
    private String examinationName;

    @Override
    public BusDrugsOrder setId(Long id){
        super.setId(id);
        return this;
    }
}

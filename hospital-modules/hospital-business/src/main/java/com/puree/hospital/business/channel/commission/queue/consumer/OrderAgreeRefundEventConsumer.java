package com.puree.hospital.business.channel.commission.queue.consumer;

import com.puree.hospital.app.api.model.event.order.OrderAgreeRefundEvent;
import com.puree.hospital.business.channel.commission.enums.ChannelOrderCommissionEventEnum;
import com.puree.hospital.business.channel.commission.enums.ChannelOrderCommissionStateEnum;
import com.puree.hospital.business.channel.commission.helper.BusOrderHelper;
import com.puree.hospital.business.channel.commission.service.IBusChannelOrderCommissionService;
import com.puree.hospital.business.channel.service.IBusChannelOrderService;
import com.puree.hospital.business.domain.BusChannelOrder;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.BusOrderAfterSales;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.enums.OrderStatusEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import com.puree.hospital.common.redis.mq.annotation.RedisConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/***
 * 订单医院同意退款事件消费者
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/08/04
 */
@Slf4j
@RedisConsumer(topic = OrderAgreeRefundEvent.TOPIC, group = OrderExchangeReceivedEventConsumer.GROUP)
public class OrderAgreeRefundEventConsumer extends RedisStreamConsumer<OrderAgreeRefundEvent> {
    protected static final String GROUP = "business_channel_order_agree_refund_consumer";

    @Resource
    private BusOrderHelper busOrderHelper;

    @Resource
    private IBusChannelOrderService busChannelOrderService;

    @Resource
    private IBusChannelOrderCommissionService busChannelOrderCommissionService;


    /**
     * 业务实现的方法
     *
     * @param message 消息内容
     * @throws Exception 业务异常
     */
    @Override
    public void onMessage(RedisMessage<OrderAgreeRefundEvent> message) throws Exception {
        log.info("订单医院同意退款事件消费者，message={}", message);
        OrderAgreeRefundEvent event = message.getBody();
        if (Objects.isNull(event) || StringUtils.isEmpty(event.getTotalOrderNo()) || Objects.isNull(event.getHospitalId())) {
            log.error("订单医院同意退款事件同步佣金结算失败，消息内容为空，忽略处理，event={}", event);
            return;
        }
        Long hospitalId = event.getHospitalId();
        String totalOrderNo = event.getTotalOrderNo();
        // 根据订单号和医院ID获取商品订单对象
        BusOrder shopBusOrder = busOrderHelper.getShopBusOrder(totalOrderNo, hospitalId);
        // 未找到商品订单 - 忽略处理
        if (Objects.isNull(shopBusOrder)) {
            log.warn("未找到含有商品的订单, totalOrderNo: {}, hospitalId: {}", totalOrderNo, hospitalId);
            return;
        }
        // 确认能找到对应的清算中的渠道订单（状态为待结算）
        List<BusChannelOrder> busChannelOrders = busChannelOrderService.selectChannelOrderByOrderNo(totalOrderNo, hospitalId);
        if (CollectionUtils.isEmpty(busChannelOrders)) {
            log.warn("未找到参与佣金计算的订单, totalOrderNo: {}, hospitalId: {}", totalOrderNo, hospitalId);
            return;
        }
        BusChannelOrder channelOrder = busChannelOrders.stream().filter(o -> ChannelOrderCommissionStateEnum.isClearing(o.getCommissionStatus())).findFirst().orElse(null);
        if (Objects.isNull(channelOrder)) {
            log.warn("未找到参与佣金计算的订单, totalOrderNo: {}, hospitalId: {}", totalOrderNo, hospitalId);
            return;
        }
        // 查询所有售后信息
        List<BusOrderAfterSales> allFinishedAfterSales = busOrderHelper.getOrderAfterSalesList(totalOrderNo);
        // 同意退款的情况下，没有售后不可能
        if (CollectionUtils.isEmpty(allFinishedAfterSales)) {
            log.error("订单没有售后，忽略处理，totalOrderNo: {}, hospitalId: {}", totalOrderNo, hospitalId);
            return;
        }
        // 分支1 ： 订单状态完结（取消,有退款） -- 走订单的全额退款流程
        if (OrderStatusEnum.isCancel(shopBusOrder.getOrderStatus())) {
            if (!allFinishedAfterSales.stream().allMatch(o -> OrderAfterSalesStatusEnum.isCloseOrEnd(o.getAfterSalesStatus()))) {
                log.error("订单状态为取消，但有未完结售后，忽略处理，totalOrderNo: {}, hospitalId: {}", totalOrderNo, hospitalId);
                return;
            }
            // 全额退款的结算
            busChannelOrderCommissionService.commissionBusOrderSettle(
                    ChannelOrderCommissionEventEnum.AFTER_REFUND_CANCEL_SETTLED,
                    channelOrder.getId(),
                    event.getHospitalId(),
                    message.getId(),
                    GROUP
            );
            return;
        }
        // 分支2 ： 订单状态完结（退款） -- 走订单的部分退款流程 - 也就是订单已经完结，然后医院同意退款，结束售后事件晚于订单关闭售后时间
        if (!OrderStatusEnum.isNotFinishWithAfterSale(shopBusOrder.getOrderStatus())) {
            // 售后都完结了 - 走订单的部分退款后的完结流程
            if (allFinishedAfterSales.stream().anyMatch(o -> OrderAfterSalesStatusEnum.isCloseOrEnd(o.getAfterSalesStatus()))) {
                busChannelOrderCommissionService.commissionBusOrderSettle(
                        ChannelOrderCommissionEventEnum.FINISHED_SETTLED,
                        channelOrder.getId(),
                        event.getHospitalId(),
                        message.getId(),
                        GROUP
                );
                return;
            }
            // 订单都完成了，但是还是有未完结的售后，还是同意退款，那就是继续部分退款的流程
            // 二次以上的退款，新增退款记录
            busChannelOrderCommissionService.commissionBusOrderSettle(
                    ChannelOrderCommissionEventEnum.PART_REFUND_NOT_SETTLED,
                    channelOrder.getId(),
                    event.getHospitalId(),
                    message.getId(),
                    GROUP
            );
            return;
        }
        // 分支3 ： 订单状态未完结（退款） -- 走订单的部分退款流程 - 订单未完结，医院同意退款，订单关闭售后时间晚于医院同意退款时间, 要到订单关闭售后时间才能结算，这里要先做退款的记录新增处理，才能保证实时性
        // 退款，新增退款记录
        busChannelOrderCommissionService.commissionBusOrderSettle(
                ChannelOrderCommissionEventEnum.PART_REFUND_NOT_SETTLED,
                channelOrder.getId(),
                event.getHospitalId(),
                message.getId(),
                GROUP
        );

    }
}

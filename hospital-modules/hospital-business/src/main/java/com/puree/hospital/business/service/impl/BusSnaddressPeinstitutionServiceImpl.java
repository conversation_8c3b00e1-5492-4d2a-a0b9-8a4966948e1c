package com.puree.hospital.business.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.business.domain.BusHospital;
import com.puree.hospital.business.domain.BusPartners;
import com.puree.hospital.business.domain.BusSnaddressPeinstitution;
import com.puree.hospital.business.mapper.BusHospitalMapper;
import com.puree.hospital.business.mapper.BusPartnersMapper;
import com.puree.hospital.business.mapper.BusSnaddressPeinstitutionMapper;
import com.puree.hospital.business.service.IBusSnaddressPeinstitutionService;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * sn地址和检查机构绑定表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class BusSnaddressPeinstitutionServiceImpl implements IBusSnaddressPeinstitutionService {
    private Logger logger = LoggerFactory.getLogger(BusSnaddressPeinstitutionServiceImpl.class);
    private final BusSnaddressPeinstitutionMapper busSnaddressPeinstitutionMapper;
    private final BusHospitalMapper hospitalMapper;
    private final BusPartnersMapper partnersMapper;
    private final Integer CODE = 10000001;
    private final Integer CODE1 = 0;

    @Override
    public List<BusSnaddressPeinstitution> selectList(BusSnaddressPeinstitution busSnaddressPeinstitution) {
        logger.info("List============BusSnaddressPeinstitution； " + busSnaddressPeinstitution);
        LambdaQueryWrapper<BusSnaddressPeinstitution> lambdaQuery = Wrappers.lambdaQuery();
        //医院ID
        lambdaQuery.eq(null != busSnaddressPeinstitution.getHospitalId(), BusSnaddressPeinstitution::getHospitalId, busSnaddressPeinstitution.getHospitalId());
        lambdaQuery.eq(null != busSnaddressPeinstitution.getPartnersId(), BusSnaddressPeinstitution::getPartnersId, busSnaddressPeinstitution.getPartnersId());
        //sn地址
        lambdaQuery.like(null != busSnaddressPeinstitution.getSnaddress(), BusSnaddressPeinstitution::getSnaddress, busSnaddressPeinstitution.getSnaddress());
        //检查机构id
        lambdaQuery.eq(null != busSnaddressPeinstitution.getPeinstitutionId(), BusSnaddressPeinstitution::getPeinstitutionId, busSnaddressPeinstitution.getPeinstitutionId());
        return busSnaddressPeinstitutionMapper.selectList(lambdaQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(BusSnaddressPeinstitution busSnaddressPeinstitution) {
        busSnaddressPeinstitution.setCreateTime(DateUtils.getNowDate());
        logger.info("save============BusSnaddressPeinstitution； " + busSnaddressPeinstitution);
        BusSnaddressPeinstitution busSnaddressResult = busSnaddressPeinstitutionMapper.selectOne(new LambdaQueryWrapper<BusSnaddressPeinstitution>()
                .eq(BusSnaddressPeinstitution::getSnaddress, busSnaddressPeinstitution.getSnaddress()));
        if (null != busSnaddressResult) {
            if (StringUtils.isNotNull(busSnaddressResult.getHospitalId())) {
                BusHospital hospital = hospitalMapper.selectById(busSnaddressResult.getHospitalId());
                if (StringUtils.isNotNull(hospital)) {
                    throw new ServiceException("设备号已被" + hospital.getHospitalName() + "绑定");
                }
            } else {
                BusPartners partners = partnersMapper.selectById(busSnaddressResult.getPartnersId());
                if (StringUtils.isNotNull(partners)) {
                    throw new ServiceException("设备号已被" + partners.getFullName() + "绑定");
                }
            }
        }
        // 2025-08-15 跟E家沟通，我们不需要设置数据推送地址，设备的机台是众爱医疗版本，就会按机构推送
        // 3 E家设备绑定需要请求三方接口 ，设置健康数据推送的全路径地址
//        if (CheckOrganizationEnum.EHOME.getCode().equals(busSnaddressPeinstitution.getPeinstitutionId().toString())) {
//            List<Map<String, String>> jsonList = new ArrayList<>();
//            Map<String, String> reqMap = new HashMap<>();
//            reqMap.put("pushUrl", HealthDataPushEnum.PUSHURL.getInfo());
//            reqMap.put("key", busSnaddressPeinstitution.getSnaddress());
//            jsonList.add(reqMap);
//            Map<String, String> headMap = new HashMap<>();
//            headMap.put("clientKey", HealthDataPushEnum.CLIENTKEY.getInfo());
//            headMap.put("type", HealthDataPushEnum.TYPE.getInfo());
//            Map<String, Object> parseMap = null;
//            try {
//                String json = JsonUtils.objectToJson((jsonList));
//                String postJson = HttpUtils.sendPostJson(HealthDataPushEnum.REQUEST_URL.getInfo(), json, headMap);
//                log.info("新增绑定信息返回" + postJson);
//                parseMap = JsonUtils.parseMap(postJson);
//            } catch (Exception e) {
//                log.error("健康数据推送的全路径地址接口請求异常", e);
//            }
//            //等于0是成功，否则失败
//            if (null != parseMap) {
//                if (CODE1 != parseMap.get("code")) {
//                    if ("存在key重复".equals(parseMap.get("info").toString()) || "存在key已被其他type关联".equals(parseMap.get("info").toString())) {
//                        //该设备绑定关系已存在
//                        throw new ServiceException("该设备绑定关系已存在");
//                    } else {
//                        //绑定失败
//                        throw new ServiceException("绑定失败");
//                    }
//                }
//
//            }
//            return busSnaddressPeinstitutionMapper.insert(busSnaddressPeinstitution);
//        }
        return busSnaddressPeinstitutionMapper.insert(busSnaddressPeinstitution);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer delete(Long id) {
        // 2025-08-15 跟E家沟通，解绑接口已下线，取消接口调用直接解除绑定关系
//        QueryWrapper<BusSnaddressPeinstitution> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("id", id).last("LIMIT 1");
//        BusSnaddressPeinstitution busSnaddressResult = busSnaddressPeinstitutionMapper.selectOne(queryWrapper);
//         if (null != busSnaddressResult) {
//            if (CheckOrganizationEnum.EHOME.getCode().equals(busSnaddressResult.getPeinstitutionId().toString())) {
//                    // E家设备绑定需要请求三方接口 ，目前接口不使用了
//               List<String> keyList = new ArrayList<>();
//                keyList.add(busSnaddressResult.getSnaddress());
//                Map<String, String> headMap = new HashMap<>();
//                headMap.put("clientKey", HealthDataPushEnum.CLIENTKEY.getInfo());
//                headMap.put("type", HealthDataPushEnum.TYPE.getInfo());
//                Map<String, Object> parseMap = null;
//                try {
//                    String json = JsonUtils.objectToJson((keyList));
//                    String postJson = HttpUtils.sendPostJson(HealthDataPushEnum.DELETEBATCH_REQUEST_URL.getInfo(), json, headMap);
//                    log.info("删除绑定信息返回" + postJson);
//                    parseMap = JsonUtils.parseMap(postJson);
//                } catch (Exception e) {
//                    log.info("健康数据推送的全路径地址接口請求异常e={}", e);
//                }
//                if (null != parseMap) {
//                    if (CODE1 != parseMap.get("code")) {
//                        if (CODE == parseMap.get("code")) {
//                            //删除失败
//                            throw new ServiceException(parseMap.get("info").toString());
//                        } else {
//                            // 内部错误
//                            throw new ServiceException("内部错误");
//                        }
//                    }
//                }
//                return busSnaddressPeinstitutionMapper.deleteById(id);
//            }
//        }
        return busSnaddressPeinstitutionMapper.deleteById(id);
    }
}

package com.puree.hospital.business.channel.commission.enums;

public enum ChannelOrderCommissionEventEnum {
    /**
     * 订单支付事件
     */
    PAYMENT,
    /**
     * 订单取消事件
     */
    CANCEL,
    /**
     * 订单无售后退款结算事件
     */
    NO_AFTER_SALE_ALL_REFUND_SETTLED,
    /**
     * 订单无退款结算事件
     */
    NO_REFUND_SETTLED,
    /**
     * 不结算事件 - 待结算实时更新
     */
    PART_REFUND_NOT_SETTLED,
    /**
     * 有退款的结算事件
     */
    FINISHED_SETTLED,
    /**
     * 退款后（全部走售后）取消订单事件
     */
    AFTER_REFUND_CANCEL_SETTLED,
    /**
     * 有售后，再后台退款取消订单事件
     */
    AFTER_REFUND_PLATFORM_CANCEL_SETTLED
}

package com.puree.hospital.insurance.processor.base.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.puree.hospital.app.api.model.BusPrescription;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SpringUtils;
import com.puree.hospital.insurance.domain.MiFeeDetailUploadExtData;
import com.puree.hospital.insurance.domain.MiPayResult;
import com.puree.hospital.insurance.domain.dto.MiPayDTO;
import com.puree.hospital.insurance.domain.dto.preprocessor.PayOrderDTO;
import com.puree.hospital.insurance.domain.model.conext.FixedInsBusinessParam;
import com.puree.hospital.insurance.domain.model.conext.MiProcessContext;
import com.puree.hospital.insurance.helper.MiExaminationFeeCacheHelper;
import com.puree.hospital.insurance.infrastructure.enums.mi.FeeTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.ReqUrlEnum;
import com.puree.hospital.insurance.infrastructure.utils.sign.MiSignUtil;
import com.puree.hospital.insurance.mapper.BusOrderMapper;
import com.puree.hospital.insurance.mapper.MiPayResultMapper;
import com.puree.hospital.insurance.processor.BaseFixedInsMiProcess;
import com.puree.hospital.insurance.processor.IMiProcessorChain;
import com.puree.hospital.order.api.model.BusOrder;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用6202医保预结算
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/11 11:26
 */
public abstract class BaseOrderPreSettleMiProcessor extends BaseFixedInsMiProcess {

    private final MiPayResultMapper miPayResultMapper;


    private final MiExaminationFeeCacheHelper miExaminationFeeCacheHelper;

    private final BusOrderMapper busOrderMapper;

    public BaseOrderPreSettleMiProcessor() {
        this.miPayResultMapper = SpringUtils.getBean(MiPayResultMapper.class);
        this.miExaminationFeeCacheHelper = SpringUtils.getBean(MiExaminationFeeCacheHelper.class);
        this.busOrderMapper = SpringUtils.getBean(BusOrderMapper.class);
    }

    @Override
    protected String getReqKey() {
        return ReqUrlEnum.PAY_ORDER_6202.getReqKey();
    }

    @Override
    public void process(MiProcessContext<FixedInsBusinessParam> context,
                        IMiProcessorChain<FixedInsBusinessParam> processorChain) {
        MiPayDTO payDTO = getMiPayDTO(context);
        // 删除历史数据
        miPayResultMapper.delete(new LambdaQueryWrapper<MiPayResult>().eq(MiPayResult::getOrderNo, payDTO.getTotalOrderNo()));
        PayOrderDTO orderDTO = getPayOrderDTO(payDTO, context);
        miDataEncryptResponseCall(orderDTO, context, new TypeReference<JSONObject>() {
        }, new ResponseHandler<JSONObject>() {
            @Override
            public String getApiDesc() {
                return "支付预结算";
            }

            @Override
            public void handle(JSONObject response) {
                if (Objects.nonNull(response)) {
                    MiPayResult miPayResult = MiPayResult.createMiPayResult(response, payDTO.getTotalOrderNo(), payDTO.getAcctUsedFlag(), payDTO.getPayAuthNo());
                    BigDecimal freight = BigDecimal.ZERO;
                    LambdaQueryWrapper<BusOrder> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(BusOrder::getOrderNo, payDTO.getTotalOrderNo())
                            .last(" limit 1");
                    BusOrder order = busOrderMapper.selectOne(queryWrapper);
                    if (Objects.nonNull(order)) {
                        freight = Objects.nonNull(order.getFreight()) ? BigDecimal.valueOf(order.getFreight()) : BigDecimal.ZERO;
                    }
                    miPayResult.setFreight(freight);
                    //设置处方编号列表
                    miPayResult.setRxNo(Joiner.on(",").join(context.getBusinessParam().getRxList().stream().map(BusPrescription::getPrescriptionNumber).collect(Collectors.toList())));
                    //计算所有处方现金支付的诊查费总和
                    BigDecimal examinationFee = BigDecimal.ZERO;
                    BigDecimal processFee = BigDecimal.ZERO;
                    for (BusPrescription p : context.getBusinessParam().getRxList()) {
                        //如果诊查费未上传，需要将诊查费作为现金支付部分
                        if (!Boolean.TRUE.equals(context.getBusinessParam().getExaminationFeeMap().get(p.getPrescriptionNumber()))) {
                            //处方中的诊查费做累加
                            examinationFee = examinationFee.add(p.getExaminationFee());
                        }
                        //处方中的中药加工费
                        if (p.getProcessPrice() != null && p.getProcessPrice().compareTo(BigDecimal.ZERO) > 0) {
                            processFee = processFee.add(p.getProcessPrice());
                        }
                    }
                    miPayResult.setExaminationFee(examinationFee);
                    miPayResult.setOtherCashAmount(freight.add(examinationFee).add(processFee));
                    if (Objects.nonNull(payDTO.getClientType())) {
                        miPayResult.setClientType(payDTO.getClientType().getType());
                    }
                    //设置支付流水号
                    miPayResult.setMiPaySerialNo(context.getBusinessParam().getMiPaySerialNo());
                    miPayResultMapper.insert(miPayResult);
                    context.getBusinessParam().setMiPayResult(miPayResult);
                }
            }

            @Override
            public void errorCallback() {
                miExaminationFeeCacheHelper.removeCache(payDTO.getTotalOrderNo(), context.getBusinessParam().getRxList());
            }
        });
        processorChain.doChain(context);
    }

    protected PayOrderDTO getPayOrderDTO(MiPayDTO payDTO, MiProcessContext<FixedInsBusinessParam> context) {
        MiFeeDetailUploadExtData feeDetailUploadExtData = context.getBusinessParam().getFeeDetailUploadExtData();
        if (Objects.isNull(feeDetailUploadExtData)) {
            throw new ServiceException("当前医保支付流程未上传费用明细数据");
        }
        PayOrderDTO orderDTO = new PayOrderDTO();
        orderDTO.setPayAuthNo(payDTO.getPayAuthNo());
        orderDTO.setPayOrdId(feeDetailUploadExtData.getPayOrdId());
        orderDTO.setPayToken(feeDetailUploadExtData.getPayToken());
        orderDTO.setOrgCodg(context.getHospitalConfig().getHospitalCode());
        orderDTO.setOrgBizSer(MiSignUtil.getRandomString(18));
        orderDTO.setChrgBchno(context.getBusinessParam().getMiPaySerialNo());
        orderDTO.setFeeType(FeeTypeEnum.F05.getCode());
        orderDTO.setMdtrtId(feeDetailUploadExtData.getMdtrtId());
        orderDTO.setAcctUsedFlag(payDTO.getAcctUsedFlag());
        Map<String, String> map = super.getExpContent(context);
        orderDTO.setExpContent(JSON.toJSONString(map));
        return orderDTO;
    }
}

package com.puree.hospital.insurance.controller;

import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.insurance.api.model.CheckInsuranceResultDTO;
import com.puree.hospital.insurance.api.model.MiPatientQueryDTO;
import com.puree.hospital.insurance.api.model.OutpatientRegisterReqDTO;
import com.puree.hospital.insurance.api.model.OutpatientRegisterResultDTO;
import com.puree.hospital.insurance.api.model.UniformRequest;
import com.puree.hospital.insurance.domain.dto.MiPayDTO;
import com.puree.hospital.insurance.helper.MiClientTypeHelper;
import com.puree.hospital.insurance.infrastructure.enums.mi.ReqUrlEnum;
import com.puree.hospital.insurance.service.IMiPreProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 前置机接口
 *
 * <AUTHOR>
 * @date 2023/5/9 11:16
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/preprocessor")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PreprocessorController extends BaseController {

    private final IMiPreProcessService miPreProcessService;
    private final MiClientTypeHelper miClientTypeHelper;

    @PostMapping("/uniformRequest")
    public AjaxResult uniformRequest(@RequestBody UniformRequest req) {
        log.info("医保接口前端入参：{}", JSON.toJSONString(req));
        req.setOperator(SecurityUtils.getUsername());
        if (ReqUrlEnum.PAY_ORDER_6202.getReqKey().equals(req.getReqKey())) {
            // 支付下单
            ClientTypeEnum clientType = miClientTypeHelper.getClientTypeWithLoginUser();
            req.setClientType(clientType);
            return AjaxResult.success(miPreProcessService.orderPreSettle(req));
        } else if (ReqUrlEnum.QUERY_ORDER_INFO_6301.getReqKey().equals(req.getReqKey())) {
            // 医保结算结果查询
            return miPreProcessService.getPayInfo(req);
        } else if (ReqUrlEnum.REFUND_ORDER_6203.getReqKey().equals(req.getReqKey())) {
            // 医保退费
            return AjaxResult.success(miPreProcessService.orderRefund(req));
        }
        throw new ServiceException("请求指令错误");
    }

    @PostMapping("/payNotUseAccount")
    public AjaxResult payNotUseAccount(@RequestBody MiPayDTO dto) {
        log.info("医保不使用接口前端入参：{}", JSON.toJSONString(dto));
        ClientTypeEnum clientType = miClientTypeHelper.getClientTypeWithLoginUser();
        dto.setClientType(clientType);
        dto.setOperator(SecurityUtils.getUsername());
        return AjaxResult.success(miPreProcessService.orderRePreSettle(dto));
    }

    @Log(title = "校验订单是否支持医保支付")
    @GetMapping("/checkSupportInsurance")
    public AjaxResult checkSupportInsurance(String orderNo) {
        return AjaxResult.success(miPreProcessService.checkSupportInsurance(orderNo));
    }

    /**
     * 查询就诊人是否有定点当前医院
     *
     * @param patientQueryDTO 查询参数
     * @return 是否有定点
     */
    @PostMapping("/checkDesignate")
    public AjaxResult checkDesignate(@RequestBody MiPatientQueryDTO patientQueryDTO) {
        return AjaxResult.success(miPreProcessService.checkDesignate(patientQueryDTO));
    }

    /**
     * 查询特殊病种
     *
     * @param patientQueryDTO 查询参数
     * @return 病种列表
     */
    @PostMapping("/querySpecialDiseaseFiling")
    public AjaxResult querySpecialDiseaseFiling(@RequestBody MiPatientQueryDTO patientQueryDTO) {
        return AjaxResult.success(miPreProcessService.querySpecialDiseaseFiling(patientQueryDTO));
    }

    /**
     * 校验处方列表是否支持医保支付
     *
     * @param rxIdList 处方id列表
     * @return 是否支持医保支付
     */
    @Log(title = "校验处方列表是否支持医保支付")
    @PostMapping("/check-rx-support-insurance")
    public R<CheckInsuranceResultDTO> checkRxSupportInsurance(@RequestBody List<Long> rxIdList) {
        return R.ok(miPreProcessService.checkRxSupportInsuranceByIds(rxIdList));
    }

    /**
     * 门诊挂号
     *
     * @param reqDTO 入参
     * @return 门诊挂号结果
     */
    @Log(title = "门诊挂号")
    @PostMapping("/outpatient-register")
    public R<OutpatientRegisterResultDTO> outpatientRegister(@RequestBody OutpatientRegisterReqDTO reqDTO) {
        return R.ok(miPreProcessService.outpatientRegister(reqDTO));
    }

}

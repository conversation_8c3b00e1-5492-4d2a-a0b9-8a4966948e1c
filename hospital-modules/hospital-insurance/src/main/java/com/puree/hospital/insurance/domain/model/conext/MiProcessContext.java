package com.puree.hospital.insurance.domain.model.conext;

import com.puree.hospital.insurance.domain.MiHospitalConfig;
import com.puree.hospital.insurance.domain.MiOutpatientRegResult;
import com.puree.hospital.insurance.domain.vo.MemberInfoVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 医保处理器上下文
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/8 11:29
 */
@Getter
@Setter
public class MiProcessContext<C extends  IMiBusinessParam> {

    /**
     * 医保医院配置
     */
    private MiHospitalConfig hospitalConfig;

    /**
     * 患者姓名
     */
    private String familyName;

    /**
     * 患者身份证
     */
    private String familyIdCard;

    /**
     * 挂号时间
     */
    private Date outpatientRegTime = new Date();

     /**
     * 门诊挂号单号
     */
    private String outpatientRegNo;

    /**
     * 医生医保编号
     */
    private String doctorMiNo;

    /**
     * 业务科室编号
     */
    private String departmentNumber;

    /**
     * 业务科室名称
     */
    private String departmentName;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 标准科室代码
     */
    private String deptCode;

     /**
     * 签到流水号
     */
    private String signNo;

    /**
     * 医保人员信息信息
     */
    private MemberInfoVO memberInfoVO;

    /**
     * 医保门诊挂号结果
     */
    private MiOutpatientRegResult miOutpatientRegResult;

    /**
     * 业务参数
     */
    private C businessParam;

}

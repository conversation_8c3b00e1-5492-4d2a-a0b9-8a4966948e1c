package com.puree.hospital.insurance.processor.base;

import cn.hutool.core.collection.CollectionUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.insurance.domain.dto.preprocessor.MemberInfoDTO;
import com.puree.hospital.insurance.domain.model.conext.FixedInsBusinessParam;
import com.puree.hospital.insurance.domain.preprocessor.MiRequest;
import com.puree.hospital.insurance.domain.preprocessor.MiResponse;
import com.puree.hospital.insurance.domain.vo.MemberInsuInfoVO;
import com.puree.hospital.insurance.infrastructure.constants.MiConstants;
import com.puree.hospital.insurance.infrastructure.enums.mi.MdtrtCertTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.PsnCertTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.ReqUrlEnum;
import com.puree.hospital.insurance.processor.BaseFixedInsMiProcess;
import com.puree.hospital.insurance.processor.IMiProcessorChain;
import com.puree.hospital.insurance.domain.model.conext.MiProcessContext;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用1101获取参保人员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/9 11:14
 */
public abstract class BaseMemberInfoMiProcessor extends BaseFixedInsMiProcess {

    @Override
    public String getReqKey() {
        return ReqUrlEnum.GET_MEMBER_INFO_1101.getReqKey();
    }

    @Override
    public void process(MiProcessContext<FixedInsBusinessParam> context,
                        IMiProcessorChain<FixedInsBusinessParam> processorChain) {
        // 封装医保局请求参数
        MemberInfoDTO memberInfoDTO = getMemberInfoDTO(context);
        //获取miRequest
        MiRequest miRequest = getMiRequest(context, getSignNo(context), new Date(), null, null);
        HashMap<String, Object> data = new HashMap<>(1);
        data.put(MiConstants.DATA, memberInfoDTO);
        miRequest.setInput(data);
        miResponseCall(miRequest, context, new ResponseHandler<MiResponse>() {
            @Override
            public String getApiDesc() {
                return "获取就诊人信息";
            }

            @Override
            public void handle(MiResponse response) {
                handleOutput(response.getOutput(), context);
            }
        });
        processorChain.doChain(context);
    }

    /**
     * 获取MemberInfoDTO
     *
     * @param context 请求参数
     * @return memberInfoDTO
     */
    protected MemberInfoDTO getMemberInfoDTO(MiProcessContext<FixedInsBusinessParam> context) {
        MemberInfoDTO memberInfoDTO = new MemberInfoDTO();
        memberInfoDTO.setMdtrt_cert_type(MdtrtCertTypeEnum.M02.getCode());
        memberInfoDTO.setMdtrt_cert_no(context.getFamilyIdCard());
        memberInfoDTO.setBegntime(DateUtils.getTime());
        memberInfoDTO.setPsn_cert_type(PsnCertTypeEnum.MIEC.getCode());
        memberInfoDTO.setCertno(context.getFamilyIdCard());
        memberInfoDTO.setPsn_name(context.getFamilyName());
        return memberInfoDTO;
    }

    /**
     * 处理返回结果
     *
     * @param output   返回结果
     * @param context  上下文
     */
    protected abstract void handleOutput(Object output, MiProcessContext<FixedInsBusinessParam> context);

    /**
     * 就医参保地与医保参保地一致
     *
     * @param insuplcAdmdvs 就医参保地
     * @param cityCode      医保参保地
     * @return true：一致，false：不一致
     */
    protected boolean checkInsuplcAdmdvs(String insuplcAdmdvs, String cityCode) {
        if (StringUtils.isBlank(insuplcAdmdvs) || StringUtils.isBlank(cityCode)) {
            return false;
        }
        String insuplcAdmdvsPrefix = insuplcAdmdvs.substring(0, 4);
        String cityCodePrefix = cityCode.substring(0, 4);
        // 一致的逻辑 = 就医参保地与医保参保地一致 || 就医参保地与医保参保地前4位一致
        return Objects.equals(insuplcAdmdvs, cityCode) || Objects.equals(insuplcAdmdvsPrefix, cityCodePrefix);
    }

    /**
     * 获取参保信息
     *
     * @param insuInfoList 参保明细列表
     * @param context      请求上下文
     * @return 参保信息
     */
    protected MemberInsuInfoVO getZoneInfo(List<MemberInsuInfoVO> insuInfoList, MiProcessContext<FixedInsBusinessParam> context) {
        if (CollectionUtil.isNotEmpty(insuInfoList)) {
            insuInfoList =  insuInfoList.stream()
                    .filter(i -> StringUtils.isBlank(i.getPsn_insu_stas()) || Objects.equals(i.getPsn_insu_stas(), "1"))
                    .sorted(Comparator.comparing(MemberInsuInfoVO::getInsutype)).collect(Collectors.toList());
        }
        return insuInfoList.stream()
                .filter(i -> checkInsuplcAdmdvs(i.getInsuplc_admdvs(), context.getBusinessParam().getFixedInsParamConfig().getCityCode()))
                .findFirst().orElse(null);
    }
}

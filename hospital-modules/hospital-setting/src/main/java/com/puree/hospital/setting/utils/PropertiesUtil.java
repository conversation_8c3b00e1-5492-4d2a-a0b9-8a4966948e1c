package com.puree.hospital.setting.utils;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.StringReader;
import java.util.Properties;
import java.util.Set;

/**
 * @ClassName PropertiesConversionStringUtil
 * <AUTHOR>
 * @Description 属性工具类
 * @Date 2023/11/28 15:53
 * @Version 1.0
 */
@Slf4j
public final class PropertiesUtil {

    /**
     * 可复用 builder
     */
    private static final StrBuilder builder = StrUtil.strBuilder();
    /**
     * 鉴权 header 名称
     */
    public static final String AUTHENTICATION_HEADER = "X-CONF-ROLES";

    /**
     * aes 密钥
     */
    private static byte[] key = new byte[]{13, -108, 98, -119, 30, 16, 127, 24, -114, -79, 50, -89, -90, -35, 116, -64};
    /**
     * 构建 aes
     */
    private static AES aes = SecureUtil.aes(key);
    /**
     * @Param
     * @Return
     * @Description 工具类不允许实例化
     * <AUTHOR>
     * @Date 2023/11/28 16:07
     **/
    private PropertiesUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * @Param properties
     * @Return java.lang.String
     * @Description Properties 转换 String
     * <AUTHOR>
     * @Date 2023/11/28 15:57
     **/
    public static String propertiesConversionString(Properties properties) {
        //重置 builder
        builder.reset();
        Set<String> keys = properties.stringPropertyNames();
        for (String key : keys) {
            String value = properties.getProperty(key);
            if (CharSequenceUtil.isNotBlank(value)) {
                builder.append(key).append("=").append(value).append("\n");
            }
        }
        return builder.toString();
    }


    /**
     * @Param content
     * @Return java.util.Properties
     * @Description String 转换为 Properties
     * <AUTHOR>
     * @Date 2023/11/28 16:02
     **/
    public static Properties stringConversionProperties(String content) {
        Properties properties = new Properties();
        try {
            properties.load(new StringReader(content));
        } catch (IOException e) {
            log.error("转换失败", e);
            throw new IllegalArgumentException("转换失败，格式不正确");
        }
        return properties;
    }

    /**
     * @Param
     * @Return java.lang.String
     * @Description header 解密
     * <AUTHOR>
     * @Date 2024/1/16 18:59
     **/
    public static String headerDecryptStr(String str){
        if (CharSequenceUtil.isEmpty(str)) {
            return "";
        }
        return aes.decryptStr(str);
    }

}

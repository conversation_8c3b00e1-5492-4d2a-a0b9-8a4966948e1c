package com.puree.hospital.setting.constants;

import com.puree.hospital.setting.serializer.JsonArraySerializer;
import com.puree.hospital.setting.serializer.DefaultSerializer;
import com.puree.hospital.setting.serializer.JsonSerializer;
import lombok.Getter;

import java.util.function.Function;

/**
 * <p>
 * 配置项类型常量类
 * 类型;前端定义值是多少
 * * 0 单行文本
 * * 1 多行文本
 * * 2 富文本
 * * 3 数字
 * * 4 是/否
 * * 5 选择项
 * * 6 日期时间
 * * 7 表格
 * * 8 动态字典
 * * 9 静态字典
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/15 11:07
 */
@Getter
public enum ItemTypeEnum {

    /**
     * 未知类型，mysql数据库中enum类型未定义的类型，对应值0以及空字符串
     */
    UNKNOWN("",new DefaultSerializer()),

    /**
     * 单行文本
     */
    TEXT("text",new DefaultSerializer()),
    /**
     * 多行文本
     */
    MULTILINE_TEXT("multiline-text",new DefaultSerializer()),
    /**
     * 富文本
     */
    RICH_TEXT("rich-text",new DefaultSerializer()),
    /**
     * 数字
     */
    NUMBER("number",new DefaultSerializer()),
    /**
     * 布尔
     */
    BOOLEAN("boolean",new DefaultSerializer()),
    /**
     * 选择
     */
    CHOICE("choice",new DefaultSerializer()),
    /**
     * 日期时间
     */
    DATETIME("datetime",new DefaultSerializer()),
    /**
     * 表格
     */
    TABLE("table",new JsonArraySerializer()),
    /**
     * 动态字典
     */
    DYNAMIC_MAP("dynamic-map",new JsonArraySerializer()),
    /**
     * 静态字典
     */
    STATIC_MAP("static-map",new JsonSerializer());


    /**
     *  类型
     */
    private final String type;

    /**
     * 默认值序列化器
     */
    private final Function<String,String> serializer;

    ItemTypeEnum(String type, Function<String,String> serializer) {
        this.type = type;
        this.serializer = serializer;
    }

    /**
     * 根据前端定义的code与后端的枚举值顺序比较，判断是否是表格类型
     *
     * @param code 前端定义的原始值，从0开始；而后端的enum是跟mysql对应，unknown是0，因此有1位的偏差
     */
    public static boolean isTable(Integer code) {
        return code == TABLE.ordinal() - 1;
    }


    public static String getType(Integer code) {
        if (ItemTypeEnum.values().length <= code) {
            return ItemTypeEnum.UNKNOWN.type;
        }
        return ItemTypeEnum.values()[code].getType();
    }
    public static Integer getOrdinal(String desc) {
        for (ItemTypeEnum itemType : ItemTypeEnum.values()) {
            if (itemType.getType().equals(desc)) {
                return itemType.ordinal();
            }
        }
        return ItemTypeEnum.UNKNOWN.ordinal();
    }

    /**
     * 判断模版类型对应的配置项value值存储的是否是json字符串
     * @param code 模版类型
     * @return true：json字符串；false：非json字符串
     */
    public static boolean isJsonStr(Integer code) {
        return code == DYNAMIC_MAP.ordinal() - 1 || code == STATIC_MAP.ordinal() - 1 || code == TABLE.ordinal() - 1;
    }

    public static ItemTypeEnum getHandel(String desc) {
        for (ItemTypeEnum itemType : ItemTypeEnum.values()){
            if (itemType.getType().equals(desc)) {
                return itemType;
            }
        }
        return ItemTypeEnum.UNKNOWN;
    }

    public static ItemTypeEnum getHandel(Integer code) {
        if (ItemTypeEnum.values().length <= code) {
            return ItemTypeEnum.UNKNOWN;
        }
        return ItemTypeEnum.values()[code + 1];
    }
}

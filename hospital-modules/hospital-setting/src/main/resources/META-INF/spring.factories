org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
  com.puree.hospital.setting.config.NacosConfigurationProperties,\
  com.puree.hospital.setting.config.HttpCacheProperties,\
  com.puree.hospital.setting.config.NacosPushConfigurationProperties,\
  com.puree.hospital.setting.config.MybatisPlusConfig,\
  com.puree.hospital.setting.exception.ConfigExceptionHandler,\
  com.puree.hospital.setting.service.platform.impl.NacosServiceImpl,\
  com.puree.hospital.setting.service.hospital.impl.HospitalMenuServiceImpl,\
  com.puree.hospital.setting.service.hospital.impl.HospitalTemplateServiceImpl,\
  com.puree.hospital.setting.service.hospital.impl.HospitalHistoryServiceImpl,\
  com.puree.hospital.setting.service.platform.impl.PlatformHistoryServiceImpl,\
  com.puree.hospital.setting.service.platform.impl.PlatformItemsServiceImpl,\
  com.puree.hospital.setting.service.platform.impl.PlatformConfigHttpApiServiceImpl,\
  com.puree.hospital.setting.service.platform.impl.PlatformMenuServiceImpl,\
  com.puree.hospital.setting.service.hospital.impl.HospitalItemsServiceImpl,\
  com.puree.hospital.setting.service.platform.impl.PlatformPushBindServiceImpl,\
  com.puree.hospital.setting.controller.platform.HistoryController,\
  com.puree.hospital.setting.controller.platform.ItemsController,\
  com.puree.hospital.setting.controller.platform.MenuController,\
  com.puree.hospital.setting.controller.hospital.HistoryController,\
  com.puree.hospital.setting.controller.hospital.ItemsController,\
  com.puree.hospital.setting.controller.xxl.XxlJobController,\
  com.puree.hospital.setting.controller.hospital.MenuController,\
  com.puree.hospital.setting.controller.hospital.TemplateController,\
  com.puree.hospital.setting.controller.platform.HttpApiController,\
  com.puree.hospital.setting.controller.hospital.HttpApiController,\
  org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration,\
  com.puree.hospital.setting.config.HttpConfig,\
  com.puree.hospital.setting.config.WebConfig,\
  com.puree.hospital.setting.converter.CsvHttpMessageConverter,\
  com.puree.hospital.setting.converter.ExcelHttpMessageConverter,\
  com.puree.hospital.setting.controller.hospital.FeignController,\
  com.puree.hospital.setting.controller.platform.FeignController,\
  com.puree.hospital.setting.aspect.ValidateAuthAspect,\
  com.puree.hospital.setting.config.TemplatePhysicalConfig

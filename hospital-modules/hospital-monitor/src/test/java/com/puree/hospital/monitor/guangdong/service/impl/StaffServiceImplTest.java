package com.puree.hospital.monitor.guangdong.service.impl;

import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.datashare.service.SysDictDataService;
import com.puree.hospital.monitor.guangdong.domain.Staff;
import com.puree.hospital.monitor.guangdong.infrastructure.Zyjszwdm;
import com.puree.hospital.monitor.guangdong.infrastructure.Zyjszwlbdm;
import com.puree.hospital.monitor.guangdong.mapper.StaffMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.verify;


/**
 * 监管医生数据服务测试类
 * <AUTHOR>
 * @date 2025-07-11
 */

@ExtendWith(MockitoExtension.class)
public class StaffServiceImplTest {

    @Mock
    private StaffMapper staffMapper;

    @InjectMocks
    private StaffServiceImpl staffService;

    @Mock
    private SysDictDataService sysDictDataService;

    @Mock
    private HospitalJgIdConfig hospitalJgIdConfig;


    private Staff staff;

    @BeforeEach
    void setUp() {
        staff = new Staff();
        staff.setSfzh("6dAPWQgxBgxZ1mHN0GpX5g82C6V8alTR");
        staff.setJgdm("JG001");
        staff.setYhrygh("EMP123456");
        staff.setYhryxm("张三");
        staff.setXb("0"); // 假设 0 表示男性
        staff.setCsrq(new Date()); // 当前日期作为出生日期
        staff.setZjlbdm("ID_CARD");
        staff.setKsdm("KS001");
        staff.setZyjszwdm("232");
        staff.setZyjszwlbdm("ZG");
        staff.setZzlbmc("主治医师");
        staff.setZgzsbh("ZG123456789");
        staff.setZghdsj(new Date());
        staff.setZyzsbm("ZY123456789");
        staff.setFzrq(new Date());
        staff.setZydd("广州市第一人民医院");
        staff.setZyfw("内科诊疗");
        staff.setZyzyyljgdm("HOSP001");
        staff.setZyzyyymc("广州市第一人民医院");
        staff.setQkysbz("1");
        staff.setSjhm("wOzMraSleQl3qGn9i2grVw==");
        staff.setCjgzrq(new Date());
        staff.setZcsj(new Date());
        staff.setGrzpcfdz("/path/to/photo.jpg");
        staff.setSjscsj(new Date());
        staff.setCxbz("0");
        staff.setCreateTime(new Date());
        staff.setHospitalId(1L);
        staff.setTitle(100L); // 职称代码
    }

    /**
     * 测试执业证和资格证地址只保留第一个
     * 需要一个Staff对象，并设置一些属性
     */
    @Test
    void testSyncData_GetFirstData() {
        staff.setZyzcfdz("path1,path2,path3");
        staff.setZgzcfdz("path1,path2,path3");
        List<Staff> syncDataList = Collections.singletonList(staff);
        Map<Long, String> mockDictMap = new HashMap<>();
        // 100L -> 对应到枚举中的某个info（例如ZY231的"主任医师"）
        mockDictMap.put(100L, Zyjszwdm.ZY231.getInfo());
        when(sysDictDataService.<Long, String>getDictMapByTypes(anyList(), any(), any()))
                .thenReturn(mockDictMap);

        // 配置其他Mock
        when(staffMapper.selectSyncDataList(anyLong())).thenReturn(syncDataList);
        when(hospitalJgIdConfig.getJgId(anyLong())).thenReturn("MOCK_JG_ID");
        staffService.syncData(1L);

        // 验证插入操作是否只保留第一个地址
        Assertions.assertEquals("path1", staff.getZgzcfdz());
        Assertions.assertEquals("path1", staff.getZyzcfdz());
    }
    
    /**
     * 测试执业证和资格证地址只保留第一个
     * 需要一个Staff对象，并设置一些属性
     */
    @Test
    void testSyncData_GetFirstData1() {
        staff.setZyzcfdz("path1");
        staff.setZgzcfdz("path1");
        List<Staff> syncDataList = Collections.singletonList(staff);
        Map<Long, String> mockDictMap = new HashMap<>();
        // 100L -> 对应到枚举中的某个info（例如ZY231的"主任医师"）
        mockDictMap.put(100L, Zyjszwdm.ZY231.getInfo());
        when(sysDictDataService.<Long, String>getDictMapByTypes(anyList(), any(), any()))
                .thenReturn(mockDictMap);

        // 配置其他Mock
        when(staffMapper.selectSyncDataList(anyLong())).thenReturn(syncDataList);
        when(hospitalJgIdConfig.getJgId(anyLong())).thenReturn("MOCK_JG_ID");
        staffService.syncData(1L);

        // 验证插入操作是否只保留第一个地址
        Assertions.assertEquals("path1", staff.getZgzcfdz());
        Assertions.assertEquals("path1", staff.getZyzcfdz());
    }

    /**
     * 测试主任医师职称代码映射
     * 验证修复后的代码能够正确将主任医师映射为对应的职务代码和职务类别代码
     */
    @Test
    @DisplayName("测试主任医师职称代码映射")
    void testZyjszwdmMapping_ZY231() {
        // 准备测试数据
        Staff testStaff = createTestStaff();
        Map<Long, String> mockDictMap = new HashMap<>();
        mockDictMap.put(100L, Zyjszwdm.ZY231.getInfo()); // 主任医师
        
        // 配置Mock
        configureCommonMocks(Collections.singletonList(testStaff), mockDictMap);
        
        // 执行测试
        staffService.syncData(1L);
        
        // 验证结果
        ArgumentCaptor<Staff> staffCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffMapper).insert(staffCaptor.capture());
        Staff capturedStaff = staffCaptor.getValue();
        
        assertEquals(Zyjszwdm.ZY231.getCode(), capturedStaff.getZyjszwdm());
        assertEquals(Zyjszwlbdm.ZG.getCode(), capturedStaff.getZyjszwlbdm());
    }
    
    /**
     * 测试副主任医师职称代码映射
     * 验证修复后的代码能够正确将副主任医师映射为对应的职务代码和职务类别代码
     */
    @Test
    @DisplayName("测试副主任医师职称代码映射")
    void testZyjszwdmMapping_ZY232() {
        // 准备测试数据
        Staff testStaff = createTestStaff();
        Map<Long, String> mockDictMap = new HashMap<>();
        mockDictMap.put(100L, Zyjszwdm.ZY232.getInfo()); // 副主任医师
        
        // 配置Mock
        configureCommonMocks(Collections.singletonList(testStaff), mockDictMap);
        
        // 执行测试
        staffService.syncData(1L);
        
        // 验证结果
        ArgumentCaptor<Staff> staffCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffMapper).insert(staffCaptor.capture());
        Staff capturedStaff = staffCaptor.getValue();
        
        assertEquals(Zyjszwdm.ZY232.getCode(), capturedStaff.getZyjszwdm());
        assertEquals(Zyjszwlbdm.FG.getCode(), capturedStaff.getZyjszwlbdm());
    }
    
    /**
     * 测试主治医师职称代码映射
     * 验证修复后的代码能够正确将主治医师映射为对应的职务代码和职务类别代码
     */
    @Test
    @DisplayName("测试主治医师职称代码映射")
    void testZyjszwdmMapping_ZY233() {
        // 准备测试数据
        Staff testStaff = createTestStaff();
        Map<Long, String> mockDictMap = new HashMap<>();
        mockDictMap.put(100L, Zyjszwdm.ZY233.getInfo()); // 主治医师
        
        // 配置Mock
        configureCommonMocks(Collections.singletonList(testStaff), mockDictMap);
        
        // 执行测试
        staffService.syncData(1L);
        
        // 验证结果
        ArgumentCaptor<Staff> staffCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffMapper).insert(staffCaptor.capture());
        Staff capturedStaff = staffCaptor.getValue();
        
        assertEquals(Zyjszwdm.ZY233.getCode(), capturedStaff.getZyjszwdm());
        assertEquals(Zyjszwlbdm.ZJ.getCode(), capturedStaff.getZyjszwlbdm());
    }
    
    /**
     * 测试医师职称代码映射
     * 验证修复后的代码能够正确将医师映射为对应的职务代码和职务类别代码
     */
    @Test
    @DisplayName("测试医师职称代码映射")
    void testZyjszwdmMapping_ZY234() {
        // 准备测试数据
        Staff testStaff = createTestStaff();
        Map<Long, String> mockDictMap = new HashMap<>();
        mockDictMap.put(100L, Zyjszwdm.ZY234.getInfo()); // 医师
        
        // 配置Mock
        configureCommonMocks(Collections.singletonList(testStaff), mockDictMap);
        
        // 执行测试
        staffService.syncData(1L);
        
        // 验证结果
        ArgumentCaptor<Staff> staffCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffMapper).insert(staffCaptor.capture());
        Staff capturedStaff = staffCaptor.getValue();
        
        assertEquals(Zyjszwdm.ZY234.getCode(), capturedStaff.getZyjszwdm());
        assertEquals(Zyjszwlbdm.SJB.getCode(), capturedStaff.getZyjszwlbdm());
    }
    
    /**
     * 测试空的职称代码
     * 验证当职称代码为空时，代码不会抛出NullPointerException，而是正确地处理
     */
    @Test
    @DisplayName("测试职称代码为null时不抛出NullPointerException")
    void testZyjszwdmMapping_Null() {
        // 准备测试数据 - zyjszwdm字段设置为null
        Staff testStaff = createTestStaff();
        Map<Long, String> mockDictMap = new HashMap<>();
        mockDictMap.put(100L, null); // 职称映射为null
        
        // 配置Mock
        configureCommonMocks(Collections.singletonList(testStaff), mockDictMap);
        
        // 执行测试 - 不应抛出NullPointerException
        assertDoesNotThrow(() -> staffService.syncData(1L));
        
        // 验证结果 - 应该正常插入，职称代码应该为空字符串
        ArgumentCaptor<Staff> staffCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffMapper).insert(staffCaptor.capture());
        Staff capturedStaff = staffCaptor.getValue();
        
        // 由于没有匹配任何职称代码，zyjszwdm和zyjszwlbdm应该是空字符串
        assertEquals("", capturedStaff.getZyjszwdm());
        assertEquals("", capturedStaff.getZyjszwlbdm());
    }
    
    /**
     * 测试未知的职称代码
     * 验证当职称代码不是预定义的代码之一时，代码能够正确地处理
     */
    @Test
    @DisplayName("测试未知的职称代码")
    void testZyjszwdmMapping_Unknown() {
        // 准备测试数据
        Staff testStaff = createTestStaff();
        Map<Long, String> mockDictMap = new HashMap<>();
        mockDictMap.put(100L, "未知职称"); // 不存在于Zyjszwdm枚举中的职称
        
        // 配置Mock
        configureCommonMocks(Collections.singletonList(testStaff), mockDictMap);
        
        // 执行测试
        staffService.syncData(1L);
        
        // 验证结果
        ArgumentCaptor<Staff> staffCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffMapper).insert(staffCaptor.capture());
        Staff capturedStaff = staffCaptor.getValue();
        
        // 由于没有匹配任何职称代码，zyjszwdm和zyjszwlbdm应该是空字符串
        assertEquals("", capturedStaff.getZyjszwdm());
        assertEquals("", capturedStaff.getZyjszwlbdm());
    }
    
    /**
     * 测试完全为null的Staff对象
     * 验证当整个Staff对象为null时，代码不会抛出NullPointerException
     */
    @Test
    @DisplayName("测试完全为null的Staff对象")
    void testZyjszwdmMapping_NullStaff() {
        // 准备测试数据 - 使用null的Staff
        Map<Long, String> mockDictMap = new HashMap<>();
        Staff staff1 = new Staff();
        staff1.setSfzh("341324201208168679");
        staff1.setSjhm("12345678910");
        // 配置Mock，但传入null的Staff列表项
        List<Staff> staffList = new ArrayList<>();
        staffList.add(staff1);
        when(staffMapper.selectSyncDataList(anyLong())).thenReturn(staffList);
        when(sysDictDataService.<Long, String>getDictMapByTypes(anyList(), any(), any()))
                .thenReturn(mockDictMap);
        when(hospitalJgIdConfig.getJgId(anyLong())).thenReturn("MOCK_JG_ID");
        when(staffMapper.selectList(any())).thenReturn(Collections.emptyList());
        
        // 执行测试 - 不应抛出NullPointerException
        assertDoesNotThrow(() -> staffService.syncData(1L));
    }
    
    /**
     * 创建用于测试的Staff对象
     */
    private Staff createTestStaff() {
        Staff testStaff = new Staff();
        testStaff.setSfzh("test_sfzh");
        testStaff.setSjhm("test_phone");
        testStaff.setXb("0");
        testStaff.setHospitalId(1L);
        testStaff.setTitle(100L);
        return testStaff;
    }
    
    /**
     * 配置通用的Mock设置
     */
    private void configureCommonMocks(List<Staff> staffList, Map<Long, String> dictMap) {
        when(staffMapper.selectSyncDataList(anyLong())).thenReturn(staffList);
        when(sysDictDataService.<Long, String>getDictMapByTypes(anyList(), any(), any()))
                .thenReturn(dictMap);
        when(hospitalJgIdConfig.getJgId(anyLong())).thenReturn("MOCK_JG_ID");
        when(staffMapper.selectList(any())).thenReturn(Collections.emptyList());
    }
}
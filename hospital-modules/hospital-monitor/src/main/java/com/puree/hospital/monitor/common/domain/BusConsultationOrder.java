package com.puree.hospital.monitor.common.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 问诊订单信息（由于现阶段，领域划分混乱,如果通过api调用，开发工作量大，而且影响业务，所以先放这里）
 *
 * <AUTHOR>
 * @date 2024/6/25 14:51
 */
@Data
@TableName("bus_consultation_order")
public class BusConsultationOrder extends Entity {

    private static final long serialVersionUID = -1924627625140458787L;
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 问诊ID
     */
    private Long consultationId;

    /**
     * 订单状态（0待支付,1待接诊 未使用,2问诊中 使用中, 3问诊中 已开处方,4已完成 已使用,5已失效,6已退号,7已退款,8已取消,9退款中）
     */
    private String status;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 订单类型（0:图文;1:视频）
     */
    private String orderType;

    /**
     * 问诊人ID
     */
    private Long familyId;

    /**
     * 问诊人姓名
     */
    private String familyName;

    /**
     * 性别（0女 1男）
     */
    private String familySex;

    /**
     * 问诊人年龄
     */
    private String familyAge;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生照片
     */
    private String photo;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 医生职称
     */
    private String title;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 问诊回合
     */
    private Integer round;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 科室ID
     */
    private Long departmentId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 退号原因
     */
    private String reason;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 购买类型（0诊室内购买 1诊室外购买）
     */
    private String buyType;

    /**
     * 问诊类型（0挂科室 1挂医生）
     */
    private String consultationType;

    /**
     * 待接诊状态
     */
    private String preStatus;

    /**
     * 关注订单
     */
    private String follow;

    /**
     * 报到时间
     */
    private Date checkTime;

    /**
     * 视频问诊预约开始时间
     */
    private Date startTime;

    /**
     * 视频问诊预约结束时间
     */
    private Date endTime;

    /**
     * 视频问诊订单状态
     * 0待支付,1待接诊 2问诊中(未开处方), 3问诊中(已开处方),4已完成,5已失效,6已退号,7已退款,8已取消,9退款中,10已预约(未报到),11已预约(已报到),12已过号
     */
    private String videoStatus;

    /**
     * 机构code
     */
    private String partnersCode;

    /**
     * 叫号是否过号
     */
    private Integer isGuoHao;
    /**
     * 取消时间
     */
    private Date cancelTime;
    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 1表示通过预审
     */
    private Integer pass;

    /**
     * 是否删除（0否 1是）
     */
    private String delStatus;

    /**
     * 完成时间
     */
    private Date completeTime;
    /**
     * 退号时间
     */
    private Date withdrawalTime;

    /**
     * 支付方式 0-微信支付 1-通联支付
     */
    private String payWay;

    /**
     * 通联订单编号
     */
    private String tonglianTrxid;

    /**
     * 被复诊的订单id
     */
    private Long furtherConsultationId;

    /**
     * 是否新订单（0否 1是）
     */
    private Integer newOrderOrNot;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 发票状态, 参考 InvoiceStatusEnum.java
     */
    private Integer invoiceStatus;

    /**
     * 发票类型 1 税控盘 2 全电  参考 InvoiceTypeEnum.java
     */
    private Integer invoiceType;

    /**
     * 发票抬头ID
     */
    private Long invoiceHeaderId;

    /**
     * 1-患者支付 ，2-医生赠送， 3-健康解读报告
     */
    private Integer payType;

    /**
     * 挂号类型 1-复诊挂号 2-咨询挂号
     * 监管新增专用
     */
    private Integer registerType;

}

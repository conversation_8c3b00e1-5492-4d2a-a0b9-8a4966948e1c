package com.puree.hospital.monitor.common.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * <p>
 * 快速问诊信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/5 18:59
 */
@Data
@TableName("bus_quick_consultation")
public class BusQuickConsultation extends Entity {

    private static final long serialVersionUID = -128463797873109271L;

    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 业务科室ID
     */
    private Long departmentId;
    /**
     * 问诊人ID
     */
    private Long familyId;
    /**
     * 病情描述
     */
    private String symptomDescription;
    /**
     * 图片（逗号分隔）
     */
    private String picture;
    /**
     * 身高（cm）
     */
    private Integer height;
    /**
     * 体重（kg）
     */
    private Integer weight;
    /**
     * 过敏史（0无 1有）
     */
    private Boolean allergicHistory;
    /**
     * 过敏药物
     */
    private String allergicDrugs;
    /**
     * 既往史（0无 1有）
     */
    private Boolean previousIllness;
    /**
     * 既往病史
     */
    private String anamneses;
    /**
     * 现病史（0无 1有）
     */
    private Boolean presentIllness;
    /**
     * 疾病名称
     */
    private String illness;
    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 用药史
     */
    private String medicationHistory;

    /**
     * 是否找药（0否 1是）
     */
    private Integer findMedicine;

    /**
     * 费用类别：SELF:自费，MI_GENERAL:医保(普通门诊)，MI_SPECIAL:医保（门特门慢）
     */
    private String feeSettleType;

    /**
     * 门特门慢病种信息，json信息{"opspDiseCode":"M03900","opspDiseName":"高血压(慢病)"}
     */
    private String specialDiseaseInfo;

}

package com.puree.hospital.monitor.regulatory.event.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.app.api.model.BusDictDrugsFrequencyDTO;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 西医总剂量计算帮助类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/25 15:19
 */
@Component
public class WmTotalDosageHelper {

    @Resource
    private RemoteHospitalSettingApi remoteHospitalSettingApi;

    /**
     * 获取用药频次列表
     * @param hospitalId    医院ID
     * @return  用药频次列表
     */
    public List<BusDictDrugsFrequencyDTO> getFrequencyList(Long hospitalId) {
        R<String> settingValue = remoteHospitalSettingApi.getSettingValue("drug-manager.data-dict.wm-drug-frequency", hospitalId);
        if (!settingValue.isSuccess() || Objects.isNull(settingValue.getData())) {
            return Collections.emptyList();
        }
        return JSON.parseArray(settingValue.getData(), BusDictDrugsFrequencyDTO.class);
    }

    /**
     *  根据名称获取用药频次
     * @param frequencyName 用药频次名称
     * @param hospitalId    医院ID
     * @return  用药频次
     */
    public BusDictDrugsFrequencyDTO getFrequencyByName(String frequencyName, Long hospitalId) {
        List<BusDictDrugsFrequencyDTO> frequencyList = getFrequencyList(hospitalId);
        if (CollectionUtil.isEmpty(frequencyList)) {
            return null;
        }
        return frequencyList.stream().filter(item -> item.getName().equalsIgnoreCase(frequencyName)).findFirst().orElse(null);
    }

    /**
     *  计算总剂量  总剂量计算 = 单次剂量 * 用药频率几次 * 持续天数 / 用药频率天数
     * @param busDictDrugsFrequency     用药频率
     * @param singleDose        单词剂量
     * @param medicationDays    持续天数
     * @return  总剂量
     */
    public BigDecimal getTotalDosage(BusDictDrugsFrequencyDTO busDictDrugsFrequency, BigDecimal singleDose, BigDecimal medicationDays) {
        // 获取不到 或者设置为不参与计算 默认为1 ≈ 单次剂量 * 持续天数
        if (Objects.isNull(busDictDrugsFrequency) || YesNoEnum.NO.getCode().equals(busDictDrugsFrequency.getCalculate())) {
            return singleDose.multiply(medicationDays).setScale(0, RoundingMode.UP);
        }
        // 单次剂量 * 用药频率几次 * 持续天数 / 用药频率天数
        return new BigDecimal(busDictDrugsFrequency.getSeveralTimes()).multiply(singleDose).multiply(medicationDays).divide(new BigDecimal(busDictDrugsFrequency.getDays()), 0, RoundingMode.UP);
    }


}

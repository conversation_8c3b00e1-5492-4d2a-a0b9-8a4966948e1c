package com.puree.hospital.monitor.common.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * 诊疗群组（由于现阶段，领域划分混乱,如果通过api调用，开发工作量大，而且影响业务，所以先放这里）
 *
 * <AUTHOR>
 * @date 2024/6/25 14:51
 */
@Data
@TableName("bus_doctor_patient_group")
public class BusDoctorPatientGroup extends Entity {

    private static final long serialVersionUID = 9125778872710923598L;

    /**
     * 医生id
     */
    private Long doctorId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 科室id
     */
    private Long departmentId;

    /**
     * 会话id
     */
    private String conversationId;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 诊疗人id
     */
    private Long familyId;

    /**
     * 群组类型 0医生 1服务包
     */
    private String type;

    /**
     * 服务包id
     */
    private Long serviceId;

    /**
     * 是否修改就诊人 0否 1是
     */
    private String modifyFlag;

    /**
     * 是否禁用
     */
    private Boolean disable;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String delFlag;

    /**
     * 群组是否标记（0否 1是）
     */
    private Integer mark;

    /**
     * 标记变更无法使用,false  true 无法使用
     */
    private Boolean markChanges;

}

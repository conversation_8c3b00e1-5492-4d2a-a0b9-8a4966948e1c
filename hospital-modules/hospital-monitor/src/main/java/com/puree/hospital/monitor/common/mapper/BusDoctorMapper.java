package com.puree.hospital.monitor.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.monitor.common.domain.BusDoctor;
import com.puree.hospital.monitor.datashare.domain.vo.BusDoctorVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 医生信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 17:30
 */
@Mapper
public interface BusDoctorMapper extends BaseMapper<BusDoctor> {

    /**
     * 查询医生信息
     *
     * @param hospitalCode 医院标识
     * @return 医生信息
     */
    List<BusDoctorVO> queryDoctor(String hospitalCode);
}

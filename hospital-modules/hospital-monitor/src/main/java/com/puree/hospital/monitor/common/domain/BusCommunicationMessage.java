package com.puree.hospital.monitor.common.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * IM通讯消息对象
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/18 17:50
 */
@Data
@TableName("bus_communication_message")
public class BusCommunicationMessage implements Serializable {

    private static final long serialVersionUID = -7866256362653126151L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 消息的内容
     */
    private String payload;

    /**
     * 消息所属的会话 ID
     */
    private String conversationId;

    /**
     * 消息所属会话的类型
     */
    private String conversationType;

    /**
     * 接收方的 userID
     */
    @TableField("`to`")
    private String to;

    /**
     * 发送方的 userID
     */
    @TableField("`from`")
    private String from;

    /**
     * 消息的流向in 为收到的消息out 为发出的消息
     */
    private String flow;

    /**
     * 时间戳
     */
    @TableField("`time`")
    private Long time;

    /**
     * 消息状态
     */
    private String status;

    /**
     * 是否被撤回的消息，true 标识被撤回的消息
     */
    private String isRevoked;

    /**
     * 消息优先级
     */
    private String priority;

    /**
     * 发送消息者昵称
     */
    private String nick;

    /**
     * 消息发送者的头像地址
     */
    private String avatar;

    /**
     * C2C 消息对端是否已读
     */
    private String isPeerRead;

    /**
     * 消息发送者的群名片
     */
    private String nameCard;

    /**
     * 群聊时此字段存储被 at 的群成员的 userID
     */
    private String atUserList;

    /**
     * 消息自定义数据
     */
    private String cloudCustomData;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 文件类型下级类型 0文档 1音频 2视频
     */
    private String treeType;

    /**
     * 消息为文字或文档内容或名字
     */
    private String messageText;

    /**
     * 消息阻id
     */
    private String groupId;

    /**
     * 就诊人di
     */
    private Long familyId;

    /**
     * 医生id
     */
    private Long hospitalId;
}

package com.puree.hospital.monitor.common.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * <p>
 * 业务科室关联 （由于现阶段，领域划分混乱,如果通过api调用，开发工作量大，而且影响业务，所以先放这里）
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 16:18
 */
@Data
@TableName("bus_associate_department")
public class BusAssociateDepartment extends Entity {

    private static final long serialVersionUID = -5147478298300439126L;

    /**
     * 业务科室ID
     */
    private Long bizDepartmentId;

    /**
     * 标准科室ID
     */
    private Long departmentId;

    /**
     * 医院ID
     */
    private Long hospitalId;
}

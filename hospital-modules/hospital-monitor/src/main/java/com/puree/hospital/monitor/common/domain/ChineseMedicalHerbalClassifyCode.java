package com.puree.hospital.monitor.common.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 中草药分类编码
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @date 2024-06-28 10:07:23
 */
@Data
@TableName("chinese_medical_herbal_classify_code")
@Accessors(chain = true)
public class ChineseMedicalHerbalClassifyCode extends Entity {

    private static final long serialVersionUID = -5228813052897200221L;
    /**
     * 药理大分类
     */
    private String pharmacologyClassify;

    /**
     * 药理小分类
     */
    private String pharmacologySubClassify;

    /**
     * 通用名称
     */
    private String drugName;

    /**
     * 标准编码
     */
    private String drugStandardCode;

    /**
     * 拼音码
     */
    private String pinyinCode;

    /**
     * 五笔码
     */
    private String wubiCode;

    /**
     * 标识
     */
    private String remark;

    /**
     * 登记时间
     */
    private Date registrationDate;
}

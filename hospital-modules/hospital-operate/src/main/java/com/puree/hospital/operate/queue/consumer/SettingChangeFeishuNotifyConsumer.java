package com.puree.hospital.operate.queue.consumer;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospital;
import com.puree.hospital.common.core.utils.http.HttpUtil;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import com.puree.hospital.common.redis.mq.annotation.RedisConsumer;
import com.puree.hospital.operate.config.FeishuConfigProperties;
import com.puree.hospital.operate.domain.FeishuCard;
import com.puree.hospital.operate.domain.FeishuCardTemplate;
import com.puree.hospital.operate.domain.FeishuCardVariable;
import com.puree.hospital.operate.domain.dto.FeishuRequestDTO;
import com.puree.hospital.setting.constants.SettingChangeConstants;
import com.puree.hospital.setting.domain.entity.Template;
import com.puree.hospital.setting.model.event.SettingChangeEvent;
import com.puree.hospital.setting.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;


import javax.annotation.Resource;


/**
 * <p>
 * 配置项变更消息消费者
 *
 * <AUTHOR>
 * @date 2024/12/4 15:04
 */
@Slf4j
@RedisConsumer(topic = SettingChangeConstants.TOPIC, group = SettingChangeConstants.GROUP_FEISHU_NOTIFY)
public class SettingChangeFeishuNotifyConsumer extends RedisStreamConsumer<SettingChangeEvent> {

    @Resource
    private RemoteHospitalService busHospitalService;

    @Resource
    private FeishuConfigProperties feishuConfigProperties;

    /**
     * 飞书消息类型
     */
    public static final String FEISHU_MESSAGE_TYPE = "interactive";

    /**
     * 发送卡片消息至飞书
     * 请求参数示例：
     * {
     * "msg_type": "interactive",
     * "card": {
     * "type": "template",
     * "data": {
     * "template_id": "YOUR_TEMPLATE_ID",
     * "template_version_name": "YOUR_VERSION",
     * "template_variable":{
     * // 卡片变量
     * }
     * }
     * }
     * }
     * 将 YOUR_TEMPLATE_ID 替换为卡片模板的 ID，YOUR_VERSION 替换为卡片模板的版本号，YOUR_WEBHOOK_URL 替换为自定义机器人的 Webhook 地址。
     * 模版ID 和 版本号可以在飞书开发者后台获取。
     * webhook 地址可以在飞书机器人中获取。
     * 详见 <a href="https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/quick-start/send-message-cards-with-custom-bot">...</a>
     *
     * @param message 消息
     * @throws Exception 例外
     */
    @Override
    public void onMessage(RedisMessage<SettingChangeEvent> message) throws Exception {
        if (!feishuConfigProperties.isEnable()) {
            return;
        }
        log.info("飞书通知 message: {}", message);

        SettingChangeEvent body = message.getBody();
        Template template = null;
        String classify = body.getClassify().getValue();
        String url = "";
        //根据操作类型跳转不同页面
        if (SettingChangeEvent.Classify.HOSPITAL.equals(body.getClassify())) {
            if (body.getHospitalId()!= null){
                BusHospital busHospital = busHospitalService.getHospitalInfo(body.getHospitalId()).getData();
                classify = busHospital.getHospitalName();
            }
            url = feishuConfigProperties.getHospitalHistoryPageUrl();
        } else if (SettingChangeEvent.Classify.PLATFORM.equals(body.getClassify())) {
            url = feishuConfigProperties.getPlatformHistoryPageUrl();
        }
        //发送飞书通知
        FeishuRequestDTO requestDto = new FeishuRequestDTO();
        requestDto.setTimestamp(System.currentTimeMillis() / 1000);
        requestDto.setSign(feishuConfigProperties.getSecret());
        requestDto.setMsgType(FEISHU_MESSAGE_TYPE);
        FeishuCard feishuCard = getFeishuCard(body, classify, url);
        requestDto.setCard(feishuCard);
//        log.info("飞书通知请求参数: {}", JsonUtil.toPrettyJson(requestDto));
        JSONObject jsonObject = HttpUtil.doPost(feishuConfigProperties.getWebhookUrl(), JsonUtil.toJson(requestDto));
        log.info("飞书通知返回结果: {}", jsonObject.toString());
    }

    private @NotNull FeishuCard getFeishuCard(SettingChangeEvent body, String classify, String url) {
        //构建飞书发送请求 参数
        FeishuCard feishuCard = new FeishuCard();

        FeishuCardTemplate feishuCardTemplate = new FeishuCardTemplate();
        feishuCardTemplate.setTemplateId(feishuConfigProperties.getTemplateId());
        feishuCardTemplate.setTemplateVersionName(feishuConfigProperties.getTemplateVersionName());

        FeishuCardVariable feishuCardVariable = getFeishuCardVariable(body, classify, url);

        feishuCardTemplate.setTemplateVariable(feishuCardVariable);
        feishuCard.setData(feishuCardTemplate);

        return feishuCard;
    }

    /**
     * 构建飞书卡片变量
     *
     * @param body 设置变更事件，包含类型、操作者、请求体等信息
     * @param classify 分类标识，用于标识卡片的分类
     * @param url 历史记录URL模板，需格式化为包含key和hospitalId的URL
     * @return 构建完成的飞书卡片变量对象
     */
    private @NotNull FeishuCardVariable getFeishuCardVariable(@NotNull SettingChangeEvent body, String classify, String url) {
        return FeishuCardVariable.builder()
                .type(body.getType().getValue())
                .operator(body.getOperator())
                .content(body.getRequestBody())
                .environment(body.getEnvironment())
                .classify(classify)
                .operate(body.getOperate().getValue())
                .templateName(body.getTemplateName())
                .buttonText(feishuConfigProperties.getButtonText())
                .historyUrl(String.format(url, body.getKey(), body.getHospitalId()))
                .build();
    }

}

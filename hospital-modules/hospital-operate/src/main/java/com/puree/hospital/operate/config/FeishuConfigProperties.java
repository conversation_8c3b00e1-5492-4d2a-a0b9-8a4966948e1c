package com.puree.hospital.operate.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 飞书配置文件配置信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/9 18:19
 */
@RefreshScope
@Component
@ConfigurationProperties(prefix = "easy-config.notify.feishu")
@Data
public class FeishuConfigProperties {

    /**
     * 是否开启飞书通知
     */
    private boolean enable;
    /**
     * 平台历史记录页面url
     */
    private String platformHistoryPageUrl;


    /**
     * 医院历史记录页面url
     */
    private String hospitalHistoryPageUrl;


    /**
     * Webhook URL
     */
    private String webhookUrl;
    /**
     * 飞书机器人文档
     *<a href="https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot?lang=zh-CN">...</a>
     * 飞书机器人 秘钥
     *
     */
    private String secret;

    /**
     * 飞书卡片 模板ID
     */
    private String templateId;


    /**
     *飞书卡片 模板版本
     */
    private String templateVersionName;

    /**
     * 按钮文本
     */
    private String buttonText;


}

package com.puree.hospital.operate.domain.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.puree.hospital.operate.domain.FeishuCard;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <p>
 * 飞书webhook请求  dot
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/11 10:06
 */
@Data
@ToString
public class FeishuRequestDTO {

    @JsonProperty("msg_type")
    private String msgType;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 标志
     */
    private String sign;
    /**
     * 飞书卡片 json
     */
    private FeishuCard card;


    public void setSign(String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        //使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(new byte[]{});
        this.sign = new String(Base64.encodeBase64(signData));
    }

}

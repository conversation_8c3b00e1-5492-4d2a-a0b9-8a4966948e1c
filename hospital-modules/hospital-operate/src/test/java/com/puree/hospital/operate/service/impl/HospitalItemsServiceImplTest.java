package com.puree.hospital.operate.service.impl;

import com.puree.hospital.setting.domain.entity.hospital.HospitalItem;
import com.puree.hospital.setting.mapper.hospital.HospitalItemMapper;
import com.puree.hospital.setting.service.hospital.impl.HospitalItemsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class HospitalItemsServiceImplTest {

    @Mock
    private HospitalItemMapper hospitalItemMapper;

    @InjectMocks
    private HospitalItemsServiceImpl hospitalItemsService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getHospitalItemByTemplateIdAndHospitalId_正常情况() {
        // 测试正常情况
        HospitalItem expectedItem = new HospitalItem();
        expectedItem.setId(1L);
        expectedItem.setTemplateId(10L);
        expectedItem.setHospitalId(100L);

        when(hospitalItemMapper.selectByTemplateIdAndHospitalId(10L, 100L)).thenReturn(Arrays.asList(expectedItem));

        HospitalItem actualItem = hospitalItemsService.getHospitalItemByTemplateId(10L, 100L);
        assertNotNull(actualItem);
        assertEquals(expectedItem, actualItem);
    }

    @Test
    void getHospitalItemByTemplateIdAndHospitalId_空列表() {
        // 测试空列表情况
        when(hospitalItemMapper.selectByTemplateIdAndHospitalId(10L, 100L)).thenReturn(new ArrayList<>());

        HospitalItem actualItem = hospitalItemsService.getHospitalItemByTemplateId(10L, 100L);
        assertNull(actualItem);
    }

    @Test
    void getHospitalItemByTemplateIdAndHospitalId_null列表() {
        // 测试null列表情况
        when(hospitalItemMapper.selectByTemplateIdAndHospitalId(10L, 100L)).thenReturn(null);

        HospitalItem actualItem = hospitalItemsService.getHospitalItemByTemplateId(10L, 100L);
        assertNull(actualItem);
    }

    @Test
    void getItemListByTemplateId_正常情况() {
        // 测试正常情况
        HospitalItem item1 = new HospitalItem();
        item1.setId(1L);
        item1.setTemplateId(10L);
        HospitalItem item2 = new HospitalItem();
        item2.setId(2L);
        item2.setTemplateId(10L);

        when(hospitalItemMapper.selectByTemplateIdAndHospitalId(10L, null)).thenReturn(Arrays.asList(item1, item2));

        List<HospitalItem> actualItems = hospitalItemsService.getItemListByTemplateId(10L);
        assertNotNull(actualItems);
        assertEquals(2, actualItems.size());
        assertTrue(actualItems.contains(item1));
        assertTrue(actualItems.contains(item2));
    }

    @Test
    void getItemListByTemplateId_空列表() {
        // 测试空列表情况
        when(hospitalItemMapper.selectByTemplateIdAndHospitalId(10L, null)).thenReturn(new ArrayList<>());

        List<HospitalItem> actualItems = hospitalItemsService.getItemListByTemplateId(10L);
        assertNotNull(actualItems);
        assertEquals(0, actualItems.size());
    }

    @Test
    void getItemListByTemplateId_null列表() {
        // 测试null列表情况
        when(hospitalItemMapper.selectByTemplateIdAndHospitalId(10L, null)).thenReturn(null);

        List<HospitalItem> actualItems = hospitalItemsService.getItemListByTemplateId(10L);
        assertNull(actualItems);
    }

    @Test
    void getItemsByTemplateIds_正常情况() {
        // 测试正常情况
        HospitalItem item1 = new HospitalItem();
        item1.setId(1L);
        item1.setTemplateId(10L);
        HospitalItem item2 = new HospitalItem();
        item2.setId(2L);
        item2.setTemplateId(20L);

        when(hospitalItemMapper.selectByTemplateIds(any())).thenReturn(Arrays.asList(item1, item2));

        List<HospitalItem> actualItems = hospitalItemsService.getItemsByTemplateIds(Arrays.asList(10L, 20L));
        assertNotNull(actualItems);
        assertEquals(2, actualItems.size());
        assertTrue(actualItems.contains(item1));
        assertTrue(actualItems.contains(item2));
    }

    @Test
    void getItemsByTemplateIds_空列表() {
        // 测试空列表情况
        when(hospitalItemMapper.selectByTemplateIds(any())).thenReturn(new ArrayList<>());

        List<HospitalItem> actualItems = hospitalItemsService.getItemsByTemplateIds(Arrays.asList(10L, 20L));
        assertNotNull(actualItems);
        assertEquals(0, actualItems.size());
    }

    @Test
    void getItemsByTemplateIds_null列表() {
        // 测试null列表情况
        when(hospitalItemMapper.selectByTemplateIds(any())).thenReturn(null);

        List<HospitalItem> actualItems = hospitalItemsService.getItemsByTemplateIds(Arrays.asList(10L, 20L));
        assertNull(actualItems);
    }

    @Test
    void deleteByTemplateId_正常情况() {
        // 测试正常情况
        hospitalItemsService.deleteByTemplateId(10L);
        verify(hospitalItemMapper, times(1)).deleteByTemplateId(10L);
    }

    @Test
    void getHospitalItemByTemplateId_正常情况() {
        // 测试正常情况
        HospitalItem expectedItem = new HospitalItem();
        expectedItem.setId(1L);
        expectedItem.setTemplateId(10L);

        when(hospitalItemMapper.selectByTemplateId(10L)).thenReturn(Arrays.asList(expectedItem));

        List<HospitalItem> actualItems = hospitalItemsService.getHospitalItemByTemplateId(10L);
        assertNotNull(actualItems);
        assertEquals(1, actualItems.size());
        assertEquals(expectedItem, actualItems.get(0));
    }

    @Test
    void getHospitalItemByTemplateId_空列表() {
        // 测试空列表情况
        when(hospitalItemMapper.selectByTemplateId(10L)).thenReturn(new ArrayList<>());

        List<HospitalItem> actualItems = hospitalItemsService.getHospitalItemByTemplateId(10L);
        assertNotNull(actualItems);
        assertEquals(0, actualItems.size());
    }

    @Test
    void getHospitalItemByTemplateId_null列表() {
        // 测试null列表情况
        when(hospitalItemMapper.selectByTemplateId(10L)).thenReturn(null);

        List<HospitalItem> actualItems = hospitalItemsService.getHospitalItemByTemplateId(10L);
        assertNull(actualItems);
    }
}

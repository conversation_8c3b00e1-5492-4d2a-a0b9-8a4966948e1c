package com.puree.followup.admin.medical.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.puree.followup.admin.medical.mapper.ReportDataSummaryMapper;
import com.puree.followup.admin.medical.service.ReportDataSummaryService;
import com.puree.followup.domain.medical.constant.RecordJsonTypeEnum;
import com.puree.followup.domain.medical.dto.QuestionnaireDataSummaryDTO;
import com.puree.followup.domain.medical.model.ReportDataSummary;
import com.puree.followup.domain.medical.model.ReportExamRecord;
import com.puree.followup.domain.medical.model.ReportInspectRecord;
import com.puree.followup.domain.medical.model.ReportRecord;
import com.puree.followup.domain.medical.vo.QuestionnaireDataSummaryVO;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import com.puree.hospital.followup.api.model.medical.json.ExamItemJson;
import com.puree.hospital.followup.api.model.medical.json.ExamSummaryJson;
import com.puree.hospital.followup.api.model.medical.json.InspectItemJson;
import com.puree.hospital.followup.api.model.medical.json.InspectSummaryJson;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamSummaryDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamSummaryItemDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.InspectItemDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.InspectSummaryDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName ReportDataSummaryServiceImpl
 * <AUTHOR>
 * @Description 体检报告数据汇总表
 * @Date 2024/5/13 18:41
 * @Version 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReportDataSummaryServiceImpl implements ReportDataSummaryService {

    private final ReportDataSummaryMapper reportDataSummaryMapper;


    /**
     * @Param id
     * @Return com.puree.followup.domain.medical.model.ReportDataSummary
     * @Description 根据ID获取
     * <AUTHOR>
     * @Date 2024/5/17 15:46
     **/
    @Override
    public ReportDataSummary getById(Long id) {
        return reportDataSummaryMapper.getById(id);
    }

    /**
     * @Param reportDataSummary
     * @Return java.util.List<com.puree.followup.domain.medical.model.ReportDataSummary>
     * @Description 批量获取
     * <AUTHOR>
     * @Date 2024/5/17 15:46
     **/
    @Override
    public List<ReportDataSummary> getList(ReportDataSummary reportDataSummary) {
        return reportDataSummaryMapper.getList(reportDataSummary);
    }

    /**
     * @Param reportDataSummary
     * @Return com.puree.followup.domain.medical.model.ReportDataSummary
     * @Description 新增
     * <AUTHOR>
     * @Date 2024/5/17 15:46
     **/
    @Override
    public ReportDataSummary insert(ReportDataSummary reportDataSummary) {
        int insert = reportDataSummaryMapper.insert(reportDataSummary);
        if (SqlHelper.retBool(insert)) {
            return reportDataSummary;
        }
        log.warn("新增 体检报告数据汇总表 失败:{}", reportDataSummary);
        throw new ServiceException("新增 体检报告数据汇总表 失败");
    }

    /**
     * @Param reportDataSummary
     * @Return com.puree.followup.domain.medical.model.ReportDataSummary
     * @Description 修改
     * <AUTHOR>
     * @Date 2024/5/17 15:46
     **/
    @Override
    public ReportDataSummary update(ReportDataSummary reportDataSummary) {
        int update = reportDataSummaryMapper.update(reportDataSummary);
        if (SqlHelper.retBool(update)) {
            return reportDataSummary;
        }
        log.warn("修改 体检报告数据汇总表 失败:{}", reportDataSummary);
        throw new ServiceException("修改 体检报告数据汇总表 失败");
    }

    /**
     * @Param reportDataSummaries
     * @Return java.lang.Boolean
     * @Description 批量新增或修改
     * <AUTHOR>
     * @Date 2024/5/20 16:49
     **/
    @Override
    public Boolean insertOrUpdateBatch(List<ReportDataSummary> reportDataSummaries) {
        int insertOrUpdateBatch = reportDataSummaryMapper.insertOrUpdateBatch(reportDataSummaries);
        if (SqlHelper.retBool(insertOrUpdateBatch)) {
            return true;
        }
        log.warn("批量新增或修改 体检报告数据汇总表 失败");
        throw new ServiceException("批量新增或修改 体检报告数据汇总表 失败");
    }


    /**
     * @Param hospitalId
     * @Param patientId
     * @Return java.util.List<com.puree.followup.domain.medical.model.ReportDataSummary>
     * @Description 根据医院ID和患者ID获取
     * <AUTHOR>
     * @Date 2024/5/17 18:51
     **/
    @Override
    public List<ReportDataSummary> getByHospitalIdAndIdNumber(Long hospitalId, String idNumber) {
        ReportDataSummary summary = new ReportDataSummary();
        summary.setHospitalId(hospitalId);
        summary.setPatientIdNumber(idNumber);
        return getList(summary);
    }

    /**
     * @Param reportInspectRecords
     * @Param reportExamRecords
     * @Param reportRecord
     * @Return java.lang.Boolean
     * @Description 数据推送整合并新增
     * <AUTHOR>
     * @Date 2024/5/17 15:46
     **/
    @Override
    public Boolean dataPushIntegrateSummaryAndInsert(List<ReportInspectRecord> reportInspectRecords, List<ReportExamRecord> reportExamRecords, ReportRecord reportRecord) {
        Long hospitalId = reportRecord.getHospitalId();
        Long patientId = reportRecord.getFamilyId();
        String patientIdNumber = reportRecord.getPatientIdNumber();
        List<ReportDataSummary> byHospitalIdAndPatientId = getByHospitalIdAndIdNumber(hospitalId, patientIdNumber);

        //只会存在一个
        Map<String, InspectItemJson> inspectItemJsonMap = null;
        //只会存在一个
        Map<String, ExamItemJson> examItemJsonMap = null;
        Map<String, InspectSummaryJson> inspectSummaryJsonMap = null;
        Map<String, ExamSummaryJson> examSummaryJsonMap = null;

        //o(1)
        //找到检验指标和检查指标并替换
        for (ReportDataSummary item : byHospitalIdAndPatientId) {
            RecordJsonTypeEnum jsonType = item.getJsonType();
            String json = item.getJson();
            Map itemMap = JSON.parseObject(json, Map.class);

            if (inspectItemJsonMap != null && examItemJsonMap != null) {
                break;
            }
            switch (jsonType) {
                //替换操作
                case INSPECT_INDICATORS:
                    inspectItemJsonMap = itemMap;
                    inspectSummaryJsonMap = dataPushBuildInspect(reportInspectRecords, inspectItemJsonMap);
                    String jsonString = StrUtil.EMPTY_JSON;
                    if (ObjectUtil.isNotNull(inspectItemJsonMap)) {
                        jsonString = JSON.toJSONString(inspectItemJsonMap);
                    }
                    item.setJson(jsonString);
                    break;
                case EXAM_INDICATORS:
                    examItemJsonMap = itemMap;
                    examSummaryJsonMap = dataPushBuildExam(reportExamRecords, examItemJsonMap);
                    String jsonStringExam = StrUtil.EMPTY_JSON;
                    if (ObjectUtil.isNotNull(examItemJsonMap)) {
                        jsonStringExam = JSON.toJSONString(examItemJsonMap);
                    }
                    item.setJson(jsonStringExam);
                    break;
            }
        }
        //第一次添加
        if (inspectItemJsonMap == null) {
            inspectItemJsonMap = new HashMap<>();
            inspectSummaryJsonMap = dataPushBuildInspect(reportInspectRecords, inspectItemJsonMap);
            ReportDataSummary inspectSummary = new ReportDataSummary();
            inspectSummary.setHospitalId(hospitalId);
            inspectSummary.setPatientId(patientId);
            inspectSummary.setItemName("检验项指标");
            inspectSummary.setJsonType(RecordJsonTypeEnum.INSPECT_INDICATORS);
            String jsonString = StrUtil.EMPTY_JSON;
            if (ObjectUtil.isNotNull(inspectItemJsonMap)) {
                jsonString = JSON.toJSONString(inspectItemJsonMap);
            }
            inspectSummary.setJson(jsonString);
            inspectSummary.setPatientIdNumber(patientIdNumber);
            byHospitalIdAndPatientId.add(inspectSummary);
        }
        //第一次添加
        if (examItemJsonMap == null) {
            examItemJsonMap = new HashMap<>();
            examSummaryJsonMap = dataPushBuildExam(reportExamRecords, examItemJsonMap);
            ReportDataSummary examDataSummary = new ReportDataSummary();
            examDataSummary.setHospitalId(hospitalId);
            examDataSummary.setPatientId(patientId);
            examDataSummary.setItemName("检查项指标");
            examDataSummary.setJsonType(RecordJsonTypeEnum.EXAM_INDICATORS);
            String jsonString = StrUtil.EMPTY_JSON;
            if (ObjectUtil.isNotNull(examItemJsonMap)) {
                jsonString = JSON.toJSONString(examItemJsonMap);
            }
            examDataSummary.setJson(jsonString);
            examDataSummary.setPatientIdNumber(patientIdNumber);
            byHospitalIdAndPatientId.add(examDataSummary);
        }
        //o(1)
        //处理检验小结
        if (inspectSummaryJsonMap != null) {
            for (ReportDataSummary item : byHospitalIdAndPatientId) {
                RecordJsonTypeEnum jsonType = item.getJsonType();
                if (jsonType.equals(RecordJsonTypeEnum.INSPECT_SUMMARY)) {
                    String itemName = item.getItemName();
                    InspectSummaryJson summaryJson = inspectSummaryJsonMap.get(itemName);
                    if (ObjectUtil.isNotNull(summaryJson)) {
                        String jsonString = JSON.toJSONString(summaryJson);
                        item.setJson(jsonString);

                    }
                    inspectSummaryJsonMap.remove(itemName);
                }
            }
            //为空处理
            Set<String> item = inspectSummaryJsonMap.keySet();
            for (String s : item) {
                InspectSummaryJson summaryJson = inspectSummaryJsonMap.get(s);
                ReportDataSummary inspectSummary = new ReportDataSummary();
                inspectSummary.setHospitalId(hospitalId);
                inspectSummary.setPatientId(patientId);
                inspectSummary.setItemName(s);
                inspectSummary.setJsonType(RecordJsonTypeEnum.INSPECT_SUMMARY);
                String jsonString = StrUtil.EMPTY_JSON;
                if (ObjectUtil.isNotNull(summaryJson)) {
                    jsonString = JSON.toJSONString(summaryJson);
                }
                inspectSummary.setJson(jsonString);
                inspectSummary.setPatientIdNumber(patientIdNumber);
                byHospitalIdAndPatientId.add(inspectSummary);
            }
        }
        //o(1)
        //处理检查小结
        if (examSummaryJsonMap != null) {
            for (ReportDataSummary item : byHospitalIdAndPatientId) {
                RecordJsonTypeEnum jsonType = item.getJsonType();
                if (jsonType.equals(RecordJsonTypeEnum.EXAM_SUMMARY)) {
                    String itemName = item.getItemName();
                    ExamSummaryJson summaryJson = examSummaryJsonMap.get(itemName);
                    if (ObjectUtil.isNotNull(summaryJson)) {
                        String jsonString = JSON.toJSONString(summaryJson);
                        item.setJson(jsonString);
                    }
                    examSummaryJsonMap.remove(itemName);
                }
            }
            //为空处理
            Set<String> item = examSummaryJsonMap.keySet();
            for (String s : item) {
                ExamSummaryJson summaryJson = examSummaryJsonMap.get(s);
                ReportDataSummary examDataSummary = new ReportDataSummary();
                examDataSummary.setHospitalId(hospitalId);
                examDataSummary.setPatientId(patientId);
                examDataSummary.setItemName(s);
                examDataSummary.setJsonType(RecordJsonTypeEnum.EXAM_SUMMARY);
                if (ObjectUtil.isNotNull(summaryJson)) {
                    String jsonString = JSON.toJSONString(summaryJson);
                    examDataSummary.setJson(jsonString);
                }
                examDataSummary.setPatientIdNumber(patientIdNumber);
                byHospitalIdAndPatientId.add(examDataSummary);
            }
        }
        //新增或修改
        insertOrUpdateBatch(byHospitalIdAndPatientId);

        return true;
    }

    /**
     * @Param reportInspectRecords
     * @Return com.puree.followup.domain.medical.model.ReportDataSummary
     * @Description 数据推送检验项整合
     * <AUTHOR>
     * @Date 2024/5/17 15:46
     **/
    @Override
    public Map<String, InspectSummaryJson> dataPushBuildInspect(List<ReportInspectRecord> reportInspectRecords, Map<String, InspectItemJson> itemJsonMap) {
        if (CollUtil.isEmpty(reportInspectRecords)) {
            return new HashMap<>();
        }

        //小结
        Map<String, InspectSummaryJson> summaryJsonMap = new HashMap<>(reportInspectRecords.size() * 2);
        if (itemJsonMap == null) {
            itemJsonMap = new HashMap<>();
        }

        for (ReportInspectRecord item : reportInspectRecords) {
            //检验项小结
            InspectSummaryJson summaryJson = new InspectSummaryJson();
            String subject = item.getSubject();
            Long recordId = item.getRecordId();
            summaryJson.setName(subject);
            summaryJson.setExamDate(item.getExamDate());
            summaryJson.setSummary(item.getSummary());
            summaryJson.setSourceTyep(item.getSource());
            summaryJson.setRecordId(recordId);
            //直接覆盖掉
            summaryJsonMap.put(subject, summaryJson);
            //检验项指标
            String inspectItems = item.getInspectItems();
            InspectSummaryDTO dto = JSON.parseObject(inspectItems, InspectSummaryDTO.class);

            if (dto != null) {
                InspectItemDTO[] items = dto.getItems();
                if (items != null) {
                    //转换为json
                    for (InspectItemDTO itemDTO : items) {

                        InspectItemJson itemJson = new InspectItemJson();
                        String itemName = itemDTO.getName();
                        itemJson.setName(itemName);
                        itemJson.setExamDate(DateUtil.parse(dto.getExamDate()));
                        itemJson.setResult(itemDTO.getResult());
                        itemJson.setResultStr(itemDTO.getResultStr());
                        itemJson.setUnit(itemDTO.getUnit());
                        itemJson.setRange(itemDTO.getRange());
                        itemJson.setRefUpperLimit(itemDTO.getRefUpperLimit());
                        itemJson.setRefLowerLimit(itemDTO.getRefLowerLimit());
                        itemJson.setAbnormal(itemDTO.getAbnormal());
                        itemJson.setSourceTyep(RecordSourceEnum.DATA_PUSH);
                        itemJson.setRecordId(recordId);
                        //直接覆盖掉
                        itemJsonMap.put(itemName, itemJson);
                    }
                }
            }
        }

        return summaryJsonMap;
    }

    /**
     * @Param reportExamRecords
     * @Return com.puree.followup.domain.medical.model.ReportDataSummary
     * @Description 数据推送检查项整合，逻辑与上面方法一致
     * <AUTHOR>
     * @Date 2024/5/17 15:46
     **/
    @Override
    public Map<String, ExamSummaryJson> dataPushBuildExam(List<ReportExamRecord> reportExamRecords, Map<String, ExamItemJson> itemJsonMap) {


        if (CollUtil.isEmpty(reportExamRecords)) {
            return new HashMap<>();
        }
        int size = reportExamRecords.size();

        Map<String, ExamSummaryJson> summaryJsonMap = new HashMap<>(size * 2);

        if (itemJsonMap == null) {
            itemJsonMap = new HashMap<>();
        }

        for (ReportExamRecord item : reportExamRecords) {

            ExamSummaryJson summaryJson = new ExamSummaryJson();
            String subject = item.getSubject();
            Long recordId = item.getRecordId();
            summaryJson.setName(subject);
            summaryJson.setExamDate(item.getExamDate());
            summaryJson.setSummary(item.getSummary());
            summaryJson.setSourceTyep(item.getSource());
            summaryJson.setRecordId(recordId);

            summaryJsonMap.put(subject, summaryJson);

            String examItems = item.getExamItems();
            ExamSummaryDTO dto = JSON.parseObject(examItems, ExamSummaryDTO.class);

            if (dto != null) {
                ExamSummaryItemDTO[] items = dto.getItems();

                if (items != null) {
                    for (ExamSummaryItemDTO itemDTO : items) {
                        ExamItemJson itemJson = new ExamItemJson();
                        String itemName = itemDTO.getName();
                        itemJson.setName(itemName);
                        itemJson.setExamDate(DateUtil.parse(dto.getExamDate()));
                        itemJson.setDesc(itemDTO.getDesc());
                        itemJson.setSourceTyep(RecordSourceEnum.DATA_PUSH);
                        itemJson.setRecordId(recordId);
                        //直接覆盖掉
                        itemJsonMap.put(itemName, itemJson);
                    }
                }
            }
        }

        return summaryJsonMap;
    }

    private final String JSON_CONVERT = "$.\"{}\"";

    /**
     * @Param dto
     * @Return List<QuestionnaireDataSummaryVO>
     * @Description 问卷-指标项查询
     * <AUTHOR>
     * @Date 2024/7/11 12:21
     **/
    @Override
    public List<QuestionnaireDataSummaryVO> questionnaireDataSummary(QuestionnaireDataSummaryDTO dto) {
        List<String> itemNames = dto.getItemNames();
        List<String> convert = new ArrayList<>(itemNames.size() * 2);
        for (String itemName : itemNames) {
            convert.add(CharSequenceUtil.format(JSON_CONVERT, itemName));
        }
        dto.setItemNames(convert);

        List<ReportDataSummary> reportDataSummaries = reportDataSummaryMapper.questionnaireDataSummary(dto);

        List<QuestionnaireDataSummaryVO> vos = new ArrayList<>();
        if (CollUtil.isEmpty(reportDataSummaries)) {
            return vos;
        }
        reportDataSummaries.stream().forEach(item -> {
            String json = item.getJson();
            RecordJsonTypeEnum jsonType = item.getJsonType();
            switch (jsonType) {
                case INSPECT_INDICATORS:
                    Object parseInspect = JSON.parse(json);
                    if (parseInspect instanceof List) {
                        List<JSONObject> jsonListInspect = (List<JSONObject>) parseInspect;
                        for (JSONObject s : jsonListInspect) {
                            InspectItemJson itemJson = s.toJavaObject(InspectItemJson.class);
                            vos.add(buildQuestionnaireDataSummaryVO(itemJson.getName(), itemJson.getResultStr(), itemJson.getExamDate()));
                        }
                    } else {
                        InspectItemJson itemJson = JSON.parseObject(json, InspectItemJson.class);
                        vos.add(buildQuestionnaireDataSummaryVO(itemJson.getName(), itemJson.getResultStr(), itemJson.getExamDate()));
                    }
                    break;
                case EXAM_INDICATORS:
                    Object parseExam = JSON.parse(json);
                    if (parseExam instanceof List) {
                        List<JSONObject> jsonListExam = (List<JSONObject>) parseExam;
                        for (JSONObject s : jsonListExam) {
                            ExamItemJson itemJson = s.toJavaObject(ExamItemJson.class);
                            vos.add(buildQuestionnaireDataSummaryVO(itemJson.getName(), itemJson.getDesc(), itemJson.getExamDate()));
                        }
                    } else {
                        ExamItemJson itemJson = JSON.parseObject(json, ExamItemJson.class);
                        vos.add(buildQuestionnaireDataSummaryVO(itemJson.getName(), itemJson.getDesc(), itemJson.getExamDate()));
                    }

                    break;
            }
        });
        return vos;
    }

    /**
     * @Param itemName
     * @Param itemValue
     * @Param itemTime
     * @Return QuestionnaireDataSummaryVO
     * @Description 构建 VO
     * <AUTHOR>
     * @Date 2024/7/11 15:11
     **/
    private QuestionnaireDataSummaryVO buildQuestionnaireDataSummaryVO(String itemName, String itemValue, Date itemTime) {
        QuestionnaireDataSummaryVO vo = new QuestionnaireDataSummaryVO();
        vo.setItemName(itemName);
        if (ObjectUtil.isNotNull(itemTime)) {
            vo.setItemTime(DateUtils.parse_YYYY_MM_DD_HH_MM_SS(itemTime));
        }
        vo.setItemValue(itemValue);
        return vo;
    }
}

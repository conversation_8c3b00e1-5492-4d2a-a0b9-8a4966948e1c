package com.puree.followup.admin.medical.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.puree.followup.admin.medical.service.MedicalReportService;
import com.puree.followup.admin.medical.service.ReportDataSummaryService;
import com.puree.followup.admin.medical.service.ReportExamRecordService;
import com.puree.followup.admin.medical.service.ReportFileRecordService;
import com.puree.followup.admin.medical.service.ReportInspectRecordService;
import com.puree.followup.admin.medical.service.ReportRecordService;
import com.puree.followup.admin.medical.service.ReportRegularRecordService;
import com.puree.followup.admin.medical.service.ReportRegularRecordSummaryService;
import com.puree.followup.domain.event.MedicalReportUploadEvent;
import com.puree.followup.domain.medical.constant.IsHandleEnum;
import com.puree.followup.domain.medical.constant.RecordTypeEnum;
import com.puree.followup.domain.medical.model.ReportExamRecord;
import com.puree.followup.domain.medical.model.ReportFileRecord;
import com.puree.followup.domain.medical.model.ReportInspectRecord;
import com.puree.followup.domain.medical.model.ReportRecord;
import com.puree.followup.domain.medical.model.ReportRegularRecord;
import com.puree.followup.queue.producer.MedicalReportProducer;
import com.puree.hospital.app.api.RemotePatientFamilyService;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.ehr.api.RemoteEhrConfigService;
import com.puree.hospital.ehr.api.model.BusEhrConfig;
import com.puree.hospital.ehr.api.model.BusEhrConfigDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import com.puree.hospital.tool.api.RemoteOssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName MedicalReportServiceImpl
 * <AUTHOR>
 * @Description 体检报告
 * @Date 2024/4/25 17:24
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MedicalReportServiceImpl implements MedicalReportService {

    private final RemoteOssService remoteOssService;
    private final ReportRecordService reportRecordService;
    private final RemoteEhrConfigService remoteEhrConfigService;
    private final RemotePatientFamilyService remotePatientFamilyService;
    private final ReportDataSummaryService reportDataSummaryService;
    private final ReportExamRecordService reportExamRecordService;
    private final ReportFileRecordService reportFileRecordService;
    private final ReportInspectRecordService reportInspectRecordService;
    private final ReportRegularRecordService reportRegularRecordService;
    private final ReportRegularRecordSummaryService reportRegularRecordSummaryService;

    @Lazy
    @Resource
    private MedicalReportProducer medicalReportProducer;

    /**
     * @Param file
     * @Param directoryKey
     * @Return java.lang.Boolean
     * @Description OSS上传
     * <AUTHOR>
     * @Date 2024/4/25 17:32
     **/
    @Override
    public String ossUpload(MultipartFile file, Integer directoryKey) {
        Map<String, Object> map = remoteOssService.upload(file, directoryKey);
        log.info("OSS上传接口返回值：{}", map);
        if (map != null && map.get("code").equals(HttpStatus.SUCCESS)) {
            return (String) map.get("msg");
        }
        throw new RuntimeException("OSS上传异常：" + map.get("msg"));
    }

    @Override
    public Boolean upload(ExamReportInfoDTO dto) {
        Long hospitalId = dto.getHospitalId();
        if (ObjectUtil.isNull(hospitalId)) {
            //查询医院id
            BusEhrConfigDTO hospitalQuery = new BusEhrConfigDTO();
            hospitalQuery.setAppId(dto.getAppId());
            R<BusEhrConfig> r = remoteEhrConfigService.getInfo(hospitalQuery);
            if (r.getData() == null) {
                log.error("医院信息不存在，appId：{}", dto.getAppId());
                return false;
            }
            hospitalId = r.getData().getHospitalId();
        }
        // todo 待通过三方patientId查询绑定的患者信息
        BusPatientFamilyVo userVO = null;
        if (Objects.nonNull(dto.getPatientInfo())) {
            userVO = remotePatientFamilyService.queryPatientInfo(dto.getPatientInfo().getIdCard(), hospitalId).getData();
            if (userVO == null) {
                //如果查询到就诊人不存在，还需要执行后续的体检报告数据上传的逻辑，而无须处理随访入组的逻辑
                log.error("体检报告上传-就诊人不存在：{}", dto.getPatientInfo().getIdCard());
            }
        }
        //写入医院ID
        dto.setHospitalId(hospitalId);
        //写入患者ID
        dto.setPatientId(userVO == null ? null : userVO.getPatientId());
        //体检报告原始数据落库  TODO 待通过三方patientID查询人员
        ReportRecord reportRecord = reportRecordHandle(convertReportRecord(dto, userVO == null ? null : userVO.getId(), Objects.nonNull(dto.getPatientInfo()) ? dto.getPatientInfo().getIdCard() : null));
        if (reportRecord != null) {
            //发送体检报告消息事件
            MedicalReportUploadEvent event = new MedicalReportUploadEvent(reportRecord.getId(), reportRecord.getHospitalId(), reportRecord.getPatientId(), reportRecord.getFamilyId());
            log.info("体检报告上传发送消息事件：", event.getReportRecordId());
            medicalReportProducer.send(event);
        }
        return true;
    }

    /**
     * @Param reportRecord
     * @Return com.puree.followup.domain.medical.model.ReportRecord
     * @Description 写入体检报告记录
     * <AUTHOR>
     * @Date 2024/5/15 17:07
     **/
    @Override
    public ReportRecord insertReportRecord(ReportRecord reportRecord) {
        return reportRecordService.insert(reportRecord);
    }

    /**
     * @Param reportRecord
     * @Return com.puree.followup.domain.medical.model.ReportRecord
     * @Description 修改体检报告记录
     * <AUTHOR>
     * @Date 2024/5/15 17:51
     **/
    @Override
    public ReportRecord updateReportRecord(ReportRecord reportRecord) {
        return reportRecordService.update(reportRecord);
    }

    /**
     * @Param dto
     * @Return com.puree.followup.domain.medical.model.ReportRecord
     * @Description 转换类型
     * <AUTHOR>
     * @Date 2024/5/15 17:07
     **/
    @Override
    public ReportRecord convertReportRecord(ExamReportInfoDTO dto, Long familyId, String idCard) {
        ReportRecord reportRecord = new ReportRecord();
        if (dto != null) {
            reportRecord.setReportJson(JSONUtil.toJsonStr(dto));
            reportRecord.setHospitalId(dto.getHospitalId());
            reportRecord.setRecordType(RecordTypeEnum.MEDICAL_REPORT);
            reportRecord.setOrgId(dto.getOrgId());
            reportRecord.setExamId(dto.getExamId());
            reportRecord.setDeviceId(dto.getDeviceId());
            reportRecord.setExamDate(DateUtil.parse(dto.getExamDate()));
            reportRecord.setIsHandle(IsHandleEnum.NOT_PROCESSED);
            reportRecord.setReportId(dto.getReportId());
            reportRecord.setReportVer(dto.getReportVer());
            reportRecord.setReportDate(DateUtil.parse(dto.getReportDate()));
            reportRecord.setPatientIdNumber(idCard);
            reportRecord.setPatientId(dto.getPatientId());
            reportRecord.setFamilyId(familyId);
            return reportRecord;
        }
        log.warn("体检报告信息 不能为空:{}", dto);
        throw new ServiceException("体检报告信息 不能为空");
    }

    /**
     * @Param reportRecord
     * @Return com.puree.followup.domain.medical.model.ReportRecord
     * @Description 校验数据后写入
     * <AUTHOR>
     * @Date 2024/5/15 17:07
     **/
    @Override
    public ReportRecord reportRecordHandle(ReportRecord reportRecord) {
        //拿到体检报告ID
        String reportId = reportRecord.getReportId();
        //拿到体检报告版本号
        Integer reportVer = reportRecord.getReportVer();
        Date reportDate = reportRecord.getReportDate();
        Long orgId = reportRecord.getOrgId();
        Long hospitalId = reportRecord.getHospitalId();


        List<ReportRecord> list = reportRecordService.getListByPatientIdNumberDate(reportRecord.getPatientIdNumber(), reportDate,orgId,hospitalId);

        if (CollUtil.isEmpty(list)) {
            //为空可以直接添加
            return insertReportRecord(reportRecord);
        }

        ReportRecord newestRecord = list.get(0);
        Integer newestReportVer = newestRecord.getReportVer();
        //版本号大于当前版本号时，数据覆盖处理
        if (reportVer == 1 || NumberUtil.compare(reportVer, newestReportVer) > 0) {
            reportRecord.setId(newestRecord.getId());
            return updateReportRecord(reportRecord);
        }
        log.warn("该体检报告ID与版本号已存在:{}", reportRecord);
        throw new ServiceException("该体检报告ID与版本号已存在");
    }

    /**
     * @Param recordId
     * @Return void
     * @Description MQ调起
     * <AUTHOR>
     * @Date 2024/5/20 17:29
     **/
    @Override
    @Transactional
    public void dataPushHandler(Long recordId) {

        ThreadUtil.sleep(1000);

        log.info("MQ 调起数据拆分:recordId{}", recordId);
        ReportRecord reportRecord = reportRecordService.getById(recordId);
        if (reportRecord == null) {
            log.error("该体检报告ID不存在:{}", recordId);
            throw new ServiceException("该体检报告ID不存在 recordId" + recordId);
        }
        if (reportRecord.getIsHandle().equals(IsHandleEnum.PROCESSED)) {
            log.error("该体检报告已被处理:{}", recordId);
            throw new ServiceException("该体检报告ID不存在 recordId" + recordId);
        }
        log.info("MQ 调起数据拆分校验完成");
        //数据拆分
        List<ReportRegularRecord> reportRegularRecords = reportRegularRecordService.dataPushSplitAndInsert(reportRecord);
        List<ReportInspectRecord> reportInspectRecords = reportInspectRecordService.dataPushSplitAndInsert(reportRecord);
        List<ReportExamRecord> reportExamRecords = reportExamRecordService.dataPushSplitAndInsert(reportRecord);
        ReportFileRecord reportFileRecord = reportFileRecordService.dataPushSplitAndInsert(reportRecord);

        //数据整合
        reportRegularRecordSummaryService.dataPushIntegrateSummaryAndInsert(reportRegularRecords, reportRecord);
        reportDataSummaryService.dataPushIntegrateSummaryAndInsert(reportInspectRecords, reportExamRecords, reportRecord);
        reportRecord.setIsHandle(IsHandleEnum.PROCESSED);
        //修改状态
        reportRecordService.update(reportRecord);
        log.info("MQ 调起数据拆分拆分完成");
    }
}

package com.puree.followup.question.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.domain.vo.QuestionVO;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.question.mapper.QuestionMapper;
import com.puree.followup.question.service.QuestionAdminService;
import com.puree.hospital.app.api.RemotePatientFamilyService;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.his.api.common.HisPEResult;
import com.puree.hospital.his.api.entity.dto.QuestionAnswerContentDTO;
import com.puree.hospital.his.api.entity.dto.QuestionContentDTO;
import com.puree.hospital.his.api.entity.dto.QuestionOptionDTO;
import com.puree.hospital.his.api.entity.dto.QuestionTitleDTO;
import com.puree.hospital.his.api.feign.RemoteHisQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 问卷数据同步his 帮助类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/24 19:09
 */
@Slf4j
@Component
public class QuestionSyncHisHelper {

    @Resource
    private QuestionAdminService questionAdminService;
    @Resource
    private RemoteHisQuestionService remoteHisQuestionService;
    @Resource
    private PatientQuestionRecordMapper patientDetailQuestionRecordMapper;
    @Resource
    private RemotePatientFamilyService remotePatientFamilyService;
    @Resource
    private QuestionMapper questionMapper;

    /**
     *  同步问卷数据
     * @param questionId    问卷ID
     */
    public void syncQuestionData(Long questionId) {
        log.info("开始同步问卷数据 questionId:{}", questionId);
        QuestionVO questionVO = questionAdminService.adminQuestionDetail(questionId);
        if (Objects.isNull(questionVO)) {
            log.warn("问卷不存在 questionId:{}", questionId);
            return;
        }
        String questionContent = questionVO.getQuestionContent();
        // 开始解析问卷数据
        QuestionContentDTO QuestionContentDTO = JSON.parseObject(questionContent, QuestionContentDTO.class);
        // 过滤已删除的子问题
        filterDeletedSubQuestion(QuestionContentDTO.getList());
        // 组装返回参数
        log.info("{}", JSON.toJSONString(QuestionContentDTO));
        R<HisPEResult> hisPEResultR = remoteHisQuestionService.sendQuestion(QuestionContentDTO);
        // 修改同步状态
        if (hisPEResultR.isSuccess()) {
            Question question_update = new Question();
            question_update.setId(questionVO.getId());
            question_update.setSyncStatus(1);
            question_update.setUpdateTime(new Date());
            questionMapper.updateByCondition(question_update);
        }
    }

    /**
     *  同步问卷数据
     * @param questionId    问卷ID
     * @param patientId     患者ID
     * @param hospitalId    医院ID
     */
    public void syncQuestionAnswer(Long questionId, Long patientId, Long hospitalId) {
        PatientQuestionRecord query = new PatientQuestionRecord();
        query.setPatientId(patientId);
        query.setQuestionId(questionId);
        query.setHospitalId(hospitalId);
        PatientQuestionRecord patientQuestionRecord = patientDetailQuestionRecordMapper.selectOne(query);
        syncQuestionAnswer(patientQuestionRecord);
    }

    /**
     * 同步问卷答案
     * @param patientQuestionRecord     患者问卷记录
     */
    public void syncQuestionAnswer(PatientQuestionRecord patientQuestionRecord) {
        log.info("开始同步问卷答案 questionId:{} patientId:{}", patientQuestionRecord.getQuestionId(), patientQuestionRecord.getPatientId());
        if (Objects.isNull(patientQuestionRecord) || !FillQuestionStatusEnum.FINISHED.getIndex().equals(patientQuestionRecord.getFillStatus())) {
            log.warn("问卷未填写完成 questionId:{} patientId:{}", patientQuestionRecord.getQuestionId(), patientQuestionRecord.getPatientId());
            return;
        }
        // 组装数据
        QuestionAnswerContentDTO questionAnswerContentDTO = buildRequestParam(patientQuestionRecord.getAnswerContent(), patientQuestionRecord.getPatientId(), patientQuestionRecord.getHospitalId(), patientQuestionRecord.getSubmitTime());
        R<HisPEResult> hisPEResultR = remoteHisQuestionService.sendQuestionAnswer(questionAnswerContentDTO);
        // 修改同步状态
        if (hisPEResultR.isSuccess()) {
            PatientQuestionRecord patientQuestionRecord_update = new PatientQuestionRecord();
            patientQuestionRecord_update.setId(patientQuestionRecord.getId());
            patientQuestionRecord_update.setSyncStatus(1);
            patientQuestionRecord_update.setUpdateTime(new Date());
            patientDetailQuestionRecordMapper.updateByPrimaryKeySelective(patientQuestionRecord_update);
        }
    }

    /**
     *  构建请求参数
     * @param answerContent 回答内容
     * @param patientId     患者ID
     * @param hospitalId    医院ID
     * @return  请求参数
     */
    public QuestionAnswerContentDTO buildRequestParam(String answerContent, Long patientId, Long hospitalId, Date submitTime) {
        // 患者信息
        QuestionAnswerContentDTO questionAnswerContentDTO = JSONObject.parseObject(answerContent, QuestionAnswerContentDTO.class);
        R<BusPatientFamilyVo> busPatientFamilyVoR = remotePatientFamilyService.queryInfoByPatientId(patientId, hospitalId);
        if (busPatientFamilyVoR.isSuccess() && busPatientFamilyVoR.getData() != null) {
            JSONObject patientJson = new JSONObject();
            BusPatientFamilyVo data = busPatientFamilyVoR.getData();
            patientJson.put("name", data.getName());
            patientJson.put("idNumber", data.getIdNumber());
            patientJson.put("phone", data.getCellPhoneNumber());
            patientJson.put("submitTime", DateUtil.formatDateTime(submitTime));
            questionAnswerContentDTO.setPatient(patientJson);
        }
        return questionAnswerContentDTO;
    }

    /**
     * 递归过滤已删除的子问题
     * @param questionList  子题目
     */
    public void filterDeletedSubQuestion(List<QuestionTitleDTO> questionList) {
        if (CollUtil.isEmpty(questionList)) {
            return;
        }
        // 遍历第一级子题目
        for (QuestionTitleDTO questionTitle : questionList) {
            // 获取选项值
            List<QuestionOptionDTO> options = questionTitle.getOptions();
            if (CollUtil.isEmpty(options)) {
                continue;
            }
            // 遍历选项
            for (QuestionOptionDTO option : options) {
                // 如果有子选项 进行下一层级递归， 没有置空子题目
                if (option.isHaveChildren()) {
                    filterDeletedSubQuestion(option.getChildren());
                } else {
                    option.setChildren(Collections.EMPTY_LIST);
                }
            }
        }
    }


}

package com.puree.followup.question.helper;

import com.puree.followup.FollowUpApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = FollowUpApplication.class)
class QuestionSyncHisHelperTest {

    @Resource
    private QuestionSyncHisHelper questionSyncHisHelper;

    @Test
    public void syncQuestionData() {
        questionSyncHisHelper.syncQuestionData(110L);
    }

    @Test
    public void syncQuestionAnswer(){
        questionSyncHisHelper.syncQuestionAnswer(110L, 1778L, 2L);
    }

}
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hospital-modules</artifactId>
        <groupId>com.puree</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hospital-ehr</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-ehr-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-app-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-tool-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-business-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-feign</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-aliyun</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-follow-up-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-log</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-security</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-log</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-datasource</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>puree-common-pdf</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-job</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-logback</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-notification</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- thymeleaf 模板 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
        </dependency>
        <!--单元测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-system-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-system-api</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${commons.fileupload.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <copy file="target/${project.artifactId}.jar" todir="../../deploy">
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>

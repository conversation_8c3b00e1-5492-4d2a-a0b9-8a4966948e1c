package com.puree.hospital.ehr.notification;

import com.google.common.collect.Sets;
import com.puree.hospital.common.notification.service.ITemplateMessageService;
import com.puree.hospital.ehr.AbstractHospitalEhrApplicationTest;
import com.puree.hospital.ehr.api.model.event.HealthReportNotificationEvent;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * <p>
 * 微信模板消息测试
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/6 17:23
 */
public class WxTemplateMessageServiceTest extends AbstractHospitalEhrApplicationTest {

    @Resource(name = "healthReportWxUniAppTemplateMessageService")
    private ITemplateMessageService<HealthReportNotificationEvent> healthReportWxUniAppTemplateMessageService;

    @Test
    public void testSendMessage() {
        HealthReportNotificationEvent event = new HealthReportNotificationEvent();
        event.setCrId(704L);
        event.setDeviceNo("L1238402WUJY0204");
        event.setIdCardNo("110228197303030018");
        event.setCheckDate("2025-03-06");
        event.setLabels(Sets.newHashSet("LBP_001", "VHR", "BCP_004", "BCP_026", "BCP_103", "UA", "ECG_001", "TC", "LRHR", "VHR_001", "BO_001", "BCP_065", "RHR", "BF_003"));
        event.setRemark("蛋白质:10.8。null:2.4。骨骼肌率:44.5。null:9.2。体温:38.6。心率:54.0。舒张压(左):98.0。心率(左):107.0。脉搏:110.0。null:0.95。null:100.0。尿酸:0.75。甘油三酯:2.0。心电-静息心率:106.0");
        event.setFamilyId(188L);
        event.setHospitalId(2L);
        healthReportWxUniAppTemplateMessageService.sendMessage(event);
    }

}

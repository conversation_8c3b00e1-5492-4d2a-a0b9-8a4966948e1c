package com.puree.hospital.ehr.controller;

import com.puree.hospital.app.api.model.SMSLoginUser;
import com.puree.hospital.ehr.HospitalEhrApplication;
import com.puree.hospital.ehr.domain.dto.BusEhrCaseDiagnosisHistoryDTO;
import com.puree.hospital.ehr.domain.vo.BusEhrCaseDiagnosisHistoryVO;
import com.puree.hospital.ehr.service.IBusEhrCaseDiagnosisHistoryService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = HospitalEhrApplication.class)
public class BusEhrCaseDiagnosisHistoryControllerTest {

    @Autowired
    IBusEhrCaseDiagnosisHistoryService iBusEhrCaseDiagnosisHistoryService;

    @Test
    public void testGetCompletedDiagnosisHistory_no_data() {
        BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO = new BusEhrCaseDiagnosisHistoryDTO();
        busEhrCaseDiagnosisHistoryDTO.setHospitalId(1L);
        busEhrCaseDiagnosisHistoryDTO.setIdCardNo("254345");
        busEhrCaseDiagnosisHistoryDTO.setConsultationStatus("4");
        List<BusEhrCaseDiagnosisHistoryVO> busEhrCaseDiagnosisHistoryVOS = iBusEhrCaseDiagnosisHistoryService.queryDiagnosisHistoryList(busEhrCaseDiagnosisHistoryDTO);
        Assertions.assertTrue(busEhrCaseDiagnosisHistoryVOS.isEmpty());
    }

    @Test
    public void testGetCompletedDiagnosisHistory_data() {
        BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO = new BusEhrCaseDiagnosisHistoryDTO();
        busEhrCaseDiagnosisHistoryDTO.setHospitalId(100L);
        busEhrCaseDiagnosisHistoryDTO.setIdCardNo("254345");
        busEhrCaseDiagnosisHistoryDTO.setConsultationStatus("4");
        List<BusEhrCaseDiagnosisHistoryVO> busEhrCaseDiagnosisHistoryVOS = iBusEhrCaseDiagnosisHistoryService.queryDiagnosisHistoryList(busEhrCaseDiagnosisHistoryDTO);
        Assertions.assertTrue(busEhrCaseDiagnosisHistoryVOS.size() > 0);
    }

    @Test
    public void testGetCompletedDiagnosisHistory_no_permission() {
        SMSLoginUser loginUser = new SMSLoginUser();
        loginUser.setHospitalId(1L);
        loginUser.setIdentity("6");
        loginUser.setUserid(1L);
        boolean b = iBusEhrCaseDiagnosisHistoryService.checkUserRoleByIdCardNo(loginUser, "254345");
        Assertions.assertFalse(b);
    }
}
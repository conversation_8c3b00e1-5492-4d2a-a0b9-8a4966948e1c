package com.puree.hospital.ehr;

import com.alibaba.fastjson.JSONArray;
import com.puree.hospital.common.core.utils.SHA256Util;
import com.puree.hospital.common.core.utils.ScopeUtil;
import com.puree.hospital.ehr.domain.BusEhrConfig;
import com.puree.hospital.ehr.mapper.BusEhrConfigMapper;
import com.puree.hospital.ehr.service.IBusEhrCcrChemicalReportService;
import com.puree.hospital.ehr.service.IBusEhrCcrInspectionReportService;
import com.puree.hospital.ehr.service.IBusEhrCcrPhysicalReportService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 健康档案单元测试类
 *
 * <AUTHOR>
 * @date 2022/11/30 15:50
 */
public class EhrApplicationTests extends AbstractHospitalEhrApplicationTest {
    @Autowired
    private BusEhrConfigMapper configMapper;

    @Autowired
    private IBusEhrCcrInspectionReportService busEhrCcrInspectionReportService ;
    @Autowired
    private IBusEhrCcrPhysicalReportService busEhrCcrPhysicalReportService ;
    @Autowired
    private IBusEhrCcrChemicalReportService busEhrCcrChemicalReportService ;


    @Test
    public void test() {
        System.out.println(ScopeUtil.inNumRange(20.1, "[18.5,23.9]"));
        System.out.println(ScopeUtil.inNumRange(20.1, "(,18.5)"));
        System.out.println(ScopeUtil.inNumRange(20.1, "[24,28)"));
        System.out.println(ScopeUtil.inNumRange(28.1, "[28,)"));
    }

    @Test
    public void test2() {
        String str = "data:image/bmp;base64,/;";
        String substring = str.substring(str.indexOf("/") + 1, str.indexOf(";"));
        System.out.println(substring);
    }

    @Test
    public void test3(){
        JSONArray array = new JSONArray();
        array.add("1");
        array.add("2");
        array.add("3");
        ArrayList<Object> copy = new ArrayList<>(array);
        copy.remove("1");
//        JSONArray copy = new JSONArray(array);
//        copy.remove("1");
        System.out.println(array);
        System.out.println(copy);
    }

    @Test
    public void test4() {
        ArrayList<String> ranges = new ArrayList<>();
        ranges.add("[18.5,]");
        ranges.add("(,18.5)");
//        ranges.add("(23.9,)");
//        ranges.add("[28,)");
        List<Map<String, String>> rangeDots = ScopeUtil.getRangeDots(ranges);
        System.out.println(rangeDots);
    }

    @Test
    public void test5() {
        long timestampStr = System.currentTimeMillis();
        System.out.println(timestampStr);
        String params = "{\"bmi\":{\"bmi\":24.3,\"height\":173.5,\"weight\":65},\"bol\":{\"cholesterol\":1.0,\"fatHdl\":3.0,\"fatLdl\":4.0,\"triglyceride\":2.0},\"boo\":{\"bloodOxygen\":98.0,\"pulse\":88.0},\"bp\":{\"rdbp\":98.0,\"rheartRate\":100.0,\"rsbp\":60.0},\"glu\":{\"type\":\"1\",\"value\":7.8},\"checkTime\":\"2023-09-27 18:00:00\",\"idCards\":\"******************\",\"institution\":\"HuiYun\",\"temp\":{\"value\":37.5},\"ua\":{\"value\":0.8}}";
        BusEhrConfig config = configMapper.selectById(1);
        String plain = config.getAppId() + config.getAppSecret() + timestampStr + params;
        System.out.println("plain:" + plain);
        System.out.println("sign:" + SHA256Util.toHexString(SHA256Util.getSHA(plain)));
    }
}

package com.puree.hospital.ehr.service;



import com.puree.hospital.ehr.domain.BusDoctorCaseTemplate;
import com.puree.hospital.ehr.domain.dto.BusDoctorCaseTemplateCreateDTO;
import com.puree.hospital.ehr.domain.dto.BusDoctorCaseTemplateUpdateDTO;

import java.util.List;

/**
 * 医生病历模板服务接口
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface IBusDoctorCaseTemplateService {

    /**
     * 获取医生的模板列表
     *
     * @param doctorId   医生ID
     * @param hospitalId 医院ID
     * @return 模板列表
     */
    List<BusDoctorCaseTemplate> getTemplateList(Long doctorId, Long hospitalId);

    /**
     * 根据ID获取模板详情
     *
     * @param id     模板ID
     * @param hospitalId 医院ID
     * @return 模板详情
     */
    BusDoctorCaseTemplate getCheckedTemplate(Long id, Long hospitalId);

    /**
     * 新增模板
     *
     * @param createDto  模板数据
     * @param doctorId   医生ID
     * @param hospitalId 医院ID
     */
    void insertTemplate(BusDoctorCaseTemplateCreateDTO createDto, Long doctorId, Long hospitalId);

    /**
     * 更新模板
     *
     * @param updateDto 模板数据
     * @param doctorId  医生ID
     * @return 操作结果
     */
    int updateTemplate(BusDoctorCaseTemplateUpdateDTO updateDto, Long doctorId);

    /**
     * 删除模板
     * @param templateId 模板ID
     * @param doctorId 医生ID
     * @return 操作结果
     */
    int deleteTemplate(Long templateId, Long doctorId);


}

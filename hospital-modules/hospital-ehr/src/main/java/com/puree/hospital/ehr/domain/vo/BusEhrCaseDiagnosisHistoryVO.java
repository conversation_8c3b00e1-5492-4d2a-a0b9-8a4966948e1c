package com.puree.hospital.ehr.domain.vo;

import com.puree.hospital.ehr.domain.BusEhrCaseDiagnosisHistory;
import lombok.Data;

/**
 * 健康档案-病例信息-诊疗记录
 */
@Data
public class BusEhrCaseDiagnosisHistoryVO  extends BusEhrCaseDiagnosisHistory {

    /**
     *  签名保存地址
     */
    private String certSignature;

    /**
     *  医院名称
     */
    private String hospitalName;

    /**
     *  问诊单状态 （0待支付,1待接诊 未使用,2问诊中 使用中, 3问诊中 已开处方,4已完成 已使用,5已失效,6已退号,7已退款,8已取消,9退款中
     */
    private String consultationStatus;

    /**
     *  医生名称
     */
    private String doctorName;

}
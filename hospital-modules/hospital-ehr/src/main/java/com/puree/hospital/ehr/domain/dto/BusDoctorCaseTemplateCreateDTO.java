package com.puree.hospital.ehr.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 病历模板新增DTO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class BusDoctorCaseTemplateCreateDTO {

    /** 
     * 模板名称 
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /** 
     * 主诉 
     */
    private String chiefComplaint;

    /** 
     * 现病史 
     */
    private String historyOfPresentIllness;

    /** 
     * 既往史 
     */
    private String pastHistory;

    /** 
     * 过敏史 
     */
    private String allergicDrugs;

    /** 
     * 西医诊断 
     */
    private String diagnosis;

    /** 
     * 中医诊断 
     */
    private String tcmDiagnosis;

    /** 
     * 医嘱 
     */
    private String advice;
}
package com.puree.hospital.ehr.service;


import com.puree.hospital.app.api.model.SMSLoginUser;
import com.puree.hospital.ehr.api.model.ConsultationDiagnosisVO;
import com.puree.hospital.ehr.domain.BusEhrCaseDiagnosisHistory;
import com.puree.hospital.ehr.domain.dto.BusEhrCaseDiagnosisHistoryDTO;
import com.puree.hospital.ehr.domain.vo.BusEhrCaseDiagnosisHistoryVO;

import java.util.List;

/**
 * 健康档案-检查报告-诊疗记录
 * <AUTHOR>
 */
public interface IBusEhrCaseDiagnosisHistoryService {

    /**
     * 诊疗记录列表
     */
    List<BusEhrCaseDiagnosisHistoryVO> listBusEhrCaseDiagnosisHistory(BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO);
    /**
     * 新增诊疗记录
     */
    Long insertBusEhrCaseDiagnosisHistory(BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory);

    /**
     * 更新诊疗记录
     */
    int updateBusEhrCaseDiagnosisHistoryById(BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory);

    /**
     * 诊疗记录详情
     */
    BusEhrCaseDiagnosisHistoryVO getBusEhrCaseDiagnosisHistoryById(Long id);

    /**
     * 查询诊疗记录
     * @param busEhrCaseDiagnosisHistory 诊疗记录
     * @return
     */
    List<BusEhrCaseDiagnosisHistory> queryCaseDiagnosisHistory(BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory);

    BusEhrCaseDiagnosisHistory getInfo(BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory);

    /**
     * 根据用户的登录身份信息 - 检查查询的身份证号是否有权限查询该用户的诊疗记录
     * @param loginUser 登录用户
     * @param idCardNo 身份证号
     * @return 是否有权限 查询 - true:有权限 false:没有权限
     */
    boolean checkUserRoleByIdCardNo(SMSLoginUser loginUser, String idCardNo);

    /**
     *  根据问诊单id查询医生创建的就诊记录
     * @param consultationIds   问诊单id
     * @return  病例id
     */
    List<ConsultationDiagnosisVO> getDoctorCreateDiagnosisHistoryByConsultationIds(List<Long> consultationIds);

    /**
     *  查询诊疗记录列表
     * @param busEhrCaseDiagnosisHistoryDTO 入参
     * @return 列表-就诊时间降序
     */
    List<BusEhrCaseDiagnosisHistoryVO> queryDiagnosisHistoryList(BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO);
}
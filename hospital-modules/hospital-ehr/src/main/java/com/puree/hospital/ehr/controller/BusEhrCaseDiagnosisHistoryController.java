package com.puree.hospital.ehr.controller;


import com.puree.hospital.app.api.model.SMSLoginUser;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.ehr.api.model.ConsultationDiagnosisVO;
import com.puree.hospital.ehr.domain.BusEhrCaseDiagnosisHistory;
import com.puree.hospital.ehr.domain.dto.BusEhrCaseDiagnosisHistoryDTO;
import com.puree.hospital.ehr.helper.LoginUserHelper;
import com.puree.hospital.ehr.service.IBusEhrCaseDiagnosisHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 健康档案-检查报告-诊疗记录
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("diagnosisHistory")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusEhrCaseDiagnosisHistoryController extends BaseController {
    private final IBusEhrCaseDiagnosisHistoryService iBusEhrCaseDiagnosisHistoryService;
    private final LoginUserHelper loginUserHelper;

    /**
     * 诊疗记录列表
     */
    @GetMapping("list")
    public TableDataInfo listBusEhrCaseDiagnosisHistory(BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO){
        SMSLoginUser loginUser = loginUserHelper.getLoginUser();
        boolean checkUserRole = iBusEhrCaseDiagnosisHistoryService.checkUserRoleByIdCardNo(loginUser,busEhrCaseDiagnosisHistoryDTO.getIdCardNo());
        // 校验没通过 - 直接返回空列表
        if (!checkUserRole){
            return getDataTable(new ArrayList<>());
        }
        // 不管医生端还是患者端 - 限制只能查找当前医院下的患者诊疗记录 - 前端说请求头必传的 hospitalId
        busEhrCaseDiagnosisHistoryDTO.setHospitalId(SecurityUtils.getHospitalId());
        startPage();
        return getDataTable(iBusEhrCaseDiagnosisHistoryService.listBusEhrCaseDiagnosisHistory(busEhrCaseDiagnosisHistoryDTO));
    }

    /**
     * 新增诊疗记录
     */
    @PostMapping("insert")
    public AjaxResult insertBusEhrCaseDiagnosisHistory(@RequestBody BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory){
        return  AjaxResult.success(iBusEhrCaseDiagnosisHistoryService.insertBusEhrCaseDiagnosisHistory(busEhrCaseDiagnosisHistory));
    }

    /**
     * 更新诊疗记录
     */
    @PutMapping("update")
    public AjaxResult updateBusEhrCaseDiagnosisHistoryById(@RequestBody BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory){
        return  toAjax(iBusEhrCaseDiagnosisHistoryService.updateBusEhrCaseDiagnosisHistoryById(busEhrCaseDiagnosisHistory));
    }

    /**
     * 诊疗记录详情
     */
    @GetMapping("{id}")
    public AjaxResult getBusEhrCaseDiagnosisHistoryById(@PathVariable("id") Long id){
        return  AjaxResult.success(iBusEhrCaseDiagnosisHistoryService.getBusEhrCaseDiagnosisHistoryById(id));
    }

    /**
     * 诊疗记录详情
     *  通过 consultationId 查询诊疗记录详情
     * 支持 doctorId、familyId、idCardNo、source、hospitalId、familyPhone、id 查询
     * @param busEhrCaseDiagnosisHistory 健康档案-病例信息-诊疗记录
     * @return {@link AjaxResult }
     */
    @PostMapping("feign/info")
    public AjaxResult getBusEhrCaseDiagnosisHistoryInfo(@RequestBody BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory){
        return  AjaxResult.success(iBusEhrCaseDiagnosisHistoryService.getInfo(busEhrCaseDiagnosisHistory), "操作成功");
    }

    /**
     *  根据问诊单id查询医生创建的就诊记录
     * @param consultationIds   问诊单id
     * @return  病例id
     */
    @GetMapping("/feign/doctor-create")
    public R<List<ConsultationDiagnosisVO>> getDoctorCreateDiagnosisHistoryByConsultationIds(@RequestParam("consultationIds")List<Long> consultationIds) {
        return R.ok(iBusEhrCaseDiagnosisHistoryService.getDoctorCreateDiagnosisHistoryByConsultationIds(consultationIds));
    }

    /**
     *  获取问诊单已完成的诊疗记录
     * @param busEhrCaseDiagnosisHistoryDTO 入参
     * @return 列表-就诊时间降序
     */
    @GetMapping("/consultation-completed")
    public TableDataInfo getCompletedDiagnosisHistory(BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO) {
        SMSLoginUser loginUser = loginUserHelper.getLoginUser();
        boolean checkUserRole = iBusEhrCaseDiagnosisHistoryService.checkUserRoleByIdCardNo(loginUser,busEhrCaseDiagnosisHistoryDTO.getIdCardNo());
        // 校验没通过 - 直接返回空列表
        if (!checkUserRole){
            return getDataTable(Collections.emptyList());
        }
        // 不管医生端还是患者端 - 限制只能查找当前医院下的患者诊疗记录 - 前端说请求头必传的 hospitalId
        busEhrCaseDiagnosisHistoryDTO.setHospitalId(SecurityUtils.getHospitalId());
        busEhrCaseDiagnosisHistoryDTO.setConsultationStatus("4");
        startPage();
        return getDataTable(iBusEhrCaseDiagnosisHistoryService.queryDiagnosisHistoryList(busEhrCaseDiagnosisHistoryDTO));
    }

}
package com.puree.hospital.ehr.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.RemoteConsultationOrderService;
import com.puree.hospital.app.api.RemoteDoctorService;
import com.puree.hospital.app.api.RemotePatientFamilyService;
import com.puree.hospital.app.api.RemoteSignatureService;
import com.puree.hospital.app.api.model.BusConsultationOrderDTO;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.app.api.model.SMSLoginUser;
import com.puree.hospital.app.api.model.vo.BusSignatureVO;
import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospital;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderStatusEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;
import com.puree.hospital.common.core.enums.EhrSourceEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.ehr.api.model.ConsultationDiagnosisVO;
import com.puree.hospital.ehr.api.model.enums.DiagnosisCreateModeEnum;
import com.puree.hospital.ehr.domain.BusEhrCaseDiagnosisHistory;
import com.puree.hospital.ehr.domain.BusPatientFamily;
import com.puree.hospital.ehr.domain.dto.BusEhrCaseDiagnosisHistoryDTO;
import com.puree.hospital.ehr.domain.vo.BusEhrCaseDiagnosisHistoryVO;
import com.puree.hospital.ehr.mapper.BusEhrCaseDiagnosisHistoryMapper;
import com.puree.hospital.ehr.mapper.BusPatientFamilyMapper;
import com.puree.hospital.ehr.service.IBusEhrCaseDiagnosisHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 健康档案-检查报告-诊疗记录
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusEhrCaseDiagnosisHistoryServiceImpl implements IBusEhrCaseDiagnosisHistoryService {
    private final BusEhrCaseDiagnosisHistoryMapper busEhrCaseDiagnosisHistoryMapper;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final RemotePatientFamilyService remotePatientFamilyService;
    private final RemoteSignatureService remoteSignatureService;
    private final RemoteHospitalService remoteHospitalService;
    private final RemoteConsultationOrderService remoteConsultationOrderService;
    private final RemoteDoctorService remoteDoctorService;

    @Override
    public List<BusEhrCaseDiagnosisHistoryVO> listBusEhrCaseDiagnosisHistory(BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO) {
        List<BusEhrCaseDiagnosisHistory> busEhrCaseDiagnosisHistories = busEhrCaseDiagnosisHistoryMapper.selectList(
                new LambdaQueryWrapper<BusEhrCaseDiagnosisHistory>()
                        .eq(BusEhrCaseDiagnosisHistory::getIdCardNo, busEhrCaseDiagnosisHistoryDTO.getIdCardNo())
                        .eq(BusEhrCaseDiagnosisHistory::getHospitalId, busEhrCaseDiagnosisHistoryDTO.getHospitalId())
                        .eq(Objects.nonNull(busEhrCaseDiagnosisHistoryDTO.getSource()), BusEhrCaseDiagnosisHistory::getSource, busEhrCaseDiagnosisHistoryDTO.getSource())
                        .eq(Objects.nonNull(busEhrCaseDiagnosisHistoryDTO.getCreateMode()), BusEhrCaseDiagnosisHistory::getCreateMode, busEhrCaseDiagnosisHistoryDTO.getCreateMode())
                        .orderByDesc(BusEhrCaseDiagnosisHistory::getCreateTime)
        );
        if (CollectionUtils.isNotEmpty(busEhrCaseDiagnosisHistories)) {
            List<Long> consultationIds = busEhrCaseDiagnosisHistories.stream().map(BusEhrCaseDiagnosisHistory::getConsultationId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, BusConsultationOrderDTO> consultationStatusMaps = queryConsultationOrderStatus(consultationIds);
            // 转换VO类 填充问诊订单状态
            return busEhrCaseDiagnosisHistories.stream().map(busEhrCaseDiagnosisHistory -> {
                BusEhrCaseDiagnosisHistoryVO busEhrCaseDiagnosisHistoryVO = new BusEhrCaseDiagnosisHistoryVO();
                BeanUtil.copyProperties(busEhrCaseDiagnosisHistory, busEhrCaseDiagnosisHistoryVO);
                BusConsultationOrderDTO orderDTO = consultationStatusMaps.getOrDefault(busEhrCaseDiagnosisHistory.getConsultationId(), new BusConsultationOrderDTO());
                busEhrCaseDiagnosisHistoryVO.setConsultationStatus(ConsultationOrderTypeEnum.isImageText(orderDTO.getOrderType()) ? orderDTO.getStatus() : orderDTO.getVideoStatus());
                return busEhrCaseDiagnosisHistoryVO;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     *  查询问诊单状态
     * @param consultationIds   问诊单ID
     * @return
     */
    private Map<Long, BusConsultationOrderDTO> queryConsultationOrderStatus(List<Long> consultationIds) {
        if (CollectionUtils.isEmpty(consultationIds)) {
            return Collections.emptyMap();
        }
        R<List<BusConsultationOrderDTO>> listR = remoteConsultationOrderService.feignQueryInfo(consultationIds);
        if (listR.isSuccess() && CollectionUtils.isNotEmpty(listR.getData())) {
            return listR.getData().stream().collect(Collectors.toMap(BusConsultationOrderDTO::getId, Function.identity()));
        }
        return Collections.emptyMap();
    }


    @Override
    public Long insertBusEhrCaseDiagnosisHistory(BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory) {
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(busEhrCaseDiagnosisHistory.getFamilyId());
        if (busEhrCaseDiagnosisHistory.getConsultationId() != null){
            LambdaQueryWrapper<BusEhrCaseDiagnosisHistory> wrapper = Wrappers.lambdaQuery(BusEhrCaseDiagnosisHistory.class)
                    .eq(BusEhrCaseDiagnosisHistory::getConsultationId, busEhrCaseDiagnosisHistory.getConsultationId())
                    .orderByDesc(BusEhrCaseDiagnosisHistory::getCreateTime);
            List<BusEhrCaseDiagnosisHistory> busEhrCaseDiagnosisHistories = busEhrCaseDiagnosisHistoryMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(busEhrCaseDiagnosisHistories)){
                return busEhrCaseDiagnosisHistories.get(0).getId();
            }
        }
        if (busPatientFamily != null){
            busEhrCaseDiagnosisHistory.setFamilyPhone(busPatientFamily.getCellPhoneNumber());
            busEhrCaseDiagnosisHistory.setIdCardNo(busPatientFamily.getIdNumber());
            busEhrCaseDiagnosisHistory.setAge(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",busPatientFamily.getDateOfBirth())));
            if ( StrUtil.isNotBlank(busPatientFamily.getSex()) && StrUtil.isNumeric(busPatientFamily.getSex())) {
                busEhrCaseDiagnosisHistory.setSex(Integer.valueOf(busPatientFamily.getSex()));
            }
            busEhrCaseDiagnosisHistory.setDateOfBirth(busPatientFamily.getDateOfBirth());
        }

        busEhrCaseDiagnosisHistory.setCreateBy(SecurityUtils.getUsername());
        busEhrCaseDiagnosisHistory.setCreateTime(DateUtils.getNowDate());
        busEhrCaseDiagnosisHistoryMapper.insert(busEhrCaseDiagnosisHistory);
        return  busEhrCaseDiagnosisHistory.getId();
    }

    @Override
    public int updateBusEhrCaseDiagnosisHistoryById(BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory) {
        // 修改验证问诊订单状态是否完成
        if (Objects.nonNull(busEhrCaseDiagnosisHistory.getConsultationId())) {
            Map<Long, BusConsultationOrderDTO> orderMap = queryConsultationOrderStatus(Collections.singletonList(busEhrCaseDiagnosisHistory.getConsultationId()));
            BusConsultationOrderDTO orderDTO = orderMap.getOrDefault(busEhrCaseDiagnosisHistory.getConsultationId(), new BusConsultationOrderDTO());
            String orderStatus = ConsultationOrderTypeEnum.isImageText(orderDTO.getOrderType()) ? orderDTO.getStatus() : orderDTO.getVideoStatus();
            if (!StrUtil.equalsAny(orderStatus, ConsultationOrderStatusEnum.VISITING.getCode(), ConsultationOrderStatusEnum.PRESCRIBED.getCode())) {
                throw new ServiceException("当前问诊单不在问诊中，不能经行修改");
            }
        }
        busEhrCaseDiagnosisHistory.setUpdateBy(SecurityUtils.getUsername());
        busEhrCaseDiagnosisHistory.setUpdateTime(DateUtils.getNowDate());
        return busEhrCaseDiagnosisHistoryMapper.updateById(busEhrCaseDiagnosisHistory);
    }

    @Override
    public BusEhrCaseDiagnosisHistoryVO getBusEhrCaseDiagnosisHistoryById(Long id) {
        // 查询详情
        BusEhrCaseDiagnosisHistoryVO busEhrCaseDiagnosisHistoryVO = new BusEhrCaseDiagnosisHistoryVO();
        BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory = busEhrCaseDiagnosisHistoryMapper.selectById(id);
        if (Objects.isNull(busEhrCaseDiagnosisHistory)) {
            return busEhrCaseDiagnosisHistoryVO;
        }
        BeanUtil.copyProperties(busEhrCaseDiagnosisHistory, busEhrCaseDiagnosisHistoryVO);
        // 如果数据来源是医生，查询签名
        if (Objects.equals(Integer.valueOf(EhrSourceEnum.DOCTOR.getCode()), busEhrCaseDiagnosisHistoryVO.getSource())) {
            R<BusSignatureVO> doctorSignature = remoteDoctorService.getDoctorSignature(busEhrCaseDiagnosisHistoryVO.getHospitalId(), busEhrCaseDiagnosisHistoryVO.getDoctorId());
            if (doctorSignature.isSuccess() && Objects.nonNull(doctorSignature.getData())) {
                busEhrCaseDiagnosisHistoryVO.setCertSignature(doctorSignature.getData().getCertSignature());
            }
        }
        // 医院信息
        R<BusHospital> hospitalInfo = remoteHospitalService.getHospitalInfo(busEhrCaseDiagnosisHistoryVO.getHospitalId());
        if (hospitalInfo.isSuccess() && Objects.nonNull(hospitalInfo.getData())) {
            busEhrCaseDiagnosisHistoryVO.setHospitalName(hospitalInfo.getData().getHospitalName());
        }
        // 问诊单ID不为空，填充问诊单状态
        if (Objects.nonNull(busEhrCaseDiagnosisHistoryVO.getConsultationId())) {
            Map<Long, BusConsultationOrderDTO> consultationStatusMaps = queryConsultationOrderStatus(Collections.singletonList(busEhrCaseDiagnosisHistoryVO.getConsultationId()));
            BusConsultationOrderDTO orderDTO = consultationStatusMaps.getOrDefault(busEhrCaseDiagnosisHistory.getConsultationId(), new BusConsultationOrderDTO());
            busEhrCaseDiagnosisHistoryVO.setConsultationStatus(ConsultationOrderTypeEnum.isImageText(orderDTO.getOrderType()) ? orderDTO.getStatus() : orderDTO.getVideoStatus());
        }
        return busEhrCaseDiagnosisHistoryVO;
    }

    /**
     * 查询诊疗记录
     *
     * @param busEhrCaseDiagnosisHistory
     * @return
     */
    @Override
    public List<BusEhrCaseDiagnosisHistory> queryCaseDiagnosisHistory(BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory) {
        LambdaQueryWrapper<BusEhrCaseDiagnosisHistory> lambdaQuery = Wrappers.lambdaQuery();
        // 如果未认证则用就诊人id查询
        if (StringUtils.isNotBlank(busEhrCaseDiagnosisHistory.getFamilyIdCard())){
            lambdaQuery.eq(BusEhrCaseDiagnosisHistory::getIdCardNo, busEhrCaseDiagnosisHistory.getFamilyIdCard());
        }else {
            lambdaQuery.eq(BusEhrCaseDiagnosisHistory::getFamilyId, busEhrCaseDiagnosisHistory.getFamilyId());
        }
        lambdaQuery.orderByDesc(BusEhrCaseDiagnosisHistory::getCreateTime);
        List<BusEhrCaseDiagnosisHistory> ehrCaseDiagnosisHistories = busEhrCaseDiagnosisHistoryMapper.selectList(lambdaQuery);
        ehrCaseDiagnosisHistories.forEach(c -> {
            String reportFilePath = c.getMedicalRecordFilePath();
            if (StringUtils.isNotEmpty(reportFilePath)) {
                String suffix = reportFilePath.substring(reportFilePath.lastIndexOf(".") + 1);
                if ("pdf".equals(suffix)) {
                    c.setFileType("PDF文件");
                } else {
                    c.setFileType("图片");
                }
            }
        });
        return ehrCaseDiagnosisHistories;
    }

    @Override
    public BusEhrCaseDiagnosisHistory getInfo(BusEhrCaseDiagnosisHistory busEhrCaseDiagnosisHistory) {
        Long doctorId = busEhrCaseDiagnosisHistory.getDoctorId();
        Long familyId = busEhrCaseDiagnosisHistory.getFamilyId();
        String idCardNo = busEhrCaseDiagnosisHistory.getIdCardNo();
        Integer source = busEhrCaseDiagnosisHistory.getSource();
        Long hospitalId = busEhrCaseDiagnosisHistory.getHospitalId();
        String familyPhone = busEhrCaseDiagnosisHistory.getFamilyPhone();
        Long consultationId = busEhrCaseDiagnosisHistory.getConsultationId();
        Long id = busEhrCaseDiagnosisHistory.getId();
        LambdaQueryWrapper<BusEhrCaseDiagnosisHistory> wrapper = Wrappers.lambdaQuery(BusEhrCaseDiagnosisHistory.class)
                .eq(doctorId != null, BusEhrCaseDiagnosisHistory::getDoctorId, doctorId)
                .eq(familyId != null, BusEhrCaseDiagnosisHistory::getFamilyId, familyId)
                .eq(StringUtils.isNotEmpty(idCardNo), BusEhrCaseDiagnosisHistory::getIdCardNo, idCardNo)
                .eq(source != null, BusEhrCaseDiagnosisHistory::getSource, source)
                .eq(hospitalId != null, BusEhrCaseDiagnosisHistory::getHospitalId, hospitalId)
                .eq(StringUtils.isNotEmpty(familyPhone), BusEhrCaseDiagnosisHistory::getFamilyPhone, familyPhone)
                .eq(BusEhrCaseDiagnosisHistory::getConsultationId, consultationId)
                .eq(id != null, BusEhrCaseDiagnosisHistory::getId, id)
                .orderByDesc(BusEhrCaseDiagnosisHistory::getCreateTime)
                .last("LIMIT 1").orderByDesc(BusEhrCaseDiagnosisHistory::getCreateTime);
        BusEhrCaseDiagnosisHistory history = busEhrCaseDiagnosisHistoryMapper.selectOne(wrapper);

        return history == null ? new BusEhrCaseDiagnosisHistory() : history;
    }
    /**
     * 根据用户的登录身份信息 - 检查查询的身份证号是否有权限查询该用户的诊疗记录
     * @param loginUser 登录用户
     * @param idCardNo 身份证号
     * @return 是否有权限 查询 - true:有权限 false:没有权限
     */
    @Override
    public boolean checkUserRoleByIdCardNo(SMSLoginUser loginUser, String idCardNo) {
        // 患者需要做限制 - 只能查询自己账号下的诊疗记录
        if (StringUtils.isEmpty(loginUser.getIdentity()) || AppRoleEnum.PATIENT.getCode().equals(loginUser.getIdentity())) {
            // 患者端 - 需要查询患者的身份证号是否和登录用户下的用户信息一致
            R<List<BusPatientFamilyVo>> listR = remotePatientFamilyService.queryPatientList(idCardNo, null);
            if (!listR.isSuccess()) {
                return false;
            }
            List<BusPatientFamilyVo> data = listR.getData();
            if (CollUtil.isEmpty(data)) {
                return false;
            }
            return data.stream().anyMatch(busPatientFamilyVo -> Objects.equals(loginUser.getUserid(), busPatientFamilyVo.getPatientId()));
        }
        return true;
    }

    /**
     *  根据问诊单id查询医生创建的就诊记录
     * @param consultationIds   问诊单id
     * @return  病例id
     */
    @Override
    public List<ConsultationDiagnosisVO> getDoctorCreateDiagnosisHistoryByConsultationIds(List<Long> consultationIds) {
        LambdaQueryWrapper<BusEhrCaseDiagnosisHistory> queryWrapper = Wrappers.lambdaQuery(BusEhrCaseDiagnosisHistory.class)
                .in(BusEhrCaseDiagnosisHistory::getConsultationId, consultationIds)
                .eq(BusEhrCaseDiagnosisHistory::getSource, EhrSourceEnum.DOCTOR.getCode())
                .eq(BusEhrCaseDiagnosisHistory::getCreateMode, DiagnosisCreateModeEnum.MANUALLY.getCode());
        List<BusEhrCaseDiagnosisHistory> busEhrCaseDiagnosisHistories = busEhrCaseDiagnosisHistoryMapper.selectList(queryWrapper);
        return busEhrCaseDiagnosisHistories.stream().map(item -> {
            ConsultationDiagnosisVO consultationDiagnosisVO = new ConsultationDiagnosisVO();
            consultationDiagnosisVO.setDiagnosisId(item.getId());
            consultationDiagnosisVO.setConsultationId(item.getConsultationId());
            return consultationDiagnosisVO;
        }).collect(Collectors.toList());
    }

    /**
     *  查询诊疗记录列表
     * @param busEhrCaseDiagnosisHistoryDTO 入参
     * @return 列表-就诊时间降序
     */
    @Override
    public List<BusEhrCaseDiagnosisHistoryVO> queryDiagnosisHistoryList(BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO) {
        return busEhrCaseDiagnosisHistoryMapper.selectDiagnosisHistoryList(busEhrCaseDiagnosisHistoryDTO);
    }
}
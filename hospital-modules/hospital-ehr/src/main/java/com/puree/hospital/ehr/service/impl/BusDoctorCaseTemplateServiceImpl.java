package com.puree.hospital.ehr.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.ehr.domain.BusDoctorCaseTemplate;
import com.puree.hospital.ehr.domain.dto.BusDoctorCaseTemplateCreateDTO;
import com.puree.hospital.ehr.domain.dto.BusDoctorCaseTemplateUpdateDTO;
import com.puree.hospital.ehr.mapper.BusDoctorCaseTemplateMapper;
import com.puree.hospital.ehr.service.IBusDoctorCaseTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 医生病历模板服务实现
 * <AUTHOR>
 * @date 2025-08-08
 */
@Slf4j
@Service
public class BusDoctorCaseTemplateServiceImpl implements IBusDoctorCaseTemplateService {

    @Resource
    private BusDoctorCaseTemplateMapper templateMapper;

    /**
     * 获取医生的模板列表
     * @param doctorId 医生ID
     * @param hospitalId 医院ID
     * @return 模板列表
     */
    @Override
    public List<BusDoctorCaseTemplate> getTemplateList(Long doctorId, Long hospitalId) {
        LambdaQueryWrapper<BusDoctorCaseTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusDoctorCaseTemplate::getDoctorId, doctorId)
                .eq(BusDoctorCaseTemplate::getHospitalId, hospitalId)
                .orderByDesc(BusDoctorCaseTemplate::getCreateTime);
        return templateMapper.selectList(wrapper);
    }

    /**
     * 根据ID获取模板详情 & 根据doctorId校验
     * @param templateId 模板ID
     * @param doctorId 医生ID
     * @return 模板详情
     */
    @Override
    public BusDoctorCaseTemplate getCheckedTemplate(Long templateId, Long doctorId) {
        BusDoctorCaseTemplate template = templateMapper.selectById(templateId);
        if (ObjectUtil.isNull(template)) {
            throw new ServiceException("模板不存在");
        }
        if (!template.getDoctorId().equals(doctorId)) {
            throw new ServiceException("无权限查看模板");
        }
        return template;
    }

    /**
     * 新增模板
     *
     * @param dto        模板数据
     * @param doctorId   医生ID
     * @param hospitalId 医院ID
     */
    @Override
    public void insertTemplate(BusDoctorCaseTemplateCreateDTO dto, Long doctorId, Long hospitalId) {
        // 校验诊断是否失效 todo-zlq
        // 创建插入对象 & 赋值
        BusDoctorCaseTemplate template = new BusDoctorCaseTemplate();
        BeanUtils.copyProperties(dto, template);
        template.setDoctorId(doctorId);
        template.setHospitalId(hospitalId);
        template.setUpdateTime(new Date());
        templateMapper.insert(template);
    }

    /**
     * 更新模板
     * @param dto      模板数据
     * @param doctorId 医生ID
     * @return 模板ID
     */
    @Override
    public int updateTemplate(BusDoctorCaseTemplateUpdateDTO dto, Long doctorId) {
        getCheckedTemplate(dto.getId(), doctorId);
        // 校验诊断是否失效 todo-zlq
        // 创建更新对象
        BusDoctorCaseTemplate template = new BusDoctorCaseTemplate();
        BeanUtils.copyProperties(dto, template);
        return templateMapper.updateById(template);
    }

    /**
     * 删除模板
     * @param templateId 模板ID
     * @param doctorId 医生ID
     * @return 模板ID
     */
    @Override
    public int deleteTemplate(Long templateId, Long doctorId) {
        getCheckedTemplate(templateId, doctorId);
        return templateMapper.deleteById(templateId);
    }


}

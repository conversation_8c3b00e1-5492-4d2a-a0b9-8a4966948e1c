package com.puree.hospital.ehr.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.ehr.domain.BusEhrCaseDiagnosisHistory;
import com.puree.hospital.ehr.domain.dto.BusEhrCaseDiagnosisHistoryDTO;
import com.puree.hospital.ehr.domain.vo.BusEhrCaseDiagnosisHistoryVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BusEhrCaseDiagnosisHistoryMapper extends BaseMapper<BusEhrCaseDiagnosisHistory> {

    /**
     *  查询诊疗记录列表
     * @param busEhrCaseDiagnosisHistoryDTO 入参
     * @return 列表-就诊时间降序
     */
    List<BusEhrCaseDiagnosisHistoryVO> selectDiagnosisHistoryList(BusEhrCaseDiagnosisHistoryDTO busEhrCaseDiagnosisHistoryDTO);
}
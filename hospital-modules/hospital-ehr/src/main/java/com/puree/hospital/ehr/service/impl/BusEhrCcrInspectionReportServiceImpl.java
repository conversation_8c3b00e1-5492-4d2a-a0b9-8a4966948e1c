package com.puree.hospital.ehr.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.common.core.enums.CheckOrganizationEnum;
import com.puree.hospital.common.api.enums.EhrCheckItemEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.mybatis.SecureAesDbUtil;
import com.puree.hospital.common.core.text.UUID;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.common.pdf.converter.HtmlToPdfConverter;
import com.puree.hospital.ehr.domain.BusEhrCcrInspectionReport;
import com.puree.hospital.ehr.domain.BusEhrCheck;
import com.puree.hospital.ehr.domain.BusEhrCheckData;
import com.puree.hospital.ehr.domain.BusEhrReadRecord;
import com.puree.hospital.ehr.domain.BusPatientFamily;
import com.puree.hospital.ehr.domain.UploadPhysicalEcgInput;
import com.puree.hospital.ehr.domain.dto.BusEhrCcrInspectionReportDTO;
import com.puree.hospital.ehr.domain.vo.BusEhrCcrInspectionReportVO;
import com.puree.hospital.ehr.mapper.BusEhrCcrInspectionReportMapper;
import com.puree.hospital.ehr.mapper.BusEhrCheckDataMapper;
import com.puree.hospital.ehr.mapper.BusEhrReadRecordMapper;
import com.puree.hospital.ehr.mapper.BusPatientFamilyMapper;
import com.puree.hospital.ehr.mapper.XkPhysicalEcgMapper;
import com.puree.hospital.ehr.service.IBusEhrCcrInspectionReportService;
import com.puree.hospital.ehr.service.IBusEhrReportService;
import com.puree.hospital.followup.api.RemoteReportFileRecordService;
import com.puree.hospital.followup.api.model.RecordFileOperationType;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import com.puree.hospital.followup.api.model.ReportFileRecordDTO;
import com.puree.hospital.tool.api.RemoteOssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022/12/19 11:54
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusEhrCcrInspectionReportServiceImpl implements IBusEhrCcrInspectionReportService {
    private final BusEhrCcrInspectionReportMapper busEhrCcrInspectionReportMapper;
    private final TemplateEngine templateEngine;
    private final IBusEhrReportService reportService;
    private final RemoteOssService remoteOssService;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final XkPhysicalEcgMapper xkPhysicalEcgMapper;
    private final OSSUtil ossUtil;
    private final BusEhrReadRecordMapper readRecordMapper;
    private final BusEhrCheckDataMapper checkDataMapper;
    private final RemoteReportFileRecordService remoteReportFileRecordService;

    @Autowired
    private HtmlToPdfConverter pdfConverter;

    @Autowired
    private DiskFileItemFactory cleanableFileItemFactory;

    @SuppressWarnings("unused")
    @Value("${report.logo}")
    private String logo;

    /**
     * 查询体检报告列表
     */
    @Override
    public List<BusEhrCcrInspectionReport> getInspectionList(BusEhrCcrInspectionReportDTO dto) {
        LambdaQueryWrapper<BusEhrCcrInspectionReport> lambdaQuery = Wrappers.lambdaQuery(BusEhrCcrInspectionReport.class);
        lambdaQuery.eq(BusEhrCcrInspectionReport::getIdCardNo, SecureAesDbUtil.encryptHex(dto.getIdCardNo()));
        lambdaQuery.orderByDesc(BusEhrCcrInspectionReport::getCreateTime);
        return busEhrCcrInspectionReportMapper.selectList(lambdaQuery);
    }

    /**
     * 查询报告详情
     *
     * @param id 报告ID
     * @param source 报告来源哪家机构
     * @return PDF报告文件URL
     */
    @Override
    public String downloadReport(Long id, Integer source) {
        // 查询报告详细和基本信息
        List<BusEhrCheck> busEhrChecks = reportService.getDetailOfList(id, source);
        if (busEhrChecks == null) {
            throw new ServiceException("报告数据不全");
        }

        Context context = new Context();
        context.setVariable("logo", logo);
        context.setVariable("report", busEhrChecks);
        context.setVariable("basicInfo", getPatientInspectionReportVO(id, source));
        queryOrganizationRelatedVariables(id, source+"", context);
        context.setVariable("exceptionSummary", reportService.getExceptionSummary(id, source));
        String htmlString = templateEngine.process("healthReport", context);
        try {
            String url = uploadPdf(htmlString);
            if (StrUtil.isEmpty(url)) {
                throw new ServiceException("PDF文件生成失败");
            }
            /*更新文件路径到指定报告*/
            BusEhrCcrInspectionReport update = new BusEhrCcrInspectionReport();
            update.setReportFilePath(url);
            busEhrCcrInspectionReportMapper.update(update, new LambdaQueryWrapper<BusEhrCcrInspectionReport>()
                    .eq(BusEhrCcrInspectionReport::getPeInstitutionId, id)
                    .eq(BusEhrCcrInspectionReport::getSource, source));
            return url;
        } catch (IOException ex) {
            log.error("文件预览和下载ex=", ex);
            throw new ServiceException("PDF文件生成失败");
        }
    }

    private @NotNull BusEhrCcrInspectionReportVO getPatientInspectionReportVO(Long id, Integer source) {
        BusEhrCcrInspectionReport report = busEhrCcrInspectionReportMapper.selectOne(new LambdaQueryWrapper<BusEhrCcrInspectionReport>()
                .eq(BusEhrCcrInspectionReport::getSource, source)
                .eq(BusEhrCcrInspectionReport::getPeInstitutionId, id));
        if (report == null) {
            throw new ServiceException("检测报告记录不存在：" + id);
        }

        // 查询就诊人信息
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectOne(new LambdaQueryWrapper<BusPatientFamily>()
                .eq(BusPatientFamily::getIdNumber, report.getIdCardNo())
                .orderByAsc(BusPatientFamily::getCreateTime).last("limit 1"));
        if (busPatientFamily == null) {
            throw new ServiceException("就诊人为空：" + report.getIdCardNo());
        }

        BusEhrCcrInspectionReportVO reportVo = new BusEhrCcrInspectionReportVO();
        reportVo.setInspectionDate(report.getCreateTime());
        reportVo.setName(busPatientFamily.getName());
        reportVo.setSex(Integer.parseInt(busPatientFamily.getSex()));
        reportVo.setName(busPatientFamily.getName());
        reportVo.setContactNumber(busPatientFamily.getCellPhoneNumber());
        reportVo.setIdNumber(busPatientFamily.getIdNumber());
        reportVo.setInspectionNo(busPatientFamily.getOutpatientNumber());
        String age = DateUtils.dateTime(busPatientFamily.getDateOfBirth());
        reportVo.setAge(AgeUtil.getAgeDetail(age));
        return reportVo;
    }


    /**
     * 查询机构相关变量
     */
    private void queryOrganizationRelatedVariables(Long id, String source, Context context) {
        // 携康，要显示ECG
        if (CheckOrganizationEnum.XK.getCode().equals(source)) {
            UploadPhysicalEcgInput uploadPhysicalEcgInput = xkPhysicalEcgMapper.selectOne(new LambdaQueryWrapper<UploadPhysicalEcgInput>()
                    .eq(UploadPhysicalEcgInput::getQkId, id).last("limit 1"));
            if (uploadPhysicalEcgInput != null) {
                String ecg = uploadPhysicalEcgInput.getEcg();
                String ecgResult = uploadPhysicalEcgInput.getEcgResult();
                context.setVariable("ecg", ossUtil.getFullPath(ecg));
                context.setVariable("ecgResult", ecgResult);
            }
            // 双佳、标准版A，要显示心电图
        } else if (CheckOrganizationEnum.SONKA.getCode().equals(source) ||
                CheckOrganizationEnum.EHOME.getCode().equals(source)) {
            // 查询心电图数据
            BusEhrCheckData ecgData = checkDataMapper.selectOne(new LambdaQueryWrapper<BusEhrCheckData>()
                    .eq(BusEhrCheckData::getCheckItemCode, EhrCheckItemEnum.ECG_002.getKey())
                    .eq(BusEhrCheckData::getCrId, id));
            if (ecgData != null) {
                context.setVariable("ecg", ossUtil.getFullPath(ecgData.getCheckValue()));
                context.setVariable("ecgResult", ecgData.getCheckResult());
            }
        }
    }


    /**
     * 将HTML字符串转换为PDF文件，并上传报告
     *
     * @return PDF文件URL
     */
    private String uploadPdf(String htmlString) throws IOException {
        String fileName = UUID.randomUUID() + ".pdf";
        FileItem fileItem = cleanableFileItemFactory.createItem("file", MediaType.APPLICATION_PDF_VALUE, true, fileName);
        pdfConverter.convert(htmlString, fileItem.getOutputStream());
        MultipartFile multi = new CommonsMultipartFile(fileItem);
        Map<String, Object> result = remoteOssService.upload(multi, 1020);
        assert result != null;
        if (!result.get("code").equals(200)) {
            throw new ServiceException("上传PDF文件失败：" + result.get("msg"));
        }

        return result.get("msg").toString();

    }


    @Override
    public BusEhrCcrInspectionReport selectInspectionReport(Long id, Integer source) {
        return busEhrCcrInspectionReportMapper.selectOne(new LambdaQueryWrapper<BusEhrCcrInspectionReport>()
                .eq(BusEhrCcrInspectionReport::getSource, source)
                .eq(BusEhrCcrInspectionReport::getPeInstitutionId, id));
    }

    @Override
    public Boolean haveUnread(String idCard, Integer role) {
        LambdaQueryWrapper<BusEhrReadRecord> query = Wrappers.lambdaQuery();
        query.eq(BusEhrReadRecord::getIdCard, idCard);
        if (YesNoEnum.NO.getCode().equals(role)) {
            query.eq(BusEhrReadRecord::getSelfRead, false);
        } else {
            query.eq(BusEhrReadRecord::getDoctorRead, false);
        }
        return readRecordMapper.selectCount(query) > 0;
    }

    @Override
    public int updateRead(String idCard, Integer role) {
        BusEhrReadRecord report = new BusEhrReadRecord();
        if (YesNoEnum.NO.getCode().equals(role)) {
            report.setSelfRead(Boolean.TRUE);
        } else {
            report.setDoctorRead(Boolean.TRUE);
        }
        readRecordMapper.update(report, new LambdaQueryWrapper<BusEhrReadRecord>()
                .eq(BusEhrReadRecord::getIdCard, idCard));
        return 1;
    }

    /**
     * 查询体检报告
     *
     * @param busEhrCcrInspectionReport report
     * @return report list
     */
    @Override
    public List<BusEhrCcrInspectionReport> queryCcrInspectionReport(BusEhrCcrInspectionReport busEhrCcrInspectionReport) {
        LambdaQueryWrapper<BusEhrCcrInspectionReport> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.orderByDesc(BusEhrCcrInspectionReport::getCreateTime);
        lambdaQuery.eq(BusEhrCcrInspectionReport::getIdCardNo, SecureAesDbUtil.encryptHex(busEhrCcrInspectionReport.getFamilyIdCard()));

        List<BusEhrCcrInspectionReport> busEhrCcrInspectionReports = busEhrCcrInspectionReportMapper.selectList(lambdaQuery);
        if (CollectionUtil.isEmpty(busEhrCcrInspectionReports)) {
            return new ArrayList<>();
        }

        busEhrCcrInspectionReports.forEach(r -> {
            r.setFileType("PDF文件");
            r.setCheckTime(r.getCreateTime());
        });
        return busEhrCcrInspectionReports;
    }

    @Override
    public BusEhrCcrInspectionReport getExitReport(String idCards, Date checkTime, String institution) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String checkDate = format.format(checkTime);
        return busEhrCcrInspectionReportMapper.getExitReport(idCards, checkDate, institution);
    }

    @Async
    public void dataSync() {
        log.info("开始同步体检报告");
        LambdaQueryWrapper<BusEhrCcrInspectionReport> wrapper = Wrappers.lambdaQuery();
        List<BusEhrCcrInspectionReport> reports = busEhrCcrInspectionReportMapper.selectList(wrapper);
        log.info("待同步体检报告数量：{}", reports.size());
        for (BusEhrCcrInspectionReport report : reports) {
            try {
                String reportFilePath = report.getReportFilePath();
                if (StrUtil.isEmpty(reportFilePath)) {
                    continue;
                }
                ReportFileRecordDTO record = getReportFileRecordDTO(report);
                remoteReportFileRecordService.insert(record);
            } catch (Exception e) {
                log.error("同步体检报告失败", e);
            }

        }
        log.info("同步体检报告完成");
    }

    private static @NotNull ReportFileRecordDTO getReportFileRecordDTO(BusEhrCcrInspectionReport report) {
        ReportFileRecordDTO record = new ReportFileRecordDTO();

        record.setSource(RecordSourceEnum.BACKGROUND_ADD);
        record.setReportFile(report.getReportFilePath());
        record.setReportFileName(report.getReportFileName());
        record.setOrgName(report.getPeInstitutionName());
        record.setCheckTime(report.getCreateTime());
        record.setPatientIdNumber(report.getIdCardNo());
        record.setCreateTime(report.getCreateTime());
        record.setHospitalId(report.getHospitalId());
        record.setOperationType(RecordFileOperationType.DOWNLOAD_DOCUMENT);
        return record;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.ehr.mapper.BusEhrCaseDiagnosisHistoryMapper">
	<resultMap id="BaseResultMap" type="BusEhrCaseDiagnosisHistory">
            <result column="age" property="age" />
	        <result column="chief_complaint" property="chiefComplaint" />
	        <result column="consultation_time" property="consultationTime" />
	        <result column="create_by" property="createBy" />
	        <result column="create_time" property="createTime" />
	        <result column="diagnosis" property="diagnosis" />
	        <result column="doctor_id" property="doctorId" />
	        <result column="family_id" property="familyId" />
	        <result column="full_name" property="fullName" />
	        <result column="historyOfPresentIllness" property="historyOfPresentIllness" />
	        <result column="id" property="id" />
	        <result column="id_card_no" property="idCardNo" />
	        <result column="management" property="management" />
	        <result column="medical_record_file_path" property="medicalRecordFilePath" />
	        <result column="pastHistory" property="pastHistory" />
	        <result column="personalHistory" property="personalHistory" />
	        <result column="source" property="source" />
	        <result column="update_by" property="updateBy" />
	        <result column="update_time" property="updateTime" />
            <result column="family_phone" property="familyPhone" />
            <result column="sex" property="sex" />
            <result column="disease_name" property="diseaseName" />
		</resultMap>  
    
    <!-- 表字段 -->
    <sql id="baseColumns">
         t.age
        , t.chief_complaint
        , t.consultation_time
        , t.create_by
        , t.create_time
        , t.diagnosis
        , t.doctor_id
        , t.family_id
        , t.full_name
        , t.historyOfPresentIllness
        , t.id
        , t.id_card_no
        , t.management
        , t.medical_record_file_path
        , t.pastHistory
        , t.personalHistory
        , t.source
        , t.update_by
        , t.update_time
        , t.family_phone
        , t.sex
        , t.disease_name
        </sql>

    <select id="selectDiagnosisHistoryList" resultType="com.puree.hospital.ehr.domain.vo.BusEhrCaseDiagnosisHistoryVO">
        SELECT
            bec.id,
            bec.full_name,
            bec.family_id,
            bec.consultation_id,
            bec.doctor_id,
            bec.age,
            bec.consultation_time,
            bec.diagnosis,
            bec.id_card_no,
            bec.doctor_id,
            bec.source,
            bec.hospital_id,
            bec.sex,
            bec.family_phone,
            bec.disease_name,
            bec.consultation_id,
            bec.tcm_diagnosis,
            bec.date_of_birth,
            bec.create_mode,
            bd.full_name AS doctorName,
            bec.create_time,
            bec.update_time
        FROM
            bus_ehr_case_diagnosis_history bec
            LEFT JOIN bus_doctor bd ON bec.doctor_id = bd.id
            <if test="consultationStatus != null and consultationStatus != ''">
                LEFT JOIN bus_consultation_order bco ON bec.consultation_id = bco.id
            </if>
        WHERE
            bec.hospital_id = #{hospitalId}
                <if test="consultationStatus != null and consultationStatus != ''">
                    AND (bco.`status` = #{consultationStatus} or bco.video_status = #{consultationStatus})
                </if>
                <if test="familyId != null">
                    AND bec.family_id = #{familyId}
                </if>
                <if test="idCardNo != null and idCardNo != ''">
                    AND bec.id_card_no = #{idCardNo}
                </if>
                <if test="source != null">
                    AND bec.source = #{source}
                </if>
                <if test="createMode != null">
                    AND bec.create_mode = #{createMode}
                </if>
        ORDER BY
            bec.id DESC
    </select>


</mapper>
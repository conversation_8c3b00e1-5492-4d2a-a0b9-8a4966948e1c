<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.order.mapper.BusOrderMapper">
    <resultMap id="busOrderVOMap" type="BusOrderVO">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_time" property="orderTime"/>
        <result column="agent_name" property="agentName"/>
        <result column="coo_organization" property="cooOrganization"/>
        <result column="cooperation_channels" property="cooperationChannels"/>
        <result column="addressee_name" property="addresseeName"/>
        <result column="addressee_phone" property="addresseePhone"/>
        <result column="delivery_way" property="deliveryWay"/>
        <result column="pay_way" property="payWay"/>
        <result column="status" property="status"/>
        <collection property="subOrders" ofType="BusSubOrderVO">
            <id column="sub_order_id" property="subOrderId"/>
            <result column="sub_order_no" property="subOrderNo"/>
            <result column="prescription_id" property="rxId"/>
            <result column="amount" property="amount"/>
            <result column="order_classify" property="orderClassify"/>
            <result column="doctor_name" property="doctorName"/>
            <result column="pa_name" property="paName"/>
            <result column="pa_type" property="paType"/>
            <result column="pa_amount" property="paAmount"/>
            <collection property="orderItems" ofType="BusOrderItemVO">
                <id column="item_id" property="itemId"/>
                <result column="item_name" property="itemName"/>
                <result column="item_price" property="itemPrice"/>
                <result column="item_spec" property="itemSpec"/>
                <result column="item_quantity" property="itemQuantity"/>
                <result column="item_img" property="itemImg"/>
                <result column="item_weight" property="itemWeight"/>
            </collection>
        </collection>
    </resultMap>


    <resultMap id="busOrderVOMap2" type="BusOrderVO">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_time" property="orderTime"/>
        <result column="list_type" property="listType"/>
        <result column="invoice_status" property="invoiceStatus"/>
        <collection property="orders" ofType="BusOrder" column="orderNo=order_no" select="selectSubOrderList"></collection>
    </resultMap>

    <select id="selectSubOrderList" resultType="BusOrder">
        SELECT t1.* FROM bus_order t1 WHERE t1.order_no = #{orderNo}
    </select>

    <select id="selectOrderList" parameterType="String" resultType="BusOrder">
        SELECT
            t1.*,
            t2.order_no drugs_order_no,
            t3.order_no goods_order_no
        FROM
            bus_order t1
            LEFT JOIN bus_drugs_order t2 ON t1.sub_order_id = t2.id
            AND t1.sub_order_type = '0'
            LEFT JOIN bus_shop_order t3 ON t1.sub_order_id = t3.id
            AND t1.sub_order_type = '1'
        WHERE
            t1.order_no = #{orderNo}
    </select>
    <select id="getBusChannelPartner" resultType="ChannelPartnerVO">
        SELECT b2.full_name as channelName ,b3.full_name as agentName ,b.name as userName,b.phone_number as userTel FROM bus_patient  b
        INNER JOIN   bus_channel_patient_agent_relation b1  ON b.id = b1.patient_id  AND b1.hospital_id = #{hospitalId}
        LEFT JOIN  bus_channel_partner_agent b2 ON  b1.agent_id = b2.id
        LEFT JOIN  bus_channel_partner b3 ON b3.id = b2.channel_partner_id
        WHERE b1.patient_id = #{patientId}
    </select>
    <sql id="listBusSubOrderSQL" >
        SELECT * FROM
        (SELECT
        t1.id AS id,
        t1.order_no AS  order_no ,
        t1.order_time AS order_time,
        "0" AS  list_type,
        t1.invoice_status as invoice_status
        FROM 	 bus_order t1
        LEFT JOIN  bus_drugs_order t2  ON  t1.sub_order_id  =  t2.id
        LEFT JOIN  bus_partners t3 ON t3.`code` = t1.partners_code
        LEFT JOIN  bus_patient t4 ON t4.id = t1.patient_id
        LEFT JOIN  bus_prescription t5 ON t5.id = t2.prescription_id
        LEFT JOIN  bus_prescription_drugs t6 ON t5.id = t6.prescription_id
        LEFT JOIN  bus_channel_patient_agent_relation t7 ON t7.patient_id = t4.id AND t7.hospital_id = t1.hospital_id
        LEFT JOIN  bus_channel_partner_agent t8 ON t8.id = t7.agent_id
        LEFT JOIN  bus_channel_partner t9 ON t9.id = t8.channel_partner_id
        LEFT JOIN  bus_drug_order_package t10 ON t10.drugs_order_id = t1.sub_order_id AND t10.package_type = t1.sub_order_type
        WHERE  t1.hospital_id = #{hospitalId}  AND t1.sub_order_type = 0 AND  t5.pa_id IS  NULL  AND t2.prescription_id IS NOT NULL
        <if test="orderNo != null and orderNo != '' ">
            AND t1.order_no = #{orderNo}
        </if>
        <if test="addresseeName != null and addresseeName != '' ">
            AND t1.receiver = #{addresseeName}
        </if>
        <if test="addresseePhone != null and addresseePhone != '' ">
            AND t1.receive_phone = #{addresseePhone}
        </if>
        <if test="deliveryType != null ">
            AND t1.delivery_type = #{deliveryType}
        </if>
        <if test="cooOrganization != null and cooOrganization != '' ">
            AND t3.full_name  like concat("%",replace(#{cooOrganization}," ",""),"%")
        </if>
        <if test="status  != null  and status  == 0  ">
            AND t1.order_status in (0, 7)
        </if>
        <if test="status  != null  and status  == 1  ">
            AND t1.order_status = 1
        </if>
        <if test="status  != null  and status  == 2  ">
            AND (t1.order_status = 2 OR t1.order_status = 3)
        </if>
        <if test="status  != null  and status  == 3  ">
            AND t1.sub_order_id IN (SELECT order_id FROM bus_order_after_sales WHERE after_sales_type = 0 AND hospital_id =#{hospitalId})
        </if>
        <if test="status  != null  and status  == 4  ">
            AND t1.order_status in (5,6)
        </if>
        <if test="status  != null  and status  == 5  ">
            AND t1.order_status = 4
        </if>
        <if test=" timeType = 1 and orderTimeStart != null and orderTimeEnd != null ">
            AND t1.order_time &gt;= #{orderTimeStart} AND t1.order_time &lt;= #{orderTimeEnd}
        </if>
        <if test=" timeType = 2 and paymentTimeStart != null and paymentTimeEnd != null ">
            AND t1.payment_time &gt;= #{paymentTimeStart} AND t1.payment_time &lt;= #{paymentTimeEnd}
        </if>
        <if test=" timeType = 3 and completeTimeStart != null and completeTimeEnd != null ">
            AND t1.complete_time &gt;= #{completeTimeStart} AND t1.complete_time &lt;= #{completeTimeEnd}
        </if>
        <if test="cooperationChannels != null and cooperationChannels != '' ">
            AND t9.full_name  like concat("%",replace(#{cooperationChannels}," ",""),"%")
        </if>
        <if test="code != null and code != '' ">
            AND t10.code = #{code}
        </if>
        <if test="deliveryNo != null and deliveryNo != '' ">
            AND t10.delivery_no = #{deliveryNo}
        </if>
        <if test="drugShopName != null and drugShopName != '' ">
            AND t6.drugs_name like concat("%",replace(#{drugShopName}," ",""),"%")
        </if>
        <if test="partnersCode != null and partnersCode != ''">
            AND t1.partners_code = #{partnersCode}
        </if>
        <if test=" invoiceStatus!=null ">
            AND t1.invoice_status = #{invoiceStatus}
        </if>
        UNION ALL
        SELECT
        t1.id AS id,
        t1.order_no AS  order_no ,
        t1.order_time AS order_time,
        "1" AS  list_type,
        t1.invoice_status as invoice_status
        FROM bus_order t1
        LEFT JOIN  bus_drugs_order t2  ON  t1.sub_order_id  =  t2.id
        LEFT JOIN  bus_partners t3 ON t3.code = t1.partners_code
        LEFT JOIN  bus_patient t4 ON t4.id = t1.patient_id
        LEFT JOIN  bus_prescription t5 ON t5.id = t2.prescription_id
        LEFT JOIN  bus_hospital_pa t9 ON t9.id = t5.pa_id
        LEFT JOIN  bus_hospital_pa_drugs t6 ON t9.id= t6.pa_id
        LEFT JOIN  bus_directory_drugs t7 ON t7.id = t6.directory_drugs_id
        LEFT JOIN  bus_drugs t8 ON t8.id = t7.drugs_id
        LEFT JOIN  bus_channel_patient_agent_relation t10 ON t10.patient_id = t4.id AND t10.hospital_id = t1.hospital_id
        LEFT JOIN  bus_channel_partner_agent t11 ON t11.id = t10.agent_id
        LEFT JOIN  bus_channel_partner t12 ON t12.id = t11.channel_partner_id
        LEFT JOIN  bus_drug_order_package t13 ON t13.drugs_order_id = t1.sub_order_id AND t13.package_type = t1.sub_order_type
        WHERE  t1.hospital_id = #{hospitalId}  AND t2.prescription_id IS NOT NULL  AND t5.pa_id IS NOT NULL  AND t1.sub_order_type = 0  AND t2.order_no IS NOT NULL
        <if test="orderNo != null and orderNo != '' ">
            AND t1.order_no = #{orderNo}
        </if>
        <if test="addresseeName != null and addresseeName != '' ">
            AND t1.receiver = #{addresseeName}
        </if>
        <if test="addresseePhone != null and addresseePhone != '' ">
            AND t1.receive_phone = #{addresseePhone}
        </if>
        <if test="deliveryType != null ">
            AND t1.delivery_type = #{deliveryType}
        </if>
        <if test="cooOrganization != null and cooOrganization != '' ">
            AND t3.full_name  like concat("%",replace(#{cooOrganization}," ",""),"%")
        </if>
        <if test="status  != null  and status  == 0  ">
            AND t1.order_status in (0, 7)
        </if>
        <if test="status  != null  and status  == 1  ">
            AND t1.order_status = 1
        </if>
        <if test="status  != null  and status  == 2  ">
            AND (t1.order_status = 2 OR t1.order_status = 3)
        </if>
        <if test="status  != null  and status  == 3  ">
            AND t1.sub_order_id IN (SELECT order_id FROM bus_order_after_sales WHERE after_sales_type = 0 AND hospital_id =#{hospitalId})
        </if>
        <if test="status  != null  and status  == 4  ">
            AND t1.order_status in (5,6)
        </if>
        <if test="status  != null  and status  == 5  ">
            AND t1.order_status = 4
        </if>
        <if test=" timeType = 1 and orderTimeStart != null and orderTimeEnd != null ">
            AND t1.order_time &gt;= #{orderTimeStart} AND t1.order_time &lt;= #{orderTimeEnd}
        </if>
        <if test=" timeType = 2 and paymentTimeStart != null and paymentTimeEnd != null ">
            AND t1.payment_time &gt;= #{paymentTimeStart} AND t1.payment_time &lt;= #{paymentTimeEnd}
        </if>
        <if test=" timeType = 3 and completeTimeStart != null and completeTimeEnd != null ">
            AND t1.complete_time &gt;= #{completeTimeStart} AND t1.complete_time &lt;= #{completeTimeEnd}
        </if>
        <if test="cooperationChannels != null and cooperationChannels != '' ">
            AND t12.full_name  like concat("%",replace(#{cooperationChannels}," ",""),"%")
        </if>
        <if test="code != null and code != '' ">
            AND t13.code = #{code}
        </if>
        <if test="deliveryNo != null and deliveryNo != '' ">
            AND t13.delivery_no = #{deliveryNo}
        </if>
        <if test="drugShopName != null and drugShopName != '' ">
            AND t8.drugs_name like concat("%",replace(#{drugShopName}," ",""),"%")
        </if>
        <if test="partnersCode != null and partnersCode != ''">
            AND t1.partners_code = #{partnersCode}
        </if>
        <if test=" invoiceStatus!=null ">
            AND t1.invoice_status = #{invoiceStatus}
        </if>
        UNION ALL
        SELECT
        t1.id AS id,
        t1.order_no AS  order_no ,
        t1.order_time AS order_time,
        "3" AS  list_type,
        t1.invoice_status as invoiceStatus
        FROM bus_order t1
        LEFT JOIN  bus_drugs_order t2  ON  t1.sub_order_id  =  t2.id
        LEFT JOIN  bus_partners t3 ON t3.`code` = t1.partners_code
        LEFT JOIN  bus_patient t4 ON t4.id = t1.patient_id
        LEFT JOIN  bus_otc_drugs t5 ON t5.drugs_order_id = t2.id
        LEFT JOIN  bus_channel_patient_agent_relation t10 ON t10.patient_id = t4.id AND t10.hospital_id = t1.hospital_id
        LEFT JOIN  bus_channel_partner_agent t11 ON t11.id = t10.agent_id
        LEFT JOIN  bus_channel_partner t12 ON t12.id = t11.channel_partner_id
        LEFT JOIN  bus_drug_order_package t13 ON t13.drugs_order_id = t1.sub_order_id AND t13.package_type = t1.sub_order_type
        WHERE  t1.hospital_id = #{hospitalId}  AND t2.prescription_id IS NULL  AND t1.sub_order_type = 0  AND t2.order_no IS NOT NULL
        <if test="orderNo != null and orderNo != '' ">
            AND t1.order_no = #{orderNo}
        </if>
        <if test="addresseeName != null and addresseeName != '' ">
            AND t1.receiver = #{addresseeName}
        </if>
        <if test="addresseePhone != null and addresseePhone != '' ">
            AND t1.receive_phone = #{addresseePhone}
        </if>
        <if test="deliveryType != null ">
            AND t1.delivery_type = #{deliveryType}
        </if>
        <if test="cooOrganization != null and cooOrganization != '' ">
            AND t3.full_name  like concat("%",replace(#{cooOrganization}," ",""),"%")
        </if>
        <if test="status  != null  and status  == 0  ">
            AND t1.order_status in (0, 7)
        </if>
        <if test="status  != null  and status  == 1  ">
            AND t1.order_status = 1
        </if>
        <if test="status  != null  and status  == 2  ">
            AND (t1.order_status = 2 OR t1.order_status = 3)
        </if>
        <if test="status  != null  and status  == 3  ">
            AND t1.sub_order_id IN (SELECT order_id FROM bus_order_after_sales WHERE after_sales_type = 0 AND hospital_id =#{hospitalId})
        </if>
        <if test="status  != null  and status  == 4  ">
            AND t1.order_status in (5,6)
        </if>
        <if test="status  != null  and status  == 5  ">
            AND t1.order_status = 4
        </if>
        <if test=" timeType = 1 and orderTimeStart != null and orderTimeEnd != null ">
            AND t1.order_time &gt;= #{orderTimeStart} AND t1.order_time &lt;= #{orderTimeEnd}
        </if>
        <if test=" timeType = 2 and paymentTimeStart != null and paymentTimeEnd != null ">
            AND t1.payment_time &gt;= #{paymentTimeStart} AND t1.payment_time &lt;= #{paymentTimeEnd}
        </if>
        <if test=" timeType = 3 and completeTimeStart != null and completeTimeEnd != null ">
            AND t1.complete_time &gt;= #{completeTimeStart} AND t1.complete_time &lt;= #{completeTimeEnd}
        </if>
        <if test="cooperationChannels != null and cooperationChannels != '' ">
            AND t12.full_name  like concat("%",replace(#{cooperationChannels}," ",""),"%")
        </if>
        <if test="code != null and code != '' ">
            AND t13.code = #{code}
        </if>
        <if test="deliveryNo != null and deliveryNo != '' ">
            AND t13.delivery_no = #{deliveryNo}
        </if>
        <if test="drugShopName != null and drugShopName != '' ">
            AND t5.drugs_name like concat("%",replace(#{drugShopName}," ",""),"%")
        </if>
        <if test="partnersCode != null and partnersCode != ''">
            AND t1.partners_code = #{partnersCode}
        </if>
        <if test=" invoiceStatus!=null ">
            AND t1.invoice_status = #{invoiceStatus}
        </if>
        UNION ALL
        SELECT
        t1.id AS id,
        t1.order_no AS  order_no ,
        t1.order_time AS order_time,
        "3" AS  list_type ,
        t1.invoice_status as invoice_status
        FROM bus_order t1
        LEFT JOIN  bus_shop_order t2  ON  t1.sub_order_id  =  t2.id
        LEFT JOIN  bus_partners t3 ON t3.`code` = t1.partners_code
        LEFT JOIN  bus_patient t4 ON t4.id = t1.patient_id
        LEFT JOIN  bus_order_shop t5 ON t5.order_id = t2.id
        LEFT JOIN  bus_channel_patient_agent_relation t10 ON t10.patient_id = t4.id AND t10.hospital_id = t1.hospital_id
        LEFT JOIN  bus_channel_partner_agent t11 ON t11.id = t10.agent_id
        LEFT JOIN  bus_channel_partner t12 ON t12.id = t11.channel_partner_id
        LEFT JOIN  bus_drug_order_package t13 ON t13.drugs_order_id = t1.sub_order_id AND t13.package_type = t1.sub_order_type
        WHERE t1.hospital_id = #{hospitalId}  AND t2.order_no IS NOT NULL   AND t1.sub_order_type = 1
        <if test="orderNo != null and orderNo != '' ">
            AND t1.order_no = #{orderNo}
        </if>
        <if test="addresseeName != null and addresseeName != '' ">
            AND t1.receiver = #{addresseeName}
        </if>
        <if test="addresseePhone != null and addresseePhone != '' ">
            AND t1.receive_phone = #{addresseePhone}
        </if>
        <if test="deliveryType != null ">
            AND t1.delivery_type = #{deliveryType}
        </if>
        <if test="cooOrganization != null and cooOrganization != '' ">
            AND t3.full_name  like concat("%",replace(#{cooOrganization}," ",""),"%")
        </if>
        <if test="status  != null  and status  == 0  ">
            AND t1.order_status in (0, 7)
        </if>
        <if test="status  != null  and status  == 1  ">
            AND t1.order_status = 1
        </if>
        <if test="status  != null  and status  == 2  ">
            AND (t1.order_status = 2 OR t1.order_status = 3)
        </if>
        <if test="status  != null  and status  == 3  ">
            AND t1.sub_order_id IN (SELECT order_id FROM bus_order_after_sales WHERE after_sales_type = 1 AND hospital_id =#{hospitalId})
        </if>
        <if test="status  != null  and status  == 4  ">
            AND t1.order_status in (5,6)
        </if>
        <if test="status  != null  and status  == 5  ">
            AND t1.order_status = 4
        </if>
        <if test=" timeType = 1 and orderTimeStart != null and orderTimeEnd != null ">
            AND t1.order_time &gt;= #{orderTimeStart} AND t1.order_time &lt;= #{orderTimeEnd}
        </if>
        <if test=" timeType = 2 and paymentTimeStart != null and paymentTimeEnd != null ">
            AND t1.payment_time &gt;= #{paymentTimeStart} AND t1.payment_time &lt;= #{paymentTimeEnd}
        </if>
        <if test=" timeType = 3 and completeTimeStart != null and completeTimeEnd != null ">
            AND t1.complete_time &gt;= #{completeTimeStart} AND t1.complete_time &lt;= #{completeTimeEnd}
        </if>
        <if test="cooperationChannels != null and cooperationChannels != '' ">
            AND t12.full_name  like concat("%",replace(#{cooperationChannels}," ",""),"%")
        </if>
        <if test="code != null and code != '' ">
            AND t13.code = #{code}
        </if>
        <if test="deliveryNo != null and deliveryNo != '' ">
            AND t13.delivery_no = #{deliveryNo}
        </if>
        <if test="drugShopName != null and drugShopName != '' ">
            AND t5.shop_name like concat("%",replace(#{drugShopName}," ",""),"%")
        </if>
        <if test="partnersCode != null and partnersCode != ''">
            AND t1.partners_code = #{partnersCode}
        </if>
        <if test=" invoiceStatus!=null ">
            AND t1.invoice_status = #{invoiceStatus}
        </if>
        ) one
        GROUP BY order_no
        ORDER BY order_time DESC
    </sql>
    <select id="listBusSubOrder" parameterType="BusSubOrderDTO" resultMap="busOrderVOMap2">
        <include refid="listBusSubOrderSQL"></include>
    </select>

    <!-- 一个订单号会有多条订单的场景，第一版是返回busOrderVOMap2，里面会带子查询，后面版本直接返回了对象就导致后续数据丢失，目前先还原，会有导出性能原因，后续处理 -->
    <!--    <select id="listBusSubOrderPart" resultType="com.puree.hospital.order.domain.vo.BusOrderVO">-->
    <select id="listBusSubOrderPart" resultMap="busOrderVOMap2">
        <include refid="listBusSubOrderSQL"></include>
        <if test=" pageNum >= 0 ">
            limit  #{pageNum},#{pageSize}
        </if>
    </select>

    <select id="getEnterprise" resultType="String">
        SELECT  t1.enterprise_name FROM  bus_enterprise t1  WHERE t1.id = #{enterpriseId}
    </select>

    <select id="listBusSubOrder1" parameterType="BusSubOrderDTO" resultMap="busOrderVOMap2">

        SELECT
        t1.id AS id,
        t1.order_no AS  order_no ,
        t1.order_time AS order_time,
        "0" AS  list_type,
        t1.invoice_status as invoice_status
        FROM 	 bus_order t1
        LEFT JOIN  bus_drugs_order t2  ON  t1.sub_order_id  =  t2.id
        LEFT JOIN  bus_partners t3 ON t3.`code` = t1.partners_code
        LEFT JOIN  bus_patient t4 ON t4.id = t1.patient_id
        LEFT JOIN  bus_prescription t5 ON t5.id = t2.prescription_id
        LEFT JOIN  bus_prescription_drugs t6 ON t5.id = t6.prescription_id
        LEFT JOIN  bus_channel_patient_agent_relation t7 ON t7.patient_id = t4.id AND t7.hospital_id = t1.hospital_id
        LEFT JOIN  bus_channel_partner_agent t8 ON t8.id = t7.agent_id
        LEFT JOIN  bus_channel_partner t9 ON t9.id = t8.channel_partner_id
        LEFT JOIN  bus_drug_order_package t10 ON t10.drugs_order_id = t1.sub_order_id AND t10.package_type = t1.sub_order_type
        WHERE  t1.hospital_id = #{hospitalId}  AND t1.sub_order_type = 0 AND  t5.pa_id IS  NULL  AND t2.prescription_id IS NOT NULL
        <if test="orderNo != null and orderNo != '' ">
            AND t1.order_no = #{orderNo}
        </if>
        <if test="addresseeName != null and addresseeName != '' ">
            AND t1.receiver = #{addresseeName}
        </if>
        <if test="addresseePhone != null and addresseePhone != '' ">
            AND t1.receive_phone = #{addresseePhone}
        </if>
        <if test="deliveryType != null ">
            AND t1.delivery_type = #{deliveryType}
        </if>
        <if test="cooOrganization != null and cooOrganization != '' ">
            AND t3.full_name  like concat("%",replace(#{cooOrganization}," ",""),"%")
        </if>
        <if test="status  != null  and status  == 0  ">
            AND t1.order_status in (0, 7)
        </if>
        <if test="status  != null  and status  == 1  ">
            AND t1.order_status = 1
        </if>
        <if test="status  != null  and status  == 2  ">
            AND (t1.order_status = 2 OR t1.order_status = 3)
        </if>
        <if test="status  != null  and status  == 3  ">
            AND t1.sub_order_id IN (SELECT order_id FROM bus_order_after_sales WHERE after_sales_type = 0 AND hospital_id =#{hospitalId})
        </if>
        <if test="status  != null  and status  == 4  ">
            AND t1.order_status in (5,6)
        </if>
        <if test="status  != null  and status  == 5  ">
            AND t1.order_status = 4
        </if>
        <if test=" timeType = 1 and orderTimeStart != null and orderTimeEnd != null ">
            AND t1.order_time &gt;= #{orderTimeStart} AND t1.order_time &lt;= #{orderTimeEnd}
        </if>
        <if test=" timeType = 2 and paymentTimeStart != null and paymentTimeEnd != null ">
            AND t1.payment_time &gt;= #{paymentTimeStart} AND t1.payment_time &lt;= #{paymentTimeEnd}
        </if>
        <if test=" timeType = 3 and completeTimeStart != null and completeTimeEnd != null ">
            AND t1.complete_time &gt;= #{completeTimeStart} AND t1.complete_time &lt;= #{completeTimeEnd}
        </if>
        <if test="cooperationChannels != null and cooperationChannels != '' ">
            AND t9.full_name  like concat("%",replace(#{cooperationChannels}," ",""),"%")
        </if>
        <if test="code != null and code != '' ">
            AND t10.code = #{code}
        </if>
        <if test="deliveryNo != null and deliveryNo != '' ">
            AND t10.delivery_no = #{deliveryNo}
        </if>
        <if test="drugShopName != null and drugShopName != '' ">
            AND t6.drugs_name like concat("%",replace(#{drugShopName}," ",""),"%")
        </if>
        <if test="partnersCode != null and partnersCode != ''">
            AND t1.partners_code = #{partnersCode}
        </if>
        <if test=" invoiceStatus!=null ">
            AND t1.invoice_status = #{invoiceStatus}
        </if>

    </select>

    <select id="listBusSubOrder2" parameterType="BusSubOrderDTO" resultMap="busOrderVOMap2">

        SELECT
        t1.id AS id,
        t1.order_no AS  order_no ,
        t1.order_time AS order_time,
        "1" AS  list_type,
        t1.invoice_status as invoice_status
        FROM bus_order t1
        LEFT JOIN  bus_drugs_order t2  ON  t1.sub_order_id  =  t2.id
        LEFT JOIN  bus_partners t3 ON t3.code = t1.partners_code
        LEFT JOIN  bus_patient t4 ON t4.id = t1.patient_id
        LEFT JOIN  bus_prescription t5 ON t5.id = t2.prescription_id
        LEFT JOIN  bus_hospital_pa t9 ON t9.id = t5.pa_id
        LEFT JOIN  bus_hospital_pa_drugs t6 ON t9.id= t6.pa_id
        LEFT JOIN  bus_directory_drugs t7 ON t7.id = t6.directory_drugs_id
        LEFT JOIN  bus_drugs t8 ON t8.id = t7.drugs_id
        LEFT JOIN  bus_channel_patient_agent_relation t10 ON t10.patient_id = t4.id AND t10.hospital_id = t1.hospital_id
        LEFT JOIN  bus_channel_partner_agent t11 ON t11.id = t10.agent_id
        LEFT JOIN  bus_channel_partner t12 ON t12.id = t11.channel_partner_id
        LEFT JOIN  bus_drug_order_package t13 ON t13.drugs_order_id = t1.sub_order_id AND t13.package_type = t1.sub_order_type
        WHERE  t1.hospital_id = #{hospitalId}  AND t2.prescription_id IS NOT NULL  AND t5.pa_id IS NOT NULL  AND t1.sub_order_type = 0  AND t2.order_no IS NOT NULL
        <if test="orderNo != null and orderNo != '' ">
            AND t1.order_no = #{orderNo}
        </if>
        <if test="addresseeName != null and addresseeName != '' ">
            AND t1.receiver = #{addresseeName}
        </if>
        <if test="addresseePhone != null and addresseePhone != '' ">
            AND t1.receive_phone = #{addresseePhone}
        </if>
        <if test="deliveryType != null ">
            AND t1.delivery_type = #{deliveryType}
        </if>
        <if test="cooOrganization != null and cooOrganization != '' ">
            AND t3.full_name  like concat("%",replace(#{cooOrganization}," ",""),"%")
        </if>
        <if test="status  != null  and status  == 0  ">
            AND t1.order_status in (0, 7)
        </if>
        <if test="status  != null  and status  == 1  ">
            AND t1.order_status = 1
        </if>
        <if test="status  != null  and status  == 2  ">
            AND (t1.order_status = 2 OR t1.order_status = 3)
        </if>
        <if test="status  != null  and status  == 3  ">
            AND t1.sub_order_id IN (SELECT order_id FROM bus_order_after_sales WHERE after_sales_type = 0 AND hospital_id =#{hospitalId})
        </if>
        <if test="status  != null  and status  == 4  ">
            AND t1.order_status in (5,6)
        </if>
        <if test="status  != null  and status  == 5  ">
            AND t1.order_status = 4
        </if>
        <if test=" timeType = 1 and orderTimeStart != null and orderTimeEnd != null ">
            AND t1.order_time &gt;= #{orderTimeStart} AND t1.order_time &lt;= #{orderTimeEnd}
        </if>
        <if test=" timeType = 2 and paymentTimeStart != null and paymentTimeEnd != null ">
            AND t1.payment_time &gt;= #{paymentTimeStart} AND t1.payment_time &lt;= #{paymentTimeEnd}
        </if>
        <if test=" timeType = 3 and completeTimeStart != null and completeTimeEnd != null ">
            AND t1.complete_time &gt;= #{completeTimeStart} AND t1.complete_time &lt;= #{completeTimeEnd}
        </if>
        <if test="cooperationChannels != null and cooperationChannels != '' ">
            AND t12.full_name  like concat("%",replace(#{cooperationChannels}," ",""),"%")
        </if>
        <if test="code != null and code != '' ">
            AND t13.code = #{code}
        </if>
        <if test="deliveryNo != null and deliveryNo != '' ">
            AND t13.delivery_no = #{deliveryNo}
        </if>
        <if test="drugShopName != null and drugShopName != '' ">
            AND t8.drugs_name like concat("%",replace(#{drugShopName}," ",""),"%")
        </if>
        <if test="partnersCode != null and partnersCode != ''">
            AND t1.partners_code = #{partnersCode}
        </if>
        <if test=" invoiceStatus!=null ">
            AND t1.invoice_status = #{invoiceStatus}
        </if>

    </select>

    <select id="listBusSubOrder3" parameterType="BusSubOrderDTO" resultMap="busOrderVOMap2">

        SELECT
        t1.id AS id,
        t1.order_no AS  order_no ,
        t1.order_time AS order_time,
        "3" AS  list_type,
        t1.invoice_status as invoiceStatus
        FROM bus_order t1
        LEFT JOIN  bus_drugs_order t2  ON  t1.sub_order_id  =  t2.id
        LEFT JOIN  bus_partners t3 ON t3.`code` = t1.partners_code
        LEFT JOIN  bus_patient t4 ON t4.id = t1.patient_id
        LEFT JOIN  bus_otc_drugs t5 ON t5.drugs_order_id = t2.id
        LEFT JOIN  bus_channel_patient_agent_relation t10 ON t10.patient_id = t4.id AND t10.hospital_id = t1.hospital_id
        LEFT JOIN  bus_channel_partner_agent t11 ON t11.id = t10.agent_id
        LEFT JOIN  bus_channel_partner t12 ON t12.id = t11.channel_partner_id
        LEFT JOIN  bus_drug_order_package t13 ON t13.drugs_order_id = t1.sub_order_id AND t13.package_type = t1.sub_order_type
        WHERE  t1.hospital_id = #{hospitalId}  AND t2.prescription_id IS NULL  AND t1.sub_order_type = 0  AND t2.order_no IS NOT NULL
        <if test="orderNo != null and orderNo != '' ">
            AND t1.order_no = #{orderNo}
        </if>
        <if test="addresseeName != null and addresseeName != '' ">
            AND t1.receiver = #{addresseeName}
        </if>
        <if test="addresseePhone != null and addresseePhone != '' ">
            AND t1.receive_phone = #{addresseePhone}
        </if>
        <if test="deliveryType != null ">
            AND t1.delivery_type = #{deliveryType}
        </if>
        <if test="cooOrganization != null and cooOrganization != '' ">
            AND t3.full_name  like concat("%",replace(#{cooOrganization}," ",""),"%")
        </if>
        <if test="status  != null  and status  == 0  ">
            AND t1.order_status in (0, 7)
        </if>
        <if test="status  != null  and status  == 1  ">
            AND t1.order_status = 1
        </if>
        <if test="status  != null  and status  == 2  ">
            AND (t1.order_status = 2 OR t1.order_status = 3)
        </if>
        <if test="status  != null  and status  == 3  ">
            AND t1.sub_order_id IN (SELECT order_id FROM bus_order_after_sales WHERE after_sales_type = 0 AND hospital_id =#{hospitalId})
        </if>
        <if test="status  != null  and status  == 4  ">
            AND t1.order_status in (5,6)
        </if>
        <if test="status  != null  and status  == 5  ">
            AND t1.order_status = 4
        </if>
        <if test=" timeType = 1 and orderTimeStart != null and orderTimeEnd != null ">
            AND t1.order_time &gt;= #{orderTimeStart} AND t1.order_time &lt;= #{orderTimeEnd}
        </if>
        <if test=" timeType = 2 and paymentTimeStart != null and paymentTimeEnd != null ">
            AND t1.payment_time &gt;= #{paymentTimeStart} AND t1.payment_time &lt;= #{paymentTimeEnd}
        </if>
        <if test=" timeType = 3 and completeTimeStart != null and completeTimeEnd != null ">
            AND t1.complete_time &gt;= #{completeTimeStart} AND t1.complete_time &lt;= #{completeTimeEnd}
        </if>
        <if test="cooperationChannels != null and cooperationChannels != '' ">
            AND t12.full_name  like concat("%",replace(#{cooperationChannels}," ",""),"%")
        </if>
        <if test="code != null and code != '' ">
            AND t13.code = #{code}
        </if>
        <if test="deliveryNo != null and deliveryNo != '' ">
            AND t13.delivery_no = #{deliveryNo}
        </if>
        <if test="drugShopName != null and drugShopName != '' ">
            AND t5.drugs_name like concat("%",replace(#{drugShopName}," ",""),"%")
        </if>
        <if test="partnersCode != null and partnersCode != ''">
            AND t1.partners_code = #{partnersCode}
        </if>
        <if test=" invoiceStatus!=null ">
            AND t1.invoice_status = #{invoiceStatus}
        </if>

    </select>

    <select id="listBusSubOrder4" parameterType="BusSubOrderDTO" resultMap="busOrderVOMap2">

        SELECT
        t1.id AS id,
        t1.order_no AS  order_no ,
        t1.order_time AS order_time,
        "3" AS  list_type ,
        t1.invoice_status as invoice_status
        FROM bus_order t1
        LEFT JOIN  bus_shop_order t2  ON  t1.sub_order_id  =  t2.id
        LEFT JOIN  bus_partners t3 ON t3.`code` = t1.partners_code
        LEFT JOIN  bus_patient t4 ON t4.id = t1.patient_id
        LEFT JOIN  bus_order_shop t5 ON t5.order_id = t2.id
        LEFT JOIN  bus_channel_patient_agent_relation t10 ON t10.patient_id = t4.id AND t10.hospital_id = t1.hospital_id
        LEFT JOIN  bus_channel_partner_agent t11 ON t11.id = t10.agent_id
        LEFT JOIN  bus_channel_partner t12 ON t12.id = t11.channel_partner_id
        LEFT JOIN  bus_drug_order_package t13 ON t13.drugs_order_id = t1.sub_order_id AND t13.package_type = t1.sub_order_type
        WHERE t1.hospital_id = #{hospitalId}  AND t2.order_no IS NOT NULL   AND t1.sub_order_type = 1
        <if test="orderNo != null and orderNo != '' ">
            AND t1.order_no = #{orderNo}
        </if>
        <if test="addresseeName != null and addresseeName != '' ">
            AND t1.receiver = #{addresseeName}
        </if>
        <if test="addresseePhone != null and addresseePhone != '' ">
            AND t1.receive_phone = #{addresseePhone}
        </if>
        <if test="deliveryType != null ">
            AND t1.delivery_type = #{deliveryType}
        </if>
        <if test="cooOrganization != null and cooOrganization != '' ">
            AND t3.full_name  like concat("%",replace(#{cooOrganization}," ",""),"%")
        </if>
        <if test="status  != null  and status  == 0  ">
            AND t1.order_status in (0, 7)
        </if>
        <if test="status  != null  and status  == 1  ">
            AND t1.order_status = 1
        </if>
        <if test="status  != null  and status  == 2  ">
            AND (t1.order_status = 2 OR t1.order_status = 3)
        </if>
        <if test="status  != null  and status  == 3  ">
            AND t1.sub_order_id IN (SELECT order_id FROM bus_order_after_sales WHERE after_sales_type = 1 AND hospital_id =#{hospitalId})
        </if>
        <if test="status  != null  and status  == 4  ">
            AND t1.order_status in (5,6)
        </if>
        <if test="status  != null  and status  == 5  ">
            AND t1.order_status = 4
        </if>
        <if test=" timeType = 1 and orderTimeStart != null and orderTimeEnd != null ">
            AND t1.order_time &gt;= #{orderTimeStart} AND t1.order_time &lt;= #{orderTimeEnd}
        </if>
        <if test=" timeType = 2 and paymentTimeStart != null and paymentTimeEnd != null ">
            AND t1.payment_time &gt;= #{paymentTimeStart} AND t1.payment_time &lt;= #{paymentTimeEnd}
        </if>
        <if test=" timeType = 3 and completeTimeStart != null and completeTimeEnd != null ">
            AND t1.complete_time &gt;= #{completeTimeStart} AND t1.complete_time &lt;= #{completeTimeEnd}
        </if>
        <if test="cooperationChannels != null and cooperationChannels != '' ">
            AND t12.full_name  like concat("%",replace(#{cooperationChannels}," ",""),"%")
        </if>
        <if test="code != null and code != '' ">
            AND t13.code = #{code}
        </if>
        <if test="deliveryNo != null and deliveryNo != '' ">
            AND t13.delivery_no = #{deliveryNo}
        </if>
        <if test="drugShopName != null and drugShopName != '' ">
            AND t5.shop_name like concat("%",replace(#{drugShopName}," ",""),"%")
        </if>
        <if test="partnersCode != null and partnersCode != ''">
            AND t1.partners_code = #{partnersCode}
        </if>
        <if test=" invoiceStatus!=null ">
            AND t1.invoice_status = #{invoiceStatus}
        </if>

    </select>
    <select id="getOrderByTransactionId" resultType="com.puree.hospital.order.domain.BusOrder">
        select * from bus_order where tonglian_trxid = #{transactionId}
    </select>


</mapper>
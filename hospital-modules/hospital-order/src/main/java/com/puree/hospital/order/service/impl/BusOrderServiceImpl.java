package com.puree.hospital.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.puree.hospital.app.api.RemoteDoctorService;
import com.puree.hospital.app.api.model.event.order.OrderCompleteEvent;
import com.puree.hospital.common.core.enums.ChannelOrderEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.DeliveryTypeEnum;
import com.puree.hospital.common.core.enums.OrderListTypeEnum;
import com.puree.hospital.common.core.enums.OrderStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.order.domain.BusChannelOrder;
import com.puree.hospital.order.domain.BusDrugsOrder;
import com.puree.hospital.order.domain.BusHospitalPa;
import com.puree.hospital.order.domain.BusOrder;
import com.puree.hospital.order.domain.BusOrderAfterSales;
import com.puree.hospital.order.domain.BusOrderShop;
import com.puree.hospital.order.domain.BusOtcDrugs;
import com.puree.hospital.order.domain.BusPartners;
import com.puree.hospital.order.domain.BusPrescription;
import com.puree.hospital.order.domain.BusPrescriptionDrugs;
import com.puree.hospital.order.domain.BusShopOrder;
import com.puree.hospital.order.domain.ChannelPartnerVO;
import com.puree.hospital.order.domain.dto.BusSubOrderDTO;
import com.puree.hospital.order.domain.vo.BusDrugOrderPackage;
import com.puree.hospital.order.domain.vo.BusOrderItemVO;
import com.puree.hospital.order.domain.vo.BusOrderVO;
import com.puree.hospital.order.domain.vo.BusSubOrderVO;
import com.puree.hospital.order.domain.vo.OrderExportVO;
import com.puree.hospital.order.domain.vo.OrderShopExportVO;
import com.puree.hospital.order.mapper.BusChannelOrderMapper;
import com.puree.hospital.order.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.order.mapper.BusDrugsOrderMapper;
import com.puree.hospital.order.mapper.BusHospitalPaMapper;
import com.puree.hospital.order.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.order.mapper.BusOrderMapper;
import com.puree.hospital.order.mapper.BusOrderShopMapper;
import com.puree.hospital.order.mapper.BusOtcDrugsMapper;
import com.puree.hospital.order.mapper.BusPartnersMapper;
import com.puree.hospital.order.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.order.mapper.BusPrescriptionMapper;
import com.puree.hospital.order.mapper.BusShopOrderMapper;
import com.puree.hospital.order.service.IBusOrderService;
import com.puree.hospital.order.service.UniAppSyncLogisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOrderServiceImpl implements IBusOrderService {
    private final BusOrderMapper busOrderMapper;
    private final BusDrugsOrderMapper busDrugsOrderMapper;
    private final BusShopOrderMapper busShopOrderMapper;
    private final BusOrderShopMapper busOrderShopMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final BusOtcDrugsMapper busOtcDrugsMapper;
    private final BusHospitalPaMapper busHospitalPaMapper;
    private final BusPartnersMapper busPartnersMapper;
    private final BusOrderAfterSalesMapper busOrderAfterSalesMapper;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final BusChannelOrderMapper busChannelOrderMapper;
    private final RemoteDoctorService remoteDoctorService;
    private final UniAppSyncLogisticsService uniAppSyncLogisticsService;

    @Resource(name = "orderExecutor")
    private Executor orderExecutor;
    private final ApplicationEventPublisher publisher;


    @Resource(name = "orderCopyExecutor")
    private Executor orderCopyExecutor;

    @Override
    public int insertBusOrder(BusOrder busOrder) {
        busOrder.setCreateTime(DateUtils.getNowDate());
        busOrder.setCreateBy(SecurityUtils.getUsername());
        return busOrderMapper.insert(busOrder);
    }

    @Override
    public BusOrder getBusOrderById(Long busOrderId) {
        return busOrderMapper.selectById(busOrderId);
    }

    @Override
    public List<BusOrder> listBusOrder() {
        return busOrderMapper.selectList(new LambdaQueryWrapper<BusOrder>());
    }

    @Override
    public int updateBusOrderById(BusOrder busOrder) {
        busOrder.setUpdateTime(DateUtils.getNowDate());
        busOrder.setUpdateBy(SecurityUtils.getUsername());
        return busOrderMapper.updateById(busOrder);
    }

    @Override
    public BusOrder getBusOrderBySubOrderId(Long subOrderId, String subOrderType) {
        LambdaQueryWrapper<BusOrder> query = new LambdaQueryWrapper<BusOrder>()
                .eq(BusOrder::getSubOrderId, subOrderId)
                .eq(BusOrder::getSubOrderType, subOrderType);
        BusOrder busFiveServicePackOrder = busOrderMapper.selectOne(query);
        return busFiveServicePackOrder;
    }

    /**
     * 查询订单信息
     *
     * @param orderNo
     * @return
     */
    @Override
    public List<BusOrder> getOrderByNo(String orderNo) {
        return busOrderMapper.selectOrderList(orderNo);
    }

    @Override
    public List<BusOrderVO> listBusSubOrder(BusSubOrderDTO busSubOrderDTO) {
        this.dataHandle(busSubOrderDTO);
        // 修改订单为旧订单
        LambdaUpdateWrapper<BusOrder> lambdaUpdate = Wrappers.lambdaUpdate();
        lambdaUpdate.eq(BusOrder::getHospitalId, busSubOrderDTO.getHospitalId());
        BusOrder order = new BusOrder();
        order.setNewOrderOrNot(YesNoEnum.NO.getCode());
        busOrderMapper.update(order, lambdaUpdate);

        List<BusOrderVO> busSubOrderVOS = busOrderMapper.listBusSubOrder(busSubOrderDTO);
        for (BusOrderVO topItem : busSubOrderVOS) {
            List<BusOrder> orders = topItem.getOrders();
            if (!CollectionUtils.isEmpty(orders)) {
                BusOrder busOrder = orders.get(0);
                topItem.setAmountAll(new BigDecimal(busOrder.getRelPrice()));
                topItem.setDeliveryWay(Integer.valueOf(busOrder.getDeliveryType()));
                topItem.setPayWay(Integer.valueOf(busOrder.getPayWay()));
                topItem.setOrderTime(busOrder.getOrderTime());
                topItem.setStatus(busOrder.getOrderStatus());
                topItem.setAddresseeName(busOrder.getReceiver());
                topItem.setAddresseePhone(busOrder.getReceivePhone());
                topItem.setAfterOrderId(busOrder.getAfterSaleId());
                ChannelPartnerVO channelsAndAgent = getChannelsAndAgent(busOrder);
                // channelsAndAgent这个对应不可能为空，因为是new出来的对象返回的
                topItem.setCooOrganization(channelsAndAgent.getCooOrganization());
                topItem.setCooperationChannels(channelsAndAgent.getChannelName());
                topItem.setAgentName(channelsAndAgent.getAgentName());
            }
            List<BusSubOrderVO> subOrderList = new ArrayList<>();
            for (BusOrder subOrderItem : orders) {
                BusSubOrderVO subOrder = new BusSubOrderVO();
                /*如果是药品订单*/
                if (CodeEnum.NO.getCode().equals(subOrderItem.getSubOrderType())) {
                    BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectById(subOrderItem.getSubOrderId());
                    if (busDrugsOrder != null) {
                        subOrder.setSubOrderId(busDrugsOrder.getId());
                        subOrder.setSubOrderNo(busDrugsOrder.getOrderNo());
                        subOrder.setRxId(busDrugsOrder.getPrescriptionId());
                        subOrder.setOrderClassify(Integer.valueOf(busDrugsOrder.getOrderClassify()));
                        /*如果是处方药*/
                        if (busDrugsOrder.getPrescriptionId() != null) {
                            BusPrescription busPrescription = busPrescriptionMapper.selectById(busDrugsOrder.getPrescriptionId());
                            //获取药品推荐人信息
                            if(busPrescription.getReferrer() != null && topItem.getReferrer() == null){
                                topItem.setReferrer(busPrescription.getReferrer());
                                topItem.setReferrerName(remoteDoctorService.getById(busPrescription.getReferrer()).getData().getFullName());
                            }
                            List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
                                    .eq(BusPrescriptionDrugs::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                            );
                            /*如果是中药方剂*/
                            if (busPrescription.getPaId() != null) {
                                String usages = busPrescription.getUsages();
                                String[] split = usages.split(",");
                                subOrder.setDoctorName(busPrescription.getDoctorName());
                                List<BusOrderItemVO> busOrderItemVOList = new ArrayList<>();
                                BusOrderItemVO busOrderItemVO = new BusOrderItemVO();
                                /*判断售后状态*/
                                BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                                        .eq(BusOrderAfterSales::getHospitalId, subOrderItem.getHospitalId())
                                        .eq(BusOrderAfterSales::getOrderId, busDrugsOrder.getId())
                                        .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.NO.getCode())
                                        .eq(BusOrderAfterSales::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                                        .last("limit 1")
                                );
                                if (busOrderAfterSales != null) {
                                    busOrderItemVO.setItemAfterSalesStatus(Integer.valueOf(busOrderAfterSales.getAfterSalesStatus()));
                                }
                                /*判断发货状态*/
                                BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                                        .eq(BusDrugOrderPackage::getHospitalId, subOrderItem.getHospitalId())
                                        .eq(BusDrugOrderPackage::getDrugsOrderId, busDrugsOrder.getId())
                                        .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                                        .eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                                        .last("limit 1")
                                );
                                if (busDrugOrderPackage != null && busDrugOrderPackage.getDeliveryTime() != null) {
                                    busOrderItemVO.setItemStatus(YesNoEnum.YES.getCode());
                                } else {
                                    busOrderItemVO.setItemStatus(YesNoEnum.NO.getCode());
                                }
                                BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(busPrescription.getPaId());
                                /*如果是中药方剂则展示药品*/
                                if (YesNoEnum.YES.getCode().equals(busHospitalPa.getType())) {
                                    subOrder.setListType(OrderListTypeEnum.CLASSICAL_FORMULA.getCode());
                                } else {
                                    subOrder.setListType(OrderListTypeEnum.PARTIES.getCode());
                                }
                                StringBuilder stringBuffer = new StringBuilder();
                                for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                                    stringBuffer.append(item.getDrugsName())
                                            .append(item.getDrugsSpecification())
                                            .append("×")
                                            .append(item.getWeight())
                                            .append(" ");
                                }
                                busOrderItemVO.setItemName(busHospitalPa.getPaName());
                                busOrderItemVO.setNumber(busPrescriptionDrugs.size());
                                busOrderItemVO.setItemQuantity(Integer.valueOf(split[1]));
                                //因为数据库中 中药方剂的单价可能改变
                                //中药方剂单价应为 （金额-加工费）/数量，而不是直接获取数据库中该中药方剂的单价
                                //加工费非空校验
                                BigDecimal processPrice= ObjectUtil.isNull(busPrescription.getProcessPrice())? BigDecimal.ZERO:busPrescription.getProcessPrice();
                                //计算单价
                                BigDecimal amount =busPrescription.getPrescriptionAmount().subtract(processPrice).
                                        divide(BigDecimal.valueOf(busOrderItemVO.getItemQuantity()), RoundingMode.CEILING) ;
                                busOrderItemVO.setItemPrice(amount);
                                busOrderItemVO.setDrugsNameStr(stringBuffer.toString());
                                busOrderItemVOList.add(busOrderItemVO);
                                subOrder.setOrderItems(busOrderItemVOList);
                                /*如果是一般的处方*/
                            } else {
                                /*如果是中药处方*/
                                if (YesNoEnum.NO.getCode().equals(Integer.valueOf(busPrescription.getPrescriptionType()))) {
                                    String usages = busPrescription.getUsages();
                                    String[] split = usages.split(",");
                                    subOrder.setDoctorName(busPrescription.getDoctorName());
                                    subOrder.setListType(OrderListTypeEnum.GENERAL_PRESCRIPTION.getCode());
                                    List<BusOrderItemVO> busOrderItemVOList = new ArrayList<>();
                                    BusOrderItemVO busOrderItemVO = new BusOrderItemVO();
                                    /*判断售后状态*/
                                    BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                                            .eq(BusOrderAfterSales::getHospitalId, subOrderItem.getHospitalId())
                                            .eq(BusOrderAfterSales::getOrderId, busDrugsOrder.getId())
                                            .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.NO.getCode())
                                            .eq(BusOrderAfterSales::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                                            .last("limit 1")
                                    );
                                    if (busOrderAfterSales != null) {
                                        busOrderItemVO.setItemAfterSalesStatus(Integer.valueOf(busOrderAfterSales.getAfterSalesStatus()));
                                    }
                                    /*判断发货状态*/
                                    BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                                            .eq(BusDrugOrderPackage::getDrugsOrderId, busDrugsOrder.getId())
                                            .eq(BusDrugOrderPackage::getHospitalId, subOrderItem.getHospitalId())
                                            .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                                            .eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                                            .last("limit 1")
                                    );
                                    if (busDrugOrderPackage != null && busDrugOrderPackage.getDeliveryTime() != null) {
                                        busOrderItemVO.setItemStatus(YesNoEnum.YES.getCode());
                                    } else {
                                        busOrderItemVO.setItemStatus(YesNoEnum.NO.getCode());
                                    }

                                    for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                                        BusOrderItemVO busOrderItem = new BusOrderItemVO();
                                        BeanUtils.copyProperties(busOrderItemVO, busOrderItem);
                                        busOrderItem.setItemId(item.getId());
                                        StringBuilder stringBuilder = new StringBuilder();
                                        // g/袋  g/瓶
                                        if ("g".equalsIgnoreCase(item.getDrugsSpecification())) {
                                            stringBuilder.append("1").append(item.getDrugsSpecification()).append("×").append(item.getWeight());
                                        } else {
                                            stringBuilder.append(item.getDrugsSpecification()).append("×").append(item.getWeight());
                                        }
                                        //规格
                                        busOrderItem.setItemSpec(stringBuilder.toString());
                                        //单位数量
                                        busOrderItem.setItemWeight(Integer.valueOf(item.getWeight()));
                                        //BigDecimal multiply = item.getSellingPrice().multiply(new BigDecimal(weight));
                                        //bigDecimal = bigDecimal.add(multiply);
                                        busOrderItem.setItemQuantity(item.getQuantity());
                                        //剂量
                                        busOrderItem.setNumber(Integer.valueOf(split[1]));
                                        //中药名字
                                        busOrderItem.setDrugsNameStr(item.getDrugsName());
                                        //单价
                                        busOrderItem.setItemPrice(item.getSellingPrice());
                                        busOrderItemVOList.add(busOrderItem);
                                    }
                                    subOrder.setOrderItems(busOrderItemVOList);

                                    /*如果是西药处方*/
                                } else if (YesNoEnum.YES.getCode().equals(Integer.valueOf(busPrescription.getPrescriptionType()))) {
                                    subOrder.setListType(OrderListTypeEnum.WESTERN_MEDICINE_PRESCRIPTION.getCode());
                                    subOrder.setDoctorName(busPrescription.getDoctorName());
                                    List<BusOrderItemVO> busOrderItemVOList = new ArrayList<>();
                                    busPrescriptionDrugs.forEach(item -> {
                                        /*判断售后状态*/
                                        BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                                                .eq(BusOrderAfterSales::getHospitalId, subOrderItem.getHospitalId())
                                                .eq(BusOrderAfterSales::getOrderId, busDrugsOrder.getId())
                                                .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.NO.getCode())
                                                .eq(BusOrderAfterSales::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                                                .last("limit 1")
                                        );
                                        /*判断发货状态*/
                                        BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                                                .eq(BusDrugOrderPackage::getHospitalId, subOrderItem.getHospitalId())
                                                .eq(BusDrugOrderPackage::getDrugsOrderId, busDrugsOrder.getId())
                                                .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                                                .eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                                                .eq(StringUtils.isNull(item.getEnterpriseId()), BusDrugOrderPackage::getDrugsGoodsId, item.getDrugsId())
                                                .eq(StringUtils.isNotNull(item.getEnterpriseId()), BusDrugOrderPackage::getEnterpriseId, item.getEnterpriseId())
                                                .last("limit 1")
                                        );
                                        BusOrderItemVO busOrderItemVO = new BusOrderItemVO();
                                        if (StringUtils.isNotNull(busOrderAfterSales)) {
                                            busOrderItemVO.setItemAfterSalesStatus(Integer.valueOf(busOrderAfterSales.getAfterSalesStatus()));
                                        }
                                        if (StringUtils.isNotNull(busDrugOrderPackage) && StringUtils.isNotNull(busDrugOrderPackage.getDeliveryTime())) {
                                            busOrderItemVO.setItemStatus(YesNoEnum.YES.getCode());
                                        } else {
                                            busOrderItemVO.setItemStatus(YesNoEnum.NO.getCode());
                                        }
                                        busOrderItemVO.setItemId(item.getDrugsId());
                                        busOrderItemVO.setItemName(item.getDrugsName());
                                        busOrderItemVO.setItemPrice(item.getSellingPrice());
                                        busOrderItemVO.setItemQuantity(item.getQuantity());
                                        busOrderItemVO.setItemSpec(item.getDrugsSpecification());
                                        busOrderItemVO.setItemImg(item.getDrugsImg());
                                        busOrderItemVOList.add(busOrderItemVO);
                                    });
                                    subOrder.setOrderItems(busOrderItemVOList);
                                }
                            }
                            /*如果是OTC药药品*/
                        } else {
                            subOrder.setListType(OrderListTypeEnum.OTC.getCode());
                            List<BusOrderItemVO> busOrderItemVOList = new ArrayList<>();
                            List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectList(
                                    new LambdaQueryWrapper<BusOtcDrugs>()
                                            .eq(BusOtcDrugs::getDrugsOrderId, busDrugsOrder.getId())
                            );
                            busOtcDrugs.forEach(otcItem -> {
                                BusOrderItemVO busOrderItemVO = new BusOrderItemVO();
                                /*判断售后状态*/
                                BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                                        .eq(BusOrderAfterSales::getHospitalId, subOrderItem.getHospitalId())
                                        .eq(BusOrderAfterSales::getOrderId, busDrugsOrder.getId())
                                        .eq(BusOrderAfterSales::getGoodsId, otcItem.getDrugsId())
                                        .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.NO.getCode())
                                        .last("limit 1")
                                );
                                /*判断发货状态 */
                                BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                                        .eq(BusDrugOrderPackage::getHospitalId, subOrderItem.getHospitalId())
                                        .eq(BusDrugOrderPackage::getDrugsOrderId, busDrugsOrder.getId())
                                        .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                                        .eq(StringUtils.isNull(otcItem.getEnterpriseId()), BusDrugOrderPackage::getDrugsGoodsId, otcItem.getDrugsId())
                                        .eq(StringUtils.isNotNull(otcItem.getEnterpriseId()), BusDrugOrderPackage::getEnterpriseId, otcItem.getEnterpriseId())
                                        .last("limit 1")
                                );
                                if (StringUtils.isNotNull(busDrugOrderPackage) && StringUtils.isNotNull(busDrugOrderPackage.getDeliveryTime())) {
                                    busOrderItemVO.setItemStatus(YesNoEnum.YES.getCode());
                                } else {
                                    busOrderItemVO.setItemStatus(YesNoEnum.NO.getCode());
                                }
                                if (StringUtils.isNotNull(busOrderAfterSales)) {
                                    busOrderItemVO.setItemAfterSalesStatus(Integer.valueOf(busOrderAfterSales.getAfterSalesStatus()));
                                }
                                busOrderItemVO.setItemId(otcItem.getDrugsId());
                                busOrderItemVO.setItemName(otcItem.getDrugsName());
                                busOrderItemVO.setItemPrice(otcItem.getSellingPrice());
                                busOrderItemVO.setItemQuantity(otcItem.getQuantity());
                                busOrderItemVO.setItemSpec(otcItem.getDrugsSpecification());
                                busOrderItemVO.setItemImg(otcItem.getDrugsImg());
                                busOrderItemVOList.add(busOrderItemVO);
                            });
                            subOrder.setOrderItems(busOrderItemVOList);
                        }
                    }
                    /*如果是商品*/
                } else {
                    subOrder.setListType(OrderListTypeEnum.GOODS.getCode());
                    List<BusOrderItemVO> busOrderItemVOList = new ArrayList<>();
                    BusShopOrder busShopOrder = busShopOrderMapper.selectById(subOrderItem.getSubOrderId());
                    if (StringUtils.isNotNull(busShopOrder)) {
                        subOrder.setSubOrderId(busShopOrder.getId());
                        subOrder.setSubOrderNo(busShopOrder.getOrderNo());
                        List<BusOrderShop> busOrderShops = busOrderShopMapper.selectList(new LambdaQueryWrapper<BusOrderShop>()
                                .eq(BusOrderShop::getOrderId, busShopOrder.getId())
                        );
                        busOrderShops.forEach(goodsItem -> {
                            /*判断售后状态*/
                            BusOrderItemVO busOrderItemVO = new BusOrderItemVO();
                            BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                                    .eq(BusOrderAfterSales::getHospitalId, subOrderItem.getHospitalId())
                                    .eq(BusOrderAfterSales::getOrderId, busShopOrder.getId())
                                    .eq(BusOrderAfterSales::getGoodsId, goodsItem.getShopId())
                                    .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.YES.getCode())
                                    .last("limit 1")
                            );
                            /*判断发货状态*/
                            BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                                    .eq(BusDrugOrderPackage::getHospitalId, subOrderItem.getHospitalId())
                                    .eq(BusDrugOrderPackage::getDrugsOrderId, busShopOrder.getId())
                                    .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.YES.getCode())
                                    .eq(BusDrugOrderPackage::getDrugsGoodsId, goodsItem.getShopId())
                                    .eq(StringUtils.isNotNull(goodsItem.getEnterpriseId()), BusDrugOrderPackage::getEnterpriseId, goodsItem.getEnterpriseId())
                                    .last("limit 1")
                            );
                            if (StringUtils.isNotNull(busDrugOrderPackage) && StringUtils.isNotNull(busDrugOrderPackage.getDeliveryTime())) {
                                busOrderItemVO.setItemStatus(YesNoEnum.YES.getCode());
                            } else {
                                busOrderItemVO.setItemStatus(YesNoEnum.NO.getCode());
                            }
                            if (StringUtils.isNotNull(busOrderAfterSales)) {
                                busOrderItemVO.setItemAfterSalesStatus(Integer.valueOf(busOrderAfterSales.getAfterSalesStatus()));
                            }
                            busOrderItemVO.setItemId(goodsItem.getShopId());
                            busOrderItemVO.setItemName(goodsItem.getShopName());
                            busOrderItemVO.setItemPrice(goodsItem.getSellingPrice());
                            busOrderItemVO.setItemQuantity(goodsItem.getQuantity());
                            busOrderItemVO.setItemSpec(goodsItem.getShopSpecification());
                            busOrderItemVO.setItemImg(goodsItem.getShopImg());
                            busOrderItemVOList.add(busOrderItemVO);
                        });
                        subOrder.setOrderItems(busOrderItemVOList);
                    }
                }
                subOrderList.add(subOrder);
            }
            topItem.setSubOrders(subOrderList);
        }
        //判断订单支付clientType
        List<String> collect = busSubOrderVOS.stream().map(BusOrderVO::getOrderNo).collect(Collectors.toList());
        Map<String, String> map = uniAppSyncLogisticsService.getOrderClientType(collect);
        busSubOrderVOS.forEach(e->{
            e.setClientType(map.get(e.getOrderNo()));
        });
        return busSubOrderVOS;
    }


    /**
     * Order导出
     * @param busSubOrderDTO
     * @param page
     * @param pageSizeNumber
     * @return
     */
    @Override
    public List<OrderExportVO> listExportOrder(BusSubOrderDTO busSubOrderDTO, int page, int pageSizeNumber) {
        this.dataHandle(busSubOrderDTO);
        busSubOrderDTO.setPageNum(page);
        busSubOrderDTO.setPageSize(pageSizeNumber);
        List<BusOrderVO> busSubOrderVOS = busOrderMapper.listBusSubOrderPart(busSubOrderDTO);
        //制作一个线程安全的结果集
        List<OrderExportVO> resultVO = Collections.synchronizedList(new ArrayList<>());
        submitOrderFuture(busSubOrderVOS, resultVO);
        return resultVO;
    }

    /**
     * 流式对每一个查询到的集合开启异步线程执行，并做确认
     * @param busSubOrderVOS1
     * @param resultVO
     */
    private void submitOrderFuture(List<BusOrderVO> busSubOrderVOS1, List<OrderExportVO> resultVO) {
        List<CompletableFuture<String>> completableFutureList = busSubOrderVOS1.parallelStream().map(itemTop ->
                //每一个开启异步线程执行
                CompletableFuture.supplyAsync(() -> {
                    return getOrderExportResult(resultVO, itemTop);
                }, orderExecutor)

        ).collect(Collectors.toList());
        //并行确认是否线程都完成了
        confirmSucceed(completableFutureList);
    }

    /**
     * 多线程导出执行，返回的是执行结果
     * @param resultVO
     * @param itemTop
     * @return
     */
    private String getOrderExportResult(List<OrderExportVO> resultVO, BusOrderVO itemTop) {
        BusOrder busOrder = busOrderMapper.selectById(itemTop.getId());
        //查询不到直接结束该异步线程
        if (busOrder == null) {
            return "fail";
        }
        OrderExportVO result = OrikaUtils.convert(busOrder, OrderExportVO.class);
        /*查询合作机构，合作渠道，合伙人信息,患者名字，患者手机号*/
        ChannelPartnerVO channelsAndAgent = getChannelsAndAgent(busOrder);
        if (channelsAndAgent != null) {
            result.setAgentName(channelsAndAgent.getAgentName());
            result.setCooOrganization(channelsAndAgent.getCooOrganization());
            result.setCooperationChannels(channelsAndAgent.getChannelName());
            result.setUserName(channelsAndAgent.getUserName());
            result.setUserTel(channelsAndAgent.getUserTel());
        }
        /*设置其他信息*/
        if (result.getRefundAmount() != null) {
            result.setRefundAmount(result.getRefundAmount().setScale(2, RoundingMode.HALF_UP));
        }
        result.setPaymentPlatform("APP");
        BigDecimal bigDecimal = BigDecimal.valueOf(busOrder.getRelPrice() - busOrder.getFreight());
        result.setPayWay(CodeEnum.YES.getCode().equals(busOrder.getPayWay()) ? "微信支付" : "通联支付");
        result.setDeliveryType(CodeEnum.YES.getCode().equals(busOrder.getDeliveryType()) ? "快递发货" : "上门自提");
        result.setOrderStatus(OrderStatusEnum.getInfoByCode(busOrder.getOrderStatus()));
        if (CodeEnum.YES.getCode().equals(busOrder.getSubOrderType())) {
            result.setSubOrderType("商品订单");
        } else {
            result.setSubOrderType("药品订单");
            /*查询处方煎煮费用*/
            BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectById(busOrder.getSubOrderId());
            if (busDrugsOrder.getPrescriptionId() != null) {
                BusPrescription busPrescription = busPrescriptionMapper.selectById(busDrugsOrder.getPrescriptionId());
                if (busPrescription != null && busPrescription.getProcessPrice() != null) {
                    result.setProcessPrice(busPrescription.getProcessPrice().setScale(2, RoundingMode.HALF_UP));
                    bigDecimal = bigDecimal.subtract(busPrescription.getProcessPrice());
                }
            }
        }
        result.setShopAmount(bigDecimal.setScale(2, RoundingMode.HALF_UP));
        result.setShouldPrice(busOrder.getRelPrice());
        /*设置退款金额*/
        List<BusOrderAfterSales> busOrderAfterSales = busOrderAfterSalesMapper.selectList(
                new LambdaQueryWrapper<BusOrderAfterSales>().eq(BusOrderAfterSales::getOrderNo, busOrder.getOrderNo())
        );
        result.setRefundAmount(busOrderAfterSales.stream().map(BusOrderAfterSales::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        StringBuffer stringBuffer = new StringBuffer();
        List<BusOrder> orders = itemTop.getOrders();
        if (orders == null || orders.size() == 0) {
            orders = new ArrayList<BusOrder>();
            orders.add(busOrder);
        }
        //拿到每一笔订单，去查询其余信息填充到结果中
        List<CompletableFuture<String>> completableFutures = orders.parallelStream().map(orderItem ->
            CompletableFuture.supplyAsync(() -> {
                return orderCheck(busOrder, stringBuffer, orderItem);
            }, orderCopyExecutor)
        ).collect(Collectors.toList());
        //并行确认是否完成
        confirmSucceed(completableFutures);
        result.setShopName(stringBuffer.toString());
        resultVO.add(result);
        return "success";
    }

    /**
     * 根据每一笔订单去查询详细信息（Order导出）
     * @param busOrder
     * @param stringBuffer
     * @param orderItem
     * @return
     */
    private String orderCheck(BusOrder busOrder, StringBuffer stringBuffer, BusOrder orderItem) {
        if (CodeEnum.NO.getCode().equals(orderItem.getSubOrderType())) {
            BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectById(orderItem.getSubOrderId());
            if (busDrugsOrder.getPrescriptionId() != null) {
                BusPrescription busPrescription = busPrescriptionMapper.selectById(busDrugsOrder.getPrescriptionId());
                List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
                        .eq(BusPrescriptionDrugs::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                );
                if (CodeEnum.NO.getCode().equals(busPrescription.getPrescriptionType())) {
                    String usages = busPrescription.getUsages();
                    String[] split = usages.split(",");
                    for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                        stringBuffer.append(item.getDrugsName() + item.getWeight() + item.getDrugsSpecification() + " ");
                    }
                    if (busPrescriptionDrugs.size() > 0) {
                        stringBuffer.append("(" + split[1] + "剂);");
                    }
                } else if (CodeEnum.YES.getCode().equals(busPrescription.getPrescriptionType())) {
                    for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                        stringBuffer.append(item.getDrugsName() + "(" + item.getQuantity() + "件) ");
                    }
                    if (busPrescriptionDrugs.size() > 0) {
                        stringBuffer.append(";");
                    }
                } else if (CodeEnum.AGREE.getCode().equals(busPrescription.getPrescriptionType())) {
                    String usages = busPrescription.getUsages();
                    String[] split = usages.split(",");
                    BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(busPrescription.getPaId());
                    if (YesNoEnum.NO.getCode().equals(busHospitalPa.getType())) {
                        stringBuffer.append(busHospitalPa.getPaName());
                        stringBuffer.append("(" + split[1] + "剂);");
                    } else {
                        for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                            stringBuffer.append(item.getDrugsName() + item.getWeight() + item.getDrugsSpecification() + " ");
                        }
                        if (busPrescriptionDrugs.size() > 0) {
                            stringBuffer.append("(" + split[1] + "剂);");
                        }
                    }
                }
            } else {
                List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectList(new LambdaQueryWrapper<BusOtcDrugs>()
                        .eq(BusOtcDrugs::getDrugsOrderId, busDrugsOrder.getId())
                );
                for (BusOtcDrugs item : busOtcDrugs) {
                    stringBuffer.append(item.getDrugsName() + "(" + item.getQuantity() + "件) ");
                }
                if (busOtcDrugs.size() > 0) {
                    stringBuffer.append(";");
                }
            }
        } else {
            List<BusOrderShop> busOrderShops = busOrderShopMapper.selectList(new LambdaQueryWrapper<BusOrderShop>()
                    .eq(BusOrderShop::getOrderId, busOrder.getSubOrderId())
            );
            for (BusOrderShop item : busOrderShops) {
                stringBuffer.append(item.getShopName() + "(" + item.getQuantity() + "件) ");
            }
            if (busOrderShops.size() > 0) {
                stringBuffer.append(";");
            }
        }
        return "success";
    }

    /**
     * 订单商品信息导出
     * @param busSubOrderDTO
     * @param page
     * @param pageSizeNumber
     * @return
     */
    @Override
    public List<OrderShopExportVO> listExportOrderShop(BusSubOrderDTO busSubOrderDTO, int page, int pageSizeNumber) {
        //时间转换
        this.dataHandle(busSubOrderDTO);
        busSubOrderDTO.setPageNum(page * pageSizeNumber);
        busSubOrderDTO.setPageSize(pageSizeNumber);
        //联表查询
        List<BusOrderVO> busSubOrderVOS = busOrderMapper.listBusSubOrderPart(busSubOrderDTO);
        //制作一个线程安全的结果集
        List<OrderShopExportVO> resultVO = Collections.synchronizedList(new ArrayList<>());
        submitOrderShopFuture(busSubOrderVOS, resultVO);
        return resultVO;
    }

    /**
     * 并行提交线程任务
     * @param busSubOrderVOS
     * @param resultVO
     */
    private void submitOrderShopFuture(List<BusOrderVO> busSubOrderVOS, List<OrderShopExportVO> resultVO) {
        List<CompletableFuture<String>> completableFutureList = busSubOrderVOS.parallelStream().map(itemTop ->
                //每一个开启异步线程执行
                CompletableFuture.supplyAsync(() -> busSubOrderShopResult(resultVO, itemTop), orderExecutor)

        ).collect(Collectors.toList());
        //确认线程都执行完毕了
        confirmSucceed(completableFutureList);
    }

    /**
     * 确认线程集合中的所有线程都已经执行完毕了
     * @param completableFutureList
     */
    private static void confirmSucceed(List<CompletableFuture<String>> completableFutureList) {
        //并行确认是否线程都完成了
//        completableFutureList.stream().forEach(future -> {
//            if (!future.isDone()) {
//                future.join();
//            }
//        });
        //二次确认，确保每一个都完成了
        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[completableFutureList.size()])).join();
    }

    /**
     * 导出结果的赋值，返回成功还是失败
     * @param resultVO
     * @param itemTop
     * @return
     */
    private String busSubOrderShopResult(List<OrderShopExportVO> resultVO, BusOrderVO itemTop) {
        BusOrder busOrder = busOrderMapper.selectById(itemTop.getId());
        //查询不到直接结束该异步线程
        if (busOrder == null) {
            return "fail";
        }
        /*查询合作机构，合作渠道，合伙人信息*/
        OrderShopExportVO model = new OrderShopExportVO();
        ChannelPartnerVO channelsAndAgent = getChannelsAndAgent(busOrder);
        if (channelsAndAgent != null) {
            model.setAgentName(channelsAndAgent.getAgentName());
            model.setCooOrganization(channelsAndAgent.getCooOrganization());
            model.setCooperationChannels(channelsAndAgent.getChannelName());
        }
        model.setOrderStatus(OrderStatusEnum.getInfoByCode(busOrder.getOrderStatus()));
        model.setDeliveryType(CodeEnum.YES.getCode().equals(busOrder.getDeliveryType()) ? "快递发货" : "上门自提");
        //修复商品订单和药品订单显示错误
        model.setSubOrderType(CodeEnum.YES.getCode().equals(busOrder.getSubOrderType()) ? "商品订单" : "药品订单");
        model.setReceiver(busOrder.getReceiver());
        model.setReceivePhone(busOrder.getReceivePhone());
        model.setProvince(busOrder.getProvince());
        model.setCity(busOrder.getCity());
        model.setArea(busOrder.getArea());
        model.setDetailedAddress(busOrder.getDetailedAddress());
        model.setOrderNo(busOrder.getOrderNo());
        List<BusOrder> orders = itemTop.getOrders();
        if (orders == null || orders.size() == 0) {
            orders = new ArrayList<BusOrder>();
            orders.add(busOrder);
        }
        List<CompletableFuture<String>> completableFutureList = orders.parallelStream().map(orderItem -> CompletableFuture.supplyAsync(() -> {
            orderShopResult(resultVO, model, orderItem);
            return "success";
        }, orderCopyExecutor)).collect(Collectors.toList());
        //确认线程都执行完毕了
        confirmSucceed(completableFutureList);
        return "success";
    }

    /**
     * 对order商品详细导出结果的封装
     * @param resultVO
     * @param model
     * @param orderItem
     */
    private void orderShopResult(List<OrderShopExportVO> resultVO, OrderShopExportVO model, BusOrder orderItem) {
        OrderShopExportVO orderItemModel = OrikaUtils.convert(model, OrderShopExportVO.class);
        if (CodeEnum.NO.getCode().equals(orderItem.getSubOrderType())) {
            orderItemModel.setSubOrderType("药品订单");
            orderItemModel.setPaymentTime(orderItem.getPaymentTime());
            orderItemModel.setOrderTime(orderItem.getOrderTime());
            BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectById(orderItem.getSubOrderId());
            if (busDrugsOrder.getPrescriptionId() != null) {
                BusPrescription busPrescription = busPrescriptionMapper.selectById(busDrugsOrder.getPrescriptionId());
                List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
                        .eq(BusPrescriptionDrugs::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                );
                orderItemModel.setProcessPrice(busPrescription.getProcessPrice());
                //为啥是根据商品类型判断处方呢？？？类型只有0（药品），1（商品）
//                                if (PrescriptionTypeEnum.TCM.getCode().equals(Integer.valueOf(busOrder.getSubOrderType()))) {
//                                    orderItemModel.setPrType("中药处方");
//                                } else if (PrescriptionTypeEnum.MM.getCode().equals(Integer.valueOf(busOrder.getSubOrderType()))) {
//                                    orderItemModel.setPrType("西药处方");
//                                } else if (PrescriptionTypeEnum.ZYXDF.getCode().equals(Integer.valueOf(busOrder.getSubOrderType()))) {
//                                    orderItemModel.setPrType("中药方剂");
//                                }
                orderItemModel.setPrDoctor(busPrescription.getDoctorName());
                orderItemModel.setPrNumber(busPrescription.getPrescriptionNumber());
                // 中药协定方（中药方剂）
                if (CodeEnum.NO.getCode().equals(busPrescription.getPrescriptionType()) || CodeEnum.AGREE.getCode().equals(busPrescription.getPrescriptionType()) ) {
                    if (Objects.isNull(busPrescription.getPaId())) {
                        orderItemModel.setShopType("中药处方");
                        orderItemModel.setPrType("中药处方");
                    } else {
                        orderItemModel.setShopType("中药方剂");
                        orderItemModel.setPrType("中药方剂");
                    }
                    /*判断售后状态*/
                    BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                            .eq(BusOrderAfterSales::getOrderId, orderItem.getSubOrderId())
                            .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.NO.getCode())
                            .eq(BusOrderAfterSales::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                            .last("limit 1")
                    );
                    /*判断发货状态*/
                    BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                            .eq(BusDrugOrderPackage::getDrugsOrderId, orderItem.getSubOrderId())
                            .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                            .eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                            .last("limit 1")
                    );
                    if (busDrugOrderPackage != null && busDrugOrderPackage.getDeliveryTime() != null) {
                        orderItemModel.setDeliveryStatus("已发货");
                        orderItemModel.setDeliveryPeople(busDrugOrderPackage.getCreateBy());
                        orderItemModel.setDeliveryTime(busDrugOrderPackage.getDeliveryTime());
                        orderItemModel.setDeliveryNo(busDrugOrderPackage.getDeliveryNo());
                        orderItemModel.setDeliveryCorporation(busDrugOrderPackage.getLogisticsCompany());
                    } else {
                        orderItemModel.setDeliveryStatus("待发货");
                    }
                    if (busOrderAfterSales != null) {
                        if (CodeEnum.NO.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            orderItemModel.setShopRefundStatus("退款中");
                        } else if (CodeEnum.YES.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            orderItemModel.setShopRefundStatus("退款失败");
                        } else if (CodeEnum.DELETE.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            orderItemModel.setShopRefundStatus("退款成功");
                        }
                    }
                    String usages = busPrescription.getUsages();
                    String[] split = usages.split(",");
                    for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                        OrderShopExportVO result = OrikaUtils.convert(orderItemModel, OrderShopExportVO.class);
                        /*设置其他信息*/
                        if (item.getEnterpriseId() != null) {
                            String enterprise = busOrderMapper.getEnterprise(item.getEnterpriseId());
                            result.setSources(enterprise);
                        }
                        result.setShopName(item.getDrugsName());
                        BigDecimal count = new BigDecimal(split[1]);
                        BigDecimal weight = new BigDecimal(item.getWeight());
                        result.setShopId(item.getDrugsId());
                        result.setShopSpec(item.getDrugsSpecification());
                        // 商品售价 = 单价； 商品金额小计 = 单价 * 数量； 实付金额 = 商品金额小计；退款金额 = 实付金额；
                        if (Objects.isNull(busPrescription.getPaId())) {
                            BigDecimal multiply = count.multiply(weight).setScale(2, RoundingMode.HALF_UP);
                            result.setShopQuantity(multiply.stripTrailingZeros().toPlainString());
                            result.setShopAmount(item.getSellingPrice().setScale(4, RoundingMode.HALF_UP));
                            result.setShopSubtotal(item.getSellingPrice().multiply(multiply).setScale(2, RoundingMode.HALF_UP));
                        } else {
                            // 处理协定方 小计为协定方的钱，数量为剂数
                            result.setShopQuantity(count.stripTrailingZeros().toPlainString());
                            BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(busPrescription.getPaId());
                            BigDecimal paPrice = new BigDecimal(busHospitalPa.getAmount());
                            result.setShopAmount(paPrice.setScale(4, RoundingMode.HALF_UP));
                            result.setShopSubtotal(paPrice.multiply(count).setScale(2, RoundingMode.HALF_UP));
                        }
                        result.setRelPrice(busPrescription.getPrescriptionAmount());
                        result.setShopRefundAmount(result.getRelPrice());
                        resultVO.add(result);
                    }
                } else if (CodeEnum.YES.getCode().equals(busPrescription.getPrescriptionType())) {
                    orderItemModel.setPrType("西药处方");
                    orderItemModel.setShopType("西药处方");
                    /*判断售后状态*/
                    BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                            .eq(BusOrderAfterSales::getOrderId, orderItem.getSubOrderId())
                            .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.NO.getCode())
                            .eq(BusOrderAfterSales::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                            .last("limit 1")
                    );
                    if (busOrderAfterSales != null) {
                        if (CodeEnum.NO.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            orderItemModel.setShopRefundStatus("退款中");
                        } else if (CodeEnum.YES.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            orderItemModel.setShopRefundStatus("退款失败");
                        } else if (CodeEnum.DELETE.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            orderItemModel.setShopRefundStatus("退款成功");
                        }
                    }
                    for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                        OrderShopExportVO result = OrikaUtils.convert(orderItemModel, OrderShopExportVO.class);
                        /*判断发货状态*/
                        BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                                .eq(BusDrugOrderPackage::getDrugsOrderId, orderItem.getSubOrderId())
                                .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                                .eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrder.getPrescriptionId())
                                .eq(item.getEnterpriseId() == null, BusDrugOrderPackage::getDrugsGoodsId, item.getDrugsId())
                                .eq(item.getEnterpriseId() != null, BusDrugOrderPackage::getEnterpriseId, item.getEnterpriseId())
                                .last("limit 1")
                        );
                        if (busDrugOrderPackage!= null && busDrugOrderPackage.getDeliveryTime() != null) {
                            result.setDeliveryStatus("已发货");
                            result.setDeliveryPeople(busDrugOrderPackage.getCreateBy());
                            result.setDeliveryNo(busDrugOrderPackage.getDeliveryNo());
                            result.setDeliveryTime(busDrugOrderPackage.getDeliveryTime());
                            result.setDeliveryCorporation(busDrugOrderPackage.getLogisticsCompany());
                        } else {
                            result.setDeliveryStatus("待发货");
                        }

                        /*设置其他信息*/
                        if (item.getEnterpriseId() != null) {
                            String enterprise = busOrderMapper.getEnterprise(item.getEnterpriseId());
                            result.setSources(enterprise);
                        }
                        result.setShopName(item.getDrugsName());
                        result.setShopId(item.getDrugsId());
                        result.setShopSpec(item.getDrugsSpecification());
                        BigDecimal quantity = BigDecimal.valueOf(item.getQuantity());
                        // 商品售价 = 单价； 商品金额小计 = 单价 * 数量； 实付金额 = 商品金额小计；退款金额 = 实付金额；
                        result.setShopAmount(item.getSellingPrice().setScale(4, RoundingMode.HALF_UP));
                        result.setShopSubtotal(item.getSellingPrice().multiply(quantity).setScale(2, RoundingMode.HALF_UP));
                        result.setRelPrice(busPrescription.getPrescriptionAmount());
                        result.setShopRefundAmount(result.getRelPrice());
                        result.setShopQuantity(String.valueOf(item.getQuantity()));
                        resultVO.add(result);
                    }
                }
            } else {
                orderItemModel.setShopType("OTC药品");
                List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectList(new LambdaQueryWrapper<BusOtcDrugs>()
                        .eq(BusOtcDrugs::getDrugsOrderId, busDrugsOrder.getId())
                );
                for (BusOtcDrugs item : busOtcDrugs) {
                    OrderShopExportVO result = OrikaUtils.convert(orderItemModel, OrderShopExportVO.class);
                    /*判断售后状态*/
                    BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                            .eq(BusOrderAfterSales::getOrderId, orderItem.getSubOrderId())
                            .eq(BusOrderAfterSales::getGoodsId, item.getDrugsId())
                            .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.NO.getCode())
                            .last("limit 1")
                    );
                    /*判断发货状态 */
                    BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                            .eq(BusDrugOrderPackage::getDrugsOrderId, orderItem.getSubOrderId())
                            .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                            .eq( item.getEnterpriseId() == null, BusDrugOrderPackage::getDrugsGoodsId, item.getDrugsId())
                            .eq(item.getEnterpriseId() != null, BusDrugOrderPackage::getEnterpriseId, item.getEnterpriseId())
                            .last("limit 1")
                    );
                    if (busDrugOrderPackage != null && busDrugOrderPackage.getDeliveryTime() != null) {
                        result.setDeliveryStatus("已发货");
                        result.setDeliveryNo(busDrugOrderPackage.getDeliveryNo());
                        result.setDeliveryTime(busDrugOrderPackage.getDeliveryTime());
                        result.setDeliveryPeople(busDrugOrderPackage.getCreateBy());
                        result.setDeliveryCorporation(busDrugOrderPackage.getLogisticsCompany());
                    } else {
                        result.setDeliveryStatus("待发货");
                    }
                    if (busOrderAfterSales != null) {
                        result.setShopRefundAmount(busOrderAfterSales.getRefundAmount());
                        if (CodeEnum.NO.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            result.setShopRefundStatus("退款中");
                        } else if (CodeEnum.YES.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            result.setShopRefundStatus("退款失败");
                        } else if (CodeEnum.DELETE.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                            result.setShopRefundStatus("退款成功");
                        }
                    }
                    /*设置其他信息*/
                    if (item.getEnterpriseId() != null) {
                        String enterprise = busOrderMapper.getEnterprise(item.getEnterpriseId());
                        result.setSources(enterprise);
                    }
                    result.setShopName(item.getDrugsName());
                    result.setShopQuantity(String.valueOf(item.getQuantity()));
                    result.setShopSpec(item.getDrugsSpecification());
                    result.setShopId(item.getDrugsId());
                    BigDecimal quantity = BigDecimal.valueOf(item.getQuantity());
                    // 商品售价 = 单价； 商品金额小计 = 单价 * 数量； 实付金额 = 商品金额小计；退款金额 = 实付金额；
                    result.setShopAmount(item.getSellingPrice().setScale(4, RoundingMode.HALF_UP));
                    result.setShopSubtotal(item.getSellingPrice().multiply(quantity).setScale(2, RoundingMode.HALF_UP));
                    result.setRelPrice(result.getShopSubtotal());
                    result.setShopRefundAmount(result.getRelPrice());
                    resultVO.add(result);
                }
            }
        } else {
            orderItemModel.setShopType("商品");
            orderItemModel.setSubOrderType("商品订单");
            List<BusOrderShop> busOrderShops = busOrderShopMapper.selectList(new LambdaQueryWrapper<BusOrderShop>()
                    .eq(BusOrderShop::getOrderId, orderItem.getSubOrderId())
            );
            for (BusOrderShop item : busOrderShops) {
                OrderShopExportVO result = OrikaUtils.convert(orderItemModel, OrderShopExportVO.class);
                /*判断售后状态*/
                BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(new LambdaQueryWrapper<BusOrderAfterSales>()
                        .eq(BusOrderAfterSales::getOrderId, orderItem.getSubOrderId())
                        .eq(BusOrderAfterSales::getGoodsId, item.getShopId())
                        .eq(BusOrderAfterSales::getAfterSalesType, YesNoEnum.YES.getCode())
                        .last("limit 1")
                );
                /*判断发货状态*/
                BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                        .eq(BusDrugOrderPackage::getDrugsOrderId, orderItem.getSubOrderId())
                        .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.YES.getCode())
                        .eq(BusDrugOrderPackage::getDrugsGoodsId, item.getShopId())
                        .eq(item.getEnterpriseId() != null, BusDrugOrderPackage::getEnterpriseId, item.getEnterpriseId())
                        .last("limit 1")
                );
                if (busDrugOrderPackage != null && busDrugOrderPackage.getDeliveryTime() != null) {
                    result.setDeliveryStatus("已发货");
                    result.setDeliveryCorporation(busDrugOrderPackage.getLogisticsCompany());
                    result.setDeliveryNo(busDrugOrderPackage.getDeliveryNo());
                    result.setDeliveryTime(busDrugOrderPackage.getDeliveryTime());
                    result.setDeliveryPeople(busDrugOrderPackage.getCreateBy());
                } else {
                    result.setDeliveryStatus("待发货");
                }
                if (busOrderAfterSales != null) {
                    result.setShopRefundAmount(busOrderAfterSales.getRefundAmount());
                    if (CodeEnum.NO.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                        result.setShopRefundStatus("退款中");
                    } else if (CodeEnum.YES.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                        result.setShopRefundStatus("退款失败");
                    } else if (CodeEnum.DELETE.getCode().equals(busOrderAfterSales.getRefundStatus())) {
                        result.setShopRefundStatus("退款成功");
                    }
                }
                /*设置其他信息*/
                if (item.getEnterpriseId() != null) {
                    String enterprise = busOrderMapper.getEnterprise(item.getEnterpriseId());
                    result.setSources(enterprise);
                }
                String type = busOrderShopMapper.getGoodsTypeByTypeId(item.getShopId());
                if (type != null && type.equals("")) {
                    result.setGoodsType(type);
                }
                String referrerDoctorName = busOrderShopMapper.getReferrerDoctorName(item.getOrderId());
                if (StringUtils.isNotEmpty(referrerDoctorName)) {
                    result.setReferrerDoctorName(referrerDoctorName);
                }
                result.setShopName(item.getShopName());
                result.setShopQuantity(String.valueOf(item.getQuantity()));
                result.setShopSpec(item.getShopSpecification());
                result.setShopId(item.getShopId());
                BigDecimal quantity = BigDecimal.valueOf(item.getQuantity());
                // 商品售价 = 单价； 商品金额小计 = 单价 * 数量； 实付金额 = 商品金额小计；退款金额 = 实付金额；
                result.setShopAmount(item.getSellingPrice().setScale(4, RoundingMode.HALF_UP));
                result.setShopSubtotal(item.getSellingPrice().multiply(quantity).setScale(2, RoundingMode.HALF_UP));
                result.setRelPrice(result.getShopSubtotal());
                result.setShopRefundAmount(result.getRelPrice());
                result.setPaymentTime(orderItem.getPaymentTime());
                result.setOrderTime(orderItem.getOrderTime());
                resultVO.add(result);
            }
        }
    }

    @Override
    public Integer listExportCount(BusSubOrderDTO busSubOrderDTO) {
        //时间转换
        this.dataHandle(busSubOrderDTO);
        //联表查询
        List<BusOrderVO> busSubOrderVOS = busOrderMapper.listBusSubOrder(busSubOrderDTO);
        return busSubOrderVOS.size();
    }

    @Override
    public List<BusOrder> getOrderByTransactionId(String transactionId) {
        List<BusOrder> orders = busOrderMapper.getOrderByTransactionId(transactionId);
        if (orders.isEmpty()){
            log.error("订单不存在 transactionId:{}", transactionId);
            throw new ServiceException("订单不存在");
        }
        return orders;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean verify(String orderNo, String code, Boolean isVerify) {
        // 查询商品订单
        LambdaQueryWrapper<BusOrder> wrapper = Wrappers.lambdaQuery(BusOrder.class)
                .eq(BusOrder::getOrderNo, orderNo)
                .eq(BusOrder::getDeliveryType, DeliveryTypeEnum.PICKUP.getType()).last("limit 1");
        BusOrder order = busOrderMapper.selectOne(wrapper);
        if (order == null) {
            log.warn("订单不存在 orderNo={}", orderNo);
            throw new ServiceException("订单不存在");
        }
        if (OrderStatusEnum.isVerify(order.getOrderStatus())){
            log.warn("订单已核销 orderNo={}", orderNo);
            throw new ServiceException("订单已核销");
        }
        //查询核销码
        LambdaQueryWrapper<BusDrugOrderPackage> queryWrapper = Wrappers.lambdaQuery(BusDrugOrderPackage.class)
                .eq(BusDrugOrderPackage::getDrugsOrderId, order.getSubOrderId())
                .eq(BusDrugOrderPackage::getPackageType, order.getSubOrderType()).last("limit 1");
        BusDrugOrderPackage busDrugOrderPackage = busDrugOrderPackageMapper.selectOne(queryWrapper);
        if (busDrugOrderPackage == null){
            log.warn("订单未发货 orderNo={}", orderNo);
            throw new ServiceException("订单未发货");
        }
        //校验核销;
        if (isVerify){
            if (!(code.trim()).equals(busDrugOrderPackage.getCode())){
                log.warn("核销码错误 orderNo={} code={}", orderNo, code);
                throw new ServiceException("核销码错误");
            }
        }

        //更新订单状态
        order.setOrderStatus(OrderStatusEnum.FINISH.getCode());
        order.setCompleteTime(new Date());
        if (!SqlHelper.retBool(busOrderMapper.updateById(order))) {
            throw new ServiceException("更新订单状态失败");
        }
        OrderCompleteEvent orderCompleteEvent = new OrderCompleteEvent();
        orderCompleteEvent.setHospitalId(order.getHospitalId());
        orderCompleteEvent.setOrderType(order.getSubOrderType());
        orderCompleteEvent.setTotalOrderNo(order.getOrderNo());
        orderCompleteEvent.setJumpHomePage(Boolean.TRUE);
        publisher.publishEvent(orderCompleteEvent);
        return true;
    }

    /**
     * 查询合作机构，合作渠道，合伙人信息
     *
     * @param busOrder
     * @return
     */
    private ChannelPartnerVO getChannelsAndAgent(BusOrder busOrder) {
        ChannelPartnerVO result = new ChannelPartnerVO();
        BusPartners busPartners = busPartnersMapper.selectOne(new LambdaQueryWrapper<BusPartners>()
                .eq(BusPartners::getCode, busOrder.getPartnersCode())
                .eq(BusPartners::getHospitalId, busOrder.getHospitalId())
                .eq(BusPartners::getStatus, YesNoEnum.YES.getCode())
                .last("limit 1")
        );
        if (StringUtils.isNotNull(busPartners)) {
            result.setCooOrganization(busPartners.getFullName());
        }
        // 校验是否是合作渠道订单
        LambdaQueryWrapper<BusChannelOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusChannelOrder::getType, ChannelOrderEnum.TO.getCode());
        lambdaQuery.eq(BusChannelOrder::getOrderId, busOrder.getId());
        lambdaQuery.last("limit 1");
        BusChannelOrder busChannelOrder = busChannelOrderMapper.selectOne(lambdaQuery);
        if (StringUtils.isNotNull(busChannelOrder)) {
            result.setAgentName(busChannelOrder.getAgentName());
            result.setChannelName(busChannelOrder.getPartnerName());
        }
        return result;
    }

    /**
     * 处理前端传过来的时间
     *
     * @param busSubOrderDTO
     */
    private void dataHandle(BusSubOrderDTO busSubOrderDTO) {
        Integer timeType = busSubOrderDTO.getTimeType();
        if (timeType == null){
            return;
        }
        if (timeType.equals(1)) {
            List<String> orderTime = busSubOrderDTO.getOrderTime();
            if (StringUtils.isNotEmpty(orderTime) && orderTime.size() == 2) {
                busSubOrderDTO.setOrderTimeStart(orderTime.get(0) + " 00:00:00");
                busSubOrderDTO.setOrderTimeEnd(orderTime.get(1) + " 23:59:59");
            }
        } else if (timeType.equals(2)) {
            List<String> paymentTime = busSubOrderDTO.getPaymentTime();
            if (StringUtils.isNotEmpty(paymentTime) && paymentTime.size() == 2) {
                busSubOrderDTO.setPaymentTimeStart(paymentTime.get(0) + " 00:00:00");
                busSubOrderDTO.setPaymentTimeEnd(paymentTime.get(1) + " 23:59:59");
            }
        } else {
            List<String> completeTime = busSubOrderDTO.getCompleteTime();
            if (StringUtils.isNotEmpty(completeTime) && completeTime.size() == 2) {
                busSubOrderDTO.setCompleteTimeStart(completeTime.get(0) + " 00:00:00");
                busSubOrderDTO.setCompleteTimeEnd(completeTime.get(1) + " 23:59:59");
            }
        }
    }
}
package com.puree.hospital.order.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.order.api.enums.LogisticsStatusEnums;
import com.puree.hospital.order.api.enums.LogisticsTypeEnums;
import com.puree.hospital.order.assemble.DefaultOrderShippingInfoAssembler;
import com.puree.hospital.order.assemble.IOrderShippingInfoAssembler;
import com.puree.hospital.order.domain.BusOrder;
import com.puree.hospital.order.domain.UniAppSyncLogistics;
import com.puree.hospital.order.domain.dto.BusSubOrderDTO;
import com.puree.hospital.order.domain.dto.WXShippingInfoDTO;
import com.puree.hospital.order.service.IBusOrderService;
import com.puree.hospital.order.service.IUploadShippingService;
import com.puree.hospital.order.service.UniAppSyncLogisticsService;
import com.puree.hospital.order.mapper.UniAppSyncLogisticsMapper;
import com.puree.hospital.pay.api.RemotePayOrderService;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.tool.api.RemoteBaseWxService;
import com.puree.hospital.tool.api.model.WXShippingInfo;
import com.puree.hospital.tool.api.model.dto.WxAccessTokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.puree.hospital.order.api.constant.OrderTypeConstants.*;
import static com.puree.hospital.order.api.constant.OrderTypeConstants.DRUG_ORDER;

/**
 * 针对表【uni_app_sync_logistics】的数据库操作Service实现
 *
 * <AUTHOR>
 * @date 2025-02-27 17:40:48
 */
@Service
@Slf4j
public class UniAppSyncLogisticsServiceImpl extends ServiceImpl<UniAppSyncLogisticsMapper, UniAppSyncLogistics>
        implements UniAppSyncLogisticsService {

    @Lazy
    @Resource
    private UniAppSyncLogisticsService uniAppSyncLogisticsService;

    @Lazy
    @Resource
    private IBusOrderService busOrderService;

    @Lazy
    @Resource
    private IUploadShippingService uploadShippingService;

    @Resource
    private RemoteBaseWxService remoteBaseWxService;

    @Resource
    private Map<String, IOrderShippingInfoAssembler<?>> assembleMap;

    @Resource
    private RemotePayOrderService remotePayOrderService;

    @Resource
    private UniAppSyncLogisticsMapper uniAppSyncLogisticsMapper;

    @Override
    public WXShippingInfo getWechatShippingInfo(String orderNo, LogisticsTypeEnums logisticsType, String orderType) {
        IOrderShippingInfoAssembler<?> assemble = assembleMap.getOrDefault(orderType + IOrderShippingInfoAssembler.SUFFIX, new DefaultOrderShippingInfoAssembler());
        Optional<WXShippingInfo> optional = assemble.assemble(orderNo, logisticsType);
        if (!optional.isPresent()) {
            throw new ServiceException("获取物流信息失败");
        }
        return optional.get();
    }

    @Override
    public boolean uploadLogistics(String orderNo) {
        UniAppSyncLogistics uniAppSyncLogistics = uniAppSyncLogisticsService.getOne(Wrappers.<UniAppSyncLogistics>lambdaQuery().eq(UniAppSyncLogistics::getOrderNo, orderNo));
        return upload(uniAppSyncLogistics);
    }

    @Override
    public void uploadLogistics(UniAppSyncLogistics uniAppSyncLogistics) {
        // 保持原有的 uploadLogistics 方法
        upload(uniAppSyncLogistics);
    }

    @Override
    public boolean manualUploadLogistics(WXShippingInfoDTO wxShippingInfoDTO) {
        WxAccessTokenDTO wxAccessTokenDTO = new WxAccessTokenDTO();
        wxAccessTokenDTO.setHospitalId(wxAccessTokenDTO.getHospitalId());
        wxAccessTokenDTO.setClientType(ClientTypeEnum.WX_UNI_APP);
        R<String> result = remoteBaseWxService.getAccessToken(wxAccessTokenDTO, SecurityConstants.INNER);
        if (!result.isSuccess() || result.getData() == null){
            log.error("获取微信access_token失败！");
            return false;
        }
        String accessToken = result.getData();
        R<BusPayOrder> busPayOrderR = remotePayOrderService.queryPayOrder(wxShippingInfoDTO.getOrderNo());
        if (!busPayOrderR.isSuccess() || busPayOrderR.getData() == null) {
            log.error("获取订单信息失败！ orderNo: {}  {}",wxShippingInfoDTO.getOrderNo(),busPayOrderR.getMsg());
            return false;
        }
        BusPayOrder payOrder = busPayOrderR.getData();
        return uploadShippingService.upload(wxShippingInfoDTO.getOrderNo(),()-> WXShippingInfo.builder()
                .accessToken(accessToken)
                .orderKey(WXShippingInfo.OrderKey.builder()
                        .orderNumberType(2)
                        .transactionId(payOrder.getTransactionId()).build())
                .logisticsType(wxShippingInfoDTO.getLogisticsType())
                .deliveryMode(1)
                .shippingList(buildShippingList(wxShippingInfoDTO.getPackageList()))
                .uploadTime(ZonedDateTime.now(ZoneId.of("Asia/Shanghai")).toOffsetDateTime().toString())
                .payer(WXShippingInfo.Payer.builder().openid("openid").build()).build(),true);

    }

    @Override
    public Map<String, String> getOrderClientType(List<String> orderNos) {
        if (orderNos == null || orderNos.isEmpty()) {
            return Collections.emptyMap();
        }
        List<UniAppSyncLogistics> orderClientType = uniAppSyncLogisticsMapper.getOrderClientType(orderNos);
        Map<String, String> collect = orderClientType.stream().collect(Collectors.toMap(UniAppSyncLogistics::getOrderNo, e -> "uni_app"));
        for (String orderNo : orderNos) {
            if (!collect.containsKey(orderNo)){
                collect.put(orderNo,ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
            }
        }
        return collect;
    }

    private List<WXShippingInfo.Shipping> buildShippingList(List<BusSubOrderDTO> packageList) {
        List<WXShippingInfo.Shipping> shippings = new ArrayList<>();
        StringBuilder desc = new StringBuilder();
        packageList.forEach(e->{
            //todo 规格和数量 前端传过来 或者 后端查询
            desc.append(e.getDrugShopName()).append("(").append("规格").append(")").append("*").append("quantity");
        });
        WXShippingInfo.Shipping shipping = WXShippingInfo.Shipping.builder().itemDesc(desc.toString()).build();
        shippings.add(shipping);
        return shippings;
    }

    private boolean upload(UniAppSyncLogistics item) {
        if (item == null) {
            throw new ServiceException("物流信息不存在");
        }
        if (LogisticsStatusEnums.isUploaded(item.getStatus())) {
            throw new ServiceException("物流信息已上传,刷新后再试 ！");
        }
        if (LogisticsStatusEnums.isFailed(item.getStatus())) {
            throw new ServiceException(item.getFailMessage());
        }

        if (DRUG_ORDER.equals(item.getOrderType()) || SHOP_ORDER.equals(item.getOrderType())) {
            List<BusOrder> order = busOrderService.getOrderByNo(item.getOrderNo());
            for (BusOrder busOrder : order) {
                String drugsOrderNo = busOrder.getDrugsOrderNo();
                String goodsOrderNo = busOrder.getGoodsOrderNo();
                if (StringUtils.hasText(drugsOrderNo)) {
                    item.setOrderType(DRUG_ORDER);
                    if (!uploadToWechat(item, drugsOrderNo)) {
                        return false;
                    }
                }
                if (StringUtils.hasText(goodsOrderNo)) {
                    item.setOrderType(SHOP_ORDER);
                    if (!uploadToWechat(item, goodsOrderNo)) {
                        return false;
                    }
                }
            }
        } else if (!uploadToWechat(item, item.getOrderNo())) {
                return false;
        }
        //更新物流上传状态
        item.setStatus(LogisticsStatusEnums.UPLOADED.getCode());
        this.updateById(item);
        return true;
    }

    /**
     * 上传到 微信
     *
     * @param item 项目
     * @param orderNo 订单号
     * @return boolean
     */
    private boolean uploadToWechat(UniAppSyncLogistics item, String orderNo) {

       return uploadShippingService.upload(item.getOrderNo(), () -> {
            IOrderShippingInfoAssembler<?> assemble = assembleMap.getOrDefault(item.getOrderType() + IOrderShippingInfoAssembler.SUFFIX, new DefaultOrderShippingInfoAssembler());

            Optional<WXShippingInfo> optional = assemble.assemble(orderNo, item.getLogisticsType());
            if (!optional.isPresent()) {
                throw new ServiceException("获取物流信息失败");
            }
            WXShippingInfo wxShippingInfo = optional.get();
            log.info("shippingInfo: {}", JSONUtil.toJsonStr(wxShippingInfo));
            return wxShippingInfo;
        },false);

    }


}

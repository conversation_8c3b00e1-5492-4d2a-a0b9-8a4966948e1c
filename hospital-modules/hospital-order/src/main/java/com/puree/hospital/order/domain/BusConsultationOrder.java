package com.puree.hospital.order.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.Date;

/**
 * 问诊订单表   作废？？？
 */
@Data
public class BusConsultationOrder extends Entity {

    /** 问诊ID */
    private Long consultationId;
    /** 订单编号 */
    private String orderNo;
    /** 订单状态（0待支付,1待接诊 未使用,2问诊中 使用中, 3问诊中 已开处方,4已完成 已使用,5已失效,6已退号,7已退款,8已取消,9退款中） */
    private String status;
    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;
    /** 订单金额 */
    private String amount;
    /** 订单类型（0:图文;1:视频） */
    private String orderType;
    /** 问诊人ID */
    private Long familyId;
    /** 问诊人姓名 */
    private String familyName;
    /** 问诊人性别 */
    private String familySex;
    /** 问诊人年龄 */
    private String familyAge;
    /** 医生ID */
    private Long doctorId;
    /** 医生照片 */
    private String photo;
    /** 医生姓名 */
    private String doctorName;
    /** 医生职称 */
    private String title;
    /** 科室名称 */
    private String departmentName;
    /** 问诊回合 */
    private Integer round;
    /** 患者ID */
    private Long patientId;
    /** 科室ID */
    private Long departmentId;
    /** 医院ID */
    private Long hospitalId;
    /** 退号原因 */
    private String reason;
    /** 付款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;
    /**
     * 购买类型（0诊室内购买 1诊室外购买）
     */
    private String buyType;
    /**
     * 问诊类型（0挂科室 1挂医生）
     */
    private String consultationType;
    /**
     * 待接诊状态
     */
    private String preStatus;
    /**
     * 关注订单
     */
    private String follow;
    /**
     * 报到时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;
    /**
     * 视频问诊预约开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 视频问诊预约结束时间
     */
    private Date endTime;
    /**
     * 视频问诊订单状态
     * 0待支付,1待接诊 2问诊中(未开处方), 3问诊中(已开处方),4已完成,5已失效,6已退号,7已退款,8已取消,9退款中,10已预约(未报到),11已预约(已报到),12已过号
     */
    private String videoStatus;

    /**
     * 修改状态条件
     */
    @TableField(exist = false)
    private String updateStatus;

    @TableField(exist = false)
    private Long groupId;
    /**
     * 机构code
     */
    private String partnersCode;

    /**
     * 叫号是否过号
     */
    private Integer isGuoHao;
    /**
     * 取消时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;
    /**
     * 1表示通过预审
     */
    private Integer pass;

    /**
     *角色类型（0医生操作图文退号 1医助操作图文退号）
     */
    @TableField(exist = false)
    private Integer roleType;
    /**
     * 是否删除（0否 1是）
     */
    private String delStatus;
    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    /**
     * 退号时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawalTime;
    /**
     * 通联订单编号
     */
    private String tonglianTrxid;
    /**
     * 支付方式 0-微信支付 1-通联支付
     */
    private String payWay;
    /**
     * 被复诊的订单id
     */
    private Long furtherConsultationId;

    /**
     * 1-患者支付 ，2-医生赠送， 3-健康解读报告
     */
    private Integer payType ;
}

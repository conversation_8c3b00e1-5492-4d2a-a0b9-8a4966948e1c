package com.puree.hospital.his.controller;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.his.api.service.HisCreateArchivesService;
import com.puree.hospital.his.api.entity.HisPatientInfo;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoCreateResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoSaveDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 建档
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/creat-archives")
public class HisCreateArchivesController {
    private final HisCreateArchivesService hisCreateArchivesService;


    /**
     * 获取患者信息
     * @param patientName 姓名
     * @param idCardNo 身份证
     * @return 患者信息
     */
    @GetMapping("/patient-info")
    public AjaxResult<HisPatientInfo> getPatientInfo(@RequestParam("patientName")String patientName,
                                                     @RequestParam("idCardNo")String idCardNo) {
        log.info("获取患者信息，patientName：{}，idCardNo：{}", patientName, idCardNo);
        return AjaxResult.success(hisCreateArchivesService.getPatientInfo(patientName, idCardNo));
    }

    /**
     * 创建患者
     * @param dto 患者信息
     * @return patientID 以及 时间
     */
    @PostMapping("/patient")
    public AjaxResult<HisPatientInfoCreateResultDTO> createPatient(@RequestBody@Validated HisPatientInfoSaveDTO dto) {
        log.info("创建患者-请求参数：{}", dto);
        return AjaxResult.success(hisCreateArchivesService.createPatient(dto));
    }

    /**
     * 修改患者
     * @param dto 患者信息
     * @return 成功或失败
     */
    @PutMapping("/patient")
    public AjaxResult<Boolean> updatePatient(@RequestBody@Validated HisPatientInfoSaveDTO dto) {
        log.info("修改患者-请求参数：{}", dto);
        return AjaxResult.success(hisCreateArchivesService.updatePatient(dto));
    }


}

package com.puree.hospital.his.controller;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.his.api.entity.dto.HisOrderInfoDTO;
import com.puree.hospital.his.api.entity.dto.HisPayResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadResultDTO;
import com.puree.hospital.his.api.entity.dto.HisRefundDTO;
import com.puree.hospital.his.api.entity.dto.HisRefundResultDTO;
import com.puree.hospital.his.api.service.HisPayService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * his 支付/退款 控制器
 * <AUTHOR>
 * @date 2025-1-16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/pay")
public class HisPayController {
    private final HisPayService hisPayService;

    /**
     * 支付信息上报
     * @param dto 支付信息
     * @return 支付信息上传结果
     */
    @PostMapping("/upload")
    public AjaxResult<HisPayUploadResultDTO> payUpload(@RequestBody HisPayUploadDTO dto) {
        log.info("HIS缴费请求参数：{}", JSONObject.toJSONString(dto));
        return AjaxResult.success(hisPayService.pay(dto));
    }

    /**
     * 退费
     * @param dto 退费信息
     * @return 是否允许退费
     */
    @PostMapping("/refund")
    public AjaxResult<HisRefundResultDTO> refund(@RequestBody HisRefundDTO dto) {
        log.info("HIS退费请求参数：{}", dto);
        return AjaxResult.success(hisPayService.refund(dto));
    }


    /**
     * HIS 修改地址
     * @param dto 收货信息
     * @return 是否成功
     */
    @PostMapping("/modify-address")
    public AjaxResult<HisRefundResultDTO> modifyAddress(@RequestBody @Validated HisOrderInfoDTO dto) {
        log.info("HIS修改地址-请求参数：{}", dto);
        return AjaxResult.success(hisPayService.modifyAddress(dto));
    }

    /**
     * HIS 查询支付结果
     * @param dto 查询参数
     * @return 结果信息
     */
    @GetMapping("/result")
    public AjaxResult<Boolean> payResult(@Validated HisPayResultDTO dto) {
        log.info("HIS查询支付结果-请求参数：{}", dto);
        return AjaxResult.success(hisPayService.payResult(dto));
    }

    /**
     * HIS 查询发票信息
     * @param invoiceNo 发票号
     * @return 发票url
     */
    @GetMapping("/invoice")
    public AjaxResult<String> invoiceInfo(@RequestParam("invoiceNo") String invoiceNo) {
        log.info("HIS查询发票信息-请求参数：{}", invoiceNo);
        return AjaxResult.success(hisPayService.invoiceInfo(invoiceNo));
    }
}

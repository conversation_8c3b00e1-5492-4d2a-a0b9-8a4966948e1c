package com.puree.hospital.his.controller;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.his.api.entity.query.HisDrugInfoQuery;
import com.puree.hospital.his.api.service.HisBaseInfoService;
import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.entity.HisDrugInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 基础信息
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/base-info")
public class HisBaseInfoController {
    private final HisBaseInfoService hisBaseInfoService;

    /**
     * 通过 字典类型 获取字典列表
     * @param type 字典类型
     * @return 字典列表
     */
    @GetMapping("/dic-info/{type}")
    public AjaxResult<List<HisDicInfo>> getDicInfoByType(@PathVariable("type") String type) {
        return AjaxResult.success(hisBaseInfoService.getDicInfo(type));
    }

    /**
     * 通过 药品编码 获取药品信息
     * @param drugCode 药品编码
     * @return 药品信息
     */
    @GetMapping("/drug-info/{drugCode}")
    public AjaxResult<HisDrugInfo> getDrugInfoByCode(@PathVariable("drugCode") String drugCode) {
        return AjaxResult.success(hisBaseInfoService.getDrugInfoByCode(drugCode));
    }

    /**
     * 获取所有药品列表
     * @param hisDrugInfoQuery 查询参数
     * @return 药品列表
     */
    @GetMapping("/drug-list")
    public AjaxResult<List<HisDrugInfo>> getDrugList(HisDrugInfoQuery hisDrugInfoQuery) {
        return AjaxResult.success(hisBaseInfoService.getDrugInfoList(hisDrugInfoQuery));
    }



}

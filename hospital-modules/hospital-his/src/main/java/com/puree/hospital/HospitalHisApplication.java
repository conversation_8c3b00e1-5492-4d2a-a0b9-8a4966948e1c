package com.puree.hospital;

import com.puree.hospital.common.security.annotation.EnableCustomConfig;
import com.puree.hospital.common.security.annotation.EnableHospitalFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableCustomConfig
@EnableHospitalFeignClients
@SpringBootApplication
@EnableAsync
public class HospitalHisApplication {

    public static void main(String[] args) {
        System.setProperty("druid.mysql.usePingMethod", "false");
        SpringApplication.run(HospitalHisApplication.class, args);
        System.out.println("HIS 模块启动成功");
    }

}

package com.puree.hospital.his.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.his.api.common.HisResult;
import com.puree.hospital.his.api.common.SOAPActuator;
import com.puree.hospital.his.api.constant.BaseHisEnum;
import com.puree.hospital.his.api.constant.enums.HisIdCardTypeEnum;
import com.puree.hospital.his.api.exception.soap.HisPatientInfoIsNullException;
import com.puree.hospital.his.api.exception.soap.HisSOAPException;
import com.puree.hospital.his.api.service.HisCreateArchivesService;
import com.puree.hospital.his.api.entity.HisPatientInfo;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoCreateResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoSaveDTO;
import com.puree.hospital.his.api.entity.query.HisPatientInfoQuery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.xml.soap.SOAPException;

/**
 * 建档相关
 */
@Slf4j
@Service
@AllArgsConstructor
public class HisCreateArchivesServiceImpl implements HisCreateArchivesService {


    /**
     * 提供 身份证和姓名查询患者信息
     * @param patientName 姓名
     * @param idCardNo 身份证
     * @return 患者信息
     */
    @Override
    public HisPatientInfo getPatientInfo(String patientName, String idCardNo) {
        Assert.notEmpty(patientName, "患者姓名不能为空");
        Assert.notEmpty(idCardNo, "身份证号不能为空");
        HisPatientInfoQuery query = new HisPatientInfoQuery();
        query.setPatientName(patientName);
        query.setIDCardNO(idCardNo);
        try {
            HisResult hisResult = SOAPActuator.execute("QueryOutPatientInfoHLW", query);
            return JSON.parseObject(hisResult.getResult(), HisPatientInfo.class);
        }catch (HisPatientInfoIsNullException e){
            return null;
        } catch (SOAPException e) {
            log.error("查询患者信息失败, 错误: {}", e.getMessage());
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 创建患者
     * @param dto 患者信息
     * @return patientID 以及 时间
     */
    @Override
    public HisPatientInfoCreateResultDTO createPatient(HisPatientInfoSaveDTO dto) {
        //创建赋空值
        dto.setCardNO("");
        dictOurSideToHis(dto);
        try {
            HisResult hisResult = SOAPActuator.execute("CreateCardInfoHLW", dto);
            return JSON.parseObject(hisResult.getResult(), HisPatientInfoCreateResultDTO.class);
        } catch (SOAPException e) {
            log.error("创建患者信息失败, 错误: {}", e.getMessage());
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 字典数据转换 我方 转 HIS
     * @return dto 转换HIS字典后的患者信息
     */
    private static void dictOurSideToHis(HisPatientInfoSaveDTO dto){
        String mapping = BaseHisEnum.codeToMapping(HisIdCardTypeEnum.class, dto.getIdCardType());
        if (mapping != null){
            dto.setIdCardType(mapping);
        }
    }


    /**
     * 字典数据转换 HIS 转我方
     * @param info 转换我方字典后的患者信息
     */
    private void dictHisToOurSide(HisPatientInfo info){
        String mapping = BaseHisEnum.mappingToCode(HisIdCardTypeEnum.class, info.getIdCardType());
        if (mapping != null){
            info.setIdCardType(mapping);
        }
    }


    /**
     * 修改患者
     * @param dto 患者信息
     * @return 成功或失败
     */
    @Override
    public Boolean updatePatient(HisPatientInfoSaveDTO dto) {
        //参数校验
        Assert.notEmpty(dto.getCardNO(), "患者卡号不能为空");
        try {
            SOAPActuator.execute("UpdateCardInfo", dto);
            return true;
        } catch (SOAPException e) {
            log.error("修改患者信息失败, 错误: {}", e.getMessage());
            throw new HisSOAPException(e.getMessage());
        }
    }
}

package com.puree.hospital.his.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.his.api.common.HisBaseServiceImpl;
import com.puree.hospital.his.api.common.HisResult;
import com.puree.hospital.his.api.common.SOAPActuator;
import com.puree.hospital.his.api.constant.enums.HisPactCodeEnum;
import com.puree.hospital.his.api.constant.enums.HisUsageEnum;
import com.puree.hospital.his.api.entity.HisPrescriptionDetail;
import com.puree.hospital.his.api.entity.HisPrescriptionInfo;
import com.puree.hospital.his.api.entity.HisPrescriptionInfoSyncResult;
import com.puree.hospital.his.api.entity.dto.HisDiagnosisSyncDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionDelDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionInfoDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionInfoSyncDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionItemDTO;
import com.puree.hospital.his.api.entity.query.HisPrescriptionDetailQuery;
import com.puree.hospital.his.api.exception.soap.HisSOAPException;
import com.puree.hospital.his.api.service.HisPrescriptionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Service;

import javax.xml.soap.SOAPException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * his 处方
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class HisPrescriptionServiceImpl extends HisBaseServiceImpl implements HisPrescriptionService {

    private static final String PRESCRIPTION_DEL_METHOD = "DeleteRecipeSyn";
    private static final String RECIPE_SYN_METHOD = "RecipeSyn";
    private static final String WESTERN_MEDICINE_DEPT_CODE = "3003";
    private static final String WESTERN_MEDICINE_DEPT_NAME = "西药房(西区)";
    private static final String CHINESE_MEDICINE_DEPT_CODE = "3004";
    private static final String CHINESE_MEDICINE_DEPT_NAME = "中药房(西区)";
    private static final String PRESCRIPTION_DETAIL_METHOD = "QueryToPayDetail";
    private static final String DIAGNOSIS_SYNC_METHOD = "DiagnoseSyn";
    private static final String PRESCRIPTION_INFO_METHOD = "QueryRecipeDetail";

    /**
     * 诊查费信息同步
     * @param dto his诊查费信息
     * @return his诊查费信息同步结果
     */
    private String examinationFeeSync(HisPrescriptionInfoSyncDTO dto) {
        if(dto.getExaminationFeeItem() == null){
            return null;
        }
        log.info("开始同步诊查费信息: {}", JSON.toJSONString(dto));
        HisPrescriptionInfoSyncDTO examinationFeeDTO = BeanUtil.copyProperties(dto, HisPrescriptionInfoSyncDTO.class);
        // 转换协议代码
        pactTransform(examinationFeeDTO);
        HisPrescriptionItemDTO itemDTO = BeanUtil.copyProperties(dto.getExaminationFeeItem(), HisPrescriptionItemDTO.class);
        itemDTO.setExceDept(dto.getDeptCode());
        itemDTO.setExceDeptName(dto.getDeptName());
        examinationFeeDTO.setList(Arrays.asList(itemDTO));
        try {
            // 诊查费同步
            HisResult hisResult = SOAPActuator.execute(RECIPE_SYN_METHOD, examinationFeeDTO);
            log.info("诊查费同步结果信息: {}", hisResult);
            if(StringUtils.isEmpty(hisResult.getResult())){
                log.error("诊查费同步失败：{}", hisResult);
                throw new HisSOAPException(hisResult.getMessage());
            }
            return JSONObject.parseObject(hisResult.getResult()).getString("recipeNo");
        } catch (SOAPException e) {
            log.error("诊查费同步失败, 入参: {}, 错误: {}", JSON.toJSONString(dto), e.getMessage(), e);
            throw new HisSOAPException("诊查费同步失败: " + e.getMessage());
        }
    }

    /**
     * 处方信息同步
     * @param dto his 处方信息
     * @return his 处方信息同步结果
     */
    @Override
    public HisPrescriptionInfoSyncResult prescriptionInfoSync(HisPrescriptionInfoSyncDTO dto) {
        Assert.notNull(dto, "处方信息不能为空");
        Assert.notEmpty(dto.getList(), "处方明细不能为空");

        try {
            log.info("开始同步处方信息: {}", JSON.toJSONString(dto));

            // 转换协议代码
            pactTransform(dto);

            // 转换用药频率
            dto.getList().stream()
                    .filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setDrugsUsageValueCode(null);
                        // 不用转换，数据库已改为his的值
//                        item.setMedicationFrequencyRemarks(dictOurSideToHis(item.getMedicationFrequency(), HisFrequenceEnum.class));
                        item.setMedicationFrequencyRemarks(item.getMedicationFrequency());
                        //由于是 name 转换,目前只有这一个，就单独写
                        for (HisUsageEnum value : HisUsageEnum.values()) {
                            if (CharSequenceUtil.equals(value.getName(), item.getDrugsUsageValue())) {
                                item.setDrugsUsageValueCode(value.getMapping());
                            }
                        }
                        if(StringUtils.isEmpty(item.getDrugsUsageValueCode())){
                            log.error("用药途径编码无映射：{}", item);
                            throw new HisSOAPException("用药途径编码无映射: " + item.getDrugsUsageValue());
                        }
                    });

            HisResult hisResult = SOAPActuator.execute(RECIPE_SYN_METHOD, dto);
            HisPrescriptionInfoSyncResult result = new HisPrescriptionInfoSyncResult();
            result.setRecipeNo(JSONObject.parseObject(hisResult.getResult()).getString("recipeNo"));

            log.info("处方同步成功, 结果: {}", JSON.toJSONString(result));
            return result;
        } catch (SOAPException e) {
            log.error("处方同步失败, 入参: {}, 错误: {}", JSON.toJSONString(dto), e.getMessage(), e);
            throw new HisSOAPException("处方同步失败: " + e.getMessage());
        }
    }

    private void pactTransform(HisPrescriptionInfoSyncDTO dto){
        // 转换协议代码
        HisPactCodeEnum hisPactCodeEnum = dictOurSideToHisEnum(dto.getPactCode(), HisPactCodeEnum.class);
        dto.setPactCode(hisPactCodeEnum.getMapping());
        dto.setPactName(hisPactCodeEnum.getName());
    }

    /**
     * 删除处方
     * @param dto 请求参数
     * @return 是否删除成功
     */
    @Override
    public boolean delete(HisPrescriptionDelDTO dto) {
        try {
            HisResult hisResult = SOAPActuator.execute(PRESCRIPTION_DEL_METHOD, dto);
            return CharSequenceUtil.equals(hisResult.getMessage(), "0");
        } catch (SOAPException e) {
            log.error("HIS删除处方失败 - 参数: {}, 错误: {} ", dto, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 西药处方同步
     * @param dto his 处方信息
     * @return his 处方信息同步结果
     */
    @Override
    public HisPrescriptionInfoSyncResult prescriptionInfoWesternSync(HisPrescriptionInfoSyncDTO dto) {
        setDepartmentInfo(dto, WESTERN_MEDICINE_DEPT_CODE, WESTERN_MEDICINE_DEPT_NAME);

        //诊查费
        String examinationFeeRecipeNo = examinationFeeSync(dto);
        //处方
        HisPrescriptionInfoSyncResult syncResult = prescriptionInfoSync(dto);
        syncResult.setExaminationFeeRecipeNo(examinationFeeRecipeNo);
        return syncResult;
    }

    /**
     * 中药处方同步
     * @param dto his 处方信息
     * @return his 处方信息同步结果
     */
    @Override
    public HisPrescriptionInfoSyncResult prescriptionInfoChineseSync(HisPrescriptionInfoSyncDTO dto) {
        setDepartmentInfo(dto, CHINESE_MEDICINE_DEPT_CODE, CHINESE_MEDICINE_DEPT_NAME);
        return prescriptionInfoSync(dto);
    }

    /**
     * 设置科室信息
     * @param dto 处方信息
     * @param deptCode 科室代码
     * @param deptName 科室名称
     */
    private void setDepartmentInfo(HisPrescriptionInfoSyncDTO dto, String deptCode, String deptName) {
        Assert.notNull(dto, "处方信息不能为空");
        Assert.notEmpty(dto.getList(), "处方明细不能为空");

        dto.getList().stream()
                .filter(Objects::nonNull)
                .forEach(item -> {
                    item.setExceDept(deptCode);
                    item.setExceDeptName(deptName);
                });
    }

    /**
     * 查询处方费用明细
     * @param recipeNums 处方号
     * @return 处方明细
     */
    @Override
    public List<HisPrescriptionDetail> prescriptionDetail(String recipeNums) {
        Assert.notNull(recipeNums, "处方号不能为空");

        try {
            HisPrescriptionDetailQuery query = new HisPrescriptionDetailQuery();
            query.setRecipeSEQ(recipeNums);
            HisResult hisResult = SOAPActuator.execute(PRESCRIPTION_DETAIL_METHOD, query);
            List<HisPrescriptionDetail> details = handlePrescripDetailXML(hisResult.getResult());;
            log.info("HIS处方费用明细，处方号：{}，查询结果：{}", recipeNums, details);
            return details;
        } catch (SOAPException e) {
            log.error("HIS处方费用明细查询失败 - 参数: {}, 错误: {} ", recipeNums, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 处方明细处理
     * @param prescripDetailXML 处方明细字符串
     * @return 处方明细
     */
    private List<HisPrescriptionDetail> handlePrescripDetailXML(String prescripDetailXML) {
        Document document;
        try {
            document = DocumentHelper.parseText(prescripDetailXML);
        } catch (DocumentException e) {
            log.error("HIS处方明细处理失败 - 参数: {}, ", prescripDetailXML, e);
            throw new HisSOAPException(e.getMessage());
        }
        Element rootElement = document.getRootElement();
        List<Element> elements =  rootElement.elements("Item");
        return elements.stream().map(item -> {
            HisPrescriptionDetail detail = new HisPrescriptionDetail();
            detail.setRecipeSEQ(item.elementText("RecipeSEQ"));
            detail.setCost(item.elementText("Cost"));
            return detail;
        }).collect(Collectors.toList());
    }

    /**
     * 诊断信息同步
     * @param dto dto
     * @return 同步结果
     */
    @Override
    public Boolean diagnosisSync(HisDiagnosisSyncDTO dto) {
        Assert.notNull(dto, "诊断信息不能为空");
        try {
            HisResult hisResult = SOAPActuator.execute(DIAGNOSIS_SYNC_METHOD, dto);
            return CharSequenceUtil.equals(hisResult.getMessage(), "0");
        } catch (SOAPException e) {
            log.error("HIS诊断信息同步失败 - 参数: {}, 错误: {} ", dto, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 处方信息查询
     * @param dto dto
     * @return 处方信息
     */
    @Override
    public List<HisPrescriptionInfo> prescriptionInfo(HisPrescriptionInfoDTO dto) {
        Assert.notNull(dto, "处方信息查询参数不能为空");
        try {
            List<HisPrescriptionInfo> hisPrescriptionInfos = SOAPActuator.executeQueryAndConvertList(PRESCRIPTION_INFO_METHOD, dto, HisPrescriptionInfo.class);
            hisPrescriptionInfos.removeIf(info -> "其它".equals(info.getOrderTypeName()) || Objects.isNull(info.getOrderTypeName()));
            return hisPrescriptionInfos;
        } catch (Exception e) {
            log.error("HIS查询处方信息失败 - 参数: {}, 错误: {} ", dto, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

}



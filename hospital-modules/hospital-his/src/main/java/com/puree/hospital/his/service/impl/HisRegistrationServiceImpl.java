package com.puree.hospital.his.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.puree.hospital.his.helper.HisRegLevelCacheHelper;
import com.puree.hospital.his.api.common.HisBaseServiceImpl;
import com.puree.hospital.his.api.common.HisResult;
import com.puree.hospital.his.api.common.SOAPActuator;
import com.puree.hospital.his.api.constant.enums.HisPactCodeEnum;
import com.puree.hospital.his.api.constant.enums.HisPaymentModeEnum;
import com.puree.hospital.his.api.constant.enums.HisTitleLevelEnum;
import com.puree.hospital.his.api.entity.HisActiveDepartment;
import com.puree.hospital.his.api.entity.HisActiveDoctor;
import com.puree.hospital.his.api.entity.HisConsultationRegistration;
import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.entity.HisExamineReport;
import com.puree.hospital.his.api.entity.HisExamineReportDetail;
import com.puree.hospital.his.api.entity.HisInspectReport;
import com.puree.hospital.his.api.entity.HisInspectReportDetail;
import com.puree.hospital.his.api.entity.HisOutPatientRecord;
import com.puree.hospital.his.api.entity.HisRegisteredInfo;
import com.puree.hospital.his.api.entity.dto.HisConsultationCancellationDTO;
import com.puree.hospital.his.api.entity.dto.HisConsultationRegistrationDTO;
import com.puree.hospital.his.api.entity.query.HisActiveDepartmentListQuery;
import com.puree.hospital.his.api.entity.query.HisActiveDoctorListQuery;
import com.puree.hospital.his.api.entity.query.HisExamineReportQuery;
import com.puree.hospital.his.api.entity.query.HisInspectReportQuery;
import com.puree.hospital.his.api.entity.query.HisOutPatientRecordQuery;
import com.puree.hospital.his.api.entity.query.HisRegisteredInfoQuery;
import com.puree.hospital.his.api.exception.soap.HisActiveDoctorNullException;
import com.puree.hospital.his.api.exception.soap.HisRegLevelErrorException;
import com.puree.hospital.his.api.exception.soap.HisSOAPException;
import com.puree.hospital.his.api.service.HisRegistrationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.xml.soap.SOAPException;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 挂号相关
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class HisRegistrationServiceImpl extends HisBaseServiceImpl implements HisRegistrationService {

    private final HisRegLevelCacheHelper hisRegLevelCacheHelper;
    
    /**
     * SOAP方法常量
     */
    private static final class SOAPMethods {
        static final String QUERY_DEPARTMENT = "QueryDepartmentListHLW";
        static final String QUERY_DOCTOR = "QueryDoctorList";
        static final String CREATE_REGISTER = "CreateRegisterForHlw";
        static final String CANCEL_REGISTER = "CancleReg";
        static final String QUERY_REG_INFO = "QueryRegInfoListHLW";
        static final String OUT_PATIENT_RECORD = "OutpatientRecord";
        static final String EXAMINE = "QueryLisReport";
        static final String EXAMINE_DETAIL = "QueryLisReportDetail";
        static final String INSPECT = "QueryPacsReport";
        static final String INSPECT_DETAIL = "QueryPacsReportDetail";
    }

    /**
     * 执行SOAP查询并转换结果列表的通用方法
     * @param methodName SOAP方法名
     * @param query 查询参数
     * @param resultClass 结果类型
     * @return 查询结果列表
     * @throws HisSOAPException SOAP调用异常
     */
    private <T, Q> List<T> executeSOAPQuery(String methodName, Q query, Class<T> resultClass) {
        try {
            return SOAPActuator.executeQueryAndConvertList(methodName, query, resultClass);
        } catch (HisActiveDoctorNullException e) {
            log.debug("查询结果为空 - 方法: {}, 参数: {}", methodName, query);
            return Collections.emptyList();
        } catch (SOAPException e) {
            log.error("SOAP调用失败 - 方法: {}, 参数: {}, 错误: {}", methodName, query, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 根据日期获取出诊科室列表 不使用
     * @param date 日期
     * @return 出诊科室列表
     * @throws IllegalArgumentException 如果日期为空
     */
    @Override
    public List<HisActiveDepartment> getActiveDepartmentList(Date date) {
        Assert.notNull(date, "查询出诊科室列表的日期不能为空");

        HisActiveDepartmentListQuery query = new HisActiveDepartmentListQuery();
        query.setDate(DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));

        return executeSOAPQuery(SOAPMethods.QUERY_DEPARTMENT, query, HisActiveDepartment.class);
    }
    /**
     * 根据科室ID和日期查询出诊医生列表 - 不使用
     * @param departmentId 科室ID
     * @param date 日期
     * @return 出诊医生列表
     * @throws IllegalArgumentException 如果科室ID为空或日期为空
     */
    @Override
    public List<HisActiveDoctor> getActiveDoctorList(String departmentId, Date date) {
        Assert.notEmpty(departmentId, "查询出诊医生列表的科室ID不能为空");
        Assert.notNull(date, "查询出诊医生列表的日期不能为空");

        HisActiveDoctorListQuery query = new HisActiveDoctorListQuery();
        query.setDate(DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
        query.setDepartmentID(departmentId);

        return executeSOAPQuery(SOAPMethods.QUERY_DOCTOR, query, HisActiveDoctor.class);
    }

    /**
     * HIS挂号
     * @param dto 挂号信息
     * @return 挂号结果
     * @throws HisSOAPException SOAP调用异常
     */
    @Override
    public HisConsultationRegistration consultationRegistration(HisConsultationRegistrationDTO dto) {
        Assert.notNull(dto, "挂号信息不能为空");

        try {
            //字典转换
            dto.setPayTypeID(dictOurSideToHis(dto.getPayTypeID(), HisPaymentModeEnum.class));
            //由于是 name 转换,目前只有这一个，就单独写
            for (HisTitleLevelEnum value : HisTitleLevelEnum.values()) {
                if (CharSequenceUtil.equals(value.getName(), dto.getRegLevelName())) {
                    dto.setRevelCode(value.getMapping());
                }
            }
            HisPactCodeEnum hisPactCodeEnum = dictOurSideToHisEnum(dto.getPactCode(), HisPactCodeEnum.class);
            dto.setPactCode(hisPactCodeEnum.getMapping());
            dto.setPactName(hisPactCodeEnum.getName());

            return executeConsultationRegistrationSoap(dto);
        } catch (HisRegLevelErrorException ex) {
            // 重试
            log.info("HIS挂号失败 - 挂号级别字典不匹配，进行自动重试");
            hisRegLevelCacheHelper.reloadCacheDictInfo();
            try {
                return executeConsultationRegistrationSoap(dto);
            } catch (SOAPException e) {
                throw new HisSOAPException(e.getMessage());
            }
        } catch (SOAPException e) {
            log.error("HIS挂号失败 - 参数: {}, 错误: {}", dto, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     *  设置挂号级别，并执行SOAP挂号请求
     * @param dto   参数
     * @return  结果
     * @throws SOAPException
     */
    private HisConsultationRegistration executeConsultationRegistrationSoap(HisConsultationRegistrationDTO dto) throws SOAPException {
        //开发区医院挂号，同一走该编码 从缓存走，字典可能发生变化
//            dto.setRevelCode("12");
//            dto.setRegLevelName("互联网医院");
        HisDicInfo internetHospitalDictInfo = hisRegLevelCacheHelper.getInternetHospitalDictInfo();
        dto.setRevelCode(internetHospitalDictInfo.getCode());
        dto.setRegLevelName(internetHospitalDictInfo.getName());
        HisResult hisResult = SOAPActuator.execute(SOAPMethods.CREATE_REGISTER, dto);
        return JSON.parseObject(hisResult.getResult(), HisConsultationRegistration.class);
    }



    /**
     * HIS取消挂号
     * @param dto 取消挂号信息
     * @return 是否取消成功
     * @throws HisSOAPException SOAP调用异常
     */
    @Override
    public boolean consultationCancellation(HisConsultationCancellationDTO dto) {
        Assert.notNull(dto, "取消挂号信息不能为空");

        try {
            HisResult hisResult = SOAPActuator.execute(SOAPMethods.CANCEL_REGISTER, dto);
            return CharSequenceUtil.equals(hisResult.getMessage(), "0");
        } catch (SOAPException e) {
            log.error("HIS取消挂号失败 - 参数: {}, 错误: {}", dto, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 查询就诊记录
     * @param query 查询条件
     * @return 就诊记录列表
     * @throws HisSOAPException SOAP调用异常
     */
    @Override
    public List<HisRegisteredInfo> getRegisteredInfo(HisRegisteredInfoQuery query) {
        Assert.notNull(query, "查询条件不能为空");
        return executeSOAPQuery(SOAPMethods.QUERY_REG_INFO, query, HisRegisteredInfo.class);
    }

    /**
     * 查询门诊患者文本病历
     * @param query 查询条件
     * @return 患者病历
     */
    @Override
    public List<HisOutPatientRecord> getOutpatientRecord(HisOutPatientRecordQuery query) {
        Assert.notNull(query, "查询条件不能为空");
        try {
            HisResult hisResult = SOAPActuator.execute(SOAPMethods.OUT_PATIENT_RECORD, query);
            String result = hisResult.getResult();
            JSONArray jsonArray = JSON.parseArray(result);
            log.info("查询门诊患者文本病历-结果：{}", jsonArray);
            return jsonArray.toJavaList(HisOutPatientRecord.class);
        } catch (SOAPException e) {
            log.error("HIS查询门诊患者文本病历失败 - 参数: {}, 错误: {} ", query, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    @Override
    public List<HisExamineReport> getExamineList(HisExamineReportQuery query) {
        Assert.notNull(query, "查询条件不能为空");
        try {
            HisResult hisResult = SOAPActuator.execute(SOAPMethods.EXAMINE, query);
            log.info("结果信息：{}", hisResult);
            JSONArray jsonArray = JSON.parseArray(hisResult.getResult());
            return jsonArray.toJavaList(HisExamineReport.class);
        } catch (SOAPException e) {
            log.error("HIS查询检验列表失败 - 参数: {}, 错误: {} ", query, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    @Override
    public List<HisExamineReportDetail> getExamineDetail(HisExamineReportQuery query) {
        Assert.notNull(query, "查询条件不能为空");
        try {
            HisResult hisResult = SOAPActuator.execute(SOAPMethods.EXAMINE_DETAIL, query);
            log.info("结果信息：{}", hisResult);
            JSONArray jsonArray = JSON.parseArray(hisResult.getResult());
            return jsonArray.toJavaList(HisExamineReportDetail.class);
        } catch (SOAPException e) {
            log.error("HIS查询检验明细失败 - 参数: {}, 错误: {} ", query, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    @Override
    public List<HisInspectReport> getInspectList(HisInspectReportQuery query) {
        Assert.notNull(query, "查询条件不能为空");
        try {
            HisResult hisResult = SOAPActuator.execute(SOAPMethods.INSPECT, query);
            log.info("结果信息：{}", hisResult);
            JSONArray jsonArray = JSON.parseArray(hisResult.getResult());
            return jsonArray.toJavaList(HisInspectReport.class);
        } catch (SOAPException e) {
            log.error("HIS查询检查列表失败 - 参数: {}, 错误: {} ", query, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    @Override
    public List<HisInspectReportDetail> getInspectDetail(HisInspectReportQuery query) {
        Assert.notNull(query, "查询条件不能为空");
        try {
            HisResult hisResult = SOAPActuator.execute(SOAPMethods.INSPECT_DETAIL, query);
            log.info("结果信息：{}", hisResult);
            JSONArray jsonArray = JSON.parseArray(hisResult.getResult());
            return jsonArray.toJavaList(HisInspectReportDetail.class);
        } catch (SOAPException e) {
            log.error("HIS查询检查明细失败 - 参数: {}, 错误: {} ", query, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }
}
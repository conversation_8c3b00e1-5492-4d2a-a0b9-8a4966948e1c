package com.puree.hospital.his.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.api.enums.DeliveryTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.his.api.common.HisBaseServiceImpl;
import com.puree.hospital.his.api.common.HisResult;
import com.puree.hospital.his.api.common.SOAPActuator;
import com.puree.hospital.his.api.constant.enums.HisPactCodeEnum;
import com.puree.hospital.his.api.entity.dto.HisOrderInfoDTO;
import com.puree.hospital.his.api.entity.dto.HisPayResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadResultDTO;
import com.puree.hospital.his.api.entity.dto.HisRefundDTO;
import com.puree.hospital.his.api.entity.dto.HisRefundResultDTO;
import com.puree.hospital.his.api.entity.query.HisInvoiceQuery;
import com.puree.hospital.his.api.entity.query.HisPayResultQuery;
import com.puree.hospital.his.api.exception.soap.HisSOAPException;
import com.puree.hospital.his.api.service.HisPayService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.xml.soap.SOAPException;

/**
 * his 支付/退款 服务实现
 * <AUTHOR>
 * @date 2025-1-16
 */
@Slf4j
@Service
@AllArgsConstructor
public class HisPayServiceImpl extends HisBaseServiceImpl implements HisPayService {

    private static final String UPLOAD_METHOD = "DoPay";
    private static final String HIS_REFUND_METHOD = "Refund";
    private static final String HIS_MODIFY_ADDRESS = "RynOrderInfo";
    private static final String HIS_PAY_RESULT_METHOD = "QueryPayResult";
    private static final String HIS_INVOICE_METHOD = "QueryElcUrl";

    /**
     * 支付信息上报
     * @param dto 支付信息
     * @return 支付信息上传结果
     */
    @Override
    public HisPayUploadResultDTO pay(HisPayUploadDTO dto) {
        Assert.notNull(dto, "支付信息不能为空");
        try {
            // 转换合同编码字段
            HisPactCodeEnum hisPactCodeEnum = dictOurSideToHisEnum(dto.getPayType(), HisPactCodeEnum.class);
            dto.setPactCode(hisPactCodeEnum.getMapping());
            dto.setPactName(hisPactCodeEnum.getName());
            HisResult hisResult = SOAPActuator.execute(UPLOAD_METHOD, dto);
            log.info("HIS缴费信息上报-结果信息：{}", hisResult);
            return JSON.parseObject(hisResult.getResult(), HisPayUploadResultDTO.class);
        } catch (SOAPException e) {
            log.error("缴费信息上报失败, 错误: {} ", e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 退费
     * @param dto 退费信息
     * @return 是否允许退款
     */
    @Override
    public HisRefundResultDTO refund(HisRefundDTO dto) {
        Assert.notNull(dto, "退费信息不能为空");
        try {
            HisResult hisResult = SOAPActuator.execute(HIS_REFUND_METHOD, dto);
            log.info("HIS退费-结果信息：{}", hisResult);
            return new HisRefundResultDTO(CharSequenceUtil.equals(hisResult.getMessage(), "0"), hisResult.getError());
        } catch (SOAPException e) {
            log.error("退费失败 - 参数: {}, 错误: {} ", dto, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * HIS 修改地址
     * @param dto 收货信息
     * @return 是否成功
     */
    @Override
    public HisRefundResultDTO modifyAddress(HisOrderInfoDTO dto) {
        Assert.notNull(dto, "地址信息不能为空");
        checkReceiveInfo(dto);
        try {
            HisResult hisResult = SOAPActuator.execute(HIS_MODIFY_ADDRESS, dto);
            log.info("HIS 同步收货信息-结果信息：{}", hisResult);
            return new HisRefundResultDTO(CharSequenceUtil.equals(hisResult.getMessage(), "0"), hisResult.getError());
        } catch (SOAPException e) {
            log.error("同步收货信息 失败 - 参数: {}, 错误: {} ", dto, e.getMessage(), e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     *  检查收货信息 如果配送类型为配送市， 收货信息参数不能为空
     * @param dto
     */
    private void checkReceiveInfo(HisOrderInfoDTO dto) {
        if (DeliveryTypeEnum.isExpressDelivery(dto.getSendType())
                && StrUtil.hasBlank(dto.getName(), dto.getPhone(), dto.getDestProvince(), dto.getDestCity(), dto.getDestDistrict(), dto.getDestAddress())) {
            throw new ServiceException("地址信息不能为空");
        }
    }

    /**
     * HIS 查询支付结果
     * @param dto 查询参数
     * @return 结果信息
     */
    @Override
    public Boolean payResult(HisPayResultDTO dto) {
        Assert.notNull(dto, "查询参数不能为空");
        try {
            HisPayResultQuery query = new HisPayResultQuery();
            query.setPayOrdld(dto.getPayOrdId());
            HisResult hisResult = SOAPActuator.execute(HIS_PAY_RESULT_METHOD, query);
            log.info("HIS 查询支付结果：{}", hisResult);
            return true;
        } catch (SOAPException e) {
            log.error("查询支付结果 失败 - 参数: {} ", dto,  e);
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * HIS 查询发票信息
     * @param invoiceNo 发票号
     * @return 发票url
     */
    @Override
    public String invoiceInfo(String invoiceNo) {
        try {
            HisInvoiceQuery query = new HisInvoiceQuery(invoiceNo);
            HisResult hisResult = SOAPActuator.execute(HIS_INVOICE_METHOD, query);
            log.info("HIS 查询发票信息结果：{}", hisResult);
            return hisResult.getResult();
        } catch (SOAPException e) {
            log.error("查询支付结果 失败 - 参数: {} ", invoiceNo,  e);
            throw new HisSOAPException(e.getMessage());
        }
    }
}

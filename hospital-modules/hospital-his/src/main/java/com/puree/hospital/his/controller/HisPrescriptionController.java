package com.puree.hospital.his.controller;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.his.api.common.group.HisChineseMedicineGroup;
import com.puree.hospital.his.api.entity.HisPrescriptionDetail;
import com.puree.hospital.his.api.entity.HisPrescriptionInfo;
import com.puree.hospital.his.api.entity.HisPrescriptionInfoSyncResult;
import com.puree.hospital.his.api.entity.dto.HisDiagnosisSyncDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionDelDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionInfoDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionInfoSyncDTO;
import com.puree.hospital.his.api.service.HisPrescriptionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.groups.Default;
import java.util.List;

/**
 * his 处方 控制器
 * <AUTHOR>
 * @date 2025-2-6
 * 处方相关
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/prescription")
public class HisPrescriptionController {

    private final HisPrescriptionService hisPrescriptionService;

    /**
     * 西药 HIS 处方同步
     * @param dto dto
     * @return 挂号响应
     */
    @PostMapping("/western-sync")
    public AjaxResult<HisPrescriptionInfoSyncResult> prescriptionInfoWesternSync(@RequestBody@Validated(Default.class) HisPrescriptionInfoSyncDTO dto) {
        log.info("西药HIS处方同步 - 请求参数：{}", dto);
        return AjaxResult.success(hisPrescriptionService.prescriptionInfoWesternSync(dto));
    }

    /**
     * 中药 HIS 处方同步
     * @param dto dto
     * @return 挂号响应
     */
    @PostMapping("/chinese-sync")
    public AjaxResult<HisPrescriptionInfoSyncResult> prescriptionInfoChineseSync(@RequestBody@Validated({HisChineseMedicineGroup.class,Default.class}) HisPrescriptionInfoSyncDTO dto) {
        return AjaxResult.success(hisPrescriptionService.prescriptionInfoChineseSync(dto));
    }

    /**
     * 删除处方
     * @param dto 处方信息
     * @return 是否删除成功
     */
    @DeleteMapping
    public AjaxResult<Boolean> del(@RequestBody HisPrescriptionDelDTO dto) {
        log.info("HIS删除处方请求参数：{}", dto);
        return AjaxResult.success(hisPrescriptionService.delete(dto));
    }

    /**
     * 查询处方费用明细
     * @param recipeNums 处方号
     * @return 处方明细
     */
    @GetMapping("/detail")
    AjaxResult<List<HisPrescriptionDetail>> prescriptionDetail(@RequestParam("recipeNums") String recipeNums){
        return AjaxResult.success(hisPrescriptionService.prescriptionDetail(recipeNums));
    }

    /**
     * 诊断信息同步
     * @param dto dto
     * @return 同步结果
     */
    @PostMapping("/diagnosis-sync")
    public AjaxResult<Boolean> diagnosisSync(@Valid @RequestBody HisDiagnosisSyncDTO dto) {
        log.info("诊断信息同步-请求参数：{}", dto);
        return AjaxResult.success(hisPrescriptionService.diagnosisSync(dto));
    }

    /**
     * 处方信息查询
     * @param dto dto
     * @return 处方信息
     */
    @GetMapping("/info")
    public AjaxResult<List<HisPrescriptionInfo>> prescriptionInfo(HisPrescriptionInfoDTO dto) {
        log.info("处方信息查询-请求参数：{}", dto);
        List<HisPrescriptionInfo> list = hisPrescriptionService.prescriptionInfo(dto);
        return AjaxResult.success(list);
    }

}

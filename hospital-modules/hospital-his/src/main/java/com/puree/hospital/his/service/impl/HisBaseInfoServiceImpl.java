package com.puree.hospital.his.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.puree.hospital.his.api.common.HisResult;
import com.puree.hospital.his.api.common.SOAPActuator;
import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.entity.HisDrugInfo;
import com.puree.hospital.his.api.entity.query.HisDictQuery;
import com.puree.hospital.his.api.entity.query.HisDrugInfoQuery;
import com.puree.hospital.his.api.exception.soap.HisSOAPException;
import com.puree.hospital.his.api.service.HisBaseInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.xml.soap.SOAPException;
import java.util.List;

/**
 * 基础信息
 */
@Slf4j
@Service
@AllArgsConstructor
public class HisBaseInfoServiceImpl implements HisBaseInfoService {


    @Override
    public List<HisDicInfo> getDicInfo(String type) {
        Assert.notNull(type, "字典类型不能为空");
        try {
            HisDictQuery query = new HisDictQuery();
            query.setType(type);
            HisResult hisResult = SOAPActuator.execute("GetDicInfoForHlw", query);
            String result = hisResult.getResult();
            JSONArray objects = JSON.parseArray(result);
            return objects.toJavaList(HisDicInfo.class);
        } catch (SOAPException e) {
            log.error("查询字典信息失败, 错误: {}", e.getMessage());
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 通过药品代码获取药品信息
     * @param drugCode 药品代码
     * @return 药品信息
     */
    @Override
    public HisDrugInfo getDrugInfoByCode(String drugCode) {
        Assert.notNull(drugCode, "药品代码不能为空");
        HisDrugInfoQuery query = new HisDrugInfoQuery();
        query.setDrugCode(drugCode);
        List<HisDrugInfo> drugInfo = getDrugInfoList(query);
        if (CollUtil.isEmpty(drugInfo)) {
            return null;
        }
        //drugCode 只能查到一个
        return drugInfo.get(0);
    }

    /**
     * 获取药品信息
     * @param query 查询参数
     * @return 药品列表
     */
    @Override
    public List<HisDrugInfo> getDrugInfoList(HisDrugInfoQuery query) {
        try {
            HisResult hisResult = SOAPActuator.execute("GetDrugInfoForHlw", query);
            String result = hisResult.getResult();
            JSONArray objects = JSON.parseArray(result);
            return objects.toJavaList(HisDrugInfo.class);
        } catch (SOAPException e) {
            log.error("查询药品信息失败, 错误: {}", e.getMessage());
            throw new HisSOAPException(e.getMessage());
        }
    }

    /**
     * 获取药品列表
     * @return 药品列表
     */
    @Override
    public List<HisDrugInfo> getDrugInfoList() {
        return getDrugInfoList(new HisDrugInfoQuery());
    }
}

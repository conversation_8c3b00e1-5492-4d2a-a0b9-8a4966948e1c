package com.puree.hospital.his.helper;

import cn.hutool.core.collection.CollUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.his.api.constant.HisConstant;
import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.service.HisBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * HIS系统 RegLevel 级别字典缓存helper类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/5/21 15:07
 */
@Slf4j
@Component
public class HisRegLevelCacheHelper {

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private HisBaseInfoService hisBaseInfoService;

    /**
     *  缓存key
     */
    private final static String REG_LEVEL_KEY = "his:dict:regLevel";

    /**
     *  字典名称
     */
    private final static String DICT_NAME = "REGLEVEL";

    /**
     *  获取互联网医院字典信息
     * @return  HisDicInfo
     */
    public HisDicInfo getInternetHospitalDictInfo() {
        Object dictValue = redisTemplate.opsForHash().get(REG_LEVEL_KEY, HisConstant.USER_NAME);
        if (Objects.nonNull(dictValue)) {
            return (HisDicInfo) dictValue;
        }
        reloadCacheDictInfo();
        dictValue = redisTemplate.opsForHash().get(REG_LEVEL_KEY, HisConstant.USER_NAME);
        if (dictValue == null) {
            throw new ServiceException("获取挂号级别字典信息失败");
        }
        return (HisDicInfo) dictValue;
    }

    /**
     *  重新加载 “互联网医院” 字典值
     */
    public void reloadCacheDictInfo() {
        List<HisDicInfo> dicInfoList = hisBaseInfoService.getDicInfo(DICT_NAME);
        if (CollUtil.isEmpty(dicInfoList)) {
            throw new ServiceException("获取挂号级别字典信息失败");
        }
        for (HisDicInfo dicInfo : dicInfoList) {
            redisTemplate.opsForHash().put(REG_LEVEL_KEY, dicInfo.getName(), dicInfo);
        }
    }

}

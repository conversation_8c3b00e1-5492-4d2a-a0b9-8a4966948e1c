package com.puree.hospital.his.helper;

import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.his.api.common.HisPEResult;
import com.puree.hospital.his.api.common.PESOAPActuator;
import com.puree.hospital.his.api.entity.HisPhysicalExaminationData;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoQueryDTO;
import com.puree.hospital.his.api.entity.dto.QuestionAnswerContentDTO;
import com.puree.hospital.his.api.entity.dto.QuestionContentDTO;
import com.puree.hospital.his.api.entity.query.HisPhysicalExaminationPatientQuery;
import com.puree.hospital.his.api.entity.query.HisPhysicalExaminationTokenQuery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.xml.soap.SOAPException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 开区体检科 his 帮助类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/19 12:18
 */
@Slf4j
@Component
@AllArgsConstructor
public class KqPhysicalExaminationHisHelper {

    private final RedisService redisService;

    /**
     *  token key
     */
    private static final String PE_HIS_TOKEN_KEY = "third_tokens:pe_his_token";

    /**
     * token 过期时间  token有效期12个小时 现在缓存6个小时 6 * 60
     */
    private static final long TOKEN_EXPIRE_TIME = 360L;

    private static final String GET_TOKEN_URL = "Get_Token";

    /**
     *  根据姓名和电话号码查询患者信息
     */
    private static final String GET_PATIENT_INFO_URL = "Get_patient_by_nametel";

    /**
     *  发送问卷题目
     */
    private static final String SEND_WJINFO_TO_PEIS = "Send_WjInfo_To_Peis";

    /**
     * 问卷答案
     */
    private static final String SEND_WJDC_TO_PEIS = "Send_Wjdc_To_Peis";

    /**
     *  获取 token
     * @return  获取到的 token
     * @throws SOAPException    异常
     */
    public String getPeHisToken() throws SOAPException {
        HisPhysicalExaminationTokenQuery hisPhysicalExaminationTokenQuery = new HisPhysicalExaminationTokenQuery();
        HisPEResult result = PESOAPActuator.execute(GET_TOKEN_URL, hisPhysicalExaminationTokenQuery, PESOAPActuator.USER_INFO_PARAM, PESOAPActuator.SOAP_TOKEN_URL);
        return result.getToken();
    }

    /**
     *  获取 token
     * @return  优先从缓存里面读取
     * @throws SOAPException    请求异常
     */
    public String getAccessToken() throws SOAPException {
        String token = redisService.getCacheObject(PE_HIS_TOKEN_KEY);
        if (Objects.nonNull(token)) {
            return token;
        }
        token = getPeHisToken();
        redisService.setCacheObject(PE_HIS_TOKEN_KEY, token, TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);
        return token;
    }

    /**
     *  通过手机号和姓名获取患者信息
     * @param queryDTO          查询dto
     * @return                  his患者信息
     * @throws SOAPException    请求异常
     */
    public HisPEResult getPeHisPatient(HisPatientInfoQueryDTO queryDTO) throws SOAPException {
        HisPhysicalExaminationData<HisPhysicalExaminationPatientQuery> data = new HisPhysicalExaminationData<>();
        HisPhysicalExaminationPatientQuery hisPhysicalExaminationPatientQuery = new HisPhysicalExaminationPatientQuery();
        hisPhysicalExaminationPatientQuery.setName(queryDTO.getName());
        hisPhysicalExaminationPatientQuery.setTel(queryDTO.getPhone());
        hisPhysicalExaminationPatientQuery.setToken(getAccessToken());
        data.setData(hisPhysicalExaminationPatientQuery);
        return PESOAPActuator.execute(GET_PATIENT_INFO_URL, data, PESOAPActuator.PATIENT_INFO_PARAM);
    }

    /**
     *  同步问卷到开区体检科
     * @param questionContentDTOS  问卷内容
     * @return  结果
     * @throws SOAPException    请求异常
     */
    public HisPEResult sendQuestion(QuestionContentDTO questionContentDTOS) throws SOAPException {
        HisPhysicalExaminationData<QuestionContentDTO> data = new HisPhysicalExaminationData<>();
        data.setData(questionContentDTOS);
        Map<String, Object> param = new HashMap<>();
        param.put("token", getAccessToken());
        param.put("wjInfo_json", JSON.toJSONString(data));
        return PESOAPActuator.execute(SEND_WJINFO_TO_PEIS, param, PESOAPActuator.SOAP_BASE_URL);
    }

    /**
     *  同步问卷答案到开区体检科
     * @param questionAnswerContentDTO    答案
     * @return  结果
     */
    public HisPEResult sendQuestionAnswer(QuestionAnswerContentDTO questionAnswerContentDTO) throws SOAPException {
        HisPhysicalExaminationData<QuestionAnswerContentDTO> data = new HisPhysicalExaminationData<>();
        data.setData(questionAnswerContentDTO);
        Map<String, Object> param = new HashMap<>();
        param.put("token", getAccessToken());
        param.put("wjdc_json", JSON.toJSONString(data));
        return PESOAPActuator.execute(SEND_WJDC_TO_PEIS, param, PESOAPActuator.SOAP_BASE_URL);
    }
}

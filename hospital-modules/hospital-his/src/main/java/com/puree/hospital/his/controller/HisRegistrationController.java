package com.puree.hospital.his.controller;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.his.api.entity.HisConsultationRegistration;
import com.puree.hospital.his.api.entity.HisExamineReport;
import com.puree.hospital.his.api.entity.HisExamineReportDetail;
import com.puree.hospital.his.api.entity.HisInspectReport;
import com.puree.hospital.his.api.entity.HisInspectReportDetail;
import com.puree.hospital.his.api.entity.HisOutPatientRecord;
import com.puree.hospital.his.api.entity.HisRegisteredInfo;
import com.puree.hospital.his.api.entity.dto.HisConsultationCancellationDTO;
import com.puree.hospital.his.api.entity.dto.HisConsultationRegistrationDTO;
import com.puree.hospital.his.api.entity.query.HisExamineReportQuery;
import com.puree.hospital.his.api.entity.query.HisInspectReportQuery;
import com.puree.hospital.his.api.entity.query.HisOutPatientRecordQuery;
import com.puree.hospital.his.api.entity.query.HisRegisteredInfoQuery;
import com.puree.hospital.his.api.service.HisRegistrationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 挂号相关
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/registration")
public class HisRegistrationController {
    private final HisRegistrationService hisRegistrationService;

    /**
     * HIS 挂号
     * @param dto dto
     * @return 挂号响应
     */
    @PostMapping("/consultation-registration")
    public AjaxResult<HisConsultationRegistration> consultationRegistration(@RequestBody HisConsultationRegistrationDTO dto) {
        log.info("HIS 挂号-请求参数：{}", dto);
        return AjaxResult.success(hisRegistrationService.consultationRegistration(dto));
    }

    /**
     * HIS 取消挂号
     * @param dto dto
     * @return boolean
     */
    @PutMapping("/consultation-cancellation")
    public AjaxResult<Boolean> consultationCancellation(@RequestBody HisConsultationCancellationDTO dto) {
        return AjaxResult.success(hisRegistrationService.consultationCancellation(dto));
    }

    /**
     * 获取就诊记录
     * @param query 查询条件
     * @return 就诊记录
     */
    @GetMapping("/registered-info")
    public AjaxResult<List<HisRegisteredInfo>> getRegisteredInfo(HisRegisteredInfoQuery query) {
        return AjaxResult.success(hisRegistrationService.getRegisteredInfo(query));
    }

    /**
     * 查询门诊患者文本病历
     * @param query 查询条件
     * @return 患者病历
     */
    @GetMapping("/outpatient-record")
    public AjaxResult<List<HisOutPatientRecord>> getOutPatientRecord(HisOutPatientRecordQuery query) {
        log.info("查询门诊患者文本病历-请求参数：{}", query);
        return AjaxResult.success(hisRegistrationService.getOutpatientRecord(query));
    }


    /**
     * 获取检验报告
     * @param query 查询条件
     * @return 患者检验报告
     */
    @GetMapping("/examine")
    public AjaxResult<List<HisExamineReport>> getExamineList(HisExamineReportQuery query) {
        log.info("获取检验报告-查询参数：{}",  query);
        return AjaxResult.success(hisRegistrationService.getExamineList(query));
    }

    /**
     * 获取检验报告明细
     * @param query 查询条件
     * @return 患者检验报告明细
     */
    @GetMapping("/examine-detail")
    public AjaxResult<List<HisExamineReportDetail>> getExamineDetail(HisExamineReportQuery query) {
        log.info("获取检验报告明细-查询参数：{}",  query);
        return AjaxResult.success(hisRegistrationService.getExamineDetail(query));
    }

    /**
     * 获取检查报告
     * @param query 查询条件
     * @return 患者检查报告
     */
    @GetMapping("/inspect")
    public AjaxResult<List<HisInspectReport>> getInspectList(HisInspectReportQuery query) {
        log.info("获取检查报告-查询参数：{}",  query);
        return AjaxResult.success(hisRegistrationService.getInspectList(query));
    }

    /**
     * 获取检查报告明细
     * @param query 查询条件
     * @return 患者检查报告明细
     */
    @GetMapping("/inspect-detail")
    public AjaxResult<List<HisInspectReportDetail>> getInspectDetail(HisInspectReportQuery query) {
        log.info("获取检查报告明细-查询参数：{}",  query);
        return AjaxResult.success(hisRegistrationService.getInspectDetail(query));
    }

}

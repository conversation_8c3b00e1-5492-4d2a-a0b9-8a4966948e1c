package com.puree.hospital.his.controller;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.his.helper.KqPhysicalExaminationHisHelper;
import com.puree.hospital.his.api.common.HisPEResult;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoQueryDTO;
import com.puree.hospital.his.api.entity.dto.QuestionAnswerContentDTO;
import com.puree.hospital.his.api.entity.dto.QuestionContentDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.xml.soap.SOAPException;

/**
 * <p>
 * his 问卷控制器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/25 16:19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/question")
public class HisQuestionController {

    private final KqPhysicalExaminationHisHelper kqPhysicalExaminationHisHelper;

    /**
     *  获取患者信息
     * @param queryDTO  查询参数
     * @return  结果
     * @throws SOAPException
     */
    @PostMapping("/patient")
    public R<HisPEResult> getPeHisPatient(@RequestBody HisPatientInfoQueryDTO queryDTO) throws SOAPException {
        return R.ok(kqPhysicalExaminationHisHelper.getPeHisPatient(queryDTO));
    }

    /**
     *  同步问卷信息
     * @param questionContentDTOS   问卷内容
     * @return 结果
     * @throws SOAPException
     */
    @PostMapping("/send")
    public R<HisPEResult> sendQuestion(@RequestBody QuestionContentDTO questionContentDTOS) throws SOAPException {
        return R.ok(kqPhysicalExaminationHisHelper.sendQuestion(questionContentDTOS));
    }

    /**
     *  发送问卷答案
     * @param questionAnswerContentDTO 答案
     * @return  结果
     */
    @PostMapping("/send/answer")
    public R<HisPEResult> sendQuestionAnswer(@RequestBody QuestionAnswerContentDTO questionAnswerContentDTO) throws SOAPException {
        return R.ok(kqPhysicalExaminationHisHelper.sendQuestionAnswer(questionAnswerContentDTO));
    }
}

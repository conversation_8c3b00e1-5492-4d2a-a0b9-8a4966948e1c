package com.puree.hospital.his.prescription;

import com.alibaba.fastjson.JSON;
import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.api.common.HisResult;
import com.puree.hospital.his.api.common.SOAPActuator;
import com.puree.hospital.his.api.entity.HisPrescriptionInfoSyncResult;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionInfoSyncDTO;
import com.puree.hospital.his.api.service.HisPrescriptionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.xml.soap.SOAPException;
import java.util.HashMap;
import java.util.Map;

/**
 * 处方
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HospitalHisApplication.class)
public class HisPrescriptionTest {

    @Autowired
    private HisPrescriptionService hisPrescriptionService;

    /**
     * 处方同步
     */
    @Test
    public void RecipeSyn() throws SOAPException {
        String str =
                "{\"TranSerialNO\":\"3106178\",\n" +
//                "{\"TranSerialNO\":\"1234567\",\n" +
                "\"PactCode\":\"01\",\n" +
                "\"PactName\":\"自费\",\n" +
                "\"MedtType\":\"\",\n" +
                "\"MedtName\":\"\",\n" +
                "\"DiseCodg\":\"\",\n" +
                "\"DiseName\":\"\",\n" +
                "\"Icd10Code\":\"R05.x00\",\n" +
                "\"Icd10Name\":\"咳嗽\",\n" +
                "\"TradDiseaseCode\":\"\",\n" +
                "\"TradDiseaseName\":\"\",\n" +
                "\"TradSyndromCode\":\"\",\n" +
                "\"TradSyndromName\":\"\",\n" +
                "\"DeptCode\":\"1777\",\n" +
                "\"DeptName\":\"测试门诊\",\n" +
                "\"CardNo\":\"1000428123\",\n" +
                "\"OperCode\":\"009999\",\n" +
                "\"OperName\":\"信息科\",\n" +
                "\n" +
                "\"List\":[{\n" +
                "\"ItemNo\":\"F00000254741\",\n" +
                "\"ItemType\":\"0\",\n" +
                "\"QTY\":\"10.00\",\n" +
                "\"ExceDept\":\"1777\",\n" +
                "\"ExceDeptName\":\"测试门诊\",\n" +
                "\"singleDose\":\"\",\n" +
                "\"unit\":\"\",\n" +
                "\"medicationFrequency\":\"ONCE\",\n" +
                "\"medicationFrequencyRemarks\":\"一次\",\n" +
                "\"drugsUsageValue\":\"\",\n" +
                "\"drugsUsageValueCode\":\"\",\n" +
                "\"comboNo\":\"0\",\n" +
                "\"RangFlag\":\"0\",\n" +
                "\"HerbDecoMethodCode\":\"\",\n" +
                "\"HerbDecoMethodName\":\"\",\n" +
                "\"HerbMemo\":\"\",\n" +
                "\"DecoctingMethod\":\"\",\n" +
                "\"CourseSpan\":\"1\"\n" +
                "},\n" +
                "{\n" +
                "\"ItemNo\":\"Y00000300460\",\n" +
                "\"ItemType\":\"1\",\n" +
                "\"QTY\":\"60.00\",\n" +
                "\"ExceDept\":\"3003\",\n" +
                "\"ExceDeptName\":\"西药房\",\n" +
                "\"singleDose\":\"100.0000\",\n" +
                "\"unit\":\"mg\",\n" +
                "\"medicationFrequency\":\"QD\",\n" +
                "\"medicationFrequencyRemarks\":\"每日一次\",\n" +
                "\"drugsUsageValue\":\"口服\",\n" +
                "\"drugsUsageValueCode\":\"1\",\n" +
                "\"comboNo\":\"1\",\n" +
                "\"RangFlag\":\"0\",\n" +
                "\"HerbDecoMethodCode\":\"\",\n" +
                "\"HerbDecoMethodName\":\"\",\n" +
                "\"HerbMemo\":\"\",\n" +
                "\"DecoctingMethod\":\"\",\n" +
                "\"CourseSpan\":\"1\"\n" +
                "},\n" +
                "{\n" +
                "\"ItemNo\":\"Y00000302898\",\n" +
                "\"ItemType\":\"1\",\n" +
                "\"QTY\":\"70.00\",\n" +
                "\"ExceDept\":\"3004\",\n" +
                "\"ExceDeptName\":\"中药房\",\n" +
                "\"singleDose\":\"10.0000\",\n" +
                "\"unit\":\"g\",\n" +
                "\"medicationFrequency\":\"11\",\n" +
                "\"medicationFrequencyRemarks\":\"每日一剂\",\n" +
                "\"drugsUsageValue\":\"内服\",\n" +
                "\"drugsUsageValueCode\":\"1\",\n" +
                "\"comboNo\":\"2\",\n" +
                "\"RangFlag\":\"1\",\n" +
                "\"HerbDecoMethodCode\":\"221\",\n" +
                "\"HerbDecoMethodName\":\"自煎\",\n" +
                "\"HerbMemo\":\"水煎至200ml\",\n" +
                "\"DecoctingMethod\":\"\",\n" +
                "\"CourseSpan\":\"2\"\n" +
                "},\n" +
                "{\n" +
                "\"ItemNo\":\"Y00000303579\",\n" +
                "\"ItemType\":\"1\",\n" +
                "\"QTY\":\"70.00\",\n" +
                "\"ExceDept\":\"3004\",\n" +
                "\"ExceDeptName\":\"中药房\",\n" +
                "\"singleDose\":\"10.0000\",\n" +
                "\"unit\":\"g\",\n" +
                "\"medicationFrequency\":\"11\",\n" +
                "\"medicationFrequencyRemarks\":\"每日一剂\",\n" +
                "\"drugsUsageValue\":\"内服\",\n" +
                "\"drugsUsageValueCode\":\"1\",\n" +
                "\"comboNo\":\"2\",\n" +
                "\"RangFlag\":\"2\",\n" +
                "\"HerbDecoMethodCode\":\"221\",\n" +
                "\"HerbDecoMethodName\":\"自煎\",\n" +
                "\"HerbMemo\":\"水煎至200ml\",\n" +
                "\"DecoctingMethod\":\"\",\n" +
                "\"CourseSpan\":\"2\"\n" +
                "}]\n" +
                "}";
        HisPrescriptionInfoSyncDTO dto = JSON.parseObject(str, HisPrescriptionInfoSyncDTO.class);

        HisPrescriptionInfoSyncResult hisPrescriptionInfoSyncResult = hisPrescriptionService.prescriptionInfoSync(dto);
        log.info("hisPrescriptionInfoSyncResult: {}", hisPrescriptionInfoSyncResult);
    }

    @Test
    public void prescriptionInfo() throws SOAPException {
        //查询处方明细
        Map<String, String> params = new HashMap<>();
        params.put("TranSerialNO", "3106449"); //门诊流水号
        params.put("CardNO", "1000694280");
        params.put("StartDate", "2021-03-26 10:01:01");
        params.put("EndDate", "2025-03-26 10:01:01");

        HisResult hisResult = SOAPActuator.execute("QueryRecipeDetail", params);
        System.out.println("his 查询处方信息 返回：" + hisResult.getResult());
    }

    @Test
    public void prescriptionInfo1() throws SOAPException {
        //查询处方明细 检查检验治疗数据
        Map<String, String> params = new HashMap<>();
        params.put("TranSerialNO", "3106451"); //门诊流水号
        params.put("CardNO", "**********");
        params.put("StartDate", "2000-03-26 10:01:01");
        params.put("EndDate", "2025-04-26 10:01:01");

        HisResult hisResult = SOAPActuator.execute("QueryRecipeDetail", params);
        System.out.println("his 查询处方信息 返回：" + hisResult.getResult());
    }

    @Test
    public void pInfo() throws SOAPException {
        //查询处方明细 检查检验治疗数据
        Map<String, String> params = new HashMap<>();
        params.put("PatientID", "**********");
        params.put("StartDate", "2000-03-26 10:01:01");
        params.put("EndDate", "2025-04-26 10:01:01");

        HisResult hisResult = SOAPActuator.execute("QueryRegInfoListHLW", params);
        System.out.println("his 查询就诊记录 返回：" + hisResult.getResult());
    }

    @Test
    public void pInfo1() throws SOAPException {
        //患者信息查询
        Map<String, String> params = new HashMap<>();
        params.put("IDCardNO", "**********");
        params.put("PatientName", "");
        params.put("CardTypeID", "1");

        HisResult hisResult = SOAPActuator.execute("QueryOutPatientInfoHLW", params);
        System.out.println("his 患者信息查询 返回：" + hisResult.getResult());
    }

}

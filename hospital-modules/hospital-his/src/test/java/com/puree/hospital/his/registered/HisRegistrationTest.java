package com.puree.hospital.his.registered;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.api.entity.HisRegisteredInfo;
import com.puree.hospital.his.api.entity.query.HisRegisteredInfoQuery;
import com.puree.hospital.his.api.service.HisRegistrationService;
import com.puree.hospital.his.api.entity.HisActiveDepartment;
import com.puree.hospital.his.api.entity.HisActiveDoctor;
import com.puree.hospital.his.api.entity.HisConsultationRegistration;
import com.puree.hospital.his.api.entity.dto.HisConsultationCancellationDTO;
import com.puree.hospital.his.api.entity.dto.HisConsultationRegistrationDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.xml.soap.SOAPException;
import java.util.Date;
import java.util.List;

/**
 * HIS 挂号相关测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HospitalHisApplication.class)
public class HisRegistrationTest {

    @Autowired
    private HisRegistrationService hisRegistrationService;

    /**
     * 3.1.8 出诊科室列表
     * @throws SOAPException
     */
    @Test
    public void QueryDepartmentListHLW() {
        Date date = new Date();
        List<HisActiveDepartment> activeDepartmentList = hisRegistrationService.getActiveDepartmentList(date);
        log.info("activeDepartmentList: {}", activeDepartmentList);

    }

    /**
     * 3.1.9 查询出诊医生列表
     * @throws SOAPException
     */
    @Test
    public void QueryDoctorList() {
        Date date = new Date();
        //1046
        List<HisActiveDoctor> activeDoctorList = hisRegistrationService.getActiveDoctorList("1009", date);
        log.info("activeDoctorList: {}", JSON.toJSONString(activeDoctorList));

    }


    /**
     * 3.1.10 互联网医院挂号
     * @throws SOAPException
     */
    @Test
    public void CreateRegisterForHlw() throws SOAPException {
        HisConsultationRegistrationDTO dto = new HisConsultationRegistrationDTO();
        dto.setPatientId("**********");
        dto.setDeptCode("1009");
        dto.setDeptName("眼科门诊(西区)");
        dto.setDoctCode("Y0860");
        dto.setDoctName("钟德斌");
        dto.setRevelCode("3");
        dto.setRegLevelName("副主任医师");
        dto.setTotCost("10");
        dto.setPayTypeID("3");
        dto.setPOSTransNO("1111");
        dto.setUserID("HLW001");

        HisConsultationRegistration hisConsultationRegistration1 = hisRegistrationService.consultationRegistration(dto);

        log.info("CreateRegisterForHlw: {}", hisConsultationRegistration1);
    }

    /**
     * 3.1.11 互联网医院退号
     * @throws SOAPException
     */
    @Test
    public void CancleReg() throws SOAPException {
        HisConsultationCancellationDTO dto = new HisConsultationCancellationDTO();
        dto.setTranSerialNO("3106118");
        dto.setUserID("HLW001");

        boolean b = hisRegistrationService.consultationCancellation(dto);
        log.info("CancleReg: {}", b);
    }

    /**
     * 3.1.12 就诊记录查询
     */
    @Test
    public void QueryRegInfoListHLW() {
        HisRegisteredInfoQuery dto = new HisRegisteredInfoQuery();
        dto.setStartDate(new DateTime("2025-01-03 00:00:00").toString());
        dto.setEndDate(new DateTime("2025-01-03 23:59:59").toString());
        dto.setPatientID("**********");

        List<HisRegisteredInfo> registeredInfo = hisRegistrationService.getRegisteredInfo(dto);
        log.info("registeredInfo: {}", JSON.toJSONString(registeredInfo));

    }





}

package com.puree.hospital.his.enums;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.api.constant.enums.HisDosageFormEnum;
import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.service.HisBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <p>
 * 药品剂型
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/3 16:38
 */
@Slf4j
@SpringBootTest(classes = HospitalHisApplication.class)
public class HisDosageFormEnumTest {

    @Autowired
    private HisBaseInfoService hisBaseInfoService;

    @Test
    public void sql() {
        String sqlTemplate = "INSERT INTO `hospital_admin`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`) " +
                "VALUES ({}, 0, '{}', '{}', 'bus_drugs_dosage_form', NULL, 'default', 'N', '0', 'admin');";
        for (HisDosageFormEnum value : HisDosageFormEnum.values()) {
            System.out.println(StrUtil.format(sqlTemplate, value.getCode(), value.getName(), value.getMapping()));
        }
    }


    @Test
    public void generateEnum() {
        String template = "DOSEUNIT_{}(\"{}\", \"{}\", \"{}\", \"DOSEUNIT\"),";
        List<HisDicInfo> dicList = hisBaseInfoService.getDicInfo("DOSAGEFORM");
        log.info("dicList:{}", JSON.toJSONString(dicList));
        int index = 1000041;
        for (HisDicInfo dicInfo : dicList) {
            System.out.println(StrUtil.format(template, dicInfo.getCode(), index++, dicInfo.getCode(), dicInfo.getName()));
        }
    }

}

package com.puree.hospital.his.archives;

import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.api.service.HisCreateArchivesService;
import com.puree.hospital.his.api.entity.HisPatientInfo;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoCreateResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoSaveDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.xml.soap.SOAPException;

/**
 * 建档相关
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HospitalHisApplication.class)
public class HisCreateArchivesTest {

    @Autowired
    private HisCreateArchivesService hisCreateArchivesService;
    /**
     * 3.1.5 患者信息查询
     * @throws SOAPException
     */
    @Test
    public void QueryOutPatientInfoHLW(){
        HisPatientInfo patientInfo = hisCreateArchivesService.getPatientInfo("李测试78944", "******************");
        log.info("查询结果：{}",patientInfo);
    }

    /**
     * 建立档案信息
     * @throws SOAPException
     */
    @Test
    public void CreateCardInfoHLW(){

        HisPatientInfoSaveDTO dto = new HisPatientInfoSaveDTO();
        dto.setPatientName("李测试4");
        dto.setPatientSexID("1");
        dto.setBirthday("1976-05-16");
        dto.setIDCardNO("******************");
        dto.setIdCardType("01");
        dto.setAddress("广东省广州市");
        dto.setMobile("18724524127");
        dto.setLinkmanName("******");
        dto.setLinkmanTel("******");
        dto.setRelaCode("******");
        dto.setUserID("009999");

        HisPatientInfoCreateResultDTO patient = hisCreateArchivesService.createPatient(dto);
        log.info("创建结果：{}",patient);
    }

    /**
     * 更新患者档案信息
     * @throws SOAPException
     */
    @Test
    public void UpdateCardInfo() throws SOAPException {

        HisPatientInfoSaveDTO dto = new HisPatientInfoSaveDTO();
        dto.setCardNO("**********");
        dto.setPatientName("李测试789");
        dto.setPatientSexID("1");
        dto.setBirthday("1976-05-16");
        dto.setIDCardNO("******************");
        dto.setIdCardType("01");
        dto.setAddress("广东省广州市测试区测试街道测试楼");
        dto.setMobile("18724524127");
        dto.setLinkmanName("******");
        dto.setLinkmanTel("******");
        dto.setRelaCode("******");
        dto.setUserID("009999");

        Boolean patient = hisCreateArchivesService.updatePatient(dto);
        log.info("修改结果：{}",patient);
    }
}

package com.puree.hospital.his.base;

import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.helper.HisRegLevelCacheHelper;
import com.puree.hospital.his.api.entity.HisDicInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <p>
 *  测试类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/5/21 16:09
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HospitalHisApplication.class)
public class HisRegLevelCacheHelperTest {

    @Resource
    private HisRegLevelCacheHelper hisRegLevelCacheHelper;

    @Test
    public void testGetRegLevel() {
        HisDicInfo internetHospitalDictInfo = hisRegLevelCacheHelper.getInternetHospitalDictInfo();
        log.info("{}", internetHospitalDictInfo);
    }
}

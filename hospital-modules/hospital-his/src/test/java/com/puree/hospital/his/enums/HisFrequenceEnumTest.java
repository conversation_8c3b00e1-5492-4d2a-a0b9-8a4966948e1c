package com.puree.hospital.his.enums;

import cn.hutool.core.util.StrUtil;
import com.puree.hospital.his.api.constant.enums.HisFrequenceEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.HashMap;

/**
 * <p>
 * 频率
 * </p>
 *
 * <AUTHOR>
 * @date 2025/5/30 17:19
 */
@Slf4j
@ExtendWith(SpringExtension.class)
public class HisFrequenceEnumTest {

    private HashMap<String, String> codeMap = new HashMap<>();

    @BeforeEach
    public void init() {
            codeMap.put("bid", "01");
            codeMap.put("tid", "14");
            codeMap.put("qid", "10");
            codeMap.put("q12h", "04");
            codeMap.put("q1h", "05");
            codeMap.put("q3h", "06");
            codeMap.put("q6h", "07");
            codeMap.put("q8h", "08");
            codeMap.put("qd", "09");
            codeMap.put("Hs", "03");
            codeMap.put("qod", "11");
            codeMap.put("qw", "12");
            codeMap.put("st", "13");
            codeMap.put("biw", "02");
            codeMap.put("qn", "15");
            codeMap.put("其他", "99");
    }

    /**
     *  用药频率这样存储，监管代码兼容，不需要修改，需要修复老数据
     */
    @Test
    public void toSql() {
        String sqlTemplate = "INSERT INTO `hospital_admin`.`bus_dict_drugs_frequency` (`id`, `code`, `name`, `remark`, `create_by`, `create_time`) VALUES ({}, '{}', '{}', '{}', '', NULL);";
        System.out.println("================================ HIS 频率入库SQL ================================");
        Integer index = 100;
        for (HisFrequenceEnum value : HisFrequenceEnum.values()) {
            String code = value.getCode();
            if (StrUtil.isBlank(code)) {
                code = "其他";
            }
            System.out.println(StrUtil.format(sqlTemplate, index++, codeMap.get(code), value.getMapping(), value.getName()));
        }
        // 其他数据
//        System.out.println(StrUtil.format(sqlTemplate, index, "99", "其他", "其他"));
        System.out.println("================================ 历史数据修复SQL ================================");
        fixDataSql();
        System.out.println("================================================================================");
    }

    /**
     *  历史管理关系表修复数据 SQL
     */
    public void fixDataSql() {
        String sqlTemplate = "UPDATE `hospital_admin`.`bus_prescription_drugs` SET `medication_frequency` = '{}' WHERE `medication_frequency` = '{}';";
        for (HisFrequenceEnum value : HisFrequenceEnum.values()) {
            if (StrUtil.isBlank(value.getCode())) {
                continue;
            }
            System.out.println(StrUtil.format(sqlTemplate, value.getMapping(), value.getCode()));
        }
    }


}

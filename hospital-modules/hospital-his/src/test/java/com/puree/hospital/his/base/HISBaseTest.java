package com.puree.hospital.his.base;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.webservice.SoapClient;
import cn.hutool.http.webservice.SoapProtocol;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.api.common.HisResult;
import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.entity.HisDrugInfo;
import com.puree.hospital.his.api.service.HisBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;
import java.util.ArrayList;
import java.util.List;

/**
 * his 对接测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HospitalHisApplication.class)
public class HISBaseTest {

    @Autowired
    private HisBaseInfoService hisBaseInfoService;

    /**
     * 获取字典基本信息
     * @throws SOAPException
     */
    @Test
    public void GetDicInfoForHlw() throws SOAPException {
//        HisDicEnum[] values = HisDicEnum.values();
//        for (HisDicEnum value : values) {
//            List<HisDicInfo> dicList = getDicList(value.getCode());
//            log.info("dicList:{}",dicList);
//        }
//        List<HisDicInfo> dicList = getDicList("PACTCODE");
//        log.info("dicList:{}",JSON.toJSONString(dicList));
        List<HisDicInfo> dicList = hisBaseInfoService.getDicInfo("PACKUNIT");
        log.info("dicList:{}",JSON.toJSONString(dicList));
    }

    public List<HisDicInfo> getDicList(String type) throws SOAPException {
        SOAPMessage soapMessage = SoapClient.create("https://hospital-test.getddhospi.cn/his-api/OpenhitInternetHosService.asmx?op=GetDicInfoForHlw",
                        SoapProtocol.SOAP_1_2)
                .setMethod("GetDicInfoForHlw", "http://tempuri.org/")
                .setParam("contentXML", "{\"Type\":\""+type+"\"}")
                .sendForMessage();
        SOAPBody soapBody = soapMessage.getSOAPBody();
        // 提取SOAPBody内容为Document对象
        Document bodyContent = soapBody.extractContentAsDocument();

        // 获取Body中的所有子节点
        NodeList nodeList = bodyContent.getChildNodes();

        List<HisDicInfo> list = new ArrayList<>();
        // 遍历子节点并打印内容
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            String resultStr = node.getTextContent();
            HisResult hisResult = JSON.parseObject(resultStr, HisResult.class);
            String result = hisResult.getResult();
            JSONArray objects = JSON.parseArray(result);
            List<HisDicInfo> infoList = objects.toJavaList(HisDicInfo.class);
            list.addAll(infoList);
        }

        return list;
    }


    /**
     * 根据 药品编码获取药品信息
     * @throws SOAPException
     */
    @Test
    public void getDrugInfoByCode()  {
        HisDrugInfo info = hisBaseInfoService.getDrugInfoByCode("Y00000300075");
        log.info("info:{}",info);
        Assert.notNull(info);
    }

    /**
     * 查询药品列表
     */
    @Test
    public void getDrugInfoList()  {
        List<HisDrugInfo> list = hisBaseInfoService.getDrugInfoList();
        for (HisDrugInfo hisDrugInfo : list) {
            if (StrUtil.equals(hisDrugInfo.getDrugsName(),"喉疾灵胶囊")) {
                log.info("list:{}",JSON.toJSONString(hisDrugInfo));

            }
        }
        Assert.notEmpty(list);
    }




}

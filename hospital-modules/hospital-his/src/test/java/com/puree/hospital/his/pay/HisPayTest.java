package com.puree.hospital.his.pay;

import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.api.common.HisResult;
import com.puree.hospital.his.api.common.SOAPActuator;
import com.puree.hospital.his.api.entity.dto.HisOrderInfoDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.xml.soap.SOAPException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@RunWith(SpringRunner.class)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@SpringBootTest(classes = HospitalHisApplication.class)
public class HisPayTest {

    @Test
    public void payUpload() throws SOAPException {
        HisPayUploadDTO uploadDTO = new HisPayUploadDTO();
        uploadDTO.setRecipeSEQ("5032|5031");
        uploadDTO.setCardNO("1000694383");
        uploadDTO.setTotCost("0.05");
        uploadDTO.setPrepayCost("0");
        uploadDTO.setPubCost("0");
        uploadDTO.setRegisterNO("3160");
        uploadDTO.setUserID("HLW001");
        uploadDTO.setOwnCost("0.05");
        uploadDTO.setPayOrdId("4200002534202501136830577009");
        uploadDTO.assemblePayInfo(new BigDecimal("0.05"), null);
        HisResult result = SOAPActuator.execute("DoPay", uploadDTO);
        System.out.println("his支付结果上传：" + result);
        // (result=, message=-1, error=缴款金额与待缴处方汇总金额不一致！)

    }


    @Test
    public void refund() throws SOAPException {
        Map<String, String> param = new HashMap<>();
        param.put("InvoiceNO", "123456");
        param.put("RegisterNO", "001");
        param.put("UserID", "HLW001");

        HisResult result = SOAPActuator.execute("Refund", param);
        System.out.println("his退费结果返回：" + result);
        //(result=, message=-1, error=药房已发药，请跟药房确认退药后再进行退费操作！)

    }


    @Test
    public void delPrescription() throws SOAPException {
        Map<String, String> param = new HashMap<>();
        param.put("TranSerialNO", "001");
        param.put("RecipeNo", "20250113180935232");

        HisResult result = SOAPActuator.execute("DeleteRecipeSyn", param);
        System.out.println("his删除处方结果返回：" + result);


    }

    /**
     * 3.1.22 同步药品收货信息
     * @throws SOAPException
     */
    @Test
    public void RynOrderInfo() throws SOAPException {
        HisOrderInfoDTO dto = new HisOrderInfoDTO();


        HisResult result = SOAPActuator.execute("RynOrderInfo", dto);
        System.out.println("his删除处方结果返回：" + result);


    }

}
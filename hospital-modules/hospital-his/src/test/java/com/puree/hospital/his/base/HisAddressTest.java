package com.puree.hospital.his.base;

import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.api.entity.dto.HisOrderInfoDTO;
import com.puree.hospital.his.api.service.HisPayService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <p>
 * his 地址测试
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/7 18:27
 */
@SpringBootTest(classes = HospitalHisApplication.class)
public class HisAddressTest {

    @Resource
    private HisPayService hisPayService;

    @Test
    public void syncAddress() {
        HisOrderInfoDTO dto = new HisOrderInfoDTO();
        dto.setInvoiceNO("P00000000027");
        dto.setRegisterNO("5355515");
        dto.setName("孔林丽");
        dto.setPhone("13729874501");
        dto.setDestProvince("广东省");
        dto.setDestCity("广州市");
        dto.setDestDistrict("黄埔区");
        dto.setDestAddress("夏港街道普晖九街一号603");
        dto.setUserID("HLW01");
        dto.setType(1);
        dto.setSendType("1");
        hisPayService.modifyAddress(dto);
    }
}

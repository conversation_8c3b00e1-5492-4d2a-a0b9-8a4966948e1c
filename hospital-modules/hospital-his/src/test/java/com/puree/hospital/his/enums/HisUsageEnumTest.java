package com.puree.hospital.his.enums;

import cn.hutool.core.util.StrUtil;
import com.puree.hospital.his.api.constant.enums.HisUsageEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.HashMap;

/**
 * <p>
 * HIS 药品用法表
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/3 12:16
 */
@Slf4j
@ExtendWith(SpringExtension.class)
public class HisUsageEnumTest {

    private HashMap<String, String> codeMap = new HashMap<>();
    private HashMap<String, String> nameMap = new HashMap<>();
    private HashMap<Integer, Integer> idMap = new HashMap<>();

    @BeforeEach
    public void init() {
        codeMap.put("100", "1");
        codeMap.put("101", "612");
        codeMap.put("102", "607");
        codeMap.put("103", "608");
        codeMap.put("104", "401");
        codeMap.put("105", "402");
        codeMap.put("106", "403");
        codeMap.put("107", "404");
        codeMap.put("108", "5");
        codeMap.put("109", "6");
        codeMap.put("110", "601");
        codeMap.put("111", "602");
        codeMap.put("112", "603");
        codeMap.put("113", "604");
        codeMap.put("114", "605");
        codeMap.put("115", "606");
        codeMap.put("116", "3");
        codeMap.put("117", "4");
        codeMap.put("118", "609");
        codeMap.put("119", "610");
        codeMap.put("120", "611");
        codeMap.put("121", "2");
        codeMap.put("122", "699");
        codeMap.put("123", "9");

//        nameMap.put("口服", "口服");
        nameMap.put("擦皮肤", "外用");
//        nameMap.put("滴眼", "滴眼");
        nameMap.put("滴鼻", "喷鼻");
//        nameMap.put("皮下注射", "皮下注射");
//        nameMap.put("皮内注射", "皮内注射");
        nameMap.put("肌肉注射", "肌注");
        nameMap.put("静脉注射或静脉滴注", "静脉滴注[避光]");
//        nameMap.put("吸入给药", "吸入给药");
//        nameMap.put("局部用药", "局部用药");
//        nameMap.put("椎管内给药", "");
        nameMap.put("关节腔内给药", "宫腔内用药");
//        nameMap.put("胸膜腔给药", "");
//        nameMap.put("腹腔给药", "");
//        nameMap.put("气管内用药", "");
        nameMap.put("阴道用药", "阴道上药");
//        nameMap.put("舌下给药", "舌下含服");
        nameMap.put("注射给药", "0静推");
//        nameMap.put("喷喉", "喷喉");
        nameMap.put("含化", "含服");
        nameMap.put("敷伤口", "外贴");
//        nameMap.put("直肠给药", "直肠给药");
//        nameMap.put("其他局部给药途径", "");
//        nameMap.put("其他给药途径", "");

        idMap.put(100, 100);
        idMap.put(101, 101);
        idMap.put(102, 169);
        idMap.put(103, 167);
        idMap.put(104, 198);
        idMap.put(105, 287);
        idMap.put(106, 193);
        idMap.put(107, 197);
        idMap.put(108, 164);
        idMap.put(109, 165);
        idMap.put(111, 170);
        idMap.put(115, 149);
        idMap.put(116, 125);
        idMap.put(117, 279);
        idMap.put(118, 163);
        idMap.put(119, 126);
        idMap.put(120, 157);
        idMap.put(121, 154);
    }

    @Test
    public void toSql() {
        String sqlTemplate = "INSERT INTO `hospital_admin`.`bus_dict_drugs_usage` (`id`, `parent_id`, `ancestors`, `code`, `name`, `remark`, `create_by`, `create_time`) VALUES ({}, 0, '0', {}, '{}', '{}', '', NULL);";
        System.out.println("============================== HIS 给药途径入库SQL ==============================");
        for (HisUsageEnum value : HisUsageEnum.values()) {
            String code = value.getCode();
            if (StrUtil.isBlank(code)) {
                code = "123";
            }
            System.out.println(StrUtil.format(sqlTemplate, value.getMapping(), value.getMapping(), value.getName(), codeMap.get(code)));
        }
        // 其他数据
//        System.out.println(StrUtil.format(sqlTemplate, index++, "9", "其他给药途径"));
//        System.out.println(StrUtil.format(sqlTemplate, index, "699", "其他局部给药途径"));
        System.out.println("================================ 历史数据修复SQL ================================");
//        fixDataSql();
//        fixPrescriptionDrugsSql();
        System.out.println("================================================================================");
    }

    public void fixPrescriptionDrugsSql() {
        String sqlTemplate = "UPDATE `hospital_admin`.`bus_prescription_drugs` SET `drugs_usage_value` = '{}' WHERE `drugs_usage_value` = '{}';";
        nameMap.forEach((name, hisName) -> {
            System.out.println(StrUtil.format(sqlTemplate, name, hisName));
        });
    }

    /**
     *  历史管理关系表修复数据 SQL
     */
    public void fixDataSql() {
        String sqlTemplate = "UPDATE `hospital_admin`.`bus_drugs` SET `drugs_usage` = 100 WHERE `id` = 26494;";
        for (HisUsageEnum value : HisUsageEnum.values()) {
            if (StrUtil.isBlank(value.getCode())) {
                continue;
            }
            System.out.println(StrUtil.format(sqlTemplate, value.getCode(), value.getMapping()));
        }
    }


}

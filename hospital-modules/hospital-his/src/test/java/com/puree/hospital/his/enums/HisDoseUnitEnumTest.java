package com.puree.hospital.his.enums;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.HospitalHisApplication;
import com.puree.hospital.his.api.constant.enums.HisDrugPackUnitEnum;
import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.service.HisBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <p>
 * HIS 剂量单位
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/3 17:03
 */
@Slf4j
@SpringBootTest(classes = HospitalHisApplication.class)
public class HisDoseUnitEnumTest {

    @Autowired
    private HisBaseInfoService hisBaseInfoService;

    @Test
    public void sql() {
        String sqlTemplate = "INSERT INTO `hospital_admin`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`) " +
                "VALUES ({}, 0, '{}', '{}', 'bus_drugs_packaging_unit', NULL, 'default', 'N', '0', 'admin');";
        for (HisDrugPackUnitEnum value : HisDrugPackUnitEnum.values()) {
            System.out.println(StrUtil.format(sqlTemplate, value.getCode(), value.getName(), value.getMapping()));
        }
    }

    @Test
    public void generateEnum() {
//        String template = "MINUNIT_{}(\"{}\", \"{}\", \"{}\", \"PACKUNIT\"),";
        List<HisDicInfo> dicList = hisBaseInfoService.getDicInfo("DOSEUNIT");
        log.info("dicList:{}", JSON.toJSONString(dicList));
        int index = 1001000;
        String sqlTemplate = "INSERT INTO `hospital_admin`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`) " +
                "VALUES ({}, 0, '{}', '{}', 'bus_dosage_unit', NULL, 'default', 'N', '0', 'admin');";
        for (HisDicInfo dicInfo : dicList) {
            System.out.println(StrUtil.format(sqlTemplate, index++, dicInfo.getName(), dicInfo.getCode()));
        }
    }


}

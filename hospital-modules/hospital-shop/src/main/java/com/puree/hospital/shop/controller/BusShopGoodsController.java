package com.puree.hospital.shop.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.puree.hospital.app.api.RemoteSearchRecordService;
import com.puree.hospital.app.api.model.SearchRecordInsertDTO;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.common.core.enums.SearchTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.shop.api.model.dto.BusChannelCommissionDTO;
import com.puree.hospital.shop.api.model.vo.BusChannelCommissionVO;
import com.puree.hospital.shop.api.model.vo.BusCommissionVO;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.shop.domain.BusShopGoods;
import com.puree.hospital.shop.domain.dto.BusShopGoodsDTO;
import com.puree.hospital.shop.domain.dto.BusShopSearchDTO;
import com.puree.hospital.shop.domain.dto.ShopGoodsDTO;
import com.puree.hospital.shop.domain.dto.ShopSearchDTO;
import com.puree.hospital.shop.domain.vo.ShopGoodsVO;
import com.puree.hospital.shop.service.IBusShopGoodsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * 商品控制器
 * <AUTHOR>
 * @date 2023/1/10 14:54
 */
@RestController
@RequestMapping("/goods")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusShopGoodsController extends BaseController {
    private final IBusShopGoodsService goodsService;
    private final RemoteSearchRecordService remoteSearchRecordService;

    /**
     * 添加商品
     * @param goods 商品
     * @return 添加结果
     */
    @PostMapping
    public R<Integer> addGoods(@RequestBody BusShopGoods goods){
        return R.ok(goodsService.addGoods(goods));
    }

    /**
     * 关联配送企业商品
     * @param dto 入场
     * @return 添加结果
     */
    @PostMapping("/addEntGoods")
    public R<Integer> addEntGoods(@RequestBody BusShopGoodsDTO dto){
        return R.ok(goodsService.addEntGoods(dto));
    }

    /**
     * 患教商品同步（远程调用）
     * @param dto 入参
     * @return 成功同步的商品数
     */
    @PostMapping("/sync")
    public R<Integer> goodsSync(@RequestBody BusShopGoodsDTO dto){
        return R.ok(goodsService.goodsSync(dto));
    }

    /**
     * 患教关联商品的id转换为各医院的商品id（远程调用）
     * @param goodsIds 商品id列表
     * @param hospitalId 医院id
     * @return 转换后的商品id
     */
    @PostMapping("/ids-convert")
    public R<List<Long>> convertGoodsIds(@RequestBody List<Long> goodsIds, @RequestParam(value = "hospitalId") Long hospitalId){
        return R.ok(goodsService.convertGoodsIds(goodsIds, hospitalId));
    }
    /**
     * 查询商品详情
     * @param id 商品id
     * @return 商品
     */
    @GetMapping("/detail")
    public R<BusShopGoods> getDetail(Long id){
        return R.ok(goodsService.getDetail(id));
    }

    /**
     * 修改商品
     * @param goods 商品
     * @return 修改结果
     */
    @PutMapping
    public R<Integer> updateGoods(@RequestBody BusShopGoods goods){
        return R.ok(goodsService.updateGoods(goods));
    }

    /**
     * 修改商品库存
     * @param goods
     * @return 商品
     */
    @PostMapping("updateStock")
    public R<Integer> updateStock(@RequestBody BusShopGoods goods){
        int i = goodsService.updateStock(goods);
        if(i>0){
            return R.ok(i);
        }
        return R.fail();
    }

    /**
     * 修改商品状态
     * @param goods
     * @return 商品
     */
    @PostMapping("updateStatus")
    public R<Integer> updateStatus(@RequestBody BusShopGoods goods){
        int i = goodsService.updateStatus(goods);
        if(i>0){
            return R.ok();
        }
        return R.fail();
    }

//    /**
//     * 修改商品是否冷链状态
//     * @param goods
//     * @return 商品
//     */
//    @PostMapping("updateIsColdChain")
//    public R<Object> updateIsColdChain(@RequestBody BusShopGoods goods){
//        int i = goodsService.updateIsColdChain(goods);
//        if(i>0){
//            return R.ok();
//        }
//        return R.fail();
//    }

    /**
     * 更新是否热门
     * @param goods
     * @return
     */
    @PostMapping("updateHot")
    public R<Integer> updateHot(@RequestBody BusShopGoods goods){
        int i = goodsService.updateHot(goods);
        if(i>0){
            return R.ok();
        }
        return R.fail();
    }


    /**
     * 修改商品排序
     * @param goods
     * @return 商品
     */
    @PostMapping("setOrderNum")
    public R<Integer> setOrderNum(@RequestBody BusShopGoods goods){
        int i = goodsService.setOrderNum(goods);
        if(i>0){
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 删除商品
     * @param id
     * @return 商品
     */
    @GetMapping("delete")
    public R<Integer> delete(@RequestParam Long id){
        int i = goodsService.delete(id);
        if(i>0){
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 商品列表
     * @param goods @SpringQueryMap
     * @return 商品
     */
    @GetMapping("list")
    public R<TableDataInfo> list(@ModelAttribute BusShopGoodsDTO goods){
        startPage();
        List<BusShopGoods> shopGoods = goodsService.listBusShopGoods(goods);
        return R.ok(getDataTable(shopGoods));
    }

    /**
     * @Param goods
     * @Return com.puree.hospital.common.api.domain.AjaxResult<java.util.List<com.puree.hospital.shop.domain.BusShopGoods>>
     * @Description 根据ID排序获取商品
     * <AUTHOR>
     * @Date 2024/3/4 16:32
     **/
    @GetMapping("/ids")
    public AjaxResult<List<BusShopGoods>> getByIds(@ModelAttribute BusShopGoodsDTO goods){
        String goodsIds = goods.getGoodsIds();
        if(StringUtils.isNotEmpty(goodsIds)){
            //字符串分割
            List<Long> goodsIdList = StrUtil.split(goodsIds, StrUtil.C_COMMA, -1, true, (item) -> Long.parseLong(item));
            goods.setGoodsIdList(goodsIdList);
        }
        return AjaxResult.success(goodsService.getByIds(goods));
    }

    /**
     * 查询云仓未关联商品
     * @param goods 商品
     * @return 商品列表
     */
    @GetMapping("/listNotRelEnterpriseGoods")
    public R<TableDataInfo> listNotRelEnterpriseGoods(@ModelAttribute BusShopGoodsDTO goods){
        startPage();
        List<BusShopGoods> shopGoods = goodsService.listNotRelEnterpriseGoods(goods);
        return R.ok(getDataTable(shopGoods));
    }

    /**
     * order模块调用查询商品库存
     * @param paramsMap
     * @return
     */
    @PostMapping("/list/stock")
    public R<List<BusShopGoods>> listShopStock(@RequestBody HashMap<String, Object> paramsMap) {
        return R.ok(goodsService.listShopStock(paramsMap));
    }


    /**
     * app端通过标签获取商品列表
     * @param
     * @return
     */
    @GetMapping("/app/list")
    public  R<TableDataInfo> listAppShopGoods(BusShopGoodsDTO goods) {
        /*查询整张表记录的数量，union all 排序的问题*/
        goods.setLimit(goodsService.getShopGoodsCount());
        startPage();
        return R.ok(goodsService.listAppShopGoods(goods));
    }

    /**
     * app端通过通过分类获取商品列表
     * @param
     * @return
     */
    @Deprecated
    @GetMapping("/app/typeList")
    public  R<TableDataInfo> listAppShopTypeGoods(BusShopGoodsDTO goods) {
        /*查询整张表记录的数量，union all 排序的问题*/
        goods.setLimit(goodsService.getShopGoodsCount());
        startPage();
        return R.ok(goodsService.listAppShopTypeGoods(goods));
    }

    /**
     * 通过商品分类获取商品列表【2025】
     * @param
     * @return
     */
    @GetMapping("/list/type")
    public  TableDataInfo listAppShopTypeGood(@RequestParam(value = "typeId", required = false) Long typeId,
                                              @RequestParam("pageNum") Integer pageNum,
                                              @RequestParam("pageSize") Integer pageSize) {
        // 兼容typeId为空的情况，即一级分类下不存在二级分类的时候，加载空的商品列表。
        if (ObjectUtil.isEmpty(typeId)){
            return new TableDataInfo(new ArrayList<>(), 0, Constants.SUCCESS);
        }
        // 构造查询参数
        BusShopGoodsDTO dto = new BusShopGoodsDTO();
        Long hospitalId = SecurityUtils.getHospitalId();
        dto.setHospitalId(hospitalId);
        dto.setTypeId(typeId);
        dto.setPageNum(pageNum);
        dto.setPageSize(pageSize);
        /*查询整张表记录的数量，union all 排序的问题*/
        dto.setLimit(goodsService.getShopGoodsCount());
        startPage();
        return goodsService.listAppShopTypeGoods(dto);
    }

    /**
     * 商品搜索【2025】
     * @param shopSearchDTO 搜索参数
     * @return 商品列表 / 搜索提示
     */
    @Log(title = "商品搜索")
    @GetMapping("/search")
    public TableDataInfo goodDataList(@Valid ShopSearchDTO shopSearchDTO) {
        // 校验 hospitalId
        Long requestHospitalId = SecurityUtils.getHospitalId();
        if (ObjectUtil.isNull(requestHospitalId)){
            throw new ServiceException("请求头缺少hospitalId");
        }
        Integer queryType = Optional.ofNullable(shopSearchDTO.getQueryType()).orElse(0);
        String keyWord = shopSearchDTO.getKeyWord();
        // 封装参数
        BusShopSearchDTO dto = new BusShopSearchDTO();
        dto.setHospitalId(requestHospitalId);
        dto.setQueryType(queryType);
        dto.setKeyWord(keyWord);
        // 解析token，若登录，需要插入搜索记录
        String token = SecurityUtils.getToken();
        if (queryType==1 && ObjectUtil.isNotNull(token)) {
            SearchRecordInsertDTO insertDTO = new SearchRecordInsertDTO();
            insertDTO.setSearchValue(shopSearchDTO.getKeyWord());
            insertDTO.setSearchType(SearchTypeEnum.PRODUCT.getCode());
            remoteSearchRecordService.insertSearchRecord(insertDTO);
        }
        // 查询商品列表
        startPage();
        return getDataTable(goodsService.searchGood(dto));
    }


    /**
     * 远程调用查询商品信息
     * @param dto
     * @return
     */
    @PostMapping("/feign/list")
    public R<List<ShopGoodsVO>> listShopGoods(@RequestBody ShopGoodsDTO dto) {
        return R.ok(goodsService.listShopGoods(dto));
    }

    /**
     * 远程调用查询多规格商品
     *
     * @param hospitalId 医院id
     * @param goodName 商品名
     * @return 查询多规格商品
     */
    @GetMapping("/getMultSpecGoods")
    public R<List<BusShopGoods>> getMultSpecGoods(@RequestParam(value = "hospitalId") Long hospitalId,
                                      @RequestParam(value = "goodName") String goodName) {
        return R.ok(goodsService.getMultSpecGoods(hospitalId,goodName));
    }

    /**
     * 批量查询渠道and合伙人佣金比例 内部调用接口
     *
     * @param hospitalId 医院 id
     * @param goodsIds 商品 id
     * @return 渠道和合伙人佣金比例
     */
    @GetMapping("/feign/query-commission-rate")
    public R<List<BusChannelCommissionVO>> queryCommissionRate(@RequestParam(value = "hospitalId") Long hospitalId,
                                                               @RequestParam("goodsIds") List<Long> goodsIds) {
        return R.ok(goodsService.queryCommissionRate(hospitalId, goodsIds));
    }

    /**
     * 查询商城/商品返佣比例详情
     *
     * @param hospitalId 医院 id
     * @param goodsId 商品 id
     * @return 商城/商品返佣比例详情
     */
    @GetMapping("/query-commission-rate")
    public R<BusCommissionVO> queryCommissionRateInfo(@RequestParam(value = "hospitalId") Long hospitalId,
                                                      @RequestParam(value = "goodsId", required = false) Long goodsId,
                                                      @RequestParam(value = "flag", required = false) String flag) {
        return R.ok(goodsService.queryCommissionRateInfo(hospitalId, goodsId, flag));
    }

    /**
     * 新增或修改渠道和合伙人佣金比例
     *
     * @param commissionDTO 入参
     * @return 添加结果
     */
    @PostMapping("/update-commission-rate")
    public R<Integer> updateCommissionRate(@RequestBody BusChannelCommissionDTO commissionDTO) {
        return R.ok(goodsService.updateOrInsertCommissionRate(commissionDTO));
    }

}

package com.puree.hospital.shop.service;

import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.shop.api.model.dto.BusChannelCommissionDTO;
import com.puree.hospital.shop.api.model.vo.BusChannelCommissionVO;
import com.puree.hospital.shop.api.model.vo.BusCommissionVO;
import com.puree.hospital.shop.domain.BusShopGoods;
import com.puree.hospital.shop.domain.BusShopLabel;
import com.puree.hospital.shop.domain.dto.BusShopGoodsDTO;
import com.puree.hospital.shop.domain.dto.BusShopSearchDTO;
import com.puree.hospital.shop.domain.dto.ShopGoodsDTO;
import com.puree.hospital.shop.domain.vo.ShopGoodSearchVO;
import com.puree.hospital.shop.domain.vo.ShopGoodsVO;

import java.util.HashMap;
import java.util.List;

/**
 * 商品接口
 *
 * <AUTHOR>
 * @date 2023/1/10 15:18
 */
public interface IBusShopGoodsService {


    /**
     * 添加商品
     *
     * @param goods 商品
     * @return 添加结果
     */
    int addGoods(BusShopGoods goods);

    /**
     * 商品详情
     *
     * @param id 商品id
     * @return 商品
     */
    BusShopGoods getDetail(Long id);

    /**
     * 修改商品
     *
     * @param goods 商品
     * @return 修改结果
     */
    int updateGoods(BusShopGoods goods);

    /**
     * 商品列表
     *
     * @param goods
     * @return
     */
    List<BusShopGoods> listBusShopGoods(BusShopGoodsDTO goods);

    /**
     * 删除商品
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 更新库存
     *
     * @param goods
     * @return
     */
    int updateStock(BusShopGoods goods);

    /**
     * 更新状态
     *
     * @param goods
     * @return
     */
    int updateStatus(BusShopGoods goods);

    /**
     * 更新是否热门
     *
     * @param goods
     * @return
     */
    int updateHot(BusShopGoods goods);

    /**
     * 查询配送企业未关联商品
     *
     * @param goods 商品
     * @return 商品列表
     */
    List<BusShopGoods> listNotRelEnterpriseGoods(BusShopGoodsDTO goods);

    /**
     * 关联配送企业商品
     *
     * @param dto 入参
     * @return 添加结果
     */
    int addEntGoods(BusShopGoodsDTO dto);

    /**
     * order模块调用查询商品库存
     *
     * @param paramsMap
     * @return
     */
    List<BusShopGoods> listShopStock(HashMap<String, Object> paramsMap);

    /**
     * 院内商品新增，编辑时修改商品绑定的标签信息
     *
     * @param goods
     * @return
     */
    void insertAndUpdateChangeShopLabel(BusShopGoods goods);

    /**
     * 院内商品新增，编辑时修改商品绑定的标签信息
     *
     * @param goods
     * @return
     */
    List<BusShopLabel> getShopLabelByGoodId(BusShopGoods goods);

    /**
     * app端通过标签获取商品列表
     *
     * @param goods
     * @return
     */
    TableDataInfo listAppShopGoods(BusShopGoodsDTO goods);

    /**
     * app端通过商品分类获取商品列表
     *
     * @param goods
     * @return
     */
    TableDataInfo listAppShopTypeGoods(BusShopGoodsDTO goods);

    /**
     * 计算整张表
     *
     * @param
     * @return
     */
    Long getShopGoodsCount();

    /**
     * 远程调用查询商品信息
     *
     * @param dto
     * @return
     */
    List<ShopGoodsVO> listShopGoods(ShopGoodsDTO dto);

    /**
     * 设置商品排序
     *
     * @param goods
     * @return
     */
    int setOrderNum(BusShopGoods goods);

    /**
     * 查询多规格商品
     *
     * @param hospitalId 医院id
     * @param goodName   商品名
     * @return 查询多规格商品
     */
    List<BusShopGoods> getMultSpecGoods(Long hospitalId, String goodName);

    /**
     * @Param goods
     * @Return java.util.List<com.puree.hospital.shop.domain.BusShopGoods>
     * @Description 根据ID排序获取商品
     * <AUTHOR>
     * @Date 2024/3/4 16:27
     **/
    List<BusShopGoods> getByIds(BusShopGoodsDTO goods);

    /**
     * 患教商品同步
     * @param dto 入参
     * @return 成功同步的商品数
     */
    Integer goodsSync(BusShopGoodsDTO dto);

    /**
     * 患教关联商品的id转换为各医院的商品id
     * @param goodsIds 商品id列表
     * @param hospitalId 医院id
     * @return 转换后的商品id
     */
    List<Long> convertGoodsIds(List<Long> goodsIds, Long hospitalId);

    /**
     * 商品搜索
     * @param shopSearchDTO
     * @return
     */
    List<ShopGoodSearchVO> searchGood(BusShopSearchDTO shopSearchDTO);

    /**
     * 查询返佣比例
     *
     * @param hospitalId 医院id
     * @param goodsIds 商品id列表
     * @return 返佣比例
     */
    List<BusChannelCommissionVO> queryCommissionRate(Long hospitalId, List<Long> goodsIds);

    /**
     * 查询商城/商品返佣比例详情
     *
     * @param hospitalId 医院 id
     * @param goodsId 商品 id
     * @return 商城/商品返佣比例详情
     */
    BusCommissionVO queryCommissionRateInfo(Long hospitalId, Long goodsId, String flag);

    /**
     * 新增或修改渠道和合伙人佣金比例
     *
     * @param commissionDTO 入参
     * @return 添加结果
     */
    int updateOrInsertCommissionRate(BusChannelCommissionDTO commissionDTO);


//    /**
//     * 修改是否冷链状态
//     * @param goods
//     * @return
//     */
//    int updateIsColdChain(BusShopGoods goods);
}

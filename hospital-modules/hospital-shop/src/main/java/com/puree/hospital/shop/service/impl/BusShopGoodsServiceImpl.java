package com.puree.hospital.shop.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.puree.hospital.business.api.RemoteSysServiceConfigService;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.shop.api.model.dto.BusChannelCommissionDTO;
import com.puree.hospital.shop.api.model.vo.BusChannelCommissionVO;
import com.puree.hospital.shop.api.model.vo.BusCommissionVO;
import com.puree.hospital.shop.domain.BusChannelCommissionRate;
import com.puree.hospital.shop.domain.BusShopBrand;
import com.puree.hospital.shop.domain.BusShopGoods;
import com.puree.hospital.shop.domain.BusShopGoodsLabel;
import com.puree.hospital.shop.domain.BusShopLabel;
import com.puree.hospital.shop.domain.BusShopType;
import com.puree.hospital.shop.domain.dto.BusShopGoodsDTO;
import com.puree.hospital.shop.domain.dto.BusShopSearchDTO;
import com.puree.hospital.shop.domain.dto.ShopGoodsDTO;
import com.puree.hospital.shop.domain.vo.ShopGoodSearchVO;
import com.puree.hospital.shop.domain.vo.ShopGoodsVO;
import com.puree.hospital.shop.mapper.BusChannelCommissionRateMapper;
import com.puree.hospital.shop.mapper.BusShopBrandMapper;
import com.puree.hospital.shop.mapper.BusShopGoodsLabelMapper;
import com.puree.hospital.shop.mapper.BusShopGoodsMapper;
import com.puree.hospital.shop.mapper.BusShopLabelMapper;
import com.puree.hospital.shop.mapper.BusShopTypeMapper;
import com.puree.hospital.shop.service.IBusShopGoodsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/10 15:20
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusShopGoodsServiceImpl implements IBusShopGoodsService {
    private final BusShopGoodsMapper goodsMapper;
    private final BusShopBrandMapper brandMapper;
    private final BusShopTypeMapper typeMapper;
    private final BusShopLabelMapper busShopLabelMapper;
    private final BusShopGoodsLabelMapper busShopGoodsLabelMapper;
    private final RemoteSysServiceConfigService remoteSysServiceConfigService;
    private final BusChannelCommissionRateMapper commissionRateMapper;

    private static final Long SOURCEID = -1L;

    //顶级父级
    private static final Long PARENTID = 0L;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addGoods(BusShopGoods goods) {
        // 如果是医院院内添加
        if (StringUtils.isNotNull(goods.getHospitalId())) {
            Long count = goodsMapper.selectCount(new LambdaQueryWrapper<BusShopGoods>()
                    .eq(BusShopGoods::getHospitalId, goods.getHospitalId())
                    .eq(BusShopGoods::getName, goods.getName())
                    .eq(BusShopGoods::getSpecification, goods.getSpecification()));
            if (count > 0) {
                throw new ServiceException("同商品规格已存在");
            }
        }
        goods.setCreateTime(new Date());
        int insert = goodsMapper.insert(goods);
        if (YesNoEnum.YES.getCode().equals(goods.getIsCBit())) {
            List<Long> hospitalIds = goodsMapper.getAllHospital();
            for (Long hospitalId : hospitalIds) {
                BusShopGoods hosGoods = new BusShopGoods();
                BeanUtils.copyProperties(goods, hosGoods);
                hosGoods.setId(null);
                hosGoods.setHospitalId(hospitalId);
                hosGoods.setParentId(goods.getId());
                hosGoods.setEnterpriseId(null);
                hosGoods.setLabelIds(goods.getLabelCBitIds());
                goodsMapper.insert(hosGoods);
                insertAndUpdateChangeShopLabel(hosGoods);
            }
        }
        insertAndUpdateChangeShopLabel(goods);
        return insert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addEntGoods(BusShopGoodsDTO dto) {
        for (Long id : dto.getGoodsIdList()) {
            BusShopGoods goods = goodsMapper.selectById(id);
            if (StringUtils.isNull(goods)) {
                throw new ServiceException("商品已被配送企业删除");
            }
            BusShopBrand enterpriseBrand = brandMapper.selectById(goods.getBrandId());
            if (StringUtils.isNull(enterpriseBrand)) {
                throw new ServiceException("商品云仓品牌为空");
            }
            Long count = goodsMapper.selectCount(new LambdaQueryWrapper<BusShopGoods>()
                    .eq(BusShopGoods::getHospitalId, dto.getHospitalId())
                    .eq(BusShopGoods::getName, goods.getName())
                    .eq(BusShopGoods::getSpecification, goods.getSpecification()));
            if (count > 0) {
                throw new ServiceException("同商品(" + goods.getName() + ")规格已存在");
            }

            // 如医院已有该品牌名，使用该品牌名，如医院无该品牌名，新增品牌名
            BusShopBrand hospitalBrand = brandMapper.selectOne(new LambdaQueryWrapper<BusShopBrand>()
                    .eq(BusShopBrand::getHospitalId, dto.getHospitalId())
                    .eq(BusShopBrand::getBrandName, enterpriseBrand.getBrandName()));
            if (StringUtils.isNull(hospitalBrand)) {
                enterpriseBrand.setId(null);
                enterpriseBrand.setEnterpriseId(null);
                enterpriseBrand.setHospitalId(dto.getHospitalId());
                brandMapper.insert(enterpriseBrand);
                goods.setBrandId(enterpriseBrand.getId());
            } else {
                goods.setBrandId(hospitalBrand.getId());
            }
            goods.setHospitalId(dto.getHospitalId());
            goods.setEnterpriseId(null);
            goods.setStatus(YesNoEnum.NO.getCode());
            goods.setParentId(goods.getId());
            goods.setId(null);
            goods.setSellingPrice(goods.getSuggestSellingPrice() != null ? goods.getSuggestSellingPrice() : null);
            goods.setCreateTime(new Date());
            goodsMapper.insert(goods);
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer goodsSync(BusShopGoodsDTO dto) {

        List<Long> goodIds = goodsMapper.getUnsyncGoodIds(dto.getHospitalId(), dto.getGoodsIdList());

        for (Long id : goodIds) {
            BusShopGoods goods = goodsMapper.selectById(id);
            if (goods == null) {
                log.warn("商品{}已被配送企业删除", id);
                continue;
            }
            BusShopBrand enterpriseBrand = brandMapper.selectById(goods.getBrandId());
            if (enterpriseBrand == null) {
                log.warn("商品{}云仓品牌为空", id);
                continue;
            }
            Long count = goodsMapper.selectCount(new LambdaQueryWrapper<BusShopGoods>()
                    .eq(BusShopGoods::getHospitalId, dto.getHospitalId())
                    .eq(BusShopGoods::getName, goods.getName())
                    .eq(BusShopGoods::getSpecification, goods.getSpecification()));
            if (count > 0) {
                log.warn("同商品(" + goods.getName() + ")规格已存在");
                continue;
            }

            // 如医院已有该品牌名，使用该品牌名，如医院无该品牌名，新增品牌名
            BusShopBrand hospitalBrand = brandMapper.selectOne(new LambdaQueryWrapper<BusShopBrand>()
                    .eq(BusShopBrand::getHospitalId, dto.getHospitalId())
                    .eq(BusShopBrand::getBrandName, enterpriseBrand.getBrandName()));
            if (StringUtils.isNull(hospitalBrand)) {
                enterpriseBrand.setId(null);
                enterpriseBrand.setEnterpriseId(null);
                enterpriseBrand.setHospitalId(dto.getHospitalId());
                brandMapper.insert(enterpriseBrand);
                goods.setBrandId(enterpriseBrand.getId());
            } else {
                goods.setBrandId(hospitalBrand.getId());
            }
            goods.setHospitalId(dto.getHospitalId());
            goods.setEnterpriseId(null);
            goods.setStatus(YesNoEnum.YES.getCode());
            goods.setParentId(goods.getId());
            goods.setId(null);
            goods.setSellingPrice(goods.getSuggestSellingPrice() != null ? goods.getSuggestSellingPrice() : null);
            goods.setCreateTime(new Date());
            goodsMapper.insert(goods);
        }
        return 1;
    }

    @Override
    public List<Long> convertGoodsIds(List<Long> goodsIds, Long hospitalId) {
        List<Long> goodIds = goodsMapper.getConvertGoodsIds(goodsIds, hospitalId);
        return goodIds;
    }

    @Override
    public List<BusChannelCommissionVO> queryCommissionRate(Long hospitalId, List<Long> goodsIds) {
        checkReq(hospitalId, goodsIds);
        // 获取商品返佣比例
        LambdaQueryWrapper<BusChannelCommissionRate> query = new LambdaQueryWrapper<BusChannelCommissionRate>()
                .eq(BusChannelCommissionRate::getHospitalId, hospitalId)
                .in(BusChannelCommissionRate::getGoodsId, goodsIds);
        List<BusChannelCommissionRate> commissionRates = commissionRateMapper.selectList(query);
        Map<Long, List<BusChannelCommissionRate>> foundGoodsIds= commissionRates.stream().collect(Collectors.groupingBy(BusChannelCommissionRate::getGoodsId));
        BusChannelCommissionRate shopCommissionRate = commissionRateMapper.selectShopCommissionRate(hospitalId);
        List<BusChannelCommissionVO> vos = new ArrayList<>();
        // 未设置商品返佣比例数据组装
        assembledResult(hospitalId, goodsIds, foundGoodsIds, shopCommissionRate, vos);
        // 已设置商品返佣比例数据组装
        List<BusChannelCommissionVO> collected = getBusChannelCommissionVOS(commissionRates, shopCommissionRate);
        vos.addAll(collected);
        return vos;
    }

    private static List<BusChannelCommissionVO> getBusChannelCommissionVOS(List<BusChannelCommissionRate> commissionRates, BusChannelCommissionRate shopCommissionRate) {
        List<BusChannelCommissionVO> collected = commissionRates.stream().map(c -> {
            BusChannelCommissionVO busChannelCommissionVO = new BusChannelCommissionVO();
            busChannelCommissionVO.setGoodsId(c.getGoodsId());
            busChannelCommissionVO.setHospitalId(c.getHospitalId());
            // 优先选择商品返现比例
            if (Objects.nonNull(c.getFlag()) && (Objects.nonNull(c.getChannelCommissionRate()) || Objects.nonNull(c.getAgentCommissionRate()))) {
                busChannelCommissionVO.setChannelCommissionRate(c.getChannelCommissionRate());
                busChannelCommissionVO.setAgentCommissionRate(c.getAgentCommissionRate());
                return busChannelCommissionVO;
            }
            if (Objects.isNull(shopCommissionRate)) {
                return busChannelCommissionVO;
            }
            // 商品返佣比例都为null时 取商城返佣比例
            busChannelCommissionVO.setChannelCommissionRate(shopCommissionRate.getChannelCommissionRate());
            busChannelCommissionVO.setAgentCommissionRate(shopCommissionRate.getAgentCommissionRate());
            return busChannelCommissionVO;
        }).collect(Collectors.toList());
        return collected;
    }

    private static void assembledResult(Long hospitalId, List<Long> goodsIds, Map<Long, List<BusChannelCommissionRate>> foundGoodsIds, BusChannelCommissionRate shopCommissionRate, List<BusChannelCommissionVO> vos) {
        // 未设置商品分佣比例 则取商城分佣比例
        goodsIds.forEach(goodsId -> {
            boolean found = foundGoodsIds.containsKey(goodsId);
            if (!found) {
                BusChannelCommissionVO busChannelCommissionVO = new BusChannelCommissionVO();
                if (Objects.isNull(shopCommissionRate)) {
                    busChannelCommissionVO.setGoodsId(goodsId);
                    busChannelCommissionVO.setHospitalId(hospitalId);
                    vos.add(busChannelCommissionVO);
                } else {
                    busChannelCommissionVO.setChannelCommissionRate(shopCommissionRate.getChannelCommissionRate());
                    busChannelCommissionVO.setAgentCommissionRate(shopCommissionRate.getAgentCommissionRate());
                    busChannelCommissionVO.setGoodsId(goodsId);
                    busChannelCommissionVO.setHospitalId(hospitalId);
                    vos.add(busChannelCommissionVO);
                }
            }
        });
    }

    private void checkReq(Long hospitalId, List<Long> goodsIds) {
        if (Objects.isNull(hospitalId) || CollectionUtils.isEmpty(goodsIds)) {
            throw new ServiceException("参数异常！");
        }
    }

    @Override
    public BusCommissionVO queryCommissionRateInfo(Long hospitalId, Long goodsId, String flag) {
        if (Objects.isNull(hospitalId)) {
            throw new ServiceException("获取医院信息失败，请重新登录！");
        }
        // 商品返佣比例
        if (StringUtils.isNotBlank(flag)) {
            BusChannelCommissionRate goodsRate = getGoodsCommissionRate(hospitalId, goodsId);
            if (Objects.isNull(goodsRate)) {
                return new BusCommissionVO();
            }
            BusCommissionVO busCommissionVO = new BusCommissionVO();
            busCommissionVO.setGoodsChannelCommissionRate(goodsRate.getChannelCommissionRate());
            busCommissionVO.setGoodsAgentCommissionRate(goodsRate.getAgentCommissionRate());
            return busCommissionVO;
        }
        // 商城返佣比例
        BusChannelCommissionRate shopCommissionRate = commissionRateMapper.selectShopCommissionRate(hospitalId);
        if (Objects.isNull(shopCommissionRate)) {
            return new BusCommissionVO();
        }
        BusCommissionVO busCommissionVO = new BusCommissionVO();
        busCommissionVO.setShopChannelCommissionRate(shopCommissionRate.getChannelCommissionRate());
        busCommissionVO.setShopAgentCommissionRate(shopCommissionRate.getAgentCommissionRate());
        return busCommissionVO;
    }

    @Override
    public int updateOrInsertCommissionRate(BusChannelCommissionDTO commissionDTO) {
        checkReq(commissionDTO);
        BusChannelCommissionRate busCommissionRate = new BusChannelCommissionRate();
        // 商城
        if (StringUtils.isBlank(commissionDTO.getFlag())) {
            busCommissionRate.setChannelCommissionRate(commissionDTO.getShopChannelCommissionRate());
            busCommissionRate.setAgentCommissionRate(commissionDTO.getShopAgentCommissionRate());
            busCommissionRate.setHospitalId(commissionDTO.getHospitalId());
            busCommissionRate.setFlag(null);
            BusChannelCommissionRate shopCommissionRate = commissionRateMapper.selectShopCommissionRate(commissionDTO.getHospitalId());
            return updateOrInsert(busCommissionRate, shopCommissionRate);
        }
        // 商品
        BusChannelCommissionRate commissionRateInfo = getGoodsCommissionRate(commissionDTO.getHospitalId(), commissionDTO.getGoodsId());
        busCommissionRate.setFlag(commissionDTO.getFlag());
        busCommissionRate.setChannelCommissionRate(commissionDTO.getGoodsChannelCommissionRate());
        busCommissionRate.setAgentCommissionRate(commissionDTO.getGoodsAgentCommissionRate());
        busCommissionRate.setGoodsId(commissionDTO.getGoodsId());
        busCommissionRate.setHospitalId(commissionDTO.getHospitalId());
        busCommissionRate.setGoodsName(commissionDTO.getGoodsName());
        return updateOrInsert(busCommissionRate, commissionRateInfo);
    }

    private int updateOrInsert(BusChannelCommissionRate busCommissionRate, BusChannelCommissionRate commissionRateInfo) {
        if (Objects.nonNull(commissionRateInfo)) {
            busCommissionRate.setId(commissionRateInfo.getId());
            busCommissionRate.setUpdateBy(SecurityUtils.getUsername());
            return commissionRateMapper.updateById(busCommissionRate);
        }
        busCommissionRate.setCreateBy(SecurityUtils.getUsername());
        try {
            return commissionRateMapper.insert(busCommissionRate);
        } catch (DuplicateKeyException e) {
            throw new ServiceException("请勿重复添加!");
        }
    }

    private BusChannelCommissionRate getGoodsCommissionRate(Long hospitalId, Long goodsId) {
        LambdaQueryWrapper<BusChannelCommissionRate> query = new LambdaQueryWrapper<BusChannelCommissionRate>()
                .eq(BusChannelCommissionRate::getHospitalId, hospitalId)
                .eq(BusChannelCommissionRate::getGoodsId, goodsId)
                .last("limit 1");
        return commissionRateMapper.selectOne(query);
    }

    private void checkReq(BusChannelCommissionDTO dto) {
        if (Objects.isNull(dto.getHospitalId())) {
            throw new ServiceException("参数异常！");
        }
        if (StringUtils.isNotEmpty(dto.getFlag())) {
            // 商品比例限制
            limitRateValue(dto.getGoodsChannelCommissionRate(), dto.getGoodsAgentCommissionRate());
            if (Objects.isNull(dto.getGoodsId())) {
                throw new ServiceException("商品id不能为空");
            }
        } else {
            // 商城比例限制
            limitRateValue(dto.getShopChannelCommissionRate(), dto.getShopAgentCommissionRate());
        }
    }

    private void limitRateValue(BigDecimal channelCommissionRate, BigDecimal partnerCommissionRate) {
        if (Objects.nonNull(channelCommissionRate) && Objects.nonNull(partnerCommissionRate)) {
            if (channelCommissionRate.add(partnerCommissionRate).compareTo(BigDecimal.valueOf(100)) > 0) {
                throw new ServiceException("渠道、合伙人返现比例之和不能超过100%");
            }
        } else if (Objects.nonNull(channelCommissionRate) && channelCommissionRate.compareTo(BigDecimal.valueOf(100)) > 0) {
            throw new ServiceException("渠道返现比例不能超过100%");
        } else if (Objects.nonNull(partnerCommissionRate) && partnerCommissionRate.compareTo(BigDecimal.valueOf(100)) > 0) {
            throw new ServiceException("合伙人返现比例不能超过100%");
        }
    }

    @Override
    public List<ShopGoodSearchVO> searchGood(BusShopSearchDTO shopSearchDTO) {
        return goodsMapper.searchGood(shopSearchDTO);
    }

    @Override
    public BusShopGoods getDetail(Long id) {
        BusShopGoods detail = goodsMapper.getDetail(id);
        if (ObjectUtil.isNull(detail)) {
            throw new ServiceException("商品未找到！");
        }
        BusShopType type = typeMapper.selectById(detail.getTypeId());
        ArrayList<Long> typeList = new ArrayList<>();
        if (ObjectUtil.isNotNull(type)) {
            typeList.add(type.getParentId());
            typeList.add(detail.getTypeId());
        }
        /*如果是医院添加的商品，则查询绑定的标签信息*/
        detail.setBusShopLabels(getShopLabelByGoodId(detail));
        if (ObjectUtil.isNotEmpty(detail.getBusShopLabels())) {
            List<BusShopLabel> filteredBusShopLabel = detail.getBusShopLabels().stream()
                    .filter(busShopLabel -> busShopLabel.getIsCBit().equals(YesNoEnum.YES.getCode()))
                    .collect(Collectors.toList());
            //c位标签
            detail.setLabelCBitShopLabels(filteredBusShopLabel);
        }
        detail.setTypeList(typeList);
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateGoods(BusShopGoods goods) {
        log.info("更新商品库存入参={}", JSON.toJSONString(goods));
        if (StringUtils.isNotNull(goods.getHospitalId())) {
            Long count = goodsMapper.selectCount(new LambdaQueryWrapper<BusShopGoods>()
                    .eq(BusShopGoods::getHospitalId, goods.getHospitalId())
                    .eq(BusShopGoods::getName, goods.getName())
                    .eq(BusShopGoods::getSpecification, goods.getSpecification())
                    .ne(BusShopGoods::getId, goods.getId()));
            if (count > 0) {
                throw new ServiceException("同商品规格已存在");
            }
        }
        goods.setUpdateTime(DateUtils.getNowDate());
        // 修改为C位商品
        BusShopGoods oldGoods = goodsMapper.selectById(goods.getId());
        StringUtils.isNullThrowExp(oldGoods, "商品已被删除");
        List<BusShopGoods> hosGoods = goodsMapper.selectList(new LambdaQueryWrapper<BusShopGoods>()
                .eq(BusShopGoods::getParentId, goods.getId()));
        if (YesNoEnum.YES.getCode().equals(goods.getIsCBit())) {
            if (YesNoEnum.NO.getCode().equals(oldGoods.getIsCBit())) {
                List<Long> hospitalIds = goodsMapper.getAllHospital();
                for (Long hospitalId : hospitalIds) {
                    boolean flag = false;
                    for (BusShopGoods hosGood : hosGoods) {
                        if (hosGood.getHospitalId().equals(hospitalId)) {
                            flag = true;
                            BusShopGoods updateGoods = new BusShopGoods();
                            BeanUtils.copyProperties(goods, updateGoods);
                            updateGoods.setId(hosGood.getId());
                            updateGoods.setHospitalId(hospitalId);
                            updateGoods.setParentId(goods.getId());
                            updateGoods.setEnterpriseId(null);
                            updateGoods.setLabelIds(goods.getLabelCBitIds());
                            goodsMapper.update(updateGoods, new LambdaUpdateWrapper<BusShopGoods>()
                                    .eq(BusShopGoods::getId, updateGoods.getId())
                                    .set(BusShopGoods::getTypeId, null));
                            insertAndUpdateChangeShopLabel(updateGoods);
                            break;
                        }
                    }
                    if (flag) {
                        continue;
                    }
                    BusShopGoods hosInsertGoods = new BusShopGoods();
                    BeanUtils.copyProperties(goods, hosInsertGoods);
                    hosInsertGoods.setId(null);
                    hosInsertGoods.setHospitalId(hospitalId);
                    hosInsertGoods.setParentId(goods.getId());
                    hosInsertGoods.setEnterpriseId(null);
                    hosInsertGoods.setLabelIds(goods.getLabelCBitIds());
                    goodsMapper.insert(hosInsertGoods);
                    insertAndUpdateChangeShopLabel(hosInsertGoods);
                }
            } else {
                goodsMapper.updateCGoods(goods);
                for (BusShopGoods hosGood : hosGoods) {
                    BusShopGoods labelGoods = new BusShopGoods();
                    labelGoods.setId(hosGood.getId());
                    labelGoods.setHospitalId(hosGood.getHospitalId());
                    labelGoods.setLabelIds(goods.getLabelCBitIds());
                    labelGoods.setIsCBit(goods.getIsCBit());
                    insertAndUpdateChangeShopLabel(labelGoods);
                }
            }
        } else if (YesNoEnum.NO.getCode().equals(goods.getIsCBit()) && null == goods.getHospitalId()) {
            if (YesNoEnum.YES.getCode().equals(oldGoods.getIsCBit())) {
                goodsMapper.delete(new LambdaQueryWrapper<BusShopGoods>()
                        .eq(BusShopGoods::getParentId, goods.getId()));
                goodsMapper.deleteGoodsCart(goods.getId());
                for (BusShopGoods hosGood : hosGoods) {
                    goodsMapper.deleteGoodsCart(hosGood.getId());
                }
                busShopGoodsLabelMapper.delete(new LambdaQueryWrapper<BusShopGoodsLabel>()
                        .eq(BusShopGoodsLabel::getGoodsId, goods.getId()));
                for (BusShopGoods hosGood : hosGoods) {
                    busShopGoodsLabelMapper.delete(new LambdaQueryWrapper<BusShopGoodsLabel>()
                            .eq(BusShopGoodsLabel::getGoodsId, hosGood.getId()));
                }
            }
        }
        int update = goodsMapper.updateById(goods);
        insertAndUpdateChangeShopLabel(goods);
        //调价更新
        if (PARENTID.equals(oldGoods.getParentId()) && !SOURCEID.equals(oldGoods.getSourceId())
                && YesNoEnum.NO.getCode().equals(goods.getIsCBit())) {
            BusShopGoods updateShopGoods = new BusShopGoods();
            updateShopGoods.setStock(goods.getStock());
            updateShopGoods.setCostPrice(goods.getSellingPrice());
            updateShopGoods.setUpdateTime(DateUtils.getNowDate());
            goodsMapper.update(updateShopGoods, new LambdaQueryWrapper<BusShopGoods>().eq(BusShopGoods::getParentId, goods.getId()));
        }
        return update;
    }

    @Override
    public List<BusShopGoods> listBusShopGoods(BusShopGoodsDTO goods) {
        List<BusShopGoods> busShopGoods = goodsMapper.listBusShopGoods(goods);
        for (BusShopGoods item : busShopGoods) {
            List<BusShopLabel> shopLableByGoodId = getShopLabelByGoodId(item);
            item.setBusShopLabels(shopLableByGoodId);
            if (StringUtils.isNotEmpty(shopLableByGoodId)) {
                List<BusShopLabel> filteredPersons = shopLableByGoodId.stream()
                        .filter(busShopLabel -> busShopLabel.getIsCBit() == 1)
                        .collect(Collectors.toList());
                item.setLabelCBitShopLabels(filteredPersons);
            }
        }
        return busShopGoods;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        //判断商品是否下架
        BusShopGoods goods = goodsMapper.selectById(id);
        if (!Objects.isNull(goods) && YesNoEnum.YES.getCode().equals(goods.getStatus())) {
            throw new ServiceException("商品未下架，无法删除");
        }
        List<BusShopGoods> shopGoods = goodsMapper.selectList(new LambdaQueryWrapper<BusShopGoods>()
                .eq(BusShopGoods::getParentId, id));
        if (goods.getIsCBit().equals(YesNoEnum.NO.getCode())) {
            if (StringUtils.isNotEmpty(shopGoods)) {
                throw new ServiceException("商品已有业务数据，无法删除");
            }
        } else {
            goodsMapper.delete(new LambdaQueryWrapper<BusShopGoods>().eq(BusShopGoods::getParentId, id));
            for (BusShopGoods shopGood : shopGoods) {
                busShopGoodsLabelMapper.delete(new LambdaQueryWrapper<BusShopGoodsLabel>()
                        .eq(BusShopGoodsLabel::getGoodsId, shopGood.getId()));
            }
            busShopGoodsLabelMapper.delete(new LambdaQueryWrapper<BusShopGoodsLabel>()
                    .eq(BusShopGoodsLabel::getGoodsId, id));
        }
        if (SOURCEID.equals(goods.getSourceId())
                || (null != goods.getHospitalId() && 0 == goods.getSourceId())) {
            busShopGoodsLabelMapper.delete(new LambdaQueryWrapper<BusShopGoodsLabel>()
                    .eq(BusShopGoodsLabel::getHospitalId, goods.getHospitalId())
                    .eq(BusShopGoodsLabel::getGoodsId, goods.getId()));
        }
        goodsMapper.deleteGoodsCart(id);
        for (BusShopGoods hosGood : shopGoods) {
            goodsMapper.deleteGoodsCart(hosGood.getId());
        }
        return goodsMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStock(BusShopGoods goods) {
        BusShopGoods shopGoods = goodsMapper.selectById(goods.getId());
        // 该商品是医院拉取云仓商品
        if (!PARENTID.equals(shopGoods.getParentId()) && !SOURCEID.equals(shopGoods.getSourceId())) {
            shopGoods = goodsMapper.selectById(shopGoods.getParentId());
        }
        if (PARENTID.equals(shopGoods.getParentId()) && !SOURCEID.equals(shopGoods.getSourceId())) {
            BusShopGoods updateShopGoods = new BusShopGoods();
            updateShopGoods.setStock(goods.getStock());
            updateShopGoods.setUpdateTime(DateUtils.getNowDate());
            goodsMapper.update(updateShopGoods, new LambdaQueryWrapper<BusShopGoods>().eq(BusShopGoods::getParentId, goods.getId()));
            //调价
            BusShopGoods updateShopGoodsTow = new BusShopGoods();
            updateShopGoodsTow.setCostPrice(goods.getCostPrice());
            updateShopGoodsTow.setUpdateTime(DateUtils.getNowDate());
            goodsMapper.update(updateShopGoodsTow, new LambdaQueryWrapper<BusShopGoods>().eq(BusShopGoods::getParentId, goods.getId()));
        }
        return goodsMapper.updateById(goods);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatus(BusShopGoods goods) {
        BusShopGoods shopGoods = goodsMapper.selectById(goods.getId());
        if (StringUtils.isNotNull(shopGoods.getHospitalId()) && !PARENTID.equals(shopGoods.getParentId())) {
            BusShopGoods selectOne = goodsMapper.selectOne(new LambdaQueryWrapper<BusShopGoods>().eq(BusShopGoods::getId, shopGoods.getParentId()));
            if (StringUtils.isNotNull(selectOne) && YesNoEnum.YES.getCode().equals(goods.getStatus()) && YesNoEnum.NO.getCode().equals(selectOne.getStatus())) {
                throw new ServiceException("发货渠道商品未上架,请联系发货渠道");
            }
        }
        if (PARENTID.equals(shopGoods.getParentId()) && !SOURCEID.equals(shopGoods.getSourceId())) {
            BusShopGoods updateShopGoods = new BusShopGoods();
            updateShopGoods.setStatus(goods.getStatus());
            updateShopGoods.setUpdateTime(DateUtils.getNowDate());
            goodsMapper.update(updateShopGoods, new LambdaQueryWrapper<BusShopGoods>().eq(BusShopGoods::getParentId, goods.getId()));
        }
        return goodsMapper.updateById(goods);
    }


    @Override
    public int updateHot(BusShopGoods goods) {
        return goodsMapper.updateById(goods);
    }

    @Override
    public List<BusShopGoods> listNotRelEnterpriseGoods(BusShopGoodsDTO goods) {
        return goodsMapper.listNotRelEnterpriseGoods(goods);
    }

    /**
     * order模块调用查询商品库存
     *
     * @param paramsMap
     * @return
     */
    @Override
    public List<BusShopGoods> listShopStock(HashMap<String, Object> paramsMap) {
        return goodsMapper.listShopStock(paramsMap);
    }

    @Override
    public void insertAndUpdateChangeShopLabel(BusShopGoods goods) {
        /*如果是医院院内添加*/
        if (StringUtils.isNotNull(goods.getHospitalId())) {
            /*如果标签集合为0 ，则删除当前商品在当前医院的所有标签*/
            busShopGoodsLabelMapper.delete(new LambdaQueryWrapper<BusShopGoodsLabel>()
                    .eq(BusShopGoodsLabel::getHospitalId, goods.getHospitalId())
                    .eq(BusShopGoodsLabel::getGoodsId, goods.getId())
            );
            for (Long item : goods.getLabelIds()) {
                BusShopLabel busShopLabel = busShopLabelMapper.selectById(item);
                if (StringUtils.isNotNull(busShopLabel)) {
                    BusShopGoodsLabel busShopGoodsLabel = new BusShopGoodsLabel();
                    busShopGoodsLabel.setGoodsId(goods.getId());
                    busShopGoodsLabel.setLabelId(item);
                    busShopGoodsLabel.setHospitalId(goods.getHospitalId());
                    busShopGoodsLabel.setCreateTime(DateUtils.getNowDate());
                    busShopGoodsLabel.setIsCBit(goods.getIsCBit());
                    busShopGoodsLabelMapper.insert(busShopGoodsLabel);
                }
            }
        } else {
            // c位标签
            List<Long> labelCBitIds = goods.getLabelCBitIds();
            /*如果标签集合为0 ，则删除当前商品在当前医院的所有标签*/
            busShopGoodsLabelMapper.delete(new LambdaQueryWrapper<BusShopGoodsLabel>()
                    .isNull(BusShopGoodsLabel::getHospitalId)
                    .eq(BusShopGoodsLabel::getGoodsId, goods.getId())
            );
            if (StringUtils.isNotNull(labelCBitIds)) {
                for (Long item : labelCBitIds) {
                    /*判断标签是否被删除*/
                    BusShopLabel busShopLabel = busShopLabelMapper.selectById(item);
                    if (StringUtils.isNotNull(busShopLabel)) {
                        BusShopGoodsLabel busShopGoodsLabel = new BusShopGoodsLabel();
                        //如果在c位标签不为空则，设置该商品对应的C位标签
                        busShopGoodsLabel.setIsCBit(YesNoEnum.YES.getCode());
                        busShopGoodsLabel.setGoodsId(goods.getId());
                        busShopGoodsLabel.setLabelId(item);
                        busShopGoodsLabel.setHospitalId(goods.getHospitalId());
                        busShopGoodsLabel.setCreateTime(DateUtils.getNowDate());
                        busShopGoodsLabelMapper.insert(busShopGoodsLabel);
                    }
                }
            }
        }
    }

    @Override
    public List<BusShopLabel> getShopLabelByGoodId(BusShopGoods goods) {

        /*获取商品所绑定标签的id集合*/
        List<BusShopGoodsLabel> busShopGoodsLabels = busShopGoodsLabelMapper.selectList(new LambdaQueryWrapper<BusShopGoodsLabel>()
                .eq(null != goods.getHospitalId(), BusShopGoodsLabel::getHospitalId, goods.getHospitalId())
                .eq(BusShopGoodsLabel::getGoodsId, goods.getId()));
        List<Long> collect = busShopGoodsLabels.stream().map(BusShopGoodsLabel::getLabelId).collect(Collectors.toList());
        List<BusShopLabel> busShopLabels = new ArrayList<>();
        if (StringUtils.isNotEmpty(collect)) {
            busShopLabels = busShopLabelMapper.selectList(new LambdaQueryWrapper<BusShopLabel>()
                    .in(BusShopLabel::getId, collect));
        }
        return busShopLabels;
    }

    @Override
    public TableDataInfo listAppShopGoods(BusShopGoodsDTO goods) {
        List<BusShopGoods> busShopGoods = goodsMapper.listAppShopGoods(goods);
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(busShopGoods);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(busShopGoods).getTotal());
        return rspData;

    }

    @Override
    public TableDataInfo listAppShopTypeGoods(BusShopGoodsDTO goods) {
        List<BusShopGoods> busShopGoods = goodsMapper.listAppShopTypeGoods(goods);

        List<BusShopGoods> busShopGoodsSort = new ArrayList<>();
        //非热门商品
        List<BusShopGoods> busShopGoodsNoHot = new ArrayList<>();
        for (BusShopGoods busSG : busShopGoods) {
            if ("1".equals(busSG.getHotFlag()) && null != busSG.getOrderNum()) {
                busShopGoodsSort.add(busSG);
            } else {
                busShopGoodsNoHot.add(busSG);
            }
        }
        busShopGoodsSort.sort(Comparator.comparing(BusShopGoods::getOrderNum).thenComparing(BusShopGoods::getUpdateTime));
        busShopGoodsSort.addAll(busShopGoodsNoHot);
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(busShopGoodsSort);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(busShopGoods).getTotal());
        return rspData;

    }

    @Override
    public Long getShopGoodsCount() {
        Long count = goodsMapper.selectCount(new LambdaQueryWrapper<>());
        return count;
    }

    /**
     * 远程调用查询商品信息
     *
     * @param dto
     * @return
     */
    @Override
    public List<ShopGoodsVO> listShopGoods(ShopGoodsDTO dto) {
        return goodsMapper.listShopGoods(dto);
    }

    @Override
    public int setOrderNum(BusShopGoods goods) {
        return goodsMapper.updateOrderNum(goods.getId(), goods.getOrderNum());
    }

    @Override
    public List<BusShopGoods> getMultSpecGoods(Long hospitalId, String goodName) {
        return goodsMapper.selectList(new LambdaQueryWrapper<BusShopGoods>()
                .eq(BusShopGoods::getHospitalId, hospitalId)
                .eq(BusShopGoods::getStatus, YesNoEnum.YES.getCode())
                .eq(BusShopGoods::getName, goodName));
    }

    /**
     * @Param goods
     * @Return java.util.List<com.puree.hospital.shop.domain.BusShopGoods>
     * @Description 根据ID排序获取商品
     * <AUTHOR>
     * @Date 2024/3/4 16:32
     **/
    @Override
    public List<BusShopGoods> getByIds(BusShopGoodsDTO goods) {
        List<BusShopGoods> busShopGoods = listBusShopGoods(goods);
        List<Long> goodsIdList = goods.getGoodsIdList();
        List<BusShopGoods> vos = new ArrayList<>(10);
        //排序
        for (Long id : goodsIdList) {
            for (BusShopGoods shopGoods : busShopGoods) {
                if (NumberUtil.equals(id, shopGoods.getId())) {
                    if(goods.getHospitalId() == null && shopGoods.getSuggestSellingPrice() != null){
                        shopGoods.setSellingPrice(shopGoods.getSuggestSellingPrice());
                    }
                    vos.add(shopGoods);
                }
            }
        }
        return vos;
    }


}

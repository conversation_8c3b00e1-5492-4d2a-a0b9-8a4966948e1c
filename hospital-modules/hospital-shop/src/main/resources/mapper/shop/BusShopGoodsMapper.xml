<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.shop.mapper.BusShopGoodsMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.puree.hospital.shop.domain.BusShopGoods">
        <id column="id" property="id"/>
        <result column="hospital_id" property="hospitalId"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="source_id" property="sourceId"/>
        <result column="name" property="name"/>
        <result column="title" property="title"/>
        <result column="subtitle" property="subtitle"/>
        <result column="img" property="img"/>
        <result column="video" property="video"/>
        <result column="detail" property="detail"/>
        <result column="type_id" property="typeId"/>
        <result column="brand_id" property="brandId"/>
        <result column="specification" property="specification"/>
        <result column="selling_price" property="sellingPrice"/>
        <result column="original_price" property="originalPrice"/>
        <result column="cost_price" property="costPrice"/>
        <result column="suggest_selling_price" property="suggestSellingPrice"/>
        <result column="stock" property="stock"/>
        <result column="status" property="status"/>
        <result column="parent_id" property="parentId"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="hot_flag" property="hotFlag"/>
        <result column="brand_name" property="brandName"/>
        <result column="type_name" property="typeName"/>
        <result column="sourceName" property="sourceName"/>
        <result column="order_num" property="orderNum" />
        <result column="is_c_bit" property="isCBit" />
        <collection property="labelList" column="id" ofType="BusShopLabel" javaType="list"
                    select="listShopLabelList"></collection>
    </resultMap>

    <select id="listShopLabelList" resultType="BusShopLabel" parameterType="Long">
        select * from bus_shop_goods_label  t1
        left join  bus_shop_label t2 on t2.id=t1.label_id
        where goods_id=#{id}
    </select>
    <select id="getDetail" resultType="BusShopGoods" parameterType="Long">
        select t1.*,
               t2.brand_name,
               t3.type_name,
               IF(t1.source_id=-1, '院内','众爱云仓') as sourceName
        from bus_shop_goods t1
                 left join bus_shop_brand t2 on t1.brand_id = t2.id
                 left join bus_shop_type t3 on t1.type_id = t3.id
        where t1.id = #{id}
    </select>

    <select id="listBusShopGoods" resultMap="BaseResultMap" parameterType="BusShopGoodsDTO">
        select t1.*,
        t2.brand_name,
        t3.type_name,
        IF(t1.source_id=-1, '院内','众爱云仓') as sourceName
        from bus_shop_goods t1
        left join bus_shop_brand t2 on t1.brand_id = t2.id
        left join bus_shop_type t3 on t1.type_id = t3.id
        LEFT JOIN bus_shop_goods_label t5 ON t5.goods_id = t1.id
        <where>
            <if test="shopType == 0">
                t1.shop_type = 0
            </if>
            <if test="hospitalId!=null">
                and t1.hospital_id = #{hospitalId}
            </if>
            <if test="name!=null and name!=''">
                and t1.name like concat('%',#{name}, '%')
            </if>
            <if test="sourceId !=null">
                and t1.source_id=#{sourceId}
            </if>
            <if test="typeIds != null and typeIds.size() > 0  ">
                and t1.type_id IN
                <foreach collection="typeIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="goodsIdList != null and goodsIdList.size() > 0  ">
                and t1.id IN
                <foreach collection="goodsIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="status!=null ">
                and t1.status=#{status}
            </if>
            <if test="parentId!=null ">
                and t1.parent_id=#{parentId}
            </if>
            <if test="labelId!=null">
                AND t5.label_id=#{labelId}
            </if>
        </where>
        group by t1.id
        order by t1.is_c_bit desc,t1.create_time desc
    </select>

    <select id="listNotRelEnterpriseGoods" parameterType="BusShopGoodsDTO" resultType="BusShopGoods">
        select t1.*,
        t2.brand_name,
        t3.type_name,
        IF(t1.source_id=-1, '院内','众爱云仓') as sourceName
        from bus_shop_goods t1
        left join bus_shop_brand t2 on t1.brand_id = t2.id
        left join bus_shop_type t3 on t1.type_id = t3.id
        where t1.status = 1 and t1.name like concat('%',#{name},'%')
        and t1.source_id =0
        and t1.parent_id =0
        and t1.is_c_bit!=1
        and t1.id not in (select parent_id from bus_shop_goods where hospital_id = #{hospitalId} and parent_id != 0 )
    </select>

    <select id="listShopStock" parameterType="hashmap" resultType="BusShopGoods">
        SELECT
        id,
        stock,
        parent_id
        FROM
        bus_shop_goods
        WHERE
        hospital_id = #{hospitalId}
        AND id IN
        <foreach item="item" index="index" collection="shopList" open="(" close=")" separator=",">
            #{item.shopId}
        </foreach>
    </select>


    <select id="listAppShopGoods" parameterType="BusShopGoodsDTO" resultType="BusShopGoods">
        SELECT * FROM (
        (
        SELECT b1.*,b3.brand_name FROM `bus_shop_goods` b1
        INNER JOIN bus_shop_brand b3 ON b1.brand_id = b3.id
        <if test="labelId != null">
            INNER JOIN bus_shop_goods_label b2 ON b1.id = b2.goods_id
            AND  b2.label_id = #{labelId}
        </if>
        WHERE b1.stock > 0  AND b1.status = 1 and b1.is_c_bit = 1
        and b1.hospital_id = #{hospitalId} and source_id = 0
        <if test=" name != null and name != '' ">
            AND b1.title LIKE  CONCAT('%',#{name},'%')
        </if>
        <if test="shopType != null and shopType != ''">
            and b1.shop_type = #{shopType}
        </if>
        ORDER BY b1.order_num,b1.create_time DESC  LIMIT #{limit}
        )
        UNION
        (
        SELECT b1.*,b3.brand_name FROM `bus_shop_goods` b1
        INNER JOIN bus_shop_brand b3 ON b1.brand_id = b3.id
        <if test="labelId != null">
            INNER JOIN bus_shop_goods_label b2 ON b1.id = b2.goods_id AND  b1.hospital_id  = b2.hospital_id
            AND  b2.label_id = #{labelId}
        </if>
        WHERE  b1.hot_flag = 1 AND b1.stock > 0  AND b1.status = 1
        AND b1.hospital_id =#{hospitalId}
        <if test=" name != null and name != '' ">
            AND b1.title LIKE  CONCAT('%',#{name},'%')
        </if>
        <if test="shopType != null and shopType != ''">
            and b1.shop_type = #{shopType}
        </if>
        ORDER BY b1.order_num,b1.create_time desc  LIMIT #{limit}
        )
        UNION
        (
        SELECT b1.*,b3.brand_name FROM `bus_shop_goods` b1
        INNER JOIN bus_shop_brand b3 ON b1.brand_id = b3.id
        <if test="labelId != null">
            INNER JOIN bus_shop_goods_label b2 ON b1.id = b2.goods_id AND  b1.hospital_id  = b2.hospital_id
            AND  b2.label_id = #{labelId}
        </if>
        WHERE  b1.hot_flag = 0 AND b1.stock > 0  AND b1.status = 1
        AND b1.hospital_id =#{hospitalId}
        <if test=" name != null and name != '' ">
            AND b1.title LIKE  CONCAT('%',#{name},'%')
        </if>
        <if test="shopType != null and shopType != ''">
            and b1.shop_type = #{shopType}
        </if>
        ORDER BY b1.create_time DESC  LIMIT #{limit}
        )
        UNION
        (
        SELECT b1.*,b3.brand_name FROM `bus_shop_goods` b1
        INNER JOIN bus_shop_brand b3 ON b1.brand_id = b3.id
        <if test="labelId != null">
            INNER JOIN bus_shop_goods_label b2 ON b1.id = b2.goods_id AND  b1.hospital_id  = b2.hospital_id
            AND  b2.label_id = #{labelId}
        </if>
        WHERE  b1.stock = 0  AND b1.status = 1 AND b1.hospital_id =#{hospitalId}
        <if test=" name != null and name != '' ">
            AND b1.title LIKE  CONCAT('%',#{name},'%')
        </if>
        <if test="shopType != null and shopType != ''">
            and b1.shop_type = #{shopType}
        </if>
        ORDER BY b1.create_time DESC  LIMIT #{limit}
        )
        )t3
    </select>

    <select id="listAppShopTypeGoods" resultType="com.puree.hospital.shop.domain.BusShopGoods">
        SELECT * FROM (
        (
        SELECT b1.*,b3.brand_name FROM `bus_shop_goods` b1
        INNER JOIN bus_shop_brand b3 ON b1.brand_id = b3.id
        WHERE  b1.hot_flag = 1 AND b1.stock > 0  AND b1.status = 1
        AND b1.hospital_id = #{hospitalId}
        <choose>
            <when test="typeId != null and typeId != '' and typeId == '-1'">
                AND (b1.is_c_bit = 1 or b1.type_id is null)
            </when>
            <when test="typeId != null and typeId != '' and typeId != '-1'">
                AND b1.type_id = #{typeId}
            </when>
        </choose>
        <if test=" name != null and name != '' ">
            AND b1.name LIKE  CONCAT('%',#{name},'%')
        </if>
        ORDER BY b1.create_time DESC  LIMIT #{limit}
        )
        UNION
        (
        SELECT b1.*,b3.brand_name FROM `bus_shop_goods` b1
        INNER JOIN bus_shop_brand b3 ON b1.brand_id = b3.id
        WHERE  b1.hot_flag = 0 AND b1.stock > 0  AND b1.status = 1
        AND b1.hospital_id =#{hospitalId}
        <choose>
            <when test="typeId != null and typeId != '' and typeId == '-1'">
                AND (b1.is_c_bit = 1 or b1.type_id is null)
            </when>
            <when test="typeId != null and typeId != '' and typeId != '-1'">
                AND b1.type_id = #{typeId}
            </when>
        </choose>
        <if test=" name != null and name != '' ">
            AND b1.name LIKE  CONCAT('%',#{name},'%')
        </if>
        ORDER BY b1.create_time DESC  LIMIT #{limit}
        )
        UNION
        (
        SELECT b1.*,b3.brand_name FROM `bus_shop_goods` b1
        INNER JOIN bus_shop_brand b3 ON b1.brand_id = b3.id
        WHERE  b1.stock = 0  AND b1.status = 1
        AND b1.hospital_id =#{hospitalId}
        <choose>
            <when test="typeId != null and typeId != '' and typeId == '-1'">
                AND (b1.is_c_bit = 1 or b1.type_id is null)
            </when>
            <when test="typeId != null and typeId != '' and typeId != '-1'">
                AND b1.type_id = #{typeId}
            </when>
        </choose>
        <if test=" name != null and name != '' ">
            AND b1.name LIKE  CONCAT('%',#{name},'%')
        </if>
        ORDER BY b1.create_time DESC  LIMIT #{limit}
        )
        )t3
    </select>

    <select id="listShopGoods" parameterType="com.puree.hospital.shop.domain.dto.ShopGoodsDTO" resultType="com.puree.hospital.shop.domain.vo.ShopGoodsVO">
        SELECT
        t1.id shop_id,
        t1.`name` shop_name,
        t2.brand_name shop_brand,
        t1.img shop_img,
        t1.specification shop_specification,
        t1.selling_price,
        t1.hospital_id,
        t1.enterprise_id,
        t1.shop_type,
        t1.express_type
        FROM
        bus_shop_goods t1
        LEFT JOIN bus_shop_brand t2 ON t1.brand_id = t2.id
        WHERE
        t1.hospital_id = #{hospitalId}
        AND t1.id in
        <foreach collection="goodsIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateCloudGoodsStatus">
        update bus_shop_goods
        <set>
            <if test="status != null and status != ''">
                `status` = #{status},
            </if>
            <if test="isCBit != null and isCBit != ''">
                is_c_bit = #{isCBit},
            </if>
        </set>
        where id in (select id from (
        select id
        from bus_shop_goods t1
        join (select goods_id, label_id
        from bus_shop_goods_label
        group by goods_id
        having count(goods_id) = 1
        and label_id = #{labelId}) t2
        on t1.id = t2.goods_id
        where t1.hospital_id is null
        and t1.source_id = 0
        and t1.is_c_bit = '1') sub)
    </update>

    <update id="updateCGoods" parameterType="BusShopGoods">
        update bus_shop_goods set `name` = #{name},title = #{title},
                                  subtitle = #{subtitle},img = #{img},video = #{video},
            detail = #{detail},brand_id = #{brandId},specification = #{specification},
            cost_price = #{costPrice},stock = #{stock},`status` = #{status}, `suggest_selling_price` = #{suggestSellingPrice}
        where parent_id = #{id}
    </update>

    <select id="getCLabelGoods" resultMap="BaseResultMap">
        select * from bus_shop_goods where id in (
        select goods_id from bus_shop_goods_label where label_id = #{labelId})
        order by is_c_bit desc
    </select>

    <update id="updateOrderNum">
        update bus_shop_goods set order_num = #{orderNum}
        where id = #{id}
    </update>

    <delete id="deleteGoodsCart">
        delete from bus_shop_cart where business_id = #{goodsId}
        and type = 3;
    </delete>

    <select id="getAllHospital" resultType="java.lang.Long">
        select id from bus_hospital;
    </select>

    <select id="getUnsyncGoodIds" resultType="java.lang.Long">
        select t1.id
        from bus_shop_goods t1
        where t1.status = 1
        and t1.source_id =0
        and t1.parent_id =0
        and t1.is_c_bit!=1
        and t1.id in
        <foreach collection="goodsIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and t1.id not in (select parent_id from bus_shop_goods where hospital_id = #{hospitalId} and parent_id != 0 )
    </select>

    <select id="getConvertGoodsIds" resultType="java.lang.Long">
        select distinct t1.id
        from bus_shop_goods t1
        where t1.hospital_id = #{hospitalId}  and t1.parent_id in
        <foreach collection="goodsIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and t1.status = 1
    </select>

    <select id="searchGood" parameterType="com.puree.hospital.shop.domain.dto.BusShopSearchDTO"
    resultType="com.puree.hospital.shop.domain.vo.ShopGoodSearchVO">
        SELECT
        <choose>
            <when test="queryType == 1">
                1 AS item_type,
                '' AS pre_type,
                '' AS national_drug_code,
                t1.id AS item_id,
                t3.brand_name,
                t1.enterprise_id,
                t1.img AS item_img,
                t1.title AS item_name,
                t1.specification AS item_spec,
                t1.stock AS item_stock,
                t1.selling_price,
                t1.express_type AS expressType,
                t1.has_visitor AS hasVisitor,
                t1.has_come AS hasCome
            </when>
            <otherwise>
                t1.title AS item_name
            </otherwise>
        </choose>
        FROM
        bus_shop_goods t1
        INNER JOIN bus_shop_brand t3 ON t1.brand_id = t3.id
        WHERE
        t1.hospital_id = #{hospitalId}
        AND t1.status = 1
        AND t1.title LIKE CONCAT('%', #{keyWord}, '%')
        <choose>
            <when test="queryType == 1">
                ORDER BY t1.stock > 0 DESC
            </when>
            <otherwise>
                GROUP BY t1.title
                ORDER BY t1.stock > 0 DESC
            </otherwise>
        </choose>
    </select>

</mapper>
package com.puree.hospital.his.api.service;

import com.puree.hospital.his.api.entity.dto.HisOrderInfoDTO;
import com.puree.hospital.his.api.entity.dto.HisPayResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadResultDTO;
import com.puree.hospital.his.api.entity.dto.HisRefundDTO;
import com.puree.hospital.his.api.entity.dto.HisRefundResultDTO;

/**
 * his 支付/退款 服务
 * <AUTHOR>
 * @date 2025-1-16
 */
public interface HisPayService {

    /**
     * 支付信息上传
     * @param dto 支付信息
     * @return 支付信息上传结果
     */
    HisPayUploadResultDTO pay(HisPayUploadDTO dto);

    /**
     * 退费
     * @param dto 退费信息
     * @return 是否允许退款
     */
    HisRefundResultDTO refund(HisRefundDTO dto);

    /**
     * HIS 修改地址
     * @param dto 收货信息
     * @return 是否成功
     */
    HisRefundResultDTO modifyAddress(HisOrderInfoDTO dto);

    /**
     * HIS 查询支付结果
     * @param dto 查询参数
     * @return 结果信息
     */
    Boolean payResult(HisPayResultDTO dto);

    /**
     * HIS 查询发票信息
     * @param invoiceNo 发票号
     * @return 发票url
     */
    String invoiceInfo(String invoiceNo);
}

package com.puree.hospital.his.api.feign;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.his.api.entity.HisPatientInfo;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoCreateResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoSaveDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 建档服务Feign客户端
 */
@FeignClient(contextId = "RemoteHisCreateArchivesService", value = ServiceNameConstants.HIS_SERVICE)
public interface RemoteHisCreateArchivesService {
    /**
     * 获取患者信息
     */
    @GetMapping("/creat-archives/patient-info")
    AjaxResult<HisPatientInfo> getPatientInfo(@RequestParam("patientName") String patientName,
                                              @RequestParam("idCardNo") String idCardNo);

    /**
     * 创建患者
     */
    @PostMapping("/creat-archives/patient")
    AjaxResult<HisPatientInfoCreateResultDTO> createPatient(@RequestBody HisPatientInfoSaveDTO dto);

    /**
     * 修改患者
     */
    @PutMapping("/creat-archives/patient")
    AjaxResult<Boolean> updatePatient(@RequestBody HisPatientInfoSaveDTO dto);


}

package com.puree.hospital.his.api.service;

import com.puree.hospital.his.api.entity.HisActiveDepartment;
import com.puree.hospital.his.api.entity.HisActiveDoctor;
import com.puree.hospital.his.api.entity.HisConsultationRegistration;
import com.puree.hospital.his.api.entity.HisExamineReport;
import com.puree.hospital.his.api.entity.HisExamineReportDetail;
import com.puree.hospital.his.api.entity.HisInspectReport;
import com.puree.hospital.his.api.entity.HisInspectReportDetail;
import com.puree.hospital.his.api.entity.HisOutPatientRecord;
import com.puree.hospital.his.api.entity.HisRegisteredInfo;
import com.puree.hospital.his.api.entity.dto.HisConsultationCancellationDTO;
import com.puree.hospital.his.api.entity.dto.HisConsultationRegistrationDTO;
import com.puree.hospital.his.api.entity.query.HisExamineReportQuery;
import com.puree.hospital.his.api.entity.query.HisInspectReportQuery;
import com.puree.hospital.his.api.entity.query.HisOutPatientRecordQuery;
import com.puree.hospital.his.api.entity.query.HisRegisteredInfoQuery;

import java.util.Date;
import java.util.List;

/**
 * his 挂号相关接口
 */
public interface HisRegistrationService {

    /**
     * 根据日期获取出诊科室列表 不使用
     * @param date 日期
     * @return 出诊科室列表
     */
    List<HisActiveDepartment> getActiveDepartmentList(Date date);

    /**
     * 根据 科室ID和日期查询出诊医生列表 不使用
     * @param departmentId 科室 ID
     * @param date 日期
     * @return 出诊医生列表
     */
    List<HisActiveDoctor> getActiveDoctorList(String departmentId, Date date);

    /**
     * HIS 挂号
     * @param dto dto
     * @return HisConsultationRegistration
     */
    HisConsultationRegistration consultationRegistration(HisConsultationRegistrationDTO dto);

    /**
     * HIS 取消挂号
     * @param dto dto
     * @return boolean
     */
    boolean consultationCancellation(HisConsultationCancellationDTO dto);

    /**
     * 获取就诊记录
     * @param query 查询条件
     * @return 就诊记录
     */
    List<HisRegisteredInfo> getRegisteredInfo(HisRegisteredInfoQuery query);

    /**
     * 查询门诊患者文本病历
     * @param query 查询条件
     * @return 患者病历
     */
    List<HisOutPatientRecord> getOutpatientRecord(HisOutPatientRecordQuery query);

    /**
     * 获取检验报告
     * @param query 查询条件
     * @return 患者检验报告
     */
    List<HisExamineReport> getExamineList(HisExamineReportQuery query);


    /**
     * 获取检验报告明细
     * @param query 查询条件
     * @return 患者检验报告明细
     */
    List<HisExamineReportDetail> getExamineDetail(HisExamineReportQuery query);

    /**
     * 获取检查报告
     * @param query 查询条件
     * @return 患者检查报告
     */
    List<HisInspectReport> getInspectList(HisInspectReportQuery query);

    /**
     * 获取检查报告明细
     * @param query 查询条件
     * @return 患者检查报告明细
     */
    List<HisInspectReportDetail> getInspectDetail(HisInspectReportQuery query);
}

package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 药品包装单位  字典表  dict_type： bus_drugs_packaging_unit
 */
@Getter
public enum HisDrugPackUnitEnum implements BaseHisEnum {
    // code：我方字典值   mapping：HIS字典值
    MINUNIT_79("", "79", "人份", "MINUNIT"),
    MINUNIT_72("", "72", "U", "MINUNIT"),
    MINUNIT_73("", "73", "次", "MINUNIT"),
    MINUNIT_1("", "1", "升", "MINUNIT"),
    MINUNIT_2("", "2", "毫升", "MINUNIT"),
    MINUNIT_3("", "3", "微升", "MINUNIT"),
    MINUNIT_4("", "4", "千克", "MINUNIT"),
    MINUNIT_5("", "5", "克", "MINUNIT"),
    MINUNIT_6("", "6", "毫克", "MINUNIT"),
    MINUNIT_7("", "7", "微克", "MINUNIT"),
    MINUNIT_8("", "8", "纳克", "MINUNIT"),
    MINUNIT_9("", "9", "皮克", "MINUNIT"),
    MINUNIT_10("", "10", "米", "MINUNIT"),
    MINUNIT_11("", "11", "分米", "MINUNIT"),
    MINUNIT_12("", "12", "厘米", "MINUNIT"),
    MINUNIT_13("", "13", "毫米", "MINUNIT"),
    MINUNIT_14("", "14", "微米", "MINUNIT"),
    MINUNIT_15("", "15", "纳米", "MINUNIT"),
    MINUNIT_16("", "16", "摩尔", "MINUNIT"),
    MINUNIT_17("", "17", "毫摩尔", "MINUNIT"),
    MINUNIT_18("", "18", "兆帕", "MINUNIT"),
    MINUNIT_19("", "19", "千帕", "MINUNIT"),
    MINUNIT_20("", "20", "帕", "MINUNIT"),
    MINUNIT_21("", "21", "摄氏度", "MINUNIT"),
    MINUNIT_22("", "22", "帕秒", "MINUNIT"),
    MINUNIT_23("", "23", "毫帕秒", "MINUNIT"),
    MINUNIT_24("", "24", "平方米每秒", "MINUNIT"),
    MINUNIT_25("", "25", "平方毫米每秒", "MINUNIT"),
    MINUNIT_26("", "26", "厘米的倒数", "MINUNIT"),
    MINUNIT_27("", "27", "千克每立方米", "MINUNIT"),
    MINUNIT_28("", "28", "克每立方厘米", "MINUNIT"),
    MINUNIT_29("", "29", "吉贝可", "MINUNIT"),
    MINUNIT_30("", "30", "兆贝可", "MINUNIT"),
    MINUNIT_31("", "31", "千贝可", "MINUNIT"),
    MINUNIT_32("", "32", "贝可", "MINUNIT"),
    MINUNIT_33("", "33", "天", "MINUNIT"),
    MINUNIT_34("", "34", "小时", "MINUNIT"),
    MINUNIT_35("", "35", "分钟", "MINUNIT"),
    MINUNIT_36("", "36", "秒", "MINUNIT"),
    MINUNIT_37("", "37", "单位", "MINUNIT"),
    MINUNIT_38("", "38", "国际单位", "MINUNIT"),
    MINUNIT_39("", "39", "批", "MINUNIT"),
    MINUNIT_40("443", "40", "件", "MINUNIT"),
    MINUNIT_41("", "41", "打", "MINUNIT"),
    MINUNIT_42("", "42", "合", "MINUNIT"),
    MINUNIT_43("", "43", "小合", "MINUNIT"),
    MINUNIT_44("", "44", "组", "MINUNIT"),
    MINUNIT_45("", "45", "联", "MINUNIT"),
    MINUNIT_46("444", "46", "瓶", "MINUNIT"),
    MINUNIT_47("", "47", "罐", "MINUNIT"),
    MINUNIT_48("", "48", "剂", "MINUNIT"),
    MINUNIT_81("", "81", "卷", "MINUNIT"),
    MINUNIT_74("", "74", "揿", "MINUNIT"),
    MINUNIT_80("", "80", "份", "MINUNIT"),
    MINUNIT_75("", "75", "g", "MINUNIT"),
    MINUNIT_76("", "76", "套", "MINUNIT"),
    MINUNIT_77("395", "77", "盒", "MINUNIT"),
    MINUNIT_78("", "78", "帖", "MINUNIT"),
    MINUNIT_49("449", "49", "片", "MINUNIT"),
    MINUNIT_50("459", "50", "丸", "MINUNIT"),
    MINUNIT_51("450", "51", "粒", "MINUNIT"),
    MINUNIT_52("457", "52", "枚", "MINUNIT"),
    MINUNIT_53("", "53", "条", "MINUNIT"),
    MINUNIT_54("", "54", "贴", "MINUNIT"),
    MINUNIT_55("446", "55", "支", "MINUNIT"),
    MINUNIT_56("", "56", "剂", "MINUNIT"),
    MINUNIT_57("445", "57", "袋", "MINUNIT"),
    MINUNIT_58("447", "58", "包", "MINUNIT"),
    MINUNIT_59("", "59", "AU", "MINUNIT"),
    MINUNIT_60("448", "60", "个", "MINUNIT"),
    MINUNIT_61("", "61", "块", "MINUNIT"),
    MINUNIT_62("", "62", "颗", "MINUNIT"),
    MINUNIT_63("", "63", "喷", "MINUNIT"),
    MINUNIT_64("", "64", "对", "MINUNIT"),
    MINUNIT_65("", "65", "副", "MINUNIT"),
    MINUNIT_66("", "66", "只", "MINUNIT"),
    MINUNIT_67("", "67", "种", "MINUNIT"),
    MINUNIT_68("", "68", "扎", "MINUNIT"),
    MINUNIT_69("", "69", "张", "MINUNIT"),
    MINUNIT_70("", "70", "针", "MINUNIT"),
    MINUNIT_71("452", "71", "板", "MINUNIT");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisDrugPackUnitEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * his 支付方式
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisPaymentModeEnum implements BaseHisEnum {
    CA("", "CA", "现金", "PAYMODES"),
    CD( "", "CD","银联卡", "PAYMODES"),
    CH( "", "CH","支票", "PAYMODES"),
    CZ( "", "CZ","诊查费预收款", "PAYMODES"),
    DXWX( "","DXWX", "微信(公众号)", "PAYMODES"),
    YHQ( "", "YHQ","优惠券", "PAYMODES"),
    WX("1", "HLWX", "微信", "PAYMODES"),
    ZF("", "ZF", "支付宝", "PAYMODES"),
    MC("", "MC", "社保卡", "PAYMODES"),
    YBDZ( "", "YBDZ","医保电子凭证", "PAYMODES"),
    YYDF( "", "YYDF","医院垫付", "PAYMODES"),
    WGZF( "", "WGZF","无感支付", "PAYMODES"),
    JM("", "JM", "政策减免", "PAYMODES"),
    YXZM( "", "YXZM","用血直免记账", "PAYMODES"),
    HXYH("", "HXYH", "健康通", "PAYMODES"),
    QFJZ( "", "QFJZ","欠费记账", "PAYMODES"),
    YBWX( "3", "YBHL","医保移动支付(微信)", "PAYMODES");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;


    HisPaymentModeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

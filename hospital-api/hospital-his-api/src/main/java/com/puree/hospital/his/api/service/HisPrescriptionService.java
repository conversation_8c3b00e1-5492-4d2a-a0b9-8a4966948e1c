package com.puree.hospital.his.api.service;

import com.puree.hospital.his.api.entity.HisPrescriptionDetail;
import com.puree.hospital.his.api.entity.HisPrescriptionInfo;
import com.puree.hospital.his.api.entity.HisPrescriptionInfoSyncResult;
import com.puree.hospital.his.api.entity.dto.HisDiagnosisSyncDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionInfoDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionInfoSyncDTO;
import com.puree.hospital.his.api.entity.dto.HisPrescriptionDelDTO;

import java.util.List;

/**
 * his 处方
 */
public interface HisPrescriptionService {

    /**
     * his 处方同步
     * @param dto his 处方信息
     * @return 处方号
     */
    HisPrescriptionInfoSyncResult prescriptionInfoSync(HisPrescriptionInfoSyncDTO dto);

    /**
     * 西药处方同步
     * @param dto his 处方信息
     * @return 处方号
     */
    HisPrescriptionInfoSyncResult prescriptionInfoWesternSync(HisPrescriptionInfoSyncDTO dto);

    /**
     * 中药处方同步
     * @param dto his 处方信息
     * @return 处方号
     */
    HisPrescriptionInfoSyncResult prescriptionInfoChineseSync(HisPrescriptionInfoSyncDTO dto);

    /**
     * 删除处方
     * @param dto 请求参数
     * @return 处方是否删除成功
     */
    boolean delete(HisPrescriptionDelDTO dto);

    /**
     * 查询处方费用明细
     * @param recipeNums 处方号
     * @return 处方明细
     */
    List<HisPrescriptionDetail> prescriptionDetail(String recipeNums);

    /**
     * 诊断信息同步
     * @param dto dto
     * @return 同步结果
     */
    Boolean diagnosisSync(HisDiagnosisSyncDTO dto);

    /**
     * 处方信息查询
     * @param dto dto
     * @return 处方信息
     */
    List<HisPrescriptionInfo> prescriptionInfo(HisPrescriptionInfoDTO dto);
}

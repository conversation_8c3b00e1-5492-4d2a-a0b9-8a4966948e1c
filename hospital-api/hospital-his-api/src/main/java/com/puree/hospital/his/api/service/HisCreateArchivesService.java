package com.puree.hospital.his.api.service;


import com.puree.hospital.his.api.entity.HisPatientInfo;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoCreateResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoSaveDTO;

/**
 * his 建档
 */
public interface HisCreateArchivesService {

    /**
     * 提供 身份证和姓名查询患者信息
     * @param patientName 姓名
     * @param idCardNo 身份证
     * @return 患者信息
     */
    HisPatientInfo getPatientInfo(String patientName, String idCardNo);

    /**
     * 创建患者
     * @param dto 患者信息
     * @return patientID 以及 时间
     */
    HisPatientInfoCreateResultDTO createPatient(HisPatientInfoSaveDTO dto);

    /**
     * 修改患者
     * @param dto 患者信息
     * @return 成功或失败
     */
    Boolean updatePatient(HisPatientInfoSaveDTO dto);
}

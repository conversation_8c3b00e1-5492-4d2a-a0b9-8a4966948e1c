package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 药品类型  对应表：bus_commodity_classify
 */
@Getter
public enum HisDrugTypeEnum implements BaseHisEnum {
    // code：我方字典值   mapping：HIS字典值
    DRUG_TYPE_P("553", "P", "西药", "CLASSCODE"),
    DRUG_TYPE_PCC("", "PCC", "中草药", "CLASSCODE"),
    DRUG_TYPE_PCZ("555", "PCZ", "中成药", "CLASSCODE");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisDrugTypeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

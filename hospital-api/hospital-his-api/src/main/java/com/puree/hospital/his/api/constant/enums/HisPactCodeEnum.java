package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * 患者医保类型
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisPactCodeEnum implements BaseHisEnum {
    SELF_PAY("SELF", "01", "自费", "PACTCODE"),
    WORKER_MEDICAL("", "2010", "省内异地医保", "PACTCODE"),
    OUT_OF_PROVINCE_MEDICAL("", "2011", "省外异地医保", "PACTCODE"),
    PROVINCIAL_DIRECT_MEDICAL("", "2020", "广东省直医保", "PACTCODE"),
    EMPLOYEE_MEDICAL("MI_GENERAL", "2090", "职工医保", "PACTCODE"),
    MI_SPECIAL("MI_SPECIAL", "2090", "职工医保", "PACTCODE"),
    MATERNITY_MEDICAL("", "2091", "生育医保", "PACTCODE"),
    RESIDENT_MEDICAL("", "2094", "居民医保", "PACTCODE"),
    INJURY_MEDICAL("", "3001", "工伤医保", "PACTCODE"),
    MUNICIPAL_DIRECT_MEDICAL("", "4001", "市直医保", "PACTCODE"),
    ZENGCHENG_MEDICAL("", "4004", "增城区属医保", "PACTCODE"),
    HUANGPU_MEDICAL("", "5020", "黄埔区属医保", "PACTCODE"),
    TIANHE_MEDICAL("", "5030", "天河区属医保", "PACTCODE"),
    HOSPITAL_ACCOUNT_0("", "5004", "本院记账0%", "PACTCODE"),
    HOSPITAL_ACCOUNT_5("", "9001", "本院记账5%", "PACTCODE"),
    HOSPITAL_ACCOUNT_10("", "9002", "本院记账10%", "PACTCODE"),
    HOSPITAL_ACCOUNT_20("", "9003", "本院记账20%", "PACTCODE"),
    HOSPITAL_ACCOUNT_MATERNITY("", "9004", "本院记账生育", "PACTCODE"),
    UNION_MEDICAL_ACCOUNT("", "9005", "联合医务记账", "PACTCODE"),
    AGREEMENT_UNIT_ACCOUNT("", "6001", "协议单位记账", "PACTCODE"),
    NO_SUPPORT_ACCOUNT("", "7001", "三无人员记账", "PACTCODE"),
    OCCUPATIONAL_EXPOSURE_ACCOUNT("", "7002", "职业暴露记账", "PACTCODE"),
    MEDICAL_DISPUTE_ACCOUNT("", "7003", "医疗纠纷记账", "PACTCODE"),
    EMERGENCY_ACCOUNT("", "7004", "抢救记账", "PACTCODE"),
    OTHER_ACCOUNT("", "7005", "其他人员记账", "PACTCODE"),
    TUBERCULOSIS_ACCOUNT("", "8002", "结核病记账", "PACTCODE"),
    MALARIA_SCREENING_ACCOUNT("", "8003", "疟疾筛查记账", "PACTCODE"),
    PIT_AND_FISSURE_SEALANT_ACCOUNT("", "8004", "窝沟封闭记账", "PACTCODE"),
    TWO_CANCER_SCREENING_ACCOUNT("", "8005", "两癌普查记账", "PACTCODE"),
    WOMEN_DISEASE_ACCOUNT("", "8006", "妇女病诊治记账", "PACTCODE"),
    BIRTH_SCREENING_ACCOUNT("", "8007", "出生筛查记账", "PACTCODE"),
    MATERNAL_BLOCK_ACCOUNT("", "8009", "母婴阻断记账", "PACTCODE"),
    SYPHILIS_CHECK_ACCOUNT("", "8010", "梅毒检查记账", "PACTCODE"),
    PREGNANT_SYPHILIS_ACCOUNT("", "8011", "孕妇梅毒记账", "PACTCODE"),
    INTESTINAL_SCREENING_ACCOUNT("", "8014", "肠道筛查记账", "PACTCODE"),
    FAMILY_PLANNING_ACCOUNT("", "8016", "计生记账", "PACTCODE"),
    EMERGENCY_GREEN_CHANNEL_ACCOUNT("", "8033", "急诊绿色通道记账", "PACTCODE"),
    SPECIAL_PRESCRIPTION_ACCOUNT("", "8034", "特定处方(勿用)", "PACTCODE"),
    NEWBORN_FREE_SCREENING_ACCOUNT("", "8035", "新生儿免费筛查", "PACTCODE"),
    PREMATURE_RETINOPATHY_SCREENING_ACCOUNT("", "8036", "早产儿视网膜病筛查记账", "PACTCODE"),
    TUMOR_SCREENING_ACCOUNT("", "8037", "肿瘤筛查记账", "PACTCODE"),
    CLINICAL_TRIAL_FREE_ACCOUNT("", "8050", "临床试验专项免费记账", "PACTCODE");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisPactCodeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }
}
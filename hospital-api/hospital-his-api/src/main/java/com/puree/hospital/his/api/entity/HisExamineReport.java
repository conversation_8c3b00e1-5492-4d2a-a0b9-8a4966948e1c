package com.puree.hospital.his.api.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * his 检验
 */
@Data
public class HisExamineReport {

    /**
     * 报告单ID
     */
    private String reportId;

    /**
     * 检验条码样本编号
     */
    private String requisitionId;

    /**
     * 病人来源
     */
    private String patientType;

    /**
     * 就诊号
     */
    private String inpatientId;

    /**
     * 病历号就诊卡号
     */
    private String outpatientId;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 年龄
     */
    private String ageInput;

    /**
     * 病人所在科室
     */
    private String patientDept;

    /**
     * 病人病床
     */
    private String patientBed;

    /**
     * 检验项目代码
     */
    private String testOrder;

    /**
     * 检验项目名称
     */
    private String testOrderName;

    /**
     * 送检时间
     */
    private String inputTime;

    /**
     * 审核人员
     */
    private String checkPerson;

    /**
     * 审核时间
     */
    private String checkTime;

    /**
     * 开立时间
     */
    private String orderTime;

    /**
     * 样本接收时间
     */
    private String inceptTime;

    /**
     * 开立时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDateTime;

}

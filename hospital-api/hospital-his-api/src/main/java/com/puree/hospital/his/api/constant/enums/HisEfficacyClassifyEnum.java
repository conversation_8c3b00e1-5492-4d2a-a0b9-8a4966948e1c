package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 药品药理功效分类  对应表：bus_dict_drugs_efficacy_classify
 */
@Getter
public enum HisEfficacyClassifyEnum implements BaseHisEnum {
    // code：我方字典值   mapping：HIS字典值
    EFFICACY_CLASS_9C("", "9C", "用于休克的血管活性药", "PHYFUNCTION"),
    EFFICACY_CLASS_9D("", "9D", "抗高血压药", "PHYFUNCTION"),
    EFFICACY_CLASS_15C("", "15C", "造血细胞因子", "PHYFUNCTION"),
    EFFICACY_CLASS_15D("", "15D", "血浆代用品", "PHYFUNCTION"),
    EFFICACY_CLASS_16("", "16", "抗组胺药", "PHYFUNCTION"),
    EFFICACY_CLASS_12B("", "12B", "胃粘膜保护剂", "PHYFUNCTION"),
    EFFICACY_CLASS_12C("", "12C", "解痉药", "PHYFUNCTION"),
    EFFICACY_CLASS_12D("", "12D", "促胃肠动力药", "PHYFUNCTION"),
    EFFICACY_CLASS_17("", "17", "糖类、盐类及酸碱平衡调节药", "PHYFUNCTION"),
    EFFICACY_CLASS_20("", "20", "雌激素、孕激素及抗性激素药", "PHYFUNCTION"),
    EFFICACY_CLASS_20A("", "20A", "雌激素", "PHYFUNCTION"),
    EFFICACY_CLASS_9E("", "9E", "抗心绞痛药", "PHYFUNCTION"),
    EFFICACY_CLASS_9F("", "9F", "血脂调节药", "PHYFUNCTION"),
    EFFICACY_CLASS_2A("", "2A", "巴比妥类药", "PHYFUNCTION"),
    EFFICACY_CLASS_12F("", "12F", "催吐药和止吐药", "PHYFUNCTION"),
    EFFICACY_CLASS_27A7("", "27A7", "大环内酯类", "PHYFUNCTION"),
    EFFICACY_CLASS_27A8("", "27A8", "林可霉素类", "PHYFUNCTION"),
    EFFICACY_CLASS_27A9("", "27A9", "其它抗生素", "PHYFUNCTION"),
    EFFICACY_CLASS_27B("", "27B", "磺胺类药", "PHYFUNCTION"),
    EFFICACY_CLASS_27C("", "27C", "喹诺酮类药", "PHYFUNCTION"),
    EFFICACY_CLASS_27D("", "27D", "其它抗菌药", "PHYFUNCTION"),
    EFFICACY_CLASS_27F("", "27F", "抗麻风药", "PHYFUNCTION"),
    EFFICACY_CLASS_8("", "8", "解热镇痛药及抗痛风药", "PHYFUNCTION"),
    EFFICACY_CLASS_8A("", "8A", "解热镇痛抗炎药", "PHYFUNCTION"),
    EFFICACY_CLASS_9("", "9", "心血管系统药", "PHYFUNCTION"),
    EFFICACY_CLASS_9A("", "9A", "强心药", "PHYFUNCTION"),
    EFFICACY_CLASS_9B("", "9B", "抗心律失常药", "PHYFUNCTION"),
    EFFICACY_CLASS_24("", "24", "防骨质疏松症药物", "PHYFUNCTION"),
    EFFICACY_CLASS_25("", "25", "维生素类、微量元素与营养药物", "PHYFUNCTION"),
    EFFICACY_CLASS_26("", "26", "酶类药物", "PHYFUNCTION"),
    EFFICACY_CLASS_4A("", "4A", "抗精神病药", "PHYFUNCTION"),
    EFFICACY_CLASS_4B("", "4B", "抗抑郁药", "PHYFUNCTION"),
    EFFICACY_CLASS_23("", "23", "治疗糖尿病药", "PHYFUNCTION"),
    EFFICACY_CLASS_23A("", "23A", "胰岛素", "PHYFUNCTION"),
    EFFICACY_CLASS_1A("", "1A", "吸入全麻药", "PHYFUNCTION"),
    EFFICACY_CLASS_27("", "27", "抗感染药物", "PHYFUNCTION"),
    EFFICACY_CLASS_27A1("", "27A1", "青霉素类", "PHYFUNCTION"),
    EFFICACY_CLASS_27A2("", "27A2", "头孢菌素类", "PHYFUNCTION"),
    EFFICACY_CLASS_27A4("", "27A4", "氨基糖苷类", "PHYFUNCTION"),
    EFFICACY_CLASS_27A3("", "27A3", "其它β内酰胺类", "PHYFUNCTION"),
    EFFICACY_CLASS_27A5("", "27A5", "四环素类", "PHYFUNCTION"),
    EFFICACY_CLASS_1("", "1", "麻醉药与麻醉辅助药", "PHYFUNCTION"),
    EFFICACY_CLASS_22B("", "22B", "抗甲状腺用药", "PHYFUNCTION"),
    EFFICACY_CLASS_4C("", "4C", "抗焦虑药", "PHYFUNCTION"),
    EFFICACY_CLASS_1C("", "1C", "局麻药", "PHYFUNCTION"),
    EFFICACY_CLASS_1D("", "1D", "肌松药", "PHYFUNCTION"),
    EFFICACY_CLASS_1E("", "1E", "胆碱酯酶抑制药", "PHYFUNCTION"),
    EFFICACY_CLASS_2("", "2", "催眠药与镇静药", "PHYFUNCTION"),
    EFFICACY_CLASS_22A("", "22A", "甲状腺激素", "PHYFUNCTION"),
    EFFICACY_CLASS_15A("", "15A", "抗贫血药", "PHYFUNCTION"),
    EFFICACY_CLASS_10("", "10", "脑循环与促智药", "PHYFUNCTION"),
    EFFICACY_CLASS_11("", "11", "呼吸系统用药", "PHYFUNCTION"),
    EFFICACY_CLASS_1B("", "1B", "静脉全麻药", "PHYFUNCTION"),
    EFFICACY_CLASS_20C("", "20C", "避孕药", "PHYFUNCTION"),
    EFFICACY_CLASS_20D("", "20D", "抗性激素", "PHYFUNCTION"),
    EFFICACY_CLASS_15("", "15", "血液系统药", "PHYFUNCTION"),
    EFFICACY_CLASS_27A6("", "27A6", "氯霉素类", "PHYFUNCTION"),
    EFFICACY_CLASS_13B("", "13B", "脱水药", "PHYFUNCTION"),
    EFFICACY_CLASS_14("", "14", "子宫收缩药及引产药", "PHYFUNCTION"),
    EFFICACY_CLASS_5("", "5", "抗震颤麻痹药", "PHYFUNCTION"),
    EFFICACY_CLASS_6("", "6", "镇痛药", "PHYFUNCTION"),
    EFFICACY_CLASS_7("", "7", "中枢神经兴奋药", "PHYFUNCTION"),
    EFFICACY_CLASS_15B("", "15B", "止血、抗凝血药", "PHYFUNCTION"),
    EFFICACY_CLASS_23B("", "23B", "口服降血糖药", "PHYFUNCTION"),
    EFFICACY_CLASS_23B1("", "23B1", "磺酰脲类", "PHYFUNCTION"),
    EFFICACY_CLASS_23B2("", "23B2", "双胍类", "PHYFUNCTION"),
    EFFICACY_CLASS_23B3("", "23B3", "α糖苷酶抑制剂", "PHYFUNCTION"),
    EFFICACY_CLASS_23B4("", "23B4", "噻唑烷二酮类", "PHYFUNCTION"),
    EFFICACY_CLASS_23B5("", "23B5", "非磺酰脲类促胰岛素分泌剂", "PHYFUNCTION"),
    EFFICACY_CLASS_4E("", "4E", "精神兴奋药", "PHYFUNCTION"),
    EFFICACY_CLASS_20B("", "20B", "孕激素", "PHYFUNCTION"),
    EFFICACY_CLASS_21("", "21", "垂体激素及相关药物", "PHYFUNCTION"),
    EFFICACY_CLASS_18("", "18", "肾上腺皮质激素", "PHYFUNCTION"),
    EFFICACY_CLASS_19("", "19", "雄激素及蛋白同化类固醇", "PHYFUNCTION"),
    EFFICACY_CLASS_19A("", "19A", "雄激素", "PHYFUNCTION"),
    EFFICACY_CLASS_19B("", "19B", "蛋白同化类固醇", "PHYFUNCTION"),
    EFFICACY_CLASS_12E("", "12E", "助消化药", "PHYFUNCTION"),
    EFFICACY_CLASS_11A("", "11A", "镇咳药", "PHYFUNCTION"),
    EFFICACY_CLASS_11B("", "11B", "平喘药", "PHYFUNCTION"),
    EFFICACY_CLASS_11C("", "11C", "祛痰药", "PHYFUNCTION"),
    EFFICACY_CLASS_27G("", "27G", "抗真菌药", "PHYFUNCTION"),
    EFFICACY_CLASS_27H("", "27H", "抗病毒药", "PHYFUNCTION"),
    EFFICACY_CLASS_28("", "28", "抗寄生虫药", "PHYFUNCTION"),
    EFFICACY_CLASS_28A("", "28A", "抗疟药", "PHYFUNCTION"),
    EFFICACY_CLASS_28B("", "28B", "抗利什曼原虫药", "PHYFUNCTION"),
    EFFICACY_CLASS_28C("", "28C", "抗阿米巴及滴虫药", "PHYFUNCTION"),
    EFFICACY_CLASS_28D("", "28D", "抗吸虫药", "PHYFUNCTION"),
    EFFICACY_CLASS_28E("", "28E", "抗肠虫药", "PHYFUNCTION"),
    EFFICACY_CLASS_28F("", "28F", "抗丝虫药", "PHYFUNCTION"),
    EFFICACY_CLASS_29("", "29", "抗肿瘤药", "PHYFUNCTION"),
    EFFICACY_CLASS_31("", "31", "眼科用药", "PHYFUNCTION"),
    EFFICACY_CLASS_32("", "32", "解毒药", "PHYFUNCTION"),
    EFFICACY_CLASS_33("", "33", "消毒防腐药", "PHYFUNCTION"),
    EFFICACY_CLASS_34("", "34", "X线造影剂与诊断用药", "PHYFUNCTION"),
    EFFICACY_CLASS_35("", "35", "放射性药物", "PHYFUNCTION"),
    EFFICACY_CLASS_36("", "36", "生物制品", "PHYFUNCTION"),
    EFFICACY_CLASS_36A("", "36A", "疫苗类", "PHYFUNCTION"),
    EFFICACY_CLASS_36B("", "36B", "菌苗类", "PHYFUNCTION"),
    EFFICACY_CLASS_36C("", "36C", "类霉素类", "PHYFUNCTION"),
    EFFICACY_CLASS_36D("", "36D", "抗毒素及免疫血清", "PHYFUNCTION"),
    EFFICACY_CLASS_36E("", "36E", "免疫球蛋白", "PHYFUNCTION"),
    EFFICACY_CLASS_12A("", "12A", "制酸药", "PHYFUNCTION"),
    EFFICACY_CLASS_12G("", "12G", "泻药和止泻药", "PHYFUNCTION"),
    EFFICACY_CLASS_12H("", "12H", "肝胆疾病辅助用药", "PHYFUNCTION"),
    EFFICACY_CLASS_13("", "13", "利尿药与脱水药", "PHYFUNCTION"),
    EFFICACY_CLASS_13A("", "13A", "利尿药", "PHYFUNCTION"),
    EFFICACY_CLASS_8B("", "8B", "抗痛风药", "PHYFUNCTION"),
    EFFICACY_CLASS_12("", "12", "消化系统药", "PHYFUNCTION"),
    EFFICACY_CLASS_22C("", "22C", "碘和碘制剂", "PHYFUNCTION"),
    EFFICACY_CLASS_4D("", "4D", "抗躁狂药", "PHYFUNCTION"),
    EFFICACY_CLASS_27A("", "27A", "抗生素", "PHYFUNCTION"),
    EFFICACY_CLASS_27E("", "27E", "抗结核药", "PHYFUNCTION"),
    EFFICACY_CLASS_30("", "30", "皮肤科用药", "PHYFUNCTION"),
    EFFICACY_CLASS_22("", "22", "甲状腺用药", "PHYFUNCTION"),
    EFFICACY_CLASS_2B("", "2B", "苯二氮卓类药", "PHYFUNCTION"),
    EFFICACY_CLASS_3("", "3", "抗癫痫药和抗惊厥药", "PHYFUNCTION"),
    EFFICACY_CLASS_4("", "4", "治疗精神障碍药", "PHYFUNCTION");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisEfficacyClassifyEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

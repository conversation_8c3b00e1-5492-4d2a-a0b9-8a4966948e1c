package com.puree.hospital.his.api.entity;

import lombok.Data;

/**
 * his 挂号 响应
 */
@Data
public class HisConsultationRegistration {
    /**
     * 流水号
     */
    private String tranSerialNO;

    /**
     * 票据号
     */
    private String regFlow;

    /**
     * 医生工号
     */
    private String doctorID;

    /**
     * 就诊时间
     */
    private String seeTime;

    /**
     * 看诊序号
     */
    private String seeNO;

    /**
     * 挂号级别
     */
    private String regType;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者卡号
     */
    private String patientID;

    /**
     * 费用类别
     */
    private String feeType;

    /**
     * 挂号收费项目
     */
    private String feeItem;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 科室地址
     */
    private String departmentAddress;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 卡余额
     */
    private String balance;

    /**
     * 电子发票号
     */
    private String elecInvoiceUrl;
}

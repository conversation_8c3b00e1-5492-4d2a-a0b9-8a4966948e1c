package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import com.puree.hospital.his.api.exception.HisNoCorrespondingMappingException;
import lombok.Getter;

/**
 * his 证件类型
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisIdCardTypeEnum implements BaseHisEnum {
    ID_CARD_01("01", "01", "身份证", "IDCard"),
    ID_CARD_2("", "2", "军官证", "IDCard"),
    ID_CARD_3("", "3", "出生证", "IDCard"),
    ID_CARD_4("03", "4", "护照", "IDCard"),
    ID_CARD_5("", "5", "驾驶证", "IDCard"),
    ID_CARD_6("", "6", "市民卡", "IDCard"),
    ID_CARD_7("", "7", "学生卡", "IDCard"),
    ID_CARD_8("06", "8", "港澳通行证", "IDCard"),
    ID_CARD_9("07", "9", "大陆居民往来台湾通行证", "IDCard"),
    ID_CARD_10("", "10", "其他", "IDCard"),
    ID_CARD_99("", "99", "外国人永久居留身份证", "IDCard");

    /**
     * 我方字典值（留空）
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisIdCardTypeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

    /**
     * 查询对应映射
     * @param code 我方字典值
     * @return 对应的his字典值
     */
    public static String toMapping(String code) {
        for (HisIdCardTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getMapping();
            }
        }
        throw new HisNoCorrespondingMappingException();
    }

    /**
     * 查询我方值
     * @param mapping 映射
     * @return 我方值
     */
    public static String toCode(String mapping) {
        for (HisIdCardTypeEnum value : values()) {
            if (value.getMapping().equals(mapping)) {
                return value.getCode();
            }
        }
        throw new HisNoCorrespondingMappingException();
    }

}
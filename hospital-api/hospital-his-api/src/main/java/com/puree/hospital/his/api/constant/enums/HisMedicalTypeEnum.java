package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * 医疗类别
 * 我方字典值 {@link com.puree.hospital.insurance.infrastructure.enums.mi.MedTypeEnum}
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisMedicalTypeEnum implements BaseHisEnum {
    GENERAL_OUTPATIENT("11", "11", "普通门诊", "GZSI_MED_TYPE"),
    EMERGENCY("13", "13", "急诊", "GZSI_MED_TYPE"),
    CHRONIC_OUTPATIENT("14", "14", "门诊慢特病", "GZSI_MED_TYPE"),
    GENERAL_INPATIENT("21", "21", "普通住院", "GZSI_MED_TYPE"),
    MATERNITY_OUTPATIENT("51", "51", "生育门诊", "GZSI_MED_TYPE"),
    MATERNITY_INPATIENT("52", "52", "生育住院", "GZSI_MED_TYPE"),
    FAMILY_PLANNING("53", "53", "计划生育手术费", "GZSI_MED_TYPE"),
    LONG_TERM_CARE("", "71", "长护业务", "GZSI_MED_TYPE"),
    MEDICAL_RESCUE_OUTPATIENT("", "91", "医救普通门诊", "GZSI_MED_TYPE"),
    INPATIENT_EMERGENCY("", "210104", "住院急诊", "GZSI_MED_TYPE");

    /**
     * 我方字典值（留空）
     */
    private final String code;
    /**
     * HIS字典值（使用JSON中的code字段）
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS字典类型
     */
    private final String type;

    HisMedicalTypeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }


}
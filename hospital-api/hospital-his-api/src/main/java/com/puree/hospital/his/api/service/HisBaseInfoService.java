package com.puree.hospital.his.api.service;


import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.entity.HisDrugInfo;

import com.puree.hospital.his.api.entity.query.HisDrugInfoQuery;

import java.util.List;

/**
 * his 基础信息
 */
public interface HisBaseInfoService {

    /**
     * 获取字典列表
     * @param type 字典类型
     * @return 字典
     */
    List<HisDicInfo> getDicInfo(String type);

    /**
     * 通过药品代码获取药品信息
     * @param drugCode 药品代码
     * @return 药品信息
     */
    HisDrugInfo getDrugInfoByCode(String drugCode);

    /**
     * 获取药品信息列表
     * @param query 查询参数
     * @return 药品列表
     */
    List<HisDrugInfo> getDrugInfoList(HisDrugInfoQuery query);

    /**
     * 获取药品列表
     * @return 药品列表
     */
    List<HisDrugInfo> getDrugInfoList();
}

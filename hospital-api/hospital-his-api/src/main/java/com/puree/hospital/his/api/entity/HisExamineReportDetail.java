package com.puree.hospital.his.api.entity;

import lombok.Data;

/**
 * his 检验明细
 */
@Data
public class HisExamineReportDetail {

    private static final String EXCEPTION_H = "H";
    private static final String EXCEPTION_L = "L";


    /**
     * 报告单ID
     */
    private String reportId;

    /**
     * 项目代码
     */
    private String testitemId;

    /**
     * 项目明细名称
     */
    private String chineseName;

    /**
     * 检测结果
     */
    private String quantitativeResult;

    /**
     * 单位
     */
    private String testItemUnit;

    /**
     * 参考值
     */
    private String testItemReference;

    /**
     * 定性：H偏高  L偏低  Z正常
     */
    private String qualitativeResult;

    /**
     * 异常校验
     */
    public boolean checkException() {
        if(EXCEPTION_H.equals(qualitativeResult) || EXCEPTION_L.equals(qualitativeResult)){
            return true;
        }
        return false;
    }
}

package com.puree.hospital.his.api.entity;

import lombok.Data;

/**
 * 医生信息
 */
@Data
public class HisDoctorInfo {
    /**
     * 医生身份证正反面图片，用逗号分隔
     */
    private String idCardNoImg;

    /**
     * 医生身份证
     */
    private String idCardNo;

    /**
     * 医生姓名
     */
    private String fullName;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 医生性别，0 女 1男，不传可以从身份证取
     */
    private String sex;

    /**
     * 医生头像
     */
    private String photo;

    /**
     * 医生类型，0医生、1审方药师、2护士、4健康管理师、5心理咨询师、9康复师、10营养师
     */
    private String role;

    /**
     * 医生职称
     */
    private String title;

    /**
     * 执业方式，0 多点 1 本院 2特聘专家
     */
    private Double isThisCourt;

    /**
     * 第一执业医院，多点执业的情况下
     */
    private String firstHospital;

    /**
     * 第一执业医院组织机构代码，多点执业的情况下
     */
    private String firstHospitalUnifiedCreditCode;

    /**
     * 医生科室表
     */
    private String depts;

    /**
     * 医生执业证图片路径
     */
    private String practiceCertificate;

    /**
     * 执业证发证日期，格式：yyyy-MM-dd
     */
    private String practiceCertificateTime;

    /**
     * 医生执业证编号
     */
    private String practiceCertificateNumber;

    /**
     * 执业类别编码，1 临床、2 口腔、3 公共卫生、4 中医
     */
    private String practisingTypeCode;

    /**
     * 执业范围编码
     */
    private String practisingScopeCode;

    /**
     * 医生资格证图片路径
     */
    private String qualificationCertificate;

    /**
     * 资格证发证日期，格式：yyyy-MM-dd
     */
    private String qualificationCertificateTime;

    /**
     * 医生资格证编号
     */
    private String qualificationCertificateNumber;

    /**
     * 医生职称证图片路径
     */
    private String titleCertificate;

    /**
     * 职称证发证日期，格式：yyyy-MM-dd
     */
    private String titleCertificateTime;

    /**
     * 医生职称证编号
     */
    private String titleCertificateNumber;

    /**
     * 参加工作日期，格式：yyyy-MM-dd
     */
    private String workTime;

    /**
     * 医生擅长
     */
    private String beGoodAt;

    /**
     * 医生介绍
     */
    private String introduce;

    /**
     * 医生状态，0停用 1启用
     */
    private String status;

    /**
     * 医保编码
     */
    private String nationalDoctorCode;

    /**
     * 是否医助，1-是 0-否
     */
    private String assistantFlag;

    /**
     * 员工ID
     */
    private String EMPL_ID;
}
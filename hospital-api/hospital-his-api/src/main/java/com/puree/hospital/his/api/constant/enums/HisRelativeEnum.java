package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 联系人关系,字典表  dict_type : bus_patient_relationship
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisRelativeEnum implements BaseHisEnum {
    EMPTY("", "00", "-", "RELATIVE"),
    SELF("1", "01", "本人", "RELATIVE"),
    HOUSEHOLDER("", "02", "户主", "RELATIVE"),
    SPOUSE("2", "10", "配偶", "RELATIVE"),
    HUSBAND("", "11", "夫", "RELATIVE"),
    WIFE("", "12", "妻", "RELATIVE"),
    SON("3", "20", "子", "RELATIVE"),
    ELDEST_SON("", "22", "长子", "RELATIVE"),
    ONLY_SON("", "21", "独生子", "RELATIVE"),
    SECOND_SON("", "23", "次子", "RELATIVE"),
    THIRD_SON("", "24", "三子", "RELATIVE"),
    FOURTH_SON("", "25", "四子", "RELATIVE"),
    FIFTH_SON("", "26", "五子", "RELATIVE"),
    ADOPTED_SON("", "27", "养子或继子", "RELATIVE"),
    SON_IN_LAW("", "28", "女婿", "RELATIVE"),
    OTHER_SON("", "29", "其他儿子", "RELATIVE"),
    DAUGHTER("4", "30", "女", "RELATIVE"),
    ONLY_DAUGHTER("", "31", "独生女", "RELATIVE"),
    ELDEST_DAUGHTER("", "32", "长女", "RELATIVE"),
    SECOND_DAUGHTER("", "33", "二女", "RELATIVE"),
    THIRD_DAUGHTER("", "34", "三女", "RELATIVE"),
    FOURTH_DAUGHTER("", "35", "四女", "RELATIVE"),
    FIFTH_DAUGHTER("", "36", "五女", "RELATIVE"),
    ADOPTED_DAUGHTER("", "37", "养女", "RELATIVE"),
    DAUGHTER_IN_LAW("", "38", "儿媳", "RELATIVE"),
    OTHER_DAUGHTER("", "39", "其他女儿", "RELATIVE"),
    GRANDSON("5", "41", "孙子", "RELATIVE"),
    GRANDDAUGHTER("6", "42", "孙女", "RELATIVE"),
    GRANDSON_IN_LAW("7", "43", "外孙子", "RELATIVE"),
    GRANDDAUGHTER_IN_LAW("8", "44", "外孙女", "RELATIVE"),
    GRANDSON_WIFE("", "45", "孙媳妇或外孙媳妇", "RELATIVE"),
    GRANDSON_HUSBAND("", "46", "孙女婿或外孙女婿", "RELATIVE"),
    GREAT_GRANDSON("", "47", "曾孙子或外曾孙子", "RELATIVE"),
    GREAT_GRANDDAUGHTER("", "48", "曾孙女或外曾孙女", "RELATIVE"),
    OTHER_GRANDCHILDREN("", "49", "其他孙子、孙女、或外孙子、外孙女", "RELATIVE"),
    PARENTS("9", "50", "父母", "RELATIVE"),
    FATHER("", "51", "父亲", "RELATIVE"),
    MOTHER("", "52", "母亲", "RELATIVE"),
    FATHER_IN_LAW("", "53", "公公", "RELATIVE"),
    MOTHER_IN_LAW("", "54", "婆婆", "RELATIVE"),
    FATHER_IN_LAW_WIFE("", "55", "岳父", "RELATIVE"),
    MOTHER_IN_LAW_HUSBAND("", "56", "岳母", "RELATIVE"),
    STEP_FATHER("", "57", "继父或养父", "RELATIVE"),
    STEP_MOTHER("", "58", "继母或养母", "RELATIVE"),
    OTHER_PARENT_RELATION("", "59", "其他父母关系", "RELATIVE"),
    GRANDPARENTS("10", "60", "祖父母或外祖父母,当祖父母来使用", "RELATIVE"),
    MATERNAL_GRANDPARENTS("11", "60", "祖父母或外祖父母,当外祖父母来使用", "RELATIVE"),
    GRANDFATHER("", "61", "祖父", "RELATIVE"),
    GRANDMOTHER("", "62", "祖母", "RELATIVE"),
    MATERNAL_GRANDFATHER("", "63", "外祖父", "RELATIVE"),
    MATERNAL_GRANDMOTHER("", "64", "外祖母", "RELATIVE"),
    GREAT_GRANDFATHER("", "66", "曾祖父", "RELATIVE"),
    GREAT_GRANDMOTHER("", "67", "曾祖母", "RELATIVE"),
    SPOUSE_GREAT_GRANDPARENTS("", "68", "配偶的曾祖父母或外曾祖父母", "RELATIVE"),
    OTHER_GRANDPARENTS("", "69", "其他祖父母或外祖父母关系", "RELATIVE"),
    SIBLINGS("", "70", "兄弟姐妹", "RELATIVE"),
    ELDER_BROTHER("12", "71", "兄", "RELATIVE"),
    SISTER_IN_LAW("", "72", "嫂", "RELATIVE"),
    YOUNGER_BROTHER("13", "73", "弟", "RELATIVE"),
    YOUNGER_SISTER_IN_LAW("", "74", "弟媳", "RELATIVE"),
    ELDER_SISTER("14", "75", "姐姐", "RELATIVE"),
    BROTHER_IN_LAW("", "76", "姐夫", "RELATIVE"),
    YOUNGER_SISTER("15", "77", "妹妹", "RELATIVE"),
    SISTER_IN_LAW_HUSBAND("", "78", "妹夫", "RELATIVE"),
    OTHER_SIBLINGS("", "79", "其他兄弟姐妹", "RELATIVE"),
    OTHER("16", "80", "其他", "RELATIVE"),
    UNCLE("", "81", "伯父", "RELATIVE"),
    AUNT("", "82", "伯母", "RELATIVE"),
    UNCLE_IN_LAW("", "83", "叔父", "RELATIVE"),
    AUNT_IN_LAW("", "84", "婶母", "RELATIVE"),
    MATERNAL_UNCLE("", "85", "舅父", "RELATIVE"),
    MATERNAL_AUNT("", "86", "舅母", "RELATIVE"),
    MATERNAL_UNCLE_IN_LAW("", "87", "姨父", "RELATIVE"),
    MATERNAL_AUNT_IN_LAW("", "88", "姨母", "RELATIVE"),
    PATERNAL_UNCLE("", "89", "姑父", "RELATIVE"),
    PATERNAL_AUNT("", "90", "姑母", "RELATIVE"),
    COUSIN("", "91", "堂兄弟堂兄妹", "RELATIVE"),
    COUSIN_IN_LAW("", "92", "表兄弟表姐妹", "RELATIVE"),
    NEPHEW("", "93", "侄子", "RELATIVE"),
    NIECE("", "94", "侄女", "RELATIVE"),
    NEPHEW_IN_LAW("", "95", "外甥", "RELATIVE"),
    NIECE_IN_LAW("", "96", "外甥女", "RELATIVE"),
    OTHER_RELATIVES("", "97", "其他亲属", "RELATIVE"),
    NON_RELATIVE("", "99", "非亲属", "RELATIVE"),
    COLLEAGUE("", "9901", "同事", "RELATIVE"),
    FRIEND("", "9902", "朋友", "RELATIVE"),
    ACCIDENT_CAUSER("", "9903", "肇事者", "RELATIVE"),
    BABYSITTER("", "9904", "保姆", "RELATIVE");

    private final String code;
    private final String mapping;
    private final String name;
    private final String type;

    HisRelativeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}
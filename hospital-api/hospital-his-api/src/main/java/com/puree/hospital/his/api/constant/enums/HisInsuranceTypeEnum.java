package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 医保类型  字典表  dict_type：bus_medical_insurance_type
 */
@Getter
public enum HisInsuranceTypeEnum implements BaseHisEnum {
    // code：我方字典值   mapping：HIS字典值
    SIGRADE_0("", "0", "未匹配(自费)", "SIGRADE"),
    SIGRADE_1("117", "1", "甲类", "SIGRADE"),
    SIGRADE_2("118", "2", "乙类", "SIGRADE"),
    SIGRADE_3("119", "3", "自费", "SIGRADE");


    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisInsuranceTypeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

package com.puree.hospital.his.api.entity.query;

import lombok.Data;

/**
 * 药品查询
 * <AUTHOR>
 * @date 2025-2-17
 */
@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
@Data
public class HisDrugInfoQuery {
    /**
     * 药品代码,默认为 ALL
     */
    private String drugCode = "ALL";

    /**
     * 页码（必填）
     */
    private Integer PageNumber = 1;

    /**
     * 每页条数（必填）
     */
    private Integer PageSize = 100;

    /**
     * 药品分类，字典类型=DRUGQUALITY，不过滤时请填ALL
     */
    private String ClassifyId = "ALL";

}

package com.puree.hospital.his.api.constant;

/**
 * his 异常常量
 * <AUTHOR>
 */
public class HisExceptionConstant {

    private HisExceptionConstant(){}

    /**
     * 查询不到患者信息
     */
    public static final  String PATIENT_INFO_IS_NULL = "查询不到患者信息";
    /**
     * 该科室已经没有可用的医生出诊信息
     */
    public static final  String ACTIVE_DOCTOR_IS_NULL = "查询不到医生列表,该科室已经没有可用的医生出诊信息";
    /**
     * 没有对应映射
     */
    public static final  String NO_CORRESPONDING_MAPPING = "没有对应的映射";
    /**
     * 处理对象配置异常
     */
    public static final  String CONFIG_PROCESSING_EXCEPTION = "处理对象配置时发生错误";

    /**
     *  挂号级别信息与HIS有差异，请核对！传入的级别名称：互联网医院,与HIS名称：特需门诊不符
     */
    public static final String REG_LEVEL_ERROR_PREFIX = "挂号级别信息与HIS有差异";


}

package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * his 医疗类别为 14 下的病种编码,todo 待后续开发，暂时无此功能
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisMedicalType14Enum  implements BaseHisEnum {
    M00101("", "M00101", "耐多药肺结核", "GZSI_MED_TYPE_14"),
    M00105("", "M00105", "活动性肺结核", "GZSI_MED_TYPE_14"),
    M00107("", "M00107", "淋巴结核", "GZSI_MED_TYPE_14"),
    M00201("", "M00201", "慢性乙型肝炎", "GZSI_MED_TYPE_14"),
    M00202("", "M00202", "慢性丙型肝炎（HCV RNA阳性）", "GZSI_MED_TYPE_14"),
    M00300("", "M00300", "艾滋病病毒感染", "GZSI_MED_TYPE_14"),
    M00502("", "M00502", "恶性肿瘤（放疗）", "GZSI_MED_TYPE_14"),
    M00503("", "M00503", "恶性肿瘤（化疗，含生物靶向药物、内分泌治疗）", "GZSI_MED_TYPE_14"),
    M00504("", "M00504", "恶性肿瘤（非放化疗）", "GZSI_MED_TYPE_14"),
    M00505("", "M00505", "恶性肿瘤辅助治疗（放射治疗、化学治疗及生物靶向药物治疗期间）", "GZSI_MED_TYPE_14"),
    M00902("", "M00902", "骨髓增生异常综合症", "GZSI_MED_TYPE_14"),
    M00904("", "M00904", "骨髓纤维化", "GZSI_MED_TYPE_14"),
    M01101("", "M01101", "地中海贫血（海洋性贫血或珠蛋白生成障碍性贫血）", "GZSI_MED_TYPE_14"),
    M01102("", "M01102", "再生障碍性贫血", "GZSI_MED_TYPE_14"),
    M01200("", "M01200", "血友病", "GZSI_MED_TYPE_14"),
    M01600("", "M01600", "糖尿病", "GZSI_MED_TYPE_14"),
    M01609("", "M01609", "糖尿病黄斑水肿", "GZSI_MED_TYPE_14"),
    M01701("", "M01701", "甲状腺功能减退症", "GZSI_MED_TYPE_14"),
    M01900("", "M01900", "高脂血症", "GZSI_MED_TYPE_14"),
    M01903("", "M01903", "C型尼曼匹克病", "GZSI_MED_TYPE_14"),
    M01904("", "M01904", "肝豆状核变性病（铜代谢障碍）", "GZSI_MED_TYPE_14"),
    M01908("", "M01908", "肢端肥大症", "GZSI_MED_TYPE_14"),
    M02101("", "M02101", "精神分裂症", "GZSI_MED_TYPE_14"),
    M02102("", "M02102", "双相情感障碍", "GZSI_MED_TYPE_14"),
    M02103("", "M02103", "持久的妄想性障碍（偏执性精神病）", "GZSI_MED_TYPE_14"),
    M02104("", "M02104", "分裂情感性障碍", "GZSI_MED_TYPE_14"),
    M02105("", "M02105", "癫痫所致精神障碍", "GZSI_MED_TYPE_14"),
    M02106("", "M02106", "精神发育迟滞", "GZSI_MED_TYPE_14"),
    M02300("", "M02300", "帕金森病", "GZSI_MED_TYPE_14"),
    M02400("", "M02400", "阿尔茨海默氏病", "GZSI_MED_TYPE_14"),
    M02500("", "M02500", "癫痫", "GZSI_MED_TYPE_14"),
    M02601("", "M02601", "小儿脑性瘫痪", "GZSI_MED_TYPE_14"),
    M02800("", "M02800", "肌萎缩侧索硬化症(ALS)", "GZSI_MED_TYPE_14"),
    M02900("", "M02900", "多发性硬化症", "GZSI_MED_TYPE_14"),
    M03701("", "M03701", "湿性年龄相关性黄斑变性", "GZSI_MED_TYPE_14"),
    M03703("", "M03703", "脉络膜新生血管", "GZSI_MED_TYPE_14"),
    M03704("", "M03704", "视网膜静脉阻塞所致黄斑水肿", "GZSI_MED_TYPE_14"),
    M03900("", "M03900", "高血压", "GZSI_MED_TYPE_14"),
    M04000("", "M04000", "肺动脉高压", "GZSI_MED_TYPE_14"),
    M04301("", "M04301", "慢性心功能不全", "GZSI_MED_TYPE_14"),
    M04401("", "M04401", "心房颤动抗凝治疗", "GZSI_MED_TYPE_14"),
    M04600("", "M04600", "冠心病", "GZSI_MED_TYPE_14"),
    M04803("", "M04803", "脑血管病后遗症", "GZSI_MED_TYPE_14"),
    M05300("", "M05300", "慢性阻塞性肺疾病", "GZSI_MED_TYPE_14"),
    M05400("", "M05400", "支气管哮喘", "GZSI_MED_TYPE_14"),
    M06000("", "M06000", "克罗恩病", "GZSI_MED_TYPE_14"),
    M06201("", "M06201", "肝硬化", "GZSI_MED_TYPE_14"),
    M06501("", "M06501", "溃疡性结肠炎", "GZSI_MED_TYPE_14"),
    M06700("", "M06700", "银屑病", "GZSI_MED_TYPE_14"),
    M06900("", "M06900", "类风湿关节炎", "GZSI_MED_TYPE_14"),
    M07101("", "M07101", "系统性红斑狼疮", "GZSI_MED_TYPE_14"),
    M07200("", "M07200", "强直性脊柱炎", "GZSI_MED_TYPE_14"),
    M07600("", "M07600", "慢性肾小球肾炎", "GZSI_MED_TYPE_14"),
    M07800("", "M07800", "慢性肾功能不全（非透析）", "GZSI_MED_TYPE_14"),
    M07803("", "M07803", "慢性肾功能不全（血液治疗）", "GZSI_MED_TYPE_14"),
    M07804("", "M07804", "慢性肾功能不全（腹透治疗）", "GZSI_MED_TYPE_14"),
    M08202("", "M08202", "普拉德-威利综合征", "GZSI_MED_TYPE_14"),
    M08301("", "M08301", "肾脏移植术后抗排异治疗", "GZSI_MED_TYPE_14"),
    M08302("", "M08302", "造血干细胞移植后抗排异治疗", "GZSI_MED_TYPE_14"),
    M08303("", "M08303", "心脏移植术后抗排异治疗", "GZSI_MED_TYPE_14"),
    M08304("", "M08304", "肝脏移植术后抗排异治疗", "GZSI_MED_TYPE_14"),
    M08305("", "M08305", "肺脏移植术后抗排异治疗", "GZSI_MED_TYPE_14"),
    M08405("", "M08405", "心脏瓣膜替换手术后抗凝治疗", "GZSI_MED_TYPE_14"),
    M09000("", "M09000", "骨关节炎", "GZSI_MED_TYPE_14"),
    M12100("", "M12100", "新冠肺炎出院患者门诊康复治疗", "GZSI_MED_TYPE_14"),
    MZ0201("", "MZ0201", "家庭病床", "GZSI_MED_TYPE_14"),
    MZ0301("", "MZ0301", "家庭医生", "GZSI_MED_TYPE_14"),
    MZ0501("", "MZ0501", "急诊留院观察", "GZSI_MED_TYPE_14"),
    MZ02101("", "MZ02101", "精神分裂症(长效针剂治疗)", "GZSI_MED_TYPE_14");

    private final String code;
    private final String mapping;
    private final String name;
    private final String type;

    HisMedicalType14Enum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

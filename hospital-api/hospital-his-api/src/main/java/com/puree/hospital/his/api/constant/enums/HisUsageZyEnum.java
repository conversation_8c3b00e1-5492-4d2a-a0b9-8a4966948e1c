package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * 中药用法枚举类，todo 未映射
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisUsageZyEnum implements BaseHisEnum {

    NEIFU("", "1", "内服", "USAGEZY"),
    WAIYONG("", "2", "外用", "USAGEZY"),
    CHONGFU("", "3", "冲服", "USAGEZY"),
    YANGHUA("", "4", "烊化", "USAGEZY"),
    JIUFU("", "5", "酒服", "USAGEZY"),
    ZUOYU("", "6", "坐浴", "USAGEZY"),
    PAOFU("", "7", "泡服", "USAGEZY"),
    JIAOFU("", "8", "嚼服", "USAGEZY"),
    XUNXI("", "9", "熏洗", "USAGEZY");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisUsageZyEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }
}
package com.puree.hospital.his.api.exception.soap;

import cn.hutool.core.text.CharSequenceUtil;
import com.puree.hospital.his.api.constant.HisExceptionConstant;

/**
 * 异常工厂,抛出特殊异常（有业务逻辑的异常）
 */
public class HisSOAPExceptionFactory {

    private HisSOAPExceptionFactory() {
    }

    /**
     * 异常工厂
     * @param message 异常信息
     * @return 异常
     */
    public static HisSOAPException exception(String message) {
        if (CharSequenceUtil.equals(message, HisExceptionConstant.PATIENT_INFO_IS_NULL)) {
            return new HisPatientInfoIsNullException();
        }
        if (CharSequenceUtil.equals(message, HisExceptionConstant.ACTIVE_DOCTOR_IS_NULL)) {
            return new HisActiveDoctorNullException();
        }
        if (CharSequenceUtil.startWith(message, HisExceptionConstant.REG_LEVEL_ERROR_PREFIX)) {
            return new HisRegLevelErrorException(message);
        }
        return new HisSOAPException(message);
    }
}

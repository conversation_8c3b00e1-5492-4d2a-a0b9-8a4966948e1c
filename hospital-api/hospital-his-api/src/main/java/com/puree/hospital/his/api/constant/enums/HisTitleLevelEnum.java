package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * 职称等级 枚举类
 * <AUTHOR>
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisTitleLevelEnum implements BaseHisEnum {
    SPECIAL_ALLOWANCE_EXPERT("", "1", "特殊津贴专家", "LEVEL"),
    CHIEF_PHYSICIAN("105", "2", "主任医师", "LEVEL"),
    DEPUTY_CHIEF_PHYSICIAN("106", "3", "副主任医师", "LEVEL"),
    ATTENDING_PHYSICIAN("107", "4", "主治医师", "LEVEL"),
    PHYSICIAN("108", "5", "医师", "LEVEL"),
    INTERNSHIP_PHYSICIAN("", "6", "见习医师", "LEVEL"),
    CHIEF_NURSE("", "7", "主任护理师", "LEVEL"),
    DEPUTY_CHIEF_NURSE("", "8", "副主任护理师", "LEVEL"),
    CHARGE_NURSE("", "9", "主管护理师", "LEVEL"),
    NURSE("", "10", "护理师", "LEVEL"),
    NURSE_ASSISTANT("1410", "11", "护士", "LEVEL"),
    ASSISTANT_NURSE("", "12", "助理护士", "LEVEL"),
    DEPUTY_CHIEF_TECHNICIAN("", "13", "副主任技师", "LEVEL"),
    CHARGE_TECHNICIAN("", "14", "主管技师", "LEVEL"),
    TECHNICIAN("", "15", "技师", "LEVEL"),
    TECHNICIAN_ASSISTANT("", "16", "技士", "LEVEL"),
    RESEARCHER("", "17", "研究员", "LEVEL"),
    ASSOCIATE_RESEARCHER("", "18", "副研究员", "LEVEL"),
    ASSISTANT_RESEARCHER("", "19", "助理研究员", "LEVEL"),
    RESEARCH_INTERNSHIP("", "20", "研究实习员", "LEVEL"),
    PROFESSOR("", "21", "教授", "LEVEL"),
    ASSOCIATE_PROFESSOR("", "22", "副教授", "LEVEL"),
    LECTURER("", "23", "讲师", "LEVEL"),
    SENIOR_ENGINEER("", "24", "高级工程师", "LEVEL"),
    ENGINEER("", "25", "工程师", "LEVEL"),
    ASSISTANT_ENGINEER("", "26", "助理工程师", "LEVEL"),
    DEPUTY_CHIEF_PHARMACIST("", "27", "副主任药剂师", "LEVEL"),
    CHARGE_PHARMACIST("", "28", "主管药剂师", "LEVEL"),
    PHARMACIST("", "29", "药剂师", "LEVEL"),
    PHARMACY_TECHNICIAN("", "30", "药剂士", "LEVEL"),
    ASSOCIATE_EDITOR("", "31", "副编审", "LEVEL"),
    OTHER("", "32", "其他", "LEVEL"),
    DEPUTY_CHIEF_LAB_TECHNICIAN("", "33", "副主任检验师", "LEVEL"),
    CHARGE_LAB_TECHNICIAN("", "34", "主管检验师", "LEVEL"),
    RESIDENT_PHYSICIAN("", "35", "住院医师", "LEVEL"),
    MEDICAL_ASSISTANT("", "36", "医士", "LEVEL"),
    NURSE_PRACTITIONER("1508", "37", "护师", "LEVEL"),
    ATTENDING_TRADITIONAL_CHINESE_PHYSICIAN("", "38", "主治中医师", "LEVEL"),
    CHARGE_PHARMACIST_2("1403", "39", "主管药师", "LEVEL"),
    TRADITIONAL_CHINESE_PHYSICIAN("", "40", "中医师", "LEVEL"),
    TRADITIONAL_CHINESE_PHARMACIST("", "41", "中药师", "LEVEL"),
    WESTERN_PHARMACY_TECHNICIAN("", "42", "西药士", "LEVEL"),
    TRADITIONAL_CHINESE_PHARMACY_TECHNICIAN("", "43", "中药士", "LEVEL"),
    DEPUTY_CHIEF_NURSE_2("1406", "44", "副主任护师", "LEVEL"),
    DEPUTY_CHIEF_TRADITIONAL_CHINESE_PHYSICIAN("", "45", "副主任中医师", "LEVEL"),
    CHARGE_NURSE_2("1407", "46", "主管护师", "LEVEL"),
    TRADITIONAL_CHINESE_INTERNAL_MEDICINE_PHYSICIAN("", "47", "中医内科主治医师", "LEVEL"),
    ASSISTANT_PHYSICIAN("", "48", "助理医师", "LEVEL"),
    DEPUTY_CHIEF_PHARMACIST_2("1402", "49", "副主任药师", "LEVEL"),
    WESTERN_PHARMACIST("", "50", "西药师", "LEVEL"),
    LAB_TECHNICIAN_ASSISTANT("", "51", "检验技士", "LEVEL"),
    DEPUTY_CHIEF_TRADITIONAL_CHINESE_PHARMACIST("", "52", "副主任中药师", "LEVEL"),
    MEDICAL_RECORD_INFORMATION_TECHNICIAN("", "53", "病案信息技师", "LEVEL"),
    PHARMACY_TECHNICIAN_2("", "54", "药士", "LEVEL"),
    STATISTICIAN("", "55", "统计员", "LEVEL"),
    RADIOLOGY_TECHNICIAN("", "56", "放射技师", "LEVEL"),
    CHIEF_TRADITIONAL_CHINESE_PHYSICIAN("", "57", "主任中医师", "LEVEL"),
    ASSISTANT_ACCOUNTANT("", "58", "助理会计师", "LEVEL"),
    TECHNICIAN_2("", "59", "技术员", "LEVEL"),
    PATHOLOGY_TECHNICIAN("", "60", "病理技师", "LEVEL"),
    PHARMACIST_2("1404", "61", "药师", "LEVEL"),
    CHIEF_TRADITIONAL_CHINESE_PHARMACIST("", "62", "主任中药师", "LEVEL"),
    ASSISTANT_STATISTICIAN("", "63", "助理统计师", "LEVEL"),
    RADIOLOGY_TECHNICIAN_ASSISTANT("", "64", "放射医学技士", "LEVEL"),
    CHARGE_TRADITIONAL_CHINESE_PHARMACIST("", "65", "主管中药师", "LEVEL"),
    ASSISTANT_TRADITIONAL_CHINESE_PHYSICIAN("", "67", "助理中医师", "LEVEL"),
    REHABILITATION_MEDICINE_TECHNICIAN_ASSISTANT("", "68", "康复医学治疗技士", "LEVEL"),
    ACCOUNTANT_ASSISTANT("", "69", "会计员", "LEVEL"),
    REHABILITATION_MEDICINE_TECHNICIAN("", "70", "康复医学治疗技师", "LEVEL"),
    RADIOLOGY_TECHNICIAN_ASSISTANT_2("", "71", "放射技士", "LEVEL"),
    ORAL_MEDICAL_TECHNICIAN("", "72", "口腔医学技士", "LEVEL"),
    INTERMEDIATE_NETWORK_ENGINEER("", "73", "中级网络工程师", "LEVEL"),
    LAB_TECHNICIAN("", "74", "检验技师", "LEVEL"),
    ACCOUNTANT("", "75", "会计师", "LEVEL"),
    REHABILITATION_THERAPIST("", "76", "康复治疗师", "LEVEL"),
    ASSISTANT_ECONOMIST("", "77", "助理经济师", "LEVEL"),
    FAMOUS_EXPERT("", "78", "名专家", "LEVEL"),
    DEPUTY_CHIEF_SPECIAL("", "79", "副主任特需", "LEVEL");

    private final String code;
    private final String mapping;
    private final String name;
    private final String type;

    HisTitleLevelEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

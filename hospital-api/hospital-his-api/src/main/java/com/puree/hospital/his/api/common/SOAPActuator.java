package com.puree.hospital.his.api.common;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.webservice.SoapClient;
import cn.hutool.http.webservice.SoapProtocol;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.his.api.exception.soap.HisSOAPExceptionFactory;
import com.puree.hospital.his.api.uils.HisUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * soap 执行器
 */
@Data
@Slf4j
@Component
public class SOAPActuator {

    //本地开发使用这个
//    private static final String SOAP_BASE_URL = "https://hospital-test.getddhospi.cn/his-api/OpenhitInternetHosService.asmx";
    //HIS测试环境使用这个
//    private static final String SOAP_BASE_URL = "http://10.60.85.77:8002/OpenhitInternetHosService.asmx";
    private static String SOAP_BASE_URL;
    private static final String NAMESPACE_URI = "http://tempuri.org/";
    private static final String CONTENT_XML_PARAM = "contentXML";
    private static final String LIST_KEY = "List";
    private SoapProperties soapProperties;

    @Autowired
    public void setSoapProperties(SoapProperties soapProperties){
        this.soapProperties = soapProperties;
        SOAPActuator.SOAP_BASE_URL = soapProperties.getUrl();
    }

    /**
     * 执行 soap 请求
     *
     * @param methodName 请求方法名
     * @param obj        数据
     * @return 结果
     * @throws SOAPException 执行失败
     */
    public static <T> HisResult execute(String methodName, T obj) throws SOAPException {
        Objects.requireNonNull(methodName, "方法名不能为空");
        Objects.requireNonNull(obj, "请求数据不能为空");
        log.info("SOAP_BASE_URL：{}", SOAP_BASE_URL);
        if(StringUtils.isEmpty(SOAP_BASE_URL)){
            SOAP_BASE_URL = "https://hospital-test.getddhospi.cn/his-api/OpenhitInternetHosService.asmx";
        }
        log.debug("开始执行SOAP请求, 方法: {}, 数据: {}", methodName, obj);
        try {
            SOAPMessage soapMessage = SoapClient.create(SOAP_BASE_URL,
                            SoapProtocol.SOAP_1_2)
                    .setMethod(methodName, NAMESPACE_URI)
                    .setParam(CONTENT_XML_PARAM, HisUtils.toHisFormatJsonString(obj))
                    .sendForMessage();
            log.info("body:{}",HisUtils.toHisFormatJsonString(obj));
            HisResult result = extractResult(soapMessage);
            validateResult(result, methodName);
            log.debug("SOAP请求执行完成 [方法: {}, 结果: {}]", methodName, result);

            return result;
        } catch (SOAPException e) {
            log.error("SOAP请求执行失败, 方法: {}, 错误: {}", methodName, e.getMessage());
            throw e;
        }
    }

    /**
     * 验证结果
     * @param result 响应结果
     * @param methodName 方法名
     */
    private static void validateResult(HisResult result, String methodName) {
        String errorMsg = String.format("SOAP请求执行失败 [方法: %s]", methodName);

        Assert.notNull(result, errorMsg);
        Assert.notNull(result.getResult(), errorMsg);

        if (!CharSequenceUtil.equals(result.getMessage(), "0")) {
            log.error("result:{}",result);
            throw HisSOAPExceptionFactory.exception(result.getError());
        }
    }
    /**
     * 从SOAP消息中提取结果
     *
     * @param soapMessage SOAP消息
     * @return 响应结果
     * @throws SOAPException SOAP响应内容为空
     */
    private static HisResult extractResult(SOAPMessage soapMessage) throws SOAPException {
        SOAPBody soapBody = soapMessage.getSOAPBody();
        Document bodyContent = soapBody.extractContentAsDocument();
        NodeList nodeList = bodyContent.getChildNodes();

        if (nodeList.getLength() == 0) {
            throw new SOAPException("SOAP响应内容为空");
        }

        Node firstNode = nodeList.item(0);
        String resultStr = firstNode.getTextContent();

        return JSON.parseObject(resultStr, HisResult.class);
    }


    /**
     * 执行 SOAP 查询请求并转换结果
     * 该接口仅用于 响应结果为:
     * {"result":"{"List":
     * [{
     * "RegScheduleCode":"1391239",
     * "DoctorID":"Y3267",
     * "DoctorName":"钟均其",
     * "FeeType":"副主任医师门诊诊查费",
     * "ScheduleTime":"14:00:00-14:29:00",
     * "RegCount":"1",
     * "LimitCount":"1",
     * "WaitCount":"4","
     * DoctorLevel":"副主任医师",
     * "TotalCost":"20",
     * "List1":null}]}","message":"0","error":""}
     * 此类响应的时候使用
     * @param methodName SOAP方法名
     * @param query 查询参数
     * @param resultClass 返回类型
     * @param <T> 返回类型泛型
     * @param <Q> 查询参数类型
     * @return 查询结果列表
     */
    public static <T, Q> List<T> executeQueryAndConvertList(String methodName, Q query, Class<T> resultClass) throws SOAPException {
        return Optional.ofNullable(execute(methodName, query))
                .map(HisResult::getResult)
                .map(result -> {
                    log.debug("SOAP响应结果: {}", result);
                    return JSON.parseObject(result)
                            .getJSONArray(LIST_KEY)
                            .toJavaList(resultClass);
                })
                .orElse(Collections.emptyList());
    }

}

package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * 煎药方法枚举类，todo 未映射
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisDecoctingMethodEnum implements BaseHisEnum {
    XIANJIAN("", "1", "先煎", "DecoctingMethod"),
    HOUXIA("", "2", "后下", "DecoctingMethod"),
    BAOJIAN("", "3", "包煎", "DecoctingMethod"),
    YANGHUA("", "4", "烊化", "DecoctingMethod"),
    YANMOCHONGFU("", "5", "研末冲服", "DecoctingMethod"),
    PAOFU("", "6", "泡服", "DecoctingMethod"),
    LINGJIAN("", "7", "另煎", "DecoctingMethod"),
    CHONGFU("", "8", "冲服", "DecoctingMethod");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisDecoctingMethodEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }
}
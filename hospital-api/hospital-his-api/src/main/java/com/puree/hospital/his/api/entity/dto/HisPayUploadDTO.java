package com.puree.hospital.his.api.entity.dto;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.enums.FeeSettleTypeEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.his.api.common.HisIgnoreName;
import com.puree.hospital.his.api.constant.enums.HisPaymentModeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * his 支付信息上报 DTO
 * <AUTHOR>
 * @date 2025-1-7
 */
@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
@Data
public class HisPayUploadDTO {
    /**
     * 处方流水号（必填，多个处方号用|分割）
     */
    private String recipeSEQ;

    /**
     * 卡号（必填），bus_patient_family表中的his_patient_id字段
     */
    private String cardNO;

    /**
     * 总金额（必填）
     */
    private String totCost;

    /**
     * 预交金金额
     */
    private String prepayCost;

    /**
     * 公费金额
     */
    private String pubCost;

    /**
     * 挂号流水（必填）
     */
    private String registerNO;

    /**
     * 操作人员ID（必填）
     */
    private String userID;

    /**
     * 个人缴费金额（必填）
     */
    private String ownCost;

    /**
     * 就医登记号（医保支付时必填）
     */
    private String regNo;

    /**
     * 结算ID（医保支付时必填）
     */
    private String setlId;

    /**
     * 支付订单号  移动支付2.0使用
     */
    @HisIgnoreName
    private String payOrdId;

    /**
     * 支付类型
     */
    private String payType = FeeSettleTypeEnum.SELF.getType();

    /**
     * 合同单位编码 字典PACTCODE
     */
    private String PactCode;

    /**
     * 合同单位名称
     */
    private String PactName;

    /**
     * 医保支付信息，当使用医保支付时必填
     */
    private MiInfo sITransInfo;

    @Data
    public static class MiInfo{

        public MiInfo() {
        }

        public MiInfo(String sIPatientInfo, String sIInputInfo, String sIOutput) {
            this.sIPatientInfo = sIPatientInfo;
            this.sIInputInfo = sIInputInfo;
            this.sIOutput = JSONObject.parseObject(sIOutput, OutPutData.class);
        }

        /**
         * 医保读卡信息
         */
        private String sIPatientInfo;

        /**
         * 支医保交易入参
         */
        private String sIInputInfo;

        /**
         * 医保交易出参（医保缴费的情况下必填，按医保接口文档的出参原样返回json）
         */
        private OutPutData sIOutput;

        @Data
        public static class OutPutData{
            @HisIgnoreName
            private String deposit;
            @HisIgnoreName
            private JSONObject extData;
            @HisIgnoreName
            private String fundPay;
            @HisIgnoreName
            private String ordStas;
            @HisIgnoreName
            private String payOrdId;
            @HisIgnoreName
            private String feeSumamt;
            @HisIgnoreName
            private String othFeeAmt;
            @HisIgnoreName
            private String ownPayAmt;
            @HisIgnoreName
            private String psnAcctPay;
            @HisIgnoreName
            private String hospPartAmt;
            @HisIgnoreName
            private String selfAcctPay;
            @HisIgnoreName
            private String acctMulaidPay;
        }

    }

    /**
     * 支付信息（必填）
     */
    private List<PayInfo> list ;


    @Data
    public static class PayInfo{

        public PayInfo() {
        }

        public PayInfo(String payTypeID, String payModeName, String amount) {
            this.payTypeID = payTypeID;
            this.payModeName = payModeName;
            this.amount = amount;
        }

        /**
         * 支付类型代码（必填）
         */
        private String payTypeID;

        /**
         * 支付方式名称（必填）
         */
        private String payModeName;

        /**
         * 开户行
         */
        private String openBank;

        /**
         * 账号
         */
        private String openAccount;

        /**
         * POS交易信息
         */
        private String pOSTransNO;

        /**
         * 金额（必填）
         */
        private String amount;
    }

    /**
     * 医保支付信息
     * @param psnCashPay 个人现金支出
     * @param psnAcctPay 个人账户支出
     */
    public void assemblePayInfo(BigDecimal psnCashPay, BigDecimal psnAcctPay){
        this.list = new ArrayList<>();
        if(psnCashPay != null && BigDecimal.ZERO.compareTo(psnCashPay) < 0){
            PayInfo payInfo = new PayInfo(HisPaymentModeEnum.WX.getMapping(), HisPaymentModeEnum.WX.getName(), psnCashPay.toString());
            list.add(payInfo);
        }
        if(psnAcctPay != null && BigDecimal.ZERO.compareTo(psnAcctPay) < 0){
            PayInfo payInfo = new PayInfo(HisPaymentModeEnum.YBWX.getMapping(), HisPaymentModeEnum.YBWX.getName(), psnAcctPay.toString());
            list.add(payInfo);
        }
    }

    /**
     * 医保信息组装
     * @param patientInfo 医保读卡信息
     * @param requestData 医保交易入参
     * @param responseData 医保交易出参
     */
    public void assembleTransInfo(String patientInfo, String requestData, String responseData){
        this.sITransInfo = new MiInfo(patientInfo, requestData, responseData);
        this.pubCost = getFundpaySumamt(responseData);
    }

    /**
     * 获取基金支付总额
     * @param responseData 医保交易出参
     * @return 基金支付总额
     */
    private String getFundpaySumamt(String responseData){
        if(StringUtils.isEmpty(responseData)){
            return "0";
        }
        return Optional.ofNullable(JSONObject.parseObject(responseData).getJSONObject("extData"))
                .map(extData -> extData.getJSONObject("preSetl"))
                .map(preSetl -> preSetl.getString("fund_pay_sumamt"))
                .orElse("0");
    }

}

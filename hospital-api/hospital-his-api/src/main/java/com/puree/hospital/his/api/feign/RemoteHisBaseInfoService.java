package com.puree.hospital.his.api.feign;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.his.api.entity.HisDicInfo;
import com.puree.hospital.his.api.entity.HisDrugInfo;
import com.puree.hospital.his.api.entity.query.HisDrugInfoQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * HIS基础信息服务接口
 */
@FeignClient(contextId = "RemoteHisBaseInfoService", value = ServiceNameConstants.HIS_SERVICE)
public interface RemoteHisBaseInfoService {
    /**
     * 通过字典类型获取字典列表
     * @param type 字典类型
     * @return 字典列表
     */
    @GetMapping("/base-info/dic-info/{type}")
    AjaxResult<List<HisDicInfo>> getDicInfoByType(@PathVariable("type") String type);

    /**
     * 通过药品编码获取药品信息
     * @param drugCode 药品编码
     * @return 药品信息
     */
    @GetMapping("/base-info/drug-info/{drugCode}")
    AjaxResult<HisDrugInfo> getDrugInfoByCode(@PathVariable("drugCode") String drugCode);

    /**
     * 获取所有药品列表
     * @param query 查询参数
     * @return 药品列表
     */
    @GetMapping("/base-info/drug-list")
    AjaxResult<List<HisDrugInfo>> getDrugList(@SpringQueryMap HisDrugInfoQuery query);
}

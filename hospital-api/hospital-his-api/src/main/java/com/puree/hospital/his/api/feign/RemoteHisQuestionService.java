package com.puree.hospital.his.api.feign;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.his.api.common.HisPEResult;
import com.puree.hospital.his.api.entity.dto.HisPatientInfoQueryDTO;
import com.puree.hospital.his.api.entity.dto.QuestionAnswerContentDTO;
import com.puree.hospital.his.api.entity.dto.QuestionContentDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * his 问卷远程调用服务
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/25 16:28
 */
@FeignClient(contextId = "RemoteHisQuestionService", value = ServiceNameConstants.HIS_SERVICE)
public interface RemoteHisQuestionService {

    /**
     *  获取患者信息
     * @param queryDTO  查询参数
     * @return  结果
     */
    @PostMapping("/question/patient")
    R<HisPEResult> getPeHisPatient(@RequestBody HisPatientInfoQueryDTO queryDTO);

    /**
     *  同步问卷信息
     * @param questionContentDTOS   问卷内容
     * @return 结果
     */
    @PostMapping("/question/send")
    R<HisPEResult> sendQuestion(@RequestBody QuestionContentDTO questionContentDTOS);

    /**
     *  发送问卷答案
     * @param questionAnswerContentDTO  问卷
     * @return  结果
     */
    @PostMapping("/question/send/answer")
    R<HisPEResult> sendQuestionAnswer(@RequestBody QuestionAnswerContentDTO questionAnswerContentDTO);

}

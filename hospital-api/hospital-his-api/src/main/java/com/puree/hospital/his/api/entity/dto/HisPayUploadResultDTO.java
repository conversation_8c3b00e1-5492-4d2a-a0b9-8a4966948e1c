package com.puree.hospital.his.api.entity.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * his 支付信息上报结果信息
 * <AUTHOR>
 * @date 2025-1-7
 */
@Data
public class HisPayUploadResultDTO {
    /**
     * 医院结算流水号
     */
    private String tranSerialNO;

    /**
     * 交易时间
     */
    private String tranTime;

    /**
     * 发药窗口
     */
    private String sendWin;

    /**
     * 账户余额  挂号后余额（支持诊疗卡时）
     */
    private String balance;

    /**
     * 电子发票链接 HIS自动生成电子票据后返回票夹的URL
     */
    private String elecInvoiceUrl;

    /**
     * 支付方式名称
     */
    private String pactName;

    /**
     * 总金额
     */
    private String totCost;

    /**
     * 应付金额
     */
    private String yfCost;

    /**
     * 公费金额
     */
    private String pubCost;

    /**
     * 预售金额
     */
    private String czCost;

    /**
     * 减免金额
     */
    private String ecoCost;

    /**
     * 个人缴费金额
     */
    private String ownCost;

    /**
     * 自付比例
     */
    private String payRatio;

    /**
     * 收据号
     */
    private String invoNo;

    /**
     * 费用类别
     */
    private String invoName;

    /**
     * 费用类别金额
     */
    private String invoCost;

    /**
     * 流水号
     */
    private String clinicNo;


}

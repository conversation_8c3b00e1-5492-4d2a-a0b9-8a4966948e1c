package com.puree.hospital.his.api.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 问卷题目
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/25 10:22
 */
@Data
public class QuestionTitleDTO {

    /**
     *  题目Id
     */
    private String id;

    /**
     *  题目描述
     */
    private String desc;

    /**
     *  题目类型 0.单选题 1.多选题 2.单行文本 3.多行文本 4.多行填空 5.量表/NSP 6.附件 7.说明文字题
     */
    private Integer type;

    /**
     *  题目
     */
    private String title;

    /**
     *  子选项
     */
    private List<QuestionOptionDTO> options;

    /**
     *  类型 0.格式不限 1.数字 2.手机号 3.邮箱 4.身份证号 5.日期 6.时间 7.日期和时间
     */
    private Integer pattern;

    /**
     *  是否必填 1是 0否
     */
    private Integer require;

    /**
     *  附件类型 0.类型不限 1.图片附件 2.视频附件 3.文件附件
     */
    private Integer annexType;

    /**
     *  是否所有选项都被关联
     */
    private boolean realtionAllOpt;

    /**
     *  分值
     */
    private Integer score;

}

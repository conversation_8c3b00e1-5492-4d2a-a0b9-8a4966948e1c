package com.puree.hospital.his.api.entity.event;

import com.puree.hospital.common.api.constant.OrderTypeConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * HIS支付完成事件 实体类
 * <AUTHOR>
 * @date 2025/1/7 16:15
 */
@Data
@NoArgsConstructor
public class HisPayFinishEvent {

    /**
     * topic
     */
    public static final String HIS_PAY_FINISH_TOPIC = "queue:his_pay_finish";


    public HisPayFinishEvent(Long hospitalId, String orderNo, String orderType) {
        this.hospitalId = hospitalId;
        this.orderNo = orderNo;
        this.orderType = orderType;
    }

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 订单号
     */
    String orderNo;

    /**
     * 订单类型 {@link OrderTypeConstant}
     */
    String orderType;

}

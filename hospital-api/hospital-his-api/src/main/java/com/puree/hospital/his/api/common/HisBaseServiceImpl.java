package com.puree.hospital.his.api.common;

import com.puree.hospital.his.api.constant.BaseHisEnum;

/**
 * 基础实现类
 */
public abstract class HisBaseServiceImpl {

    /**
     * 字典数据转换 我方 转 HIS
     * @param value 值
     * @param enumClazz 对应枚举
     * @return 转换HIS字典后的数据
     * @param <T> BaseHisEnum 的实现类
     */
    protected <T extends Enum<T> & BaseHisEnum>  String dictOurSideToHis(String value,Class<T> enumClazz){
        return BaseHisEnum.codeToMapping(enumClazz, value);
    }

    /**
     * 字典数据转换 我方 转 HIS
     * @param value 值
     * @param enumClazz 对应枚举
     * @return 转换HIS字典后的枚举
     * @param <T> BaseHisEnum 的实现类
     */
    protected <T extends Enum<T> & BaseHisEnum>  T dictOurSideToHisEnum(String value,Class<T> enumClazz){
        return BaseHisEnum.codeToEnum(enumClazz, value);
    }

    /**
     * 字典数据转换 HIS 转我方
     * @param value 值
     * @param enumClazz 对应枚举
     * @return 转转换我方字典后的数据
     * @param <T> BaseHisEnum 的实现类
     */
    protected <T extends Enum<T> & BaseHisEnum> String dictHisToOurSide(String value,Class<T> enumClazz){
        return BaseHisEnum.mappingToCode(enumClazz, value);
    }

}

package com.puree.hospital.his.api.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 3.1.22 同步药品收货信息 dto
 * <AUTHOR>
 */
@Data
public class HisOrderInfoDTO {
    /**
     * 发票号（必填）dopay接口返回的TranSerialNO结算流水号
     */
    @NotBlank( message = "发票号不能为空")
    private String invoiceNO;

    /**
     * 挂号单据号（必填）
     */
    @NotBlank( message = "挂号单据号不能为空")
    private String registerNO;

    /**
     * 收件人姓名（必填）
     */
//    @NotBlank( message = "收件人姓名不能为空")
    private String name;

    /**
     * 收件人电话（必填）
     */
//    @NotBlank( message = "收件人电话不能为空")
    private String phone;

    /**
     * 收件人地址（必填）
     */
//    @NotBlank( message = "收件人地址不能为空")
//    private String address;

    /**
     * 收件人省份（必填）
     */
//    @NotBlank( message = "收件人省份不能为空")
    private String destProvince;

    /**
     * 收件人城市（必填）
     */
//    @NotBlank( message = "收件人城市不能为空")
    private String destCity;

    /**
     * 收件人镇区（必填）
     */
//    @NotBlank( message = "收件人镇区不能为空")
    private String destDistrict;

    /**
     * 收件人详细地址（必填）
     */
//    @NotBlank( message = "收件人详细地址不能为空")
    private String destAddress;

    /**
     * 操作人工号（必填）
     */
    @NotNull( message = "操作人工号不能为空")
    private String userID;

    /**
     * 操作类型（1新增，2更新收件信息）
     */
    private Integer type;

    /**
     * 备注
     */
    private String mark;

    /**
     * 收货类型 （0自提1 配送）
     */
    private String sendType = "1";
}

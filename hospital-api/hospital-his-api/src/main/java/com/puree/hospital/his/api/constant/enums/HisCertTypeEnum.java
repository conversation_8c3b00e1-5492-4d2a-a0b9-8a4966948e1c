package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * his 人员证件类型
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisCertTypeEnum implements BaseHisEnum {
    YBDZ("01", "01", "医保电子凭证", "GZSI_MDTRT_CERT_TYPE"),
    SFZ("02", "02", "居民身份证", "GZSI_MDTRT_CERT_TYPE"),
    SHBZK("03", "03", "社会保障卡", "GZSI_MDTRT_CERT_TYPE");

    private final String code;
    private final String mapping;
    private final String name;
    private final String type;

    HisCertTypeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }
}
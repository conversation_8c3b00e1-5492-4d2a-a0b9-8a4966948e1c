package com.puree.hospital.his.api.common;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.webservice.SoapClient;
import cn.hutool.http.webservice.SoapProtocol;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.his.api.exception.soap.HisSOAPException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;
import java.util.Map;
import java.util.Objects;

/**
 * 体检科 soap 执行器
 */
@Data
@Slf4j
@Component
public class PESOAPActuator {

    /**
     *  公网地址
     */
//    public static String SOAP_BASE_URL = "https://qywx.getddhospi.com/Wxservices.asmx";
//    public static String SOAP_TOKEN_URL = "https://qywx.getddhospi.com/Wxservices.asmx";
    /**
     *  内网地址
     */
    public static String SOAP_BASE_URL = "http://197.0.253.131:8038/WebService1.asmx";
    public static String SOAP_TOKEN_URL = "http://197.0.253.131:8038/WebService1.asmx?wsdl";
    private static final String NAMESPACE_URI = "http://tempuri.org/";
    private static final String LIST_KEY = "List";
    public static final String USER_INFO_PARAM = "user_info";
    public static final String PATIENT_INFO_PARAM = "patient_info";

    /**
     * 执行 soap 请求
     *
     * @param methodName 请求方法名
     * @param obj        数据
     * @return 结果
     * @throws SOAPException 执行失败
     */
    public static <T> HisPEResult execute(String methodName, T obj, String paramName) throws SOAPException {
        return execute(methodName, obj, paramName, SOAP_BASE_URL);
    }

    /**
     * 执行 soap 请求
     *
     * @param methodName 请求方法名
     * @param obj        数据
     * @param soapBaseUrl soap地址
     * @return 结果
     * @throws SOAPException 执行失败
     */
    public static <T> HisPEResult execute(String methodName, T obj, String paramName, String soapBaseUrl) throws SOAPException {
        Objects.requireNonNull(methodName, "方法名不能为空");
        Objects.requireNonNull(obj, "请求数据不能为空");
        if(CharSequenceUtil.isEmpty(soapBaseUrl)){
            soapBaseUrl = SOAP_BASE_URL;
        }
        log.info("SOAP_BASE_URL：{}", soapBaseUrl);
        log.debug("开始执行SOAP请求, 方法: {}, 数据: {}", methodName, obj);
        try {
            SOAPMessage soapMessage = SoapClient.create(soapBaseUrl,
                            SoapProtocol.SOAP_1_2)
                    .setMethod(methodName, NAMESPACE_URI)
                    .setParam(paramName, JSON.toJSONString(obj))
                    .sendForMessage();
            log.info("body:{}", JSON.toJSONString(obj));
            HisPEResult result = extractResult(soapMessage);
            log.info("SOAP请求执行完成 [方法: {}, 结果: {}]", methodName, result);
            validateResult(result, methodName);
            return result;
        } catch (SOAPException e) {
            log.error("SOAP请求执行失败, 方法: {}, 错误: {}", methodName, e.getMessage());
            throw e;
        }
    }

    /**
     * 执行 soap 请求
     *
     * @param methodName 请求方法名
     * @param param 数据
     * @return 结果
     * @throws SOAPException 执行失败
     */
    public static <T> HisPEResult execute(String methodName, Map<String, Object> param, String soapBaseUrl) throws SOAPException {
        Objects.requireNonNull(methodName, "方法名不能为空");
        Objects.requireNonNull(param, "请求数据不能为空");
        if(CharSequenceUtil.isEmpty(soapBaseUrl)){
            soapBaseUrl = SOAP_BASE_URL;
        }
        log.info("SOAP_BASE_URL：{}", soapBaseUrl);
        String paramJson = JSON.toJSONString(param);
        log.info("开始执行SOAP请求, 方法: {}, 数据: {}", methodName, paramJson);
        try {
            SOAPMessage soapMessage = SoapClient.create(soapBaseUrl,
                            SoapProtocol.SOAP_1_2)
                    .setMethod(methodName, NAMESPACE_URI)
                    .setParams(param)
                    .sendForMessage();
            log.info("body:{}", paramJson);
            HisPEResult result = extractResult(soapMessage);
            log.info("SOAP请求执行完成 [方法: {}, 结果: {}]", methodName, result);
            validateResult(result, methodName);

            return result;
        } catch (SOAPException e) {
            log.error("SOAP请求执行失败, 方法: {}, 错误: {}", methodName, e.getMessage());
            throw e;
        }
    }

    /**
     * 验证结果
     * @param result 响应结果
     * @param methodName 方法名
     */
    private static void validateResult(HisPEResult result, String methodName) {
        String errorMsg = String.format("SOAP请求执行失败 [方法: %s]", methodName);

        Assert.notNull(result, errorMsg);

        if (!CharSequenceUtil.equals(result.getErr_code(), "0")) {
            log.error("result:{}",result);
            throw new HisSOAPException(errorMsg);
        }
    }

    /**
     * 从SOAP消息中提取结果
     *
     * @param soapMessage SOAP消息
     * @return 响应结果
     * @throws SOAPException SOAP响应内容为空
     */
    private static HisPEResult extractResult(SOAPMessage soapMessage) throws SOAPException {
        SOAPBody soapBody = soapMessage.getSOAPBody();
        Document bodyContent = soapBody.extractContentAsDocument();
        NodeList nodeList = bodyContent.getChildNodes();

        if (nodeList.getLength() == 0) {
            throw new SOAPException("SOAP响应内容为空");
        }

        Node firstNode = nodeList.item(0);
        String resultStr = firstNode.getTextContent();
        System.out.println(resultStr);
        return JSON.parseObject(resultStr, HisPEResult.class);
    }

}

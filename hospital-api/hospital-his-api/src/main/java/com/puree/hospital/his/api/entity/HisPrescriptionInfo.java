package com.puree.hospital.his.api.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * his 处方信息
 *
 * <AUTHOR>
 * @date 2025/4/12
 */
@Data
public class HisPrescriptionInfo {

    /**
     * HIS 就诊记录 ID
     */
    private String hisMedicalRecId;

    /**
     * 处方号
     */
    private String hisOrderRecId;

    /**
     * 就诊类别 ID （合同单位）
     */
    private String medicalCatId;

    /**
     * 就诊类别
     */
    private String medicalCatName;

    /**
     * 医嘱类型 ID
     */
    private String orderTypeld;

    /**
     * 医嘱类型
     */
    private String orderTypeName;

    /**
     * 医嘱类别 ID 互联网医院用不上
     */
    private String orderCatId;

    /**
     * 医嘱类别
     */
    private String orderCatName;

    /**
     * 处方内序号
     */
    private String parentId;

    /**
     * 医嘱类别 ，临时长期，住院用
     */
    private String term;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 医嘱组号
     */
    private String groupNum;

    /**
     * 医嘱序号
     */
    private String groupSeqNum;

    /**
     * 申请单 ID
     */
    private String reqFormld;

    /**
     * 医嘱状态 ID
     */
    private String orderStatusId;

    /**
     * 医嘱状态
     */
    private String orderStatusName;

    /**
     * 医嘱执行状态 ID
     */
    private String orderExecStatusId;

    /**
     * 医嘱执行状态
     */
    private String orderExecStatusName;

    /**
     * 医嘱执行时间
     */
    private String orderExecTime;

    /**
     * 费用状态 ID
     */
    private String feeStatusld;

    /**
     * 费用状态
     */
    private String feeStatusName;

    /**
     * 缴费状态 ID
     */
    private String payStatusId;

    /**
     * 缴费状态
     */
    private String payStatusName;

    /**
     * 医嘱项目编码
     */
    private String code;

    /**
     * 医嘱项目 ID
     */
    private String orderltemld;

    /**
     * 医嘱项目子类 ID
     */
    private String orderItemSubCatId;

    /**
     * 医嘱项目子类
     */
    private String orderItemSubCatName;

    /**
     * 医嘱项目分组 ID
     */
    private String orderItemGroupld;

    /**
     * 医嘱项目
     */
    private String orderItemName;

    /**
     * 医嘱项目分组
     */
    private String orderItemGroupName;

    /**
     * 单价
     */
    private String price;

    /**
     * 金额
     */
    private String amount;

    /**
     * 规格
     */
    private String spec;

    /**
     * 用量
     */
    private String dosage;

    /**
     * 用量单位 ID
     */
    private String dosageUnitId;

    /**
     * 用量单位
     */
    private String dosageUnitName;

    /**
     * 频率 ID
     */
    private String frequencyId;

    /**
     * 频率
     */
    private String frequencyName;

    /**
     * 频率次数
     */
    private String frequencyInterval;

    /**
     * 频率数目
     */
    private String frequencyCount;

    /**
     * 频率单位
     */
    private String frequencyUnit;

    /**
     * 频率时间
     */
    private String frequencyTime;

    /**
     * 首日时间
     */
    private String firstDayTime;

    /**
     * 尾日时间
     */
    private String lastDayTime;

    /**
     * 天(次)数
     */
    private String count;

    /**
     * 数量
     */
    private String quantity;

    /**
     * 数量单位 ID
     */
    private String quantityUnitId;

    /**
     * 数量单位名称
     */
    private String quantityUnitName;

    /**
     * 执行模式，1:按医嘱次数执行 2:按分拆单次执行
     */
    private String execMode;

    /**
     * 核对次数
     */
    private String checkCount;

    /**
     * 已执行次数
     */
    private String execDoneCount;

    /**
     * 全部执行次数
     */
    private String execTotalCount;

    /**
     * 诊断
     */
    private String diagnose;

    /**
     * 诊断名称(项目增加)
     */
    private String diagnoseName;

    /**
     * 加急标识
     */
    private String isUrgent;

    /**
     * 嘱托
     */
    private String advice;

    /**
     * 补录标识0:非补录1:补录
     */
    private String isMakeup;

    /**
     * 医嘱项目医保类别 ID
     */
    private String medicareCatId;

    /**
     * 医嘱项目医保类别
     */
    private String medicareCatName;

    /**
     * 公医类别 ID
     */
    private String publiclyFundedCatId;

    /**
     * 公医类别
     */
    private String publiclyFundedCatName;

    /**
     * 医保限制标识0:不限制1:限制
     */
    private String isMedicareLimit;

    /**
     * 医保限制范围
     */
    private String medicareLimitScope;

    /**
     * 医保报销标识，0:不可报1:可报
     */
    private String isMedicareReimburse;

    /**
     * 定点门诊标识，0:非定点1:定点
     */
    private String isFixedOutPat;

    /**
     * 特定门诊标识，0:非特定1:特定
     */
    private String isSpecialOutPat;

    /**
     * 区域 ID
     */
    private String areaId;

    /**
     * 区域
     */
    private String areaName;

    /**
     * 科室 ID
     */
    private String deptId;

    /**
     * 科室
     */
    private String deptName;

    /**
     * 校对人 ID
     */
    private String checkId;

    /**
     * 校对人
     */
    private String checkName;

    /**
     * 校对时间
     */
    private String checkTime;

    /**
     * 开立医师 ID
     */
    private String inputDoctorId;

    /**
     * 开立医生编码
     */
    private String inputDoctorCode;

    /**
     * 开立医师
     */
    private String inputDoctorName;

    /**
     * 开立时间
     */
    private String inputTime;

    /**
     * 开嘱医师 ID
     */
    private String submitDoctorId;

    /**
     * 开嘱医生编码
     */
    private String submitDoctorCode;

    /**
     * 开嘱医师
     */
    private String submitDoctorName;

    /**
     * 开嘱科室 ID
     */
    private String submitDeptId;

    /**
     * 开嘱科室
     */
    private String submitDeptName;

    /**
     * 提交时间
     */
    private String submitTime;

    /**
     * 执行科室 ID
     */
    private String execDeptId;

    /**
     * 执行科室
     */
    private String execDeptName;

    /**
     * 医嘱用法编码(项目增加)
     */
    private String usageCode;

    /**
     * 医嘱用法名称(项目增加)
     */

    private String usageName;

    /**
     * 开立时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDateTime;
}

package com.puree.hospital.his.api.entity.dto;

import lombok.Data;

/**
 * 处方同步明细项
 * <AUTHOR>
 * @date 2025-3-28 14:35:38
 */
@Data
public class HisDiagnosisItemDTO {
    /**
     * 诊断编码(必填）Icd10Code通过字典获取
     */
    private String diagCode;

    /**
     * 诊断名称(必填）Icd10Name
     */
    private String diagName;

    /**
     * 中医诊断编码(需要开草药处方时必填)
     */
    private String tradDiseaseCode;

    /**
     * 中医诊断名称(需要开草药处方时必填)
     */
    private String tradDiseaseName;

    /**
     * 中医证候编码(需要开草药处方时必填)
     */
    private String tradSyndromCode;

    /**
     * 中医证候名称(需要开草药处方时必填)
     */
    private String tradSyndromName;

    /**
     * 诊断类型(必填）默认 9
     */
    private String diagType = "9";

    /**
     * 诊断日期(必填）
     */
    private String diagDate;

    /**
     * 是否主要诊断(必填）1是 0否
     */
    private String isMain = "0";

}

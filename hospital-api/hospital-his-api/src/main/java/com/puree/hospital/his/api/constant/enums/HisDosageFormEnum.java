package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 药品剂型  字典表  dict_type：bus_drugs_dosage_form
 */
@Getter
public enum HisDosageFormEnum implements BaseHisEnum {
    // code：我方字典值   mapping：HIS字典值
    DOSAGEFORM_73("", "73", "袋冲颗粒", "DOSAGEFORM"),
    DOSAGEFORM_69("", "69", "口服溶液剂", "DOSAGEFORM"),
    DOSAGEFORM_55("1145", "55", "分散片", "DOSAGEFORM"),
    DOSAGEFORM_56("1147", "56", "浓缩丸", "DOSAGEFORM"),
    DOSAGEFORM_59("", "59", "橡胶膏剂", "DOSAGEFORM"),
    DOSAGEFORM_57("1135", "57", "糖衣片", "DOSAGEFORM"),
    DOSAGEFORM_58("1153", "58", "软胶囊(胶丸)", "DOSAGEFORM"),
    DOSAGEFORM_60("", "60", "贴膏剂", "DOSAGEFORM"),
    DOSAGEFORM_61("1143", "61", "阴道片", "DOSAGEFORM"),
    DOSAGEFORM_62("1196", "62", "栓剂", "DOSAGEFORM"),
    DOSAGEFORM_63("", "63", "外用溶液剂", "DOSAGEFORM"),
    DOSAGEFORM_64("", "64", "混悬针剂", "DOSAGEFORM"),
    DOSAGEFORM_65("", "65", "粉针", "DOSAGEFORM"),
    DOSAGEFORM_67("1149", "67", "注射液", "DOSAGEFORM"),
    DOSAGEFORM_66("1137", "66", "肠溶片", "DOSAGEFORM"),
    DOSAGEFORM_54("1149", "54", "注射剂", "DOSAGEFORM"),
    DOSAGEFORM_01("1149", "01", "水针剂", "DOSAGEFORM"),
    DOSAGEFORM_02("", "02", "注射用粉针剂", "DOSAGEFORM"),
    DOSAGEFORM_03("1156", "03", "溶液剂", "DOSAGEFORM"),
    DOSAGEFORM_04("1164", "04", "口服液体剂", "DOSAGEFORM"),
    DOSAGEFORM_05("1193", "05", "吸入剂", "DOSAGEFORM"),
    DOSAGEFORM_06("1148", "06", "冻干粉", "DOSAGEFORM"),
    DOSAGEFORM_07("1207", "07", "其它", "DOSAGEFORM"),
    DOSAGEFORM_08("", "08", "油针", "DOSAGEFORM"),
    DOSAGEFORM_09("1134", "09", "片剂", "DOSAGEFORM"),
    DOSAGEFORM_10("1152", "10", "胶囊剂", "DOSAGEFORM"),
    DOSAGEFORM_11("1204", "11", "干混悬剂", "DOSAGEFORM"),
    DOSAGEFORM_12("1138", "12", "缓释片", "DOSAGEFORM"),
    DOSAGEFORM_13("1187", "13", "颗粒剂", "DOSAGEFORM"),
    DOSAGEFORM_14("1192", "14", "混悬剂", "DOSAGEFORM"),
    DOSAGEFORM_15("1198", "15", "贴剂", "DOSAGEFORM"),
    DOSAGEFORM_16("1164", "16", "口服液", "DOSAGEFORM"),
    DOSAGEFORM_17("1162", "17", "滴剂", "DOSAGEFORM"),
    DOSAGEFORM_18("", "18", "肛门栓", "DOSAGEFORM"),
    DOSAGEFORM_19("1155", "19", "缓释胶囊剂", "DOSAGEFORM"),
    DOSAGEFORM_20("1153", "20", "胶丸剂", "DOSAGEFORM"),
    DOSAGEFORM_21("1190", "21", "气雾剂", "DOSAGEFORM"),
    DOSAGEFORM_22("1136", "22", "咀嚼片", "DOSAGEFORM"),
    DOSAGEFORM_23("1163", "23", "糖浆剂", "DOSAGEFORM"),
    DOSAGEFORM_24("1186", "24", "散剂", "DOSAGEFORM"),
    DOSAGEFORM_25("1159", "25", "凝胶剂", "DOSAGEFORM"),
    DOSAGEFORM_26("1186", "26", "粉剂", "DOSAGEFORM"),
    DOSAGEFORM_27("", "27", "滴丸剂", "DOSAGEFORM"),
    DOSAGEFORM_28("1138", "28", "控释片", "DOSAGEFORM"),
    DOSAGEFORM_29("1158", "29", "乳剂", "DOSAGEFORM"),
    DOSAGEFORM_30("1147", "30", "丸剂", "DOSAGEFORM"),
    DOSAGEFORM_31("334", "31", "合剂", "DOSAGEFORM"),
    DOSAGEFORM_32("", "32", "水蜜丸剂", "DOSAGEFORM"),
    DOSAGEFORM_34("1191", "34", "喷剂", "DOSAGEFORM"),
    DOSAGEFORM_35("1166", "35", "流浸膏剂", "DOSAGEFORM"),
    DOSAGEFORM_36("1176", "36", "滴眼剂", "DOSAGEFORM"),
    DOSAGEFORM_37("1185", "37", "眼膏剂", "DOSAGEFORM"),
    DOSAGEFORM_38("", "38", "眼用凝胶", "DOSAGEFORM"),
    DOSAGEFORM_39("1177", "39", "滴鼻剂", "DOSAGEFORM"),
    DOSAGEFORM_40("1191", "40", "喷雾剂", "DOSAGEFORM"),
    DOSAGEFORM_41("1178", "41", "滴耳剂", "DOSAGEFORM"),
    DOSAGEFORM_43("1143", "43", "阴道栓", "DOSAGEFORM"),
    DOSAGEFORM_44("1182", "44", "乳膏剂", "DOSAGEFORM"),
    DOSAGEFORM_45("1170", "45", "洗剂", "DOSAGEFORM"),
    DOSAGEFORM_46("1181", "46", "软膏剂", "DOSAGEFORM"),
    DOSAGEFORM_47("1167", "47", "酊剂", "DOSAGEFORM"),
    DOSAGEFORM_48("1172", "48", "油剂", "DOSAGEFORM"),
    DOSAGEFORM_49("1198", "49", "贴膏", "DOSAGEFORM"),
    DOSAGEFORM_50("1158", "50", "乳胶剂", "DOSAGEFORM"),
    DOSAGEFORM_51("1172", "51", "甘油剂", "DOSAGEFORM"),
    DOSAGEFORM_52("", "52", "中药饮片", "DOSAGEFORM"),
    DOSAGEFORM_53("", "53", "袋泡饮片", "DOSAGEFORM"),
    DOSAGEFORM_70("1180", "70", "灌肠剂", "DOSAGEFORM"),
    DOSAGEFORM_68("1148", "68", "冻干粉针", "DOSAGEFORM"),
    DOSAGEFORM_71("1139", "71", "泡腾片", "DOSAGEFORM"),
    DOSAGEFORM_42("", "42", "含漱液", "DOSAGEFORM"),
    DOSAGEFORM_33("1165", "33", "浸膏剂", "DOSAGEFORM");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisDosageFormEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

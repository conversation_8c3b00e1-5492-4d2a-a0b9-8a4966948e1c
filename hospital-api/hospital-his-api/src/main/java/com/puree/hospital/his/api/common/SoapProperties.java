package com.puree.hospital.his.api.common;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * @author: yanxia<PERSON><PERSON>
 * @date: 2025-04-07 10:57
 **/

@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "soap.base")
public class SoapProperties {

    private String url;

}

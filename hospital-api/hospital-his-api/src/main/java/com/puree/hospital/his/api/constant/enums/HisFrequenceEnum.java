package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 用药频次，对应表 bus_dict_drugs_frequency
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisFrequenceEnum  implements BaseHisEnum {
    QIW2("", "QIW2", "每周四次(1,3,5,7)", "FREQUENCE"),
    QW1("", "QW1", "每周1次(1)", "FREQUENCE"),
    QW5("", "QW5", "每周1次(5)", "FREQUENCE"),
    BIW3("", "BIW3", "每周2次(2,5)", "FREQUENCE"),
    BIW5("", "BIW5", "每周2次(3,6)", "FREQUENCE"),
    QW2("", "QW2", "每周1次(2)", "FREQUENCE"),
    TIW2("", "TIW2", "每周3次(2,4,6)", "FREQUENCE"),
    TIW3("", "TIW3", "每周3次(2,5,7)", "FREQUENCE"),
    QIW1("", "QIW1", "每周四次(1,2,3,4)", "FREQUENCE"),
    QW4("", "QW4", "每周1次(4)", "FREQUENCE"),
    FW("", "FW", "每周5次(1,2,3,4,5)", "FREQUENCE"),
    QW3("", "QW3", "每周1次(3)", "FREQUENCE"),
    BIW2("", "BIW2", "每周2次(2,4)", "FREQUENCE"),
    BIW4("", "BIW4", "每周2次(1,4)", "FREQUENCE"),
    QW7("", "QW7", "每周1次(7)", "FREQUENCE"),
    QW6("", "QW6", "每周1次(6)", "FREQUENCE"),
//    DAILY_2("", "12", "每日2剂", "FREQUENCE"),
//    DAILY_3("", "13", "每日3剂", "FREQUENCE"),
    BIW("biw", "BIW", "每周2次(1,3)", "FREQUENCE"),
//    DAILY_1("", "11", "每日1剂", "FREQUENCE"),
    EVERY_30_MIN("", "27", "每半小时一次", "FREQUENCE"),
    BID("bid", "BID", "每日2次", "FREQUENCE"),
    HS("Hs", "HS", "临睡前", "FREQUENCE"),
    NEW("", "NEW", "NEW", "FREQUENCE"),
    PRN("", "PRN", "必要时用", "FREQUENCE"),
    Q1("", "Q1", "Q1", "FREQUENCE"),
    Q1_2H("", "Q1/2H", "Q1/2H", "FREQUENCE"),
    Q12H("q12h", "Q12H", "每12小时1次", "FREQUENCE"),
    Q1H("q1h", "Q1H", "每小时1次", "FREQUENCE"),
    Q2("", "Q2", "Q2", "FREQUENCE"),
    Q2H("", "Q2H", "每2小时1次", "FREQUENCE"),
    Q3D("", "Q3D", "3天1次", "FREQUENCE"),
    Q3H("q3h", "Q3H", "每3小时1次", "FREQUENCE"),
    Q4D("", "Q4D", "每4天一次", "FREQUENCE"),
    Q4H("", "Q4H", "每4小时1次", "FREQUENCE"),
    Q6H("q6h", "Q6H", "每6小时1次", "FREQUENCE"),
    Q8H("q8h", "Q8H", "每8小时1次", "FREQUENCE"),
    QD("qd", "QD", "每日1次", "FREQUENCE"),
    QID("qid", "QID", "每日4次", "FREQUENCE"),
    QJ1_5("", "QJ1-5", "1周5次", "FREQUENCE"),
    QJ1_6("", "QJ1-6", "1周6次", "FREQUENCE"),
    QM("", "QM", "每早1次", "FREQUENCE"),
    QMN("", "QMN", "早晚各一次", "FREQUENCE"),
    QN("qn", "QN", "晚睡前", "FREQUENCE"),
    QOD("qod", "QOD", "隔日1次", "FREQUENCE"),
    QW("qw", "QW", "每周1次", "FREQUENCE"),
    SOS("", "SOS", "需要时(限用一次)", "FREQUENCE"),
    ST("st", "ST", "立即", "FREQUENCE"),
    TID("tid", "TID", "每日3次", "FREQUENCE"),
    TIW("", "TIW", "每周3次(1,3,5)", "FREQUENCE"),
    QOW("", "QOW", "隔周一次", "FREQUENCE"),
    ONCE("", "ONCE", "一次", "FREQUENCE"),
    Q4W("", "q4w", "每4周一次", "FREQUENCE"),
    BID812("", "BID812", "每日2次(早中)", "FREQUENCE");
//    ZYZ("", "ZYZ", "ZF", "FREQUENCE");

    private final String code;
    private final String mapping;
    private final String name;
    private final String type;

    HisFrequenceEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (HisFrequenceEnum value : HisFrequenceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }


}
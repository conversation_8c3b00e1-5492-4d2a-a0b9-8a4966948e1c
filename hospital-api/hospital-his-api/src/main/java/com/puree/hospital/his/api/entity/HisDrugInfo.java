package com.puree.hospital.his.api.entity;

import lombok.Data;

/**
 * 药品信息
 */
@Data
public class HisDrugInfo {

    /**
     * 标准通用名
     */
    private String standardCommonName;

    /**
     * 药品名称
     */
    private String drugsName;

    /**
     * 药品本位码
     */
    private String drugsStandardCode;

    /**
     * 国药准字
     */
    private String nmpn;

    /**
     * 拼音码
     */
    private String pinyinCode;

    /**
     * 药品制造商
     */
    private String drugsManufacturer;

    /**
     * 参考售价
     */
    private Double referenceSellingPrice;

    /**
     * 参考进价
     */
    private Double referencePurchasePrice;

    /**
     * 药房销售价
     */
    private Double sellingPrice;

    /**
     * 库存
     */
    private Double stock;

    /**
     * 关联科室
     */
    private String departmentIds;

    /**
     * 药品图片
     */
    private String drugsImg;

    /**
     * 药品详情
     */
    private String drugsDetails;

    /**
     * 药理/功效分类
     */
    private String efficacyClassification;

    /**
     * 药品类型
     */
    private String drugsType;

    /**
     * 药品分类
     */
    private String classifyId;

    /**
     * 药品规格
     */
    private String drugsSpecification;

    /**
     * 药品用法
     */
    private String drugsUsage;

    /**
     * 建议用量
     */
    private String recommendedDosage;

    /**
     * 最小制剂单位
     */
    private String minMakeUnit;

    /**
     * 处方标识
     */
    private String prescriptionIdentification;

    /**
     * 药品剂型
     */
    private String drugsDosageForm;

    /**
     * 药品包装单位
     */
    private String drugsPackagingUnit;

    /**
     * 医保类型
     */
    private String medicalInsuranceType;

    /**
     * 医保编码
     */
    private String nationalDrugCode;

    /**
     * 关联病种
     */
    private String icdCode;

    /**
     * 最小包装数量
     */
    private Double minPackNum;

    /**
     * 最小包装单位
     */
    private String minPackUnit;

    /**
     * 状态 0停用 1启用
     */
    private String status;

    /**
     * 药品类型 0西药 1中药
     */
    private Double type;

    /**
     * 煎煮方式
     */
    private String decoctingMethod;

    /**
     * 药品类别 P西药 PCC中草药 PCZ中成药
     */
    private String classCode;
    /**
     * 基础剂量 * 我方药品数量 = 处方中的单次剂量
     */
    private String baseDose;
    /**
     * 剂量单位 对应我方药品表的 minMakeUnit
     */
    private String doseUnit;

    /**
     *  是否是限制用药，0否 1是
     */
    private Double limitFlag;

    /**
     *  限制说明
     */
    private String limitExplan;

}

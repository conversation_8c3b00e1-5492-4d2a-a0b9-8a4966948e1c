package com.puree.hospital.his.api.entity.dto;

import com.puree.hospital.his.api.constant.enums.HisMedicalType14Enum;
import com.puree.hospital.his.api.constant.enums.HisMedicalTypeEnum;
import com.puree.hospital.his.api.constant.enums.HisPactCodeEnum;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * his处方信息
 */
@Data
public class HisPrescriptionInfoSyncDTO {
    /**
     * 挂号流水号 (必填） 挂号接口返回
     */
    @NotBlank(message = "挂号流水号不能为空")
    private String tranSerialNO;

    /**
     * 合同单位编码(必填）字典 {@link HisPactCodeEnum}
     */
    @NotBlank(message = "合同单位编码不能为空")
    private String pactCode;

    /**
     * 合同单位名称(必填）
     */
    private String pactName;

    /**
     * 医保结算类别编码(医保支付时必填）(可不填) 字典 {@link HisMedicalTypeEnum}
     */
    private String medtType;

    /**
     * 医保结算名称(医保支付时必填）(可不填)
     */
    private String medtName;

    /**
     * 病种编码(医保支付时必填）
     * {@link HisMedicalType14Enum}
     */
    private String diseCodg;

    /**
     * 病种名称(医保支付时必填）
     */
    private String diseName;

    /**
     * 诊断编码(必填） 取 表 bus_disease 中的数据
     */
    @NotBlank(message = "诊断编码不能为空")
    private String icd10Code;

    /**
     * 诊断名称(必填） 取 表 bus_disease 中的数据
     */
    @NotBlank(message = "诊断名称不能为空")
    private String icd10Name;

    /**
     * 开单科室编码(必填）
     */
    @NotBlank(message = "开单科室编码不能为空")
    private String deptCode;

    /**
     * 开单科室名称(必填）
     */
    @NotBlank(message = "开单科室名称不能为空")
    private String deptName;

    /**
     * 操作人工号(必填）
     */
    @NotBlank(message = "操作人工号不能为空")
    private String operCode;

    /**
     * 操作人姓名(必填）
     */
    @NotBlank(message = "操作人姓名不能为空")
    private String operName;

    /**
     * 就诊卡号(必填）
     */
    @NotBlank(message = "就诊卡号不能为空")
    private String cardNo;

    /**
     * 项目列表
     */
    @Valid
    private List<HisPrescriptionItemDTO> list;


    /**
     * 诊查费
     */
    private HisExaminationFeeItemDTO examinationFeeItem;
}

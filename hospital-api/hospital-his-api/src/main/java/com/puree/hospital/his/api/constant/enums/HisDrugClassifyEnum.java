package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 药品分类  对应表：bus_dict_drugs_classify
 */
@Getter
public enum HisDrugClassifyEnum implements BaseHisEnum {
    // code：我方字典值   mapping：HIS字典值
    DRUGQUALITY_S1("1", "S1", "麻醉药品", "DRUGQUALITY"),
    DRUGQUALITY_V("2", "V", "贵重药", "DRUGQUALITY"),
    DRUGQUALITY_E2("3", "E2", "试剂", "DRUGQUALITY"),
    DRUGQUALITY_02("4", "02", "保健品", "DRUGQUALITY"),
    DRUGQUALITY_O("5", "O", "普通药物", "DRUGQUALITY"),
    DRUGQUALITY_2("6", "2", "毒性药品", "DRUGQUALITY"),
    DRUGQUALITY_7("7", "7", "非限制使用的抗菌药物", "DRUGQUALITY"),
    DRUGQUALITY_8("8", "8", "限制使用的抗菌药物", "DRUGQUALITY"),
    DRUGQUALITY_9("9", "9", "特殊使用的抗菌药物", "DRUGQUALITY"),
    DRUGQUALITY_S0("10", "S0", "含兴奋剂药品", "DRUGQUALITY"),
    DRUGQUALITY_P3("11", "P3", "第一类精神类药物", "DRUGQUALITY"),
    DRUGQUALITY_P4("12", "P4", "第二类精神类药物", "DRUGQUALITY"),
    DRUGQUALITY_S6("13", "S6", "剧药", "DRUGQUALITY"),
    DRUGQUALITY_01("14", "01", "高危药品", "DRUGQUALITY"),
    DRUGQUALITY_E5("15", "E5", "未知", "DRUGQUALITY");
    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisDrugClassifyEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

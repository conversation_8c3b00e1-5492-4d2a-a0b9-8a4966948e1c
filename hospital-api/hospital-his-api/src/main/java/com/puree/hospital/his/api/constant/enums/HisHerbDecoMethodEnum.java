package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * 中药煎药方法枚举，todo 未映射
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisHerbDecoMethodEnum implements BaseHisEnum {
    HERB_DECO_METHOD_18("", "18", "煎膏调配", "HerbDecoMethod"),
    HERB_DECO_METHOD_101("", "101", "-", "HerbDecoMethod"),
    HERB_DECO_METHOD_221("", "221", "自煎", "HerbDecoMethod"),
    HERB_DECO_METHOD_222("", "222", "本院手工代煎", "HerbDecoMethod"),
    HERB_DECO_METHOD_224("", "224", "本院机器代煎（瓶装）", "HerbDecoMethod"),
    HERB_DECO_METHOD_225("", "225", "本院机器代煎（包装）", "HerbDecoMethod"),
    HERB_DECO_METHOD_226("", "226", "院外代煎", "HerbDecoMethod"),
    HERB_DECO_METHOD_333("", "333", "院内代煎", "HerbDecoMethod");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisHerbDecoMethodEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }
}
package com.puree.hospital.his.api.entity;

import lombok.Data;

import java.util.Date;

/**
 * his 就诊记录
 */
@Data
public class HisRegisteredInfo {
    /**
     * 挂号单据号
     */
    private String regFlow;

    /**
     * 挂号种类
     */
    private String regType;

    /**
     * 医生工号
     */
    private String doctorID;

    /**
     * 看诊时间
     */
    private String seeTime;

    /**
     * 挂号时间
     */
    private String regTime;

    /**
     * 看诊序号
     */
    private String seeNO;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 性别
     */
    private String patientSexID;

    /**
     * 年龄
     */
    private String patientAge;

    /**
     * 卡号
     */
    private String patientID;

    /**
     * 费用类别
     */
    private String feeType;

    /**
     * 挂号费,诊疗费
     */
    private String feeItem;

    /**
     * 挂号科室名称
     */
    private String departmentName;

    /**
     * 科室地址
     */
    private String departmentAddress;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 挂号金额
     */
    private String totalCost;

    /**
     * 支付方式
     */
    private String payTypeName;

    /**
     * 挂号流水号
     */
    private String tranSerialNO;

    /**
     * 预约时间
     */
    private String bookTime;

    /**
     * 中医病名
     */
    private String tcmDiagnose;

    /**
     * 西医诊断
     */
    private String wmDiagnose;

    /**
     * 看诊时间，Date类型
     */
    private Date seeDateTime;

}

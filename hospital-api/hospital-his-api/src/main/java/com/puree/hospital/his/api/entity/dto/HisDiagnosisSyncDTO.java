package com.puree.hospital.his.api.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * his-诊断信息
 * <AUTHOR>
 * @date 2025-3-28 14:32:16
 */
@Data
public class HisDiagnosisSyncDTO {

    /**
     * 挂号流水号 (必填） 挂号接口返回
     */
    @NotBlank(message = "挂号流水号不能为空")
    private String tranSerialNO;

    /**
     * 诊断医生编码(必填）
     */
    @NotBlank(message = "诊断医生编码不能为空")
    private String docCode;

    /**
     * 诊断医生名称(必填）
     */
    @NotBlank(message = "诊断医生名称不能为空")
    private String docName;

    /**
     * 诊断科室编码(必填）
     */
    @NotBlank(message = "诊断科室编码不能为空")
    private String deptCode;

    /**
     * 诊断科室名称(必填）
     */
    @NotBlank(message = "诊断科室名称不能为空")
    private String deptName;

    /**
     * 就诊卡号(必填）
     */
    @NotBlank(message = "就诊卡号不能为空")
    private String patientID;

    /**
     * 诊断列表
     */
    private List<HisDiagnosisItemDTO> list;


}

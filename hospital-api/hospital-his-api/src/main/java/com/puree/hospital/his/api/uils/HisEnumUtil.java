package com.puree.hospital.his.api.uils;

import com.puree.hospital.his.api.constant.BaseHisEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 枚举工具类
 */
public class HisEnumUtil {

    private HisEnumUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 缓存枚举类的 code 和 mapping 映射关系
     */
    private static final Map<Class<?>, Map<String, ? extends BaseHisEnum>> CODE_TO_ENUM_MAP = new ConcurrentHashMap<>();
    private static final Map<Class<?>, Map<String, ? extends BaseHisEnum>> MAPPING_TO_ENUM_MAP = new ConcurrentHashMap<>();

    /**
     * 通过 mapping 查询对应的 code
     * @param enumClass 枚举类
     * @param mapping HIS 值
     * @return Optional<String> 我方值
     * @param <T> HIS 枚举
     */
    public static <T extends Enum<T> & BaseHisEnum> Optional<String> mappingToCode(Class<T> enumClass, String mapping) {
        return Optional.ofNullable(getMappingToEnumMap(enumClass).get(mapping)).map(BaseHisEnum::getCode);
    }

    /**
     * 通过 name 查询对应的 code
     * @param enumClass 枚举类
     * @param name HIS 字典名称
     * @return Optional<String> 我方值
     * @param <T> HIS 枚举
     */
    public static <T extends Enum<T> & BaseHisEnum> Optional<String> nameToCode(Class<T> enumClass, String name) {
        return Optional.ofNullable(getNameToEnumMap(enumClass).get(name)).map(BaseHisEnum::getCode);
    }

    /**
     *  通过 name 获取对应的 mapping
     * @param enumClass 枚举类
     * @param name HIS 字典名称
     * @return Optional<String> 我方值
     * @param <T> HIS 枚举
     */
    public static <T extends Enum<T> & BaseHisEnum> Optional<String> nameToMapping(Class<T> enumClass, String name) {
        return Optional.ofNullable(getNameToEnumMap(enumClass).get(name)).map(BaseHisEnum::getMapping);
    }

    /**
     * 通过 code 查询对应的 mapping
     * @param enumClass 枚举类
     * @param code 我方值
     * @return Optional<String> HIS 值
     * @param <T> HIS 枚举
     */
    public static <T extends Enum<T> & BaseHisEnum> Optional<String> codeToMapping(Class<T> enumClass, String code) {
        return codeToEnum(enumClass,code).map(BaseHisEnum::getMapping);
    }

    /**
     * 通过 code 查询对应的 mapping enum
     * @param enumClass 枚举类
     * @param code 我方值
     * @return Enum<T> 枚举值
     * @param <T> HIS 枚举
     */
    public static <T extends Enum<T> & BaseHisEnum> Optional<T> codeToEnum(Class<T> enumClass, String code) {
        return Optional.ofNullable(getCodeToEnumMap(enumClass).get(code));
    }

    /**
     * 获取 code 到枚举实例的映射
     * @param enumClass 枚举类
     * @return Map<String, T> code 枚举缓存
     * @param <T> HIS 枚举
     */
    private static <T extends Enum<T> & BaseHisEnum> Map<String, T> getCodeToEnumMap(Class<T> enumClass) {
        //noinspection unchecked
        return (Map<String, T>) CODE_TO_ENUM_MAP.computeIfAbsent(enumClass, clazz -> {
            Map<String, T> map = new HashMap<>();
            for (T value : enumClass.getEnumConstants()) {
                map.put(value.getCode(), value);
            }
            return map;
        });
    }

    /**
     * 获取 mapping 到枚举实例的映射
     * @param enumClass 枚举类
     * @return Map<String, T> mapping 枚举缓存
     * @param <T> HIS 枚举
     */
    private static <T extends Enum<T> & BaseHisEnum> Map<String, T> getMappingToEnumMap(Class<T> enumClass) {
        //noinspection unchecked
        return (Map<String, T>) MAPPING_TO_ENUM_MAP.computeIfAbsent(enumClass, clazz -> {
            Map<String, T> map = new HashMap<>();
            for (T value : enumClass.getEnumConstants()) {
                map.put(value.getMapping(), value);
            }
            return map;
        });
    }

    /**
     * 获取 name 到枚举实例的映射
     * @param enumClass 枚举类
     * @return Map<String, T> name 枚举缓存
     * @param <T> HIS 枚举
     */
    private static <T extends Enum<T> & BaseHisEnum> Map<String, T> getNameToEnumMap(Class<T> enumClass) {
        //noinspection unchecked
        return (Map<String, T>) MAPPING_TO_ENUM_MAP.computeIfAbsent(enumClass, clazz -> {
            Map<String, T> map = new HashMap<>();
            for (T value : enumClass.getEnumConstants()) {
                map.put(value.getName(), value);
            }
            return map;
        });
    }
}

package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * 中药备注枚举类，todo 未映射
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisHerbMemoEnum implements BaseHisEnum {
    // 按照 mapping 字段从小到大排序
    MEMO_001("", "001", "水煎至400ml", "HerbMemo"),
    MEMO_002("", "002", "水煎2次，合并煎液", "HerbMemo"),
    MEMO_003("", "003", "水煎至200ml", "HerbMemo"),
    MEMO_004("", "004", "水煎至100ml", "HerbMemo"),
    MEMO_005("", "005", "水煎至800ml", "HerbMemo"),
    MEMO_006("", "006", "开水冲溶", "HerbMemo"),
    MEMO_007("", "007", "开水冲泡", "HerbMemo"),
    MEMO_008("", "008", "600ml煎至200ml", "HerbMemo"),
    MEMO_009("", "009", "500ml煎至100ml", "HerbMemo"),
    MEMO_010("", "010", "药膳", "HerbMemo"),
    MEMO_011("", "011", "产科中药熏洗", "HerbMemo"),
    MEMO_012("", "012", "水煎至300ml(代煎)", "HerbMemo"),
    MEMO_013("", "013", "开水冲溶至200ml", "HerbMemo"),
    MEMO_014("", "014", "开水冲溶至150ml", "HerbMemo"),
    MEMO_015("", "015", "开水冲溶至100ml", "HerbMemo"),
    MEMO_016("", "016", "开水冲溶至300ml", "HerbMemo"),
    MEMO_017("", "017", "开水冲溶至800ml", "HerbMemo"),
    MEMO_018("", "018", "开水冲溶至400ml", "HerbMemo"),
    MEMO_020("", "020", "水煎至150ml", "HerbMemo"),
    MEMO_022("", "22", "一剂煎两次，复煎", "HerbMemo"),
    MEMO_080("", "080", "外用", "HerbMemo"),
    MEMO_099("", "099", "煎膏调配", "HerbMemo"),
    MEMO_102("", "102", "双签名", "HerbMemo"),
    MEMO_261("", "261", "每日1剂", "HerbMemo"),
    MEMO_262("", "262", "每剂分2次使用", "HerbMemo"),
    MEMO_263("", "263", "每日2剂", "HerbMemo"),
    MEMO_264("", "264", "每剂分4次使用", "HerbMemo"),
    MEMO_265("", "265", "每剂分4次使用", "HerbMemo"),
    MEMO_266("", "266", "水煎分两次服", "HerbMemo"),
    MEMO_267("", "267", "水煎取400ml分早晚两次温服", "HerbMemo"),
    MEMO_268("", "268", "分早晚两次温服", "HerbMemo"),
    MEMO_269("", "269", "上午煎", "HerbMemo"),
    MEMO_270("", "270", "下午煎", "HerbMemo"),
    MEMO_271("", "271", "分上下午煎", "HerbMemo"),
    MEMO_272("", "272", "补肝益肾方", "HerbMemo"),
    MEMO_273("", "273", "益母阿胶方", "HerbMemo"),
    MEMO_274("", "274", "健脾固肾方", "HerbMemo"),
    MEMO_275("", "275", "复元通脉方", "HerbMemo"),
    MEMO_276("", "276", "健脾祛湿方", "HerbMemo"),
    MEMO_277("", "277", "鼻炎方", "HerbMemo"),
    MEMO_278("", "278", "消癖方", "HerbMemo"),
    MEMO_279("", "279", "温肾健脾方", "HerbMemo"),
    MEMO_280("", "280", "六子养精方", "HerbMemo"),
    MEMO_281("", "281", "化疗生髓方", "HerbMemo"),
    MEMO_282("", "282", "伤科开胃方", "HerbMemo"),
    MEMO_283("", "283", "每次1盒", "HerbMemo"),
    MEMO_600("", "600", "水煎至300ml", "HerbMemo"),
    MEMO_601("", "601", "600ml煎至300ml", "HerbMemo"),
    MEMO_602("", "602", "水煎至1000ml", "HerbMemo");

    /**
     * 我方字典值（留空）
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisHerbMemoEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }
}
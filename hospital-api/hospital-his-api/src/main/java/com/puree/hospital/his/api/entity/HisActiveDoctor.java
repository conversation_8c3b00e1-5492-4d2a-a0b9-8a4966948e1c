package com.puree.hospital.his.api.entity;

import lombok.Data;

import java.util.List;

/**
 * HIS 出诊医生
 */
@Data
public class HisActiveDoctor {
    /**
     * 班次代码
     */
    private String regScheduleCode;
    /**
     * 医生编码
     */
    private String doctorID;
    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 收费类别
     */
    private String feeType;
    /**
     * 出诊安排
     */
    private String scheduleTime;
    /**
     * 挂号数
     */
    private String regCount;
    /**
     * 挂号剩余数
     */
    private String limitCount;
    /**
     * 等待数
     */
    private String waitCount;
    /**
     * 医生职级代码
     */
    private String doctorLevelCode;
    /**
     * 医生职级 通过字典接口type=LEVEL获取
     */
    private String doctorLevel;
    /**
     * 总价
     */
    private String totalCost;
    /**
     * 收费项列表
     */
    private List<HisActiveDoctorFeeItem> list1;
}

package com.puree.hospital.his.api.entity;

import lombok.Data;

import java.util.Date;

/**
 * his 检查
 */
@Data
public class HisInspectReport {

    /**
     * 报告单ID
     */
    private String reportId;

    /**
     * 报告时间
     */
    private String reportDate;

    /**
     * 报告名称
     */
    private String reportName;

    /**
     * 报告医生
     */
    private String reportDoctor;

    /**
     * 患者姓名
     */
    private String patName;

    /**
     * 检查时间
     */
    private String studyTime;

    /**
     * 预约时间
     */
    private String bespeakTime;

    /**
     * 检查时间
     */
    private Date studyDateTime;


    public String getPatientName() {
        return patName;
    }
}

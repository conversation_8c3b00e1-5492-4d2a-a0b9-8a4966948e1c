package com.puree.hospital.his.api.feign;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.his.api.entity.HisConsultationRegistration;
import com.puree.hospital.his.api.entity.HisExamineReport;
import com.puree.hospital.his.api.entity.HisExamineReportDetail;
import com.puree.hospital.his.api.entity.HisInspectReport;
import com.puree.hospital.his.api.entity.HisInspectReportDetail;
import com.puree.hospital.his.api.entity.HisOutPatientRecord;
import com.puree.hospital.his.api.entity.HisRegisteredInfo;
import com.puree.hospital.his.api.entity.dto.HisConsultationCancellationDTO;
import com.puree.hospital.his.api.entity.dto.HisConsultationRegistrationDTO;
import com.puree.hospital.his.api.entity.query.HisExamineReportQuery;
import com.puree.hospital.his.api.entity.query.HisInspectReportQuery;
import com.puree.hospital.his.api.entity.query.HisOutPatientRecordQuery;
import com.puree.hospital.his.api.entity.query.HisRegisteredInfoQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * HIS系统挂号服务
 */
@FeignClient(contextId = "RemoteHisRegistrationService", value = ServiceNameConstants.HIS_SERVICE)
public interface RemoteHisRegistrationService {

    /**
     * HIS 挂号
     *
     * @param dto 挂号信息
     * @return 挂号响应结果
     */
    @PostMapping("/registration/consultation-registration")
    AjaxResult<HisConsultationRegistration> consultationRegistration(@RequestBody HisConsultationRegistrationDTO dto);

    /**
     * HIS 取消挂号
     *
     * @param dto 取消挂号信息
     * @return 取消结果
     */
    @PutMapping("/registration/consultation-cancellation")
    AjaxResult<Boolean> consultationCancellation(@RequestBody HisConsultationCancellationDTO dto);

    /**
     * 获取就诊记录
     *
     * @param query 查询条件
     * @return 就诊记录列表
     */
    @GetMapping("/registration/registered-info")
    AjaxResult<List<HisRegisteredInfo>> getRegisteredInfo(@SpringQueryMap HisRegisteredInfoQuery query);

    /**
     * 查询门诊患者文本病历
     * @param query 查询条件
     * @return 患者病历
     */
    @GetMapping("/registration/outpatient-record")
    AjaxResult<List<HisOutPatientRecord>> getOutPatientRecord(@SpringQueryMap HisOutPatientRecordQuery query);

    /**
     * 获取检验报告
     * @param query 查询条件
     * @return 患者检验报告
     */
    @GetMapping("/registration/examine")
    AjaxResult<List<HisExamineReport>> getExamineList(@SpringQueryMap HisExamineReportQuery query);

    /**
     * 获取检验报告明细
     * @param query 查询条件
     * @return 患者检验报告明细
     */
    @GetMapping("/registration/examine-detail")
    AjaxResult<List<HisExamineReportDetail>> getExamineDetail(@SpringQueryMap HisExamineReportQuery query);

    /**
     * 获取检查报告
     * @param query 查询条件
     * @return 患者检查报告
     */
    @GetMapping("/registration/inspect")
    AjaxResult<List<HisInspectReport>> getInspectList(@SpringQueryMap HisInspectReportQuery query);


    /**
     * 获取检查报告明细
     * @param query 查询条件
     * @return 患者检查报告明细
     */
    @GetMapping("/registration/inspect-detail")
    AjaxResult<List<HisInspectReportDetail>> getInspectDetail(@SpringQueryMap HisInspectReportQuery query);
}

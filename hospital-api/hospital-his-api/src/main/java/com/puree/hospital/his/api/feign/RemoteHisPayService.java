package com.puree.hospital.his.api.feign;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.his.api.entity.dto.HisOrderInfoDTO;
import com.puree.hospital.his.api.entity.dto.HisPayResultDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadDTO;
import com.puree.hospital.his.api.entity.dto.HisPayUploadResultDTO;
import com.puree.hospital.his.api.entity.dto.HisRefundDTO;
import com.puree.hospital.his.api.entity.dto.HisRefundResultDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * HIS支付/退款服务接口
 */
@FeignClient(contextId = "RemoteHisPayService", value = ServiceNameConstants.HIS_SERVICE)
public interface RemoteHisPayService {

    /**
     * 支付信息上报
     * @param dto 支付信息
     * @return 支付信息上传结果
     */
    @PostMapping("/pay/upload")
    AjaxResult<HisPayUploadResultDTO> payUpload(@RequestBody HisPayUploadDTO dto);

    /**
     * 退费
     * @param dto 退费信息
     * @return 是否允许退费
     */
    @PostMapping("/pay/refund")
    AjaxResult<HisRefundResultDTO> refund(@RequestBody HisRefundDTO dto);


    /**
     * HIS 修改地址
     * @param dto 收货信息
     * @return 是否成功
     */
    @PostMapping("/pay/modify-address")
    AjaxResult<HisRefundResultDTO> modifyAddress(@RequestBody@Validated HisOrderInfoDTO dto);


    /**
     * HIS 查询支付结果
     * @param dto 查询参数
     * @return 结果信息
     */
    @GetMapping("/pay/result")
    AjaxResult<Boolean> payResult(@SpringQueryMap @Validated HisPayResultDTO dto);

    /**
     * HIS 查询发票信息
     * @param invoiceNo 发票号
     * @return 发票url
     */
    @GetMapping("/pay/invoice")
    AjaxResult<String> invoiceInfo(@RequestParam("invoiceNo") String invoiceNo);
}

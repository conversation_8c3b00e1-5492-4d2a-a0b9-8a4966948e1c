package com.puree.hospital.his.api.uils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.NameFilter;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.puree.hospital.his.api.common.HisIgnoreName;
import com.puree.hospital.his.api.exception.HisConfigProcessingException;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * his 相关工具类
 */
@Slf4j
public class HisUtils  {

    private HisUtils() {
        throw new IllegalStateException("Utility class");
    }

//    private static final SerializeConfig CONFIG = new SerializeConfig();

    /**
     * 名称过滤器，用于处理字段名称的大小写转换
     * 如果字段带有 @HisIgnoreName 注解，则保持原样
     * 否则将首字母大写
     */
    private static final NameFilter capitalizeFilter = (obj, name, value) -> {
        if (obj == null || name == null || name.isEmpty()) {
            return name;
        }

        try {
            Field field = obj.getClass().getDeclaredField(name);
            if (field.isAnnotationPresent(HisIgnoreName.class)) {
                return name;
            }
        } catch (NoSuchFieldException e) {
            log.debug("Field '{}' not found in class {}", name, obj.getClass().getName());
            // 字段不存在时依然进行首字母大写处理
        }

        return Character.toUpperCase(name.charAt(0)) + name.substring(1);
    };

    /**
     * 递归处理对象及其嵌套对象的配置过滤器
     *
     * @param obj              需要处理的对象
     * @param processedClasses 已处理的类集合，用于防止循环引用
     * @param config           JSON SerializeConfig
     * @throws HisConfigProcessingException 当配置处理过程中发生错误时
     */
    private static void processObject(final Object obj, final Set<Class<?>> processedClasses, SerializeConfig config) {
        // 使用Optional处理空值检查
        Optional.ofNullable(obj).ifPresent(object -> {
            final Class<?> clazz = object.getClass();

            // 如果已处理过该类则直接返回
            if (!processedClasses.add(clazz)) {
                return;
            }

            try {
                // 添加过滤器配置
                processClass(clazz, config);
                // 处理字段
                processFields(object, clazz, processedClasses, config);
            } catch (Exception e) {
                log.error("配置处理过程中发生错误: ", e);
                throw new HisConfigProcessingException();
            }
        });
    }

    /**
     * 处理类的配置
     *
     * @param clazz  需要处理的类
     * @param config    JSON SerializeConfig
     */
    private static void processClass(Class<?> clazz, SerializeConfig config) {
        config.addFilter(clazz, capitalizeFilter);
    }

    /**
     * 处理对象的所有字段
     *
     * @param obj              需要处理的对象
     * @param clazz            对象的类
     * @param processedClasses 已处理的类集合
     * @param config           JSON SerializeConfig
     */
    private static void processFields(Object obj, Class<?> clazz, Set<Class<?>> processedClasses, SerializeConfig config) {
        Arrays.stream(clazz.getDeclaredFields())
                .forEach(field -> {
                    field.setAccessible(true);
                    processField(obj, field, processedClasses, config);});
    }

    /**
     * 处理单个字段
     *
     * @param obj              字段所属的对象
     * @param field            需要处理的字段
     * @param processedClasses 已处理的类集合
     * @param config           JSON SerializeConfig
     */
    private static void processField(Object obj, Field field, Set<Class<?>> processedClasses, SerializeConfig config) {
        try {
            Object fieldValue = field.get(obj);
            if (fieldValue == null) {
                return;
            }

            if (isCollection(fieldValue)) {
                processCollectionField((Collection<?>) fieldValue, processedClasses, config);
            } else if (isCustomObject(fieldValue)) {
                processObject(fieldValue, processedClasses, config);
            }
        } catch (IllegalAccessException e) {
            log.error("无法访问字段: {}", field.getName(), e);
        }
    }

    /**
     * 处理集合类型的字段
     * @param collection 需要处理的集合
     * @param processedClasses 已处理的类集合
     * @param config  JSON SerializeConfig
     */
    private static void processCollectionField(Collection<?> collection, Set<Class<?>> processedClasses, SerializeConfig config) {
        collection.stream()
                .filter(Objects::nonNull)
                .forEach(item -> processObject(item, processedClasses, config));
    }

    /**
     * 判断对象是否为集合类型
     * @param obj 需要判断的对象
     * @return 如果是集合类型返回true，否则返回false
     */
    private static boolean isCollection(Object obj) {
        return obj instanceof Collection;
    }

    /**
     * 判断是否为自定义对象
     * @param obj 需要判断的对象
     * @return 如果是自定义对象返回true，否则返回false
     */
    private static boolean isCustomObject(Object obj) {
        Class<?> clazz = obj.getClass();
        return !clazz.isPrimitive() && !clazz.getName().startsWith("java.");
    }


    /**
     * 将对象转换为特定格式的JSON字符串
     * @param obj 需要转换的对象
     * @param <T> 对象类型
     * @return 格式化的JSON字符串
     * @throws IllegalArgumentException 当输入对象为null时
     */
    public static <T> String toHisFormatJsonString(T obj) {
        if (obj == null) {
            throw new IllegalArgumentException("输入对象不能为null");
        }
        // 用于记录已经处理过的类，避免重复处理
        Set<Class<?>> processedClasses = new HashSet<>();

        // 目前看多线程的时候会抛出ConcurrentModificationException null
        SerializeConfig config = new SerializeConfig();

        // 递归处理对象及其嵌套对象
        processObject(obj, processedClasses, config);

        return JSON.toJSONString(
                obj,
                config,
                SerializerFeature.WriteNullStringAsEmpty
        );
    }
}

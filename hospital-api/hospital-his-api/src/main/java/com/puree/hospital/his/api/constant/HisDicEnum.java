package com.puree.hospital.his.api.constant;

import lombok.Getter;

/**
 * 字典枚举类
 */
@Getter
@SuppressWarnings({ "SpellCheckingInspection"})
public enum HisDicEnum {

    LEVEL("LEVEL","医师职级"),
    GZSI_MDTRT_CERT_TYPE("GZSI_mdtrt_cert_type","人员证件类型"),
    GZSI_MED_TYPE("GZSI_med_type","医疗类别"),
    GZSI_MED_TYPE_14("GZSI_med_type_14","医疗类别为14下的病种编码"),
//    GZSI_MED_TYPE_13("GZSI_med_type_13","医疗类别为13下的病种编码"),
    GZSI_MED_TYPE_11("GZSI_med_type_11","医疗类别为11下的病种编码"),
    PAYKIND("PAYKIND","医院内部结算方式"),
    IDCard("IDCard","证件类型"),
    PAYMODES("PAYMODES","支付方式"),
    USAGE("USAGE","药品用法"),
    USAGEZY("USAGEZY","药品用法（中草药）"),
    HerbDecoMethod("HerbDecoMethod","中药煎药方式（自煎/院内代煎）"),//
    HerbMemo("HerbMemo","草药备注"),//
    DecoctingMethod("HerbMemo","草药煎药法"),//
    FREQUENCE("FREQUENCE","频次（西医）"),
    FREQUENCEZY("FREQUENCE","频次(中医)"),
    ICD10("ICD10","ICD诊断"),//取 表 bus_disease 中的数据
    TradDisease("TradDisease","中医诊断"),//取表 bus_tcm_diagnosis
    TradSyndrom("TradSyndrom","中医证候"),//取表 bus_tcm_syndrome
    RELATIVE("RELATIVE","联系人关系"),
    PACTCODE("PACTCODE","合同单位"),;//todo





    private String code;
    private String value;

    HisDicEnum(String code, String value){
        this.code = code;
        this.value = value;
    }

    /**
     * 获取字典值
     * @param code 字典编码
     * @return
     */
    public static String getEnum(String code){
        for (HisDicEnum dicEnum : HisDicEnum.values()) {
            if (dicEnum.getCode().equals(code)){
                return dicEnum.getValue();
            }
        }
        return null;
    }
}

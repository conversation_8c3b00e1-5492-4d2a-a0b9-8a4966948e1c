package com.puree.hospital.his.api.entity.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;

/**
 * 查询处方明细
 */
@Data
public class HisPrescriptionInfoDTO {

    /**
     * 门诊流水号（必填）
     */
    @NotBlank(message = "门诊流水号不能为空")
    private String tranSerialNO;

    /**
     * 卡号（必填）
     */
    @NotBlank(message = "就诊卡号不能为空")
    private String cardNO;

    /**
     * 开始时间（必填）
     */
    @NotBlank(message = "开始时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startDate;

    /**
     * 结束时间（必填）
     */
    @NotBlank(message = "结束时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endDate;
}

package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * 医疗类别为 11 下的病种编码,todo 待后续开发，暂时无此功能
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisMedicalType11Enum implements BaseHisEnum {
    MZ0301("", "MZ0301", "家庭医生", "GZSI_MED_TYPE_11"),
    T01001("", "T01001", "城居生育产检", "GZSI_MED_TYPE_11"),
    T01002("", "T01002", "门诊接种狂犬病疫苗", "GZSI_MED_TYPE_11"),
    T01003("", "T01003", "门诊核酸检测", "GZSI_MED_TYPE_11"),
    NORMAL_CLINIC("", "110", "普通门诊", "GZSI_MED_TYPE_11"),
    EMERGENCY_RESUSCITATION("", "130", "急救抢救(未选点)", "GZSI_MED_TYPE_11");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisMedicalType11Enum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }


}

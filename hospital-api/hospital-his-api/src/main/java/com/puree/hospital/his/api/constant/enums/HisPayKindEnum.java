package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * his 医院内部结算方式
 */
@Getter
@SuppressWarnings("SpellCheckingInspection")
public enum HisPayKindEnum implements BaseHisEnum {
    ZF("1", "01", "自费", "PAYKIND"),
    YB("2", "02", "医保", "PAYKIND"),
    GF("", "03", "公费", "PAYKIND"),
    TYDW("", "04", "特约单位", "PAYKIND"),
    BYZG("", "05", "本院职工", "PAYKIND");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisPayKindEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }


}
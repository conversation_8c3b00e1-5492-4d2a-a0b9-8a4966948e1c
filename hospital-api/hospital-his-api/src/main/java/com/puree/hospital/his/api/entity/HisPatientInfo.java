package com.puree.hospital.his.api.entity;

import lombok.Data;

/**
 * 患者信息
 */
@Data
public class HisPatientInfo {
    /**
     * 患者ID
     */
    private String patientID;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女 9其他
     */
    private String patientSexID;

    /**
     * 身份证号
     */
    private String idCardNO;

    /**
     * 账号余额
     */
    private String balance;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 年龄
     */
    private String patientAge;

    /**
     * 卡状态
     */
    private String cardStatus;

    /**
     * 家庭电话
     */
    private String mobile;

    /**
     * 银行卡号
     */
    private String bankCardNO;

    /**
     * 就诊卡号
     */
    private String cardNO;

    /**
     * 住院号
     */
    private String patientNo;

    /**
     * 户口或家庭所在
     */
    private String home;

    /**
     * 合同单位
     */
    private String pactName;

    /**
     * 身份证件类型 字典 type=IDCard
     */
    private String idCardType;

    /**
     * 联系人姓名
     */
    private String linkmanName;

    /**
     * 联系人电话
     */
    private String linkmanTel;

    /**
     * 联系人关系编码
     */
    private String relaCode;
}

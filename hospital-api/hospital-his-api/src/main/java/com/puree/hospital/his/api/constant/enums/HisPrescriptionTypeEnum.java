package com.puree.hospital.his.api.constant.enums;

import com.puree.hospital.his.api.constant.BaseHisEnum;
import lombok.Getter;

/**
 * HIS 处方标识  字典表  dict_type : bus_prescription_type
 */
@Getter
public enum HisPrescriptionTypeEnum implements BaseHisEnum {
    // code：我方字典值   mapping：HIS字典值
    PRESCRIPTION_TYPE_0("391", "0", "处方药", "prescriptionIdentification"),
    PRESCRIPTION_TYPE_1("392", "1", "OTC", "prescriptionIdentification");

    /**
     * 我方字典值
     */
    private final String code;
    /**
     * HIS字典值
     */
    private final String mapping;
    /**
     * 字典名称
     */
    private final String name;
    /**
     * HIS 字典类型
     */
    private final String type;

    HisPrescriptionTypeEnum(String code, String mapping, String name, String type) {
        this.code = code;
        this.mapping = mapping;
        this.name = name;
        this.type = type;
    }

}

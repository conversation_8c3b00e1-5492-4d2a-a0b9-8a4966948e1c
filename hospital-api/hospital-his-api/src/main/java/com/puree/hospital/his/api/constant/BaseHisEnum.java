package com.puree.hospital.his.api.constant;


import com.puree.hospital.his.api.exception.HisNoCorrespondingMappingException;
import com.puree.hospital.his.api.uils.HisEnumUtil;

/**
 *
 */
public interface BaseHisEnum {

    /**
     * 获取枚举的 code 值
     * @return code
     */
    String getCode();

    /**
     * 获取枚举的 mapping 值
     * @return mapping
     */
    String getMapping();

    /**
     * 获取枚举的 name 值
     * @return name
     */
    String getName();

    /**
     * 查询我方值
     * @param mapping 映射
     * @return 我方值
     */
    static <T extends Enum<T> & BaseHisEnum> String mappingToCode(Class<T> enumClass, String mapping) {
        return HisEnumUtil
                .mappingToCode(enumClass, mapping)
                .orElseThrow(HisNoCorrespondingMappingException::new);
    }

    /**
     * 查询我方值
     * @param name 字典名称
     * @return 我方值
     */
    static <T extends Enum<T> & BaseHisEnum> String nameToCode(Class<T> enumClass, String name) {
        return HisEnumUtil
                .nameToCode(enumClass, name)
                .orElseThrow(HisNoCorrespondingMappingException::new);
    }

    /**
     * 查询对应映射
     * @param code 我方字典值
     * @return 对应的 his 字典值
     */
    static <T extends Enum<T> & BaseHisEnum> String codeToMapping(Class<T> enumClass, String code) {
        return HisEnumUtil
                .codeToMapping(enumClass, code)
                .orElseThrow(HisNoCorrespondingMappingException::new);
    }

    /**
     * 查询对应 枚举值
     * @param code 我方字典值
     * @return 对应的 his 字典值
     */
    static <T extends Enum<T> & BaseHisEnum> T codeToEnum(Class<T> enumClass, String code) {
        return HisEnumUtil
                .codeToEnum(enumClass, code)
                .orElseThrow(HisNoCorrespondingMappingException::new);
    }

    /**
     * 查询对应映射
     * @param code 我方字典值
     * @return 对应的 his 字典值
     */
    static <T extends Enum<T> & BaseHisEnum> String nameToMapping(Class<T> enumClass, String code) {
        return HisEnumUtil
                .nameToMapping(enumClass, code)
                .orElseThrow(HisNoCorrespondingMappingException::new);
    }
}

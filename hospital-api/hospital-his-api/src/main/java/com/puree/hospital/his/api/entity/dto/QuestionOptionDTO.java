package com.puree.hospital.his.api.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 问卷题目选项BO
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/25 12:25
 */
@Data
public class QuestionOptionDTO {

    /**
     *  选项ID
     */
    private String id;

    /**
     *  选项值
     */
    private String value;

    /**
     *  是否隐藏
     */
    private boolean hideOpt;

    /**
     *  子选项类型 0.选项 1.带输入框选项
     */
    private Integer optType;

    /**
     *  类型 0.格式不限 1.数字 2.手机号 3.邮箱 4.身份证号 5.日期 6.时间 7.日期和时间
     */
    private Integer pattern;

    /**
     *  是否必填 1是 0否
     */
    private Integer require;

    /**
     *  子题目数组
     */
    private List<QuestionTitleDTO> children;

    /**
     *  是否有子节点
     */
    private boolean isHaveChildren;

    /**
     * 关联的选项ID
     */
    private List<String> realtionOption;

    /**
     *  是否互斥
     */
    private boolean mutualExclusion;

    /**
     *  是否显示添加关联按钮
     */
    private boolean showAddRelation;

    /**
     *  关联的问卷题ID
     */
    private String realtionQuestion;

    /**
     *  关联的选项列表
     */
    private List<QuestionOptionDTO> realtionOptionList;

    /**
     *  分值
     */
    private Integer score;
}

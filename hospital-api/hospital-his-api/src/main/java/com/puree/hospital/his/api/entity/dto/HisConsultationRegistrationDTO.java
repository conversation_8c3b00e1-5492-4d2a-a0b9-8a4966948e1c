package com.puree.hospital.his.api.entity.dto;

import lombok.Data;

/**
 * his 挂号 DTO
 */
@Data
public class HisConsultationRegistrationDTO {
    /**
     * 患者ID（必填）
     */
    private String patientId;

    /**
     * 科室编码（必填）
     */
    private String deptCode;

    /**
     * 科室名称（必填）
     */
    private String deptName;

    /**
     * 医生编码（必填）
     */
    private String doctCode;

    /**
     * 医生姓名（必填）
     */
    private String doctName;

    /**
     * 职级编码（必填）
     */
    private String revelCode;

    /**
     * 挂号等级名称（必填）
     */
    private String regLevelName;

    /**
     * 金额（必填）
     */
    private String totCost;

    /**
     * 支付方式编码（必填）通过字典获取 PAYMODES
     */
    private String payTypeID;

    /**
     * 订单流水号
     */
    private String POSTransNO;

    /**
     * 操作工号
     */
    private String userID;
    /**
     * 合同单位编码（通过字典获取，type=PACTCODE）
     */
    private String pactCode;
    /**
     * 合同单位名称
     */
    private String pactName;

    /**
     *
     */
    private String openAccount = "";

}

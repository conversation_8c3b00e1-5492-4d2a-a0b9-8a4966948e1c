package com.puree.hospital.his.api.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 患者建档 新增或修改 DTO
 */
@Data
public class HisPatientInfoSaveDTO {
    /**
     * 患者卡号，传空
     */
    private String cardNO;

    /**
     * 患者姓名（必填）
     */
    @NotBlank(message = "患者姓名不能为空")
    private String patientName;

    /**
     * 患者性别（必填）
     */
    @NotBlank(message = "患者性别不能为空")
    private String patientSexID;

    /**
     * 出生日期（必填）
     */
    @NotBlank(message = "患者出生日期不能为空")
    private String birthday;

    /**
     * 身份证号
     */
    @JsonProperty("idCardNo")
    @NotBlank(message = "患者证件号不能为空")
    private String iDCardNO;

    /**
     * 身份证件类型 字典 type=IDCard,此处需要在调用HIS时转换内容
     */
    @NotBlank(message = "患者证件号类型不能为空")
    private String idCardType;

    /**
     * 地址
     */
    private String address;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 联系人姓名（14岁以下儿童时必填）
     */
    private String linkmanName;

    /**
     * 联系人电话（14岁以下儿童时必填）
     */
    private String linkmanTel;

    /**
     * 联系人关系编码（14岁以下儿童时必填），字典RELATIVE
     */
    private String relaCode;

    /**
     * 操作人员ID（必填）
     */
    private String userID;
}

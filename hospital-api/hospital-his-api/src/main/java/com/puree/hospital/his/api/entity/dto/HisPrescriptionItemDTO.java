package com.puree.hospital.his.api.entity.dto;

import com.puree.hospital.his.api.common.HisIgnoreName;
import com.puree.hospital.his.api.common.group.HisChineseMedicineGroup;
import com.puree.hospital.his.api.constant.enums.HisFrequenceEnum;
import com.puree.hospital.his.api.constant.enums.HisHerbDecoMethodEnum;
import com.puree.hospital.his.api.constant.enums.HisHerbMemoEnum;
import com.puree.hospital.his.api.constant.enums.HisUsageEnum;
import com.puree.hospital.his.api.constant.enums.HisUsageZyEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 处方同步明细项
 */
@Data
public class HisPrescriptionItemDTO {
    /**
     * 项目编码(必填）
     */
    @NotBlank(message = "项目编码不能为空")
    private String itemNo;

    /**
     * 项目类别(必填）  0为非药品 1为药品
     */
    @NotBlank(message = "项目类别不能为空")
    @Pattern(regexp = "^(0|1)$", message = "项目类别只能是 0 或 1")
    private String itemType;

    /**
     * 项目数量(必填）
     */
    @NotBlank(message = "项目数量不能为空")
    private String QTY;

    /**
     * 执行科室编码(必填）
     */
    private String exceDept;
    /**
     * 执行科室名称(必填）)
     */
    private String exceDeptName;

    /**
     * 单次剂量(必填）
     */
    @HisIgnoreName
    @NotBlank(message = "单次剂量不能为空")
    private String singleDose;

    /**
     * 剂量单位(必填）
     */
    @HisIgnoreName
    @NotNull(message = "剂量单位不能为空")
    private String unit;

    /**
     * 用药频率编码(必填）
     * 西药 {@link HisFrequenceEnum}
     * 中药 todo {@link }
     */
    @HisIgnoreName
    @NotBlank(message = "用药频率编码不能为空")
    private String medicationFrequency;

    /**
     * 用药频率名称(必填）
     * 西药 {@link HisFrequenceEnum}
     * 中药 todo {@link }
     */
    @HisIgnoreName
    @NotBlank(message = "用药频率名称不能为空")
    private String medicationFrequencyRemarks;

    /**
     * 用药途径名称(必填）字典USAGE
     * 西药 {@link HisUsageEnum}
     * 中药 {@link HisUsageZyEnum}
     */
    @HisIgnoreName
    @NotBlank(message = "用药途径名称不能为空")
    private String drugsUsageValue;

    /**
     * 用药途径编码(必填）字典USAGE
     * 西药 {@link HisUsageEnum}
     * 中药 {@link HisUsageZyEnum}
     */
    @HisIgnoreName
    @NotBlank(message = "用药途径编码不能为空")
    private String drugsUsageValueCode;

    /**
     * 组合号，同一组药传相同的组号，可为空
     */
    @HisIgnoreName
    private String comboNo;

    /**
     * 医保限制条件 0 不是限制用药  符合传 1 代表医保限制但可报销  不符合传 2
     */
    @Pattern(regexp = "^(0|1|2)$", message = "医保限制条件参数有误")
    private String rangFlag;

    /**
     * 自煎还是院内代煎
     */
    @NotBlank(message = "中药 自煎还是院内代煎代码 不能为空" ,groups = HisChineseMedicineGroup.class)
    private String herbDecoMethodCode;

    /**
     * 自煎还是院内代煎
     */
    @NotBlank(message = "中药 自煎还是院内代煎名称 不能为空" ,groups = HisChineseMedicineGroup.class)
    private String herbDecoMethodName;


    /**
     * 中药剂数
     */
    @NotBlank(message = "中药剂数不能为空" ,groups = HisChineseMedicineGroup.class)
    private String courseSpan;
    /**
     * 中药煎煮方式，字典 {@link HisHerbDecoMethodEnum}
     */
    @NotBlank(message = "中药煎煮方式不能为空" ,groups = HisChineseMedicineGroup.class)
    private String decoctingMethod;

    /**
     * 药品备注（中药处方时可根据字典type=HerbMemo）
     * 中药可采用
     * {@link HisHerbMemoEnum}
     */
    private String herbMemo;
}

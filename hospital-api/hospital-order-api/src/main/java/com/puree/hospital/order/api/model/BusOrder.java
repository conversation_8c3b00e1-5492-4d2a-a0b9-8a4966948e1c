package com.puree.hospital.order.api.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 总订单表
 */
@Data
public class BusOrder extends Entity {
	/**
	 * 订单编号
	 */
	private String orderNo;
	/**
	 * 子订单ID
	 */
	private Long subOrderId;
	/**
	 * 订单类型 0药品订单 1商品订单
	 */
	private String subOrderType;
	/**
	 * 下单时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date orderTime;
	/**
	 * 机构code
	 */
	private String partnersCode;
	/**
	 * 医院ID
	 */
	private Long hospitalId;
	/**
	 * 患者ID
	 */
	private Long patientId;

	/**
	 * 取消理由
	 */
	private String cancelResult;

	/**
	 * 实付金额
	 */
	private Double relPrice;

	/**
	 * 运费
	 */
	private Double freight;

	/**
	 * 发货方式 0-自提 1-快递
	 */
	private String deliveryType;

	/**
	 * 自提时间
	 */
	private String pickUpTime;
	/**
	 * 发货时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date deliveryTime;
	/**
	 * 付款时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date paymentTime;

	/**
	 * 取消时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date cancelTime;

	/**
	 * 完成时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date completeTime;

	/**
	 * 收货人
	 */
	private String receiver;

	/**
	 * 收货人电话
	 */
	private String receivePhone;

	/**
	 * 收货地址
	 */
	private String receiveAddress;

	/**
	 * 支付方式 1-微信支付 2-通联支付
	 */
	private String payWay;

	/**
	 * 订单状态 0待支付 1待发货 2待自提 3待收货 4已取消 5已完成
	 */
	private String orderStatus;

	/**
	 * 售后单id
	 */
	private Long afterSaleId;

	/**
	 * 医院名称
	 */
	private String hospitalName;
	/**
	 * 省
	 */
	private String province;
	/**
	 * 市
	 */
	private String city;
	/**
	 * 区
	 */
	private String area;
	/**
	 * 详细地址
	 */
	private String detailedAddress;
	/**
	 * 通联支付订单编号
	 */
	private String tonglianTrxid;
	/**
	 * 是否删除（0否 1是）
	 */
	private String delStatus;
	/**
	 * 是否改价（0否 1是）
	 */
	private Integer changePrice;
	/**
	 * 是否新订单（0否 1是）
	 */
	private Integer newOrderOrNot;
	/**
	 * 备注
	 */
	private String remarks;
	/**
	 * 子药品订单编号
	 */
	@TableField(exist = false)
	private String drugsOrderNo;
	/**
	 * 子商品订单编号
	 */
	@TableField(exist = false)
	private String goodsOrderNo;
	/**
	 * 是否修改地址（0否 1是）
	 */
	private Integer modifyAddress;
	/**
	 * 群组ID
	 */
	private Long groupId;

	/**
	 * 发票状态, 参考 InvoiceStatusEnum.java
	 */
	private Integer invoiceStatus;

	/**
	 * 发票类型 1 税控盘 2 全电  参考 InvoiceTypeEnum.java
	 */
	private Integer invoiceType ;

	/**
	 * 发票代码,   发蓝色发票返回此字段，用于开具红色发票
	 */
	@TableField(exist = false)
	private String invoiceCode ;

	/**
	 * 发票号码,   发蓝色发票返回此字段，用于开具红色发票
	 */
	@TableField(exist = false)
	private String invoiceNo ;

	/**
	 * 发票下载地址
	 */
	@TableField(exist = false)
	private String downloadUrl ;

	/**
	 * 发票预览地址
	 */
	@TableField(exist = false)
	private String picUrl ;

	/**
	 * 开具发票失败错误码
	 */
	@TableField(exist = false)
	private String invoiceErrorCode ;

	/**
	 * 开具发票失败错误信息
	 */
	@TableField(exist = false)
	private String invoiceErrorMsg ;

	private Long invoiceHeaderId ;

	/**
	 * 诊查费
	 */
	private BigDecimal examinationFee;

	/**
	 * 诊查费项目名称
	 */
	private String examinationName;

}
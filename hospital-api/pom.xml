<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hospital</artifactId>
        <groupId>com.puree</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hospital-api</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>hospital-system-api</module>
        <module>hospital-operate-api</module>
        <module>hospital-business-api</module>
        <module>hospital-im-api</module>
        <module>hospital-tool-api</module>
        <module>hospital-supplier-api</module>
        <module>hospital-suppliermanger-api</module>
        <module>hospital-app-api</module>
        <module>hospital-five-api</module>
        <module>hospital-pay-api</module>
        <module>hospital-order-api</module>
        <module>hospital-ehr-api</module>
        <module>hospital-shop-api</module>
        <module>hospital-insurance-api</module>
        <module>hospital-tutorial-api</module>
        <module>hospital-follow-up-api</module>
        <module>hospital-monitor-api</module>
        <module>hospital-setting-api</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

</project>
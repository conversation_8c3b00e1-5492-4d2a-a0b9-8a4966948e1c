package com.puree.hospital.tool.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.tool.api.RemoteBaseWxService;
import com.puree.hospital.tool.api.model.WXShippingInfo;
import com.puree.hospital.tool.api.model.WXShippingResponse;
import com.puree.hospital.tool.api.model.dto.QrCodeDTO;
import com.puree.hospital.tool.api.model.dto.WxAccessTokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <p>
 * 微信通用远程服务失败工厂
 * </p>
 *
 * <AUTHOR>
 * @date 2025/1/6 15:26
 */
@Slf4j
public class RemoteBaseWxServiceFallbackFactory implements FallbackFactory<RemoteBaseWxService> {

    @Override
    public RemoteBaseWxService create(Throwable cause) {
        log.error("远程调用微信服务失败", cause);
        return new RemoteBaseWxService() {
            @Override
            public R<String> getAccessToken(WxAccessTokenDTO wxAccessTokenDTO, String source) {
                return R.fail("调用微信服务获取access token失败");
            }

            @Override
            public R<String> refreshAccessToken(WxAccessTokenDTO wxAccessTokenDTO, String source) {
                return R.fail("刷新微信服务access token失败");
            }

            @Override
            public R<WXShippingResponse> uploadShippingInfo(WXShippingInfo wxShippingInfo, String accessToken, String source) {
                return R.fail(cause.getMessage());
            }


            @Override
            public R<Boolean> isTradeManagementConfirmationCompleted(String accessToken, String appid, String source) {
                return R.fail("查询小程序是否已完成交易结算管理确认");
            }

            @Override
            public R<Boolean> isTradeManaged(String accessToken, String appid, String source) {
                return R.fail("查询小程序是否已开通发货信息管理服务");
            }

            @Override
            public R<String> getQrCode(QrCodeDTO qrCodeDTO, String source) {
                return R.fail("获取小程序二维码失败");
            }
        };
    }
}

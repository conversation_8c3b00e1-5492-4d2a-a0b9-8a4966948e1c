package com.puree.hospital.ehr.api.model.enums;

import lombok.Getter;

/**
 * <p>
 * 诊疗记录创建方式枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/27 14:27
 */
@Getter
public enum DiagnosisCreateModeEnum {

    /**
     *  0.系统生成
     */
    SYSTEM(0, "系统生成"),

    /**
     *  1.手动添加
     */
    MANUALLY(1, "手动添加");

    private final Integer code;

    private final String name;

    DiagnosisCreateModeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}

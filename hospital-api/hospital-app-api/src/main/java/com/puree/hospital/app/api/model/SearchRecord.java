package com.puree.hospital.app.api.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 搜索记录实体类
 *
 * <AUTHOR>
 * @date 2025/7/16 10:09
 */
@Data
public class SearchRecord {
    
    /** 记录ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /** 医院ID */
    private Long hospitalId;
    
    /** 患者ID */
    private Long patientId;
    
    /** 搜索值 */
    private String searchValue;
    
    /** 创建者 */
    private String createBy;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /** 搜索类型 0-商/药 1-医生 2-擅长 3-科室 4-商品 5-药品 */
    private Integer searchType;
    
    /**
     * 搜索类型集合
     */
    @TableField(exist = false)
    private List<Integer> searchTypeList;
}

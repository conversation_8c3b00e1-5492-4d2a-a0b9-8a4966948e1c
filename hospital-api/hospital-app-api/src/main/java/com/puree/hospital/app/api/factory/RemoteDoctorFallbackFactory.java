package com.puree.hospital.app.api.factory;

import com.puree.hospital.app.api.RemoteDoctorService;
import com.puree.hospital.app.api.model.BusDoctorHospital;
import com.puree.hospital.app.api.model.BusDoctorVo;
import com.puree.hospital.app.api.model.DoctorAndPatientDTO;
import com.puree.hospital.app.api.model.DoctorPatientInfoVo;
import com.puree.hospital.app.api.model.vo.BusSignatureVO;
import com.puree.hospital.common.api.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName RemoteDoctorFallbackFactory
 * <AUTHOR>
 * @Description 医生相关
 * @Date 2024/3/12 11:41
 * @Version 1.0
 */
@Slf4j
@Component
public class RemoteDoctorFallbackFactory implements FallbackFactory<RemoteDoctorService> {
    @Override
    public RemoteDoctorService create(Throwable cause) {
        log.error("app服务调用失败:{}", cause.getMessage());
        return new RemoteDoctorService() {
            @Override
            public R<BusDoctorVo> personalData(Long hospitalId, Long doctorId) {
                return R.fail("接口调用失败!");
            }

            @Override
            public R<List<Long>> idList(Long hospitalId, Long userId) {
                return R.fail("获取医生列表失败!");
            }

            @Override
            public R<List<DoctorPatientInfoVo>> getBelongPatients(DoctorAndPatientDTO dto) {
                return R.fail("获取患者列表失败！");
            }
            @Override
            public R<BusDoctorVo> getById(Long id) {
                return R.fail("获取医生信息失败！");
            }

            @Override
            public R<Boolean> messageNotificationIntervalIsOver(Long hospitalId, Long groupId) {
                return R.fail(true);
            }

            @Override
            public R<BusSignatureVO> getDoctorSignature(Long hospitalId, Long doctorId) {
                return R.fail("获取医院医生签名失败！");
            }

            @Override
            public R<BusDoctorHospital> selectDoctorHospitalInfo(Long hospitalId, Long doctorId) {
                return R.fail("获取医生医院关联信息失败！");
            }
        };
    }
}

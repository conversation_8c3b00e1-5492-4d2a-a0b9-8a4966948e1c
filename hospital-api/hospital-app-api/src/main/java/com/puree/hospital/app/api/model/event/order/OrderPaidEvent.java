package com.puree.hospital.app.api.model.event.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <p>
 * 订单已支付事件
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/28 14:40
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderPaidEvent extends BaseOrderEvent {

    private static final long serialVersionUID = 2812305987385139019L;

    public static final String TOPIC = "queue:order_paid_event_topic";

    public OrderPaidEvent() {
        setEventType(EventType.PAID);
    }
}

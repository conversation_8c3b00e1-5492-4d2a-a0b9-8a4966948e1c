package com.puree.hospital.app.api.factory;

import com.puree.hospital.app.api.RemoteSearchRecordService;
import com.puree.hospital.app.api.model.SearchRecord;
import com.puree.hospital.app.api.model.SearchRecordInsertDTO;
import com.puree.hospital.common.api.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 搜索记录远程服务降级工厂
 *
 * <AUTHOR>
 * @date 2025/7/16 10:09
 */
@Slf4j
@Component
public class RemoteSearchRecordFallbackFactory implements FallbackFactory<RemoteSearchRecordService> {

    @Override
    public RemoteSearchRecordService create(Throwable cause) {
        log.error("搜索记录服务调用失败:{}", cause.getMessage());
        return new RemoteSearchRecordService() {
            @Override
            public R<List<SearchRecord>> searchRecordList(Integer searchType) {
                return R.fail("获取搜索记录列表失败！");
            }

            @Override
            public R<String> deleteSearchRecord(Long id) {
                return R.fail("删除搜索记录失败！");
            }

            @Override
            public R<String> insertSearchRecord(SearchRecordInsertDTO insertDTO) {
                return R.fail("插入搜索记录失败！");
            }
        };
    }
}

package com.puree.hospital.app.api.model;

import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * 医生关联医院表
 */
@Data
public class BusDoctorHospital extends Entity {
    /**
     * 医生id
     */
    private Long doctorId;
    
    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 是否是本院医生 0 多点 1 本院 2特聘专家
     */
    private Integer isThisCourt;

    /**
     * 是否认证
     */
    private Integer isAuthentication;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 医生登录token
     */
    private String doctorToken;

    /**
     * 状态 0停用 1启用
     */
    private Integer status;

    /**
     * 是否热门医生（0否 1是）
     */
    private String hot;
    
    /**
     * app显示顺序
     */
    private Integer orderNum;

    /**
     * 在线状态（0下线 1上线）
     */
    private Integer onlineStatus;

    /**
     * 医生角色  0:医生 1:审方药师 2:护士 4:健康管理师  5:心理咨询师
     */
    private String role;

    /**
     * 是否是医助 0否 1是
     */
    private String assistantFlag;

    /**
     * 审核内容（0西药 1中药 多选逗号分隔）审方药师独有
     */
    private String reviewContent;
}

package com.puree.hospital.app.api;

import com.puree.hospital.app.api.factory.RemoteDoctorFallbackFactory;
import com.puree.hospital.app.api.model.BusDoctorHospital;
import com.puree.hospital.app.api.model.BusDoctorVo;
import com.puree.hospital.app.api.model.DoctorAndPatientDTO;
import com.puree.hospital.app.api.model.DoctorPatientInfoVo;
import com.puree.hospital.app.api.model.vo.BusSignatureVO;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ClassName RemoteDoctorFallbackFactory
 * <AUTHOR>
 * @Description 医生相关
 * @Date 2024/3/12 11:41
 * @Version 1.0
 */
@FeignClient(contextId = "RemoteDoctorService", value = ServiceNameConstants.APP_SERVICE, fallbackFactory = RemoteDoctorFallbackFactory.class)
public interface RemoteDoctorService {
    /**
     * 获取医生信息
     * @return
     */
    @GetMapping("/doctor/personalData")
    R<BusDoctorVo> personalData(@RequestParam("hospitalId") Long hospitalId,
                               @RequestParam("doctorId") Long doctorId);

    /**
     * 获取我关注的医生id列表
     * @return
     */
    @GetMapping("/myAttention/id-list")
    R<List<Long>> idList(@RequestParam("hospitalId") Long hospitalId,
                               @RequestParam("userId") Long userId);


    /**
     * 随访手动添加患者-获取所属医生下的所有患者列表
     *
     * @param dto 医生和患者信息
     * @return 患者列表
     */
    @PostMapping("/doctor/belong/patients")
    R<List<DoctorPatientInfoVo>> getBelongPatients(@RequestBody DoctorAndPatientDTO dto);

    /**
     * 根据医生id，获取医生详情
     *
     * @param id 医生id
     * @return 医生信息详情
     */
    @GetMapping("/doctor/feign/{id}")
    R<BusDoctorVo> getById(@PathVariable("id") Long id);

    /**
     *  判断消息发送时间间隔是否超过限制
     * @param hospitalId    医院Id
     * @param groupId       群组Id
     * @return  是否超过限制
     */
    @GetMapping("/doctor/notification/message-interval")
    R<Boolean> messageNotificationIntervalIsOver(@RequestParam("hospitalId") Long hospitalId, @RequestParam("groupId") Long groupId);

    /**
     *  获取医院医生签名
     * @param hospitalId    医院ID
     * @param doctorId      医生ID
     * @return  医生签名，如果是特聘专家，会返回设置的开方医生签名
     */
    @GetMapping("/doctor/signature")
    R<BusSignatureVO> getDoctorSignature(@RequestParam("hospitalId") Long hospitalId, @RequestParam("doctorId") Long doctorId);

    /**
     * 获取医生医院关联信息
     * @param hospitalId    医院ID
     * @param doctorId      医生ID
     * @return  医生医院关联信息
     */
    @GetMapping("/doctor/feign/doctor-hospital")
    R<BusDoctorHospital> selectDoctorHospitalInfo(@RequestParam("hospitalId") Long hospitalId, @RequestParam("doctorId") Long doctorId);
}

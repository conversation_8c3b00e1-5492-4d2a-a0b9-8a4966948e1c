package com.puree.hospital.app.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 配送返回配送信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/21 11:29
 */
@Data
public class BusFreightResultDTO implements Serializable {

    private static final long serialVersionUID = 95014912507996570L;

    /**
     * 运费
     */
    private BigDecimal freight;
    /**
     * 运费方式
     * */
    private String freightType;
    /**
     * 包邮金额
     */
    private BigDecimal freeShipping;
}

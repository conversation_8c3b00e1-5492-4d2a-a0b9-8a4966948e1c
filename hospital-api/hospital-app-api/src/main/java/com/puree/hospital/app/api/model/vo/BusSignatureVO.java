package com.puree.hospital.app.api.model.vo;

import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 医生签名VO类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/27 14:35
 */
@Data
public class BusSignatureVO extends Entity {
    /**
     * 医生ID/审方药师ID/发货药师ID
     */
    private Long objectId;
    /**
     * 0医生 1审方药师 2护师 3医助 4健康管理师 5心理咨询师 6发货药师 7调剂师
     */
    private String objectType;
    private String certId;
    private String certSignature;
    /**
     * 签名到期时间
     */
    private Date endDate;
}

package com.puree.hospital.app.api.model.event.aftersale;

import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 基础的售后事件
 *
 * <AUTHOR>
 * @date 2025/2/27 14:43
 */
@Data
public class BaseAfterSaleEvent implements Serializable {

    private static final long serialVersionUID = 11223344L;

    /**
     * 总订单编号
     */
    private String totalOrderNo;

    /**
     *  医院ID
     */
    private Long hospitalId;

    /**
     * 事件类型
     */
    private EventType eventType;

    /**
     * 附加参数
     * <p>默认提供了hospitalId & orderNo & current=0字段</p>
     */
    private Map<String, Object> attachment;

    /**
     * 原始状态-避免数据更改，无法找到的订单状态
     */
    private String originalStatus;

    /**
     * 事件时间(默认是当前时间)
     */
    private Date eventTime = new Date();

    @Getter
    public enum EventType {
        /**
         *  用户主动关闭售后
         */
        USER_CLOSE_AFTER_SALE;

    }
}

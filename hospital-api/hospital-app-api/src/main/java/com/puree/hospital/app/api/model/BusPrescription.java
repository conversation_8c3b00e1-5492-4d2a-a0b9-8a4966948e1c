package com.puree.hospital.app.api.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class BusPrescription extends Entity {
    /** 过敏史 */
    private String allergicHistory;
    /** 临床诊断 */
    private String clinicalDiagnosis;
    /** 1:代煎；0：不代煎。 */
    private String decoct;
    /** 发货药师ID */
    private Long deliveryPharmacistId;
    /** 发货药师 */
    private String deliveryPharmacistName;
    /** 科室ID */
    private Long departmentId;
    /** 科室 */
    private String departmentName;
    /** 调配药师id */
    private Long dispensingPharmacistId;
    /** 调配药师 */
    private String dispensingPharmacistName;
    /** 患者ID */
    private Long patientId;
    private Long familyId;
    /** 患者年龄 */
    private String familyAge;
    /** 患者姓名 */
    private String familyName;
    /** 1:男；0：女。 */
    private String familySex;
    /** 患者手机号 */
    private String familyTel;
    /** 患者身份证号码 */
    private String familyIdcard;
    /** 初步诊断 */
    private String preliminaryDiagnosis;
    /** 处方金额 */
    private BigDecimal prescriptionAmount;
    /** 开方医生ID */
    private Long doctorId;
    /** 开方医生 */
    private String doctorName;
    /** 处方编号 */
    private String prescriptionNumber;
    /** 处方实付金额 */
    private BigDecimal prescriptionRealAmount;
    /** 开方时间 */
    private Date prescriptionTime;
    /** 0:中药；1：西药 2协定方。 */
    private String prescriptionType;
    /** 审方药师ID */
    private Long reviewPharmacistId;
    /** 审核药师 */
    private String reviewPharmacistName;
    /** 审核时间 */
    private Date reviewTime;
    /** 医院ID */
    private Long hospitalId;
    /** 医院名称 */
    private String hospitalName;
    /** 医院名称 */
    private String hospitalAddress;
    /** 处方状态 */
    private String status;
    /** 未通过原因 */
    private String notApprovedReason;
    /** 补充说明 */
    private String remark;
    /**药品目录类型0院内 1院外*/
    private String drugDirectoryType;
    /** 问诊订单ID */
    private Long consultationOrderId;
    /** 加工方法 */
    private String processingMethod;
    /** 用法 */
    private String usages;

    /**
     * 协定方名称
     */
    private String paName;

    /**
     * 配送方名称
     */
    private String enterpriseName;
    /**
     * 协定方id
     */
    private Long paId;
    /**
     * 配送方id
     */
    private Long enterpriseId;
    /**
     * 开方类型（0线下 1线上）
     */
    private String type;
    /**
     * 发送到药房或配送企业（0：药房 1：配送企业）
     */
    private String sendToDest;
    /**
     * 机构code
     */
    private String partnersCode;
    /**
     * 文件路径
     */
    private String path;

    /** 服务包id*/
    private Long servicePackId;

    /**
     * 处方标识（0普通开方 1找药开方）
     */
    private String identity;
    /**
     * 预订单ID
     */
    private Long preorderId;
    /**
     * 专家ID
     */
    private Long expertId;
    /**
     * 中药加工费
     */
    private BigDecimal processPrice;
    /**
     * 配送企业中药加工费
     */
    private BigDecimal enterpriseProcessPrice;
    /**
     * 处方药品
     */
    @TableField(exist = false)
    private List<BusPrescriptionDrugs> pdDrugsList;
    /**
     * 打印类型
     */
    @TableField(exist = false)
    private String printType;

    private Date validTime ;

    /**
     * 诊查费
     */
    private BigDecimal examinationFee;

    /**
     * 诊查费项目名称
     */
    private String examinationName;

    /**
     * 费用类别：SELF:自费，MI_GENERAL:医保(普通门诊)，MI_SPECIAL:医保（门特门慢）
     */
    private String feeSettleType;

    /**
     * 门特门慢病种信息，json信息{"opspDiseCode":"M03900","opspDiseName":"高血压(慢病)"}
     */
    private String specialDiseaseInfo;

    /**
     * 医保授权码
     */
    private String miAuthNo;

    /**
     * 患者详细住址
     */
    private String familyDetailAddress;

    /**
     * 处方编号条形码(base64)
     */
    @TableField(exist = false)
    private String barcodeUrl;

    /**
     * 复核状态
     */
    private String dispensingStatus;

    /**
     * 复核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dispensingTime;
}

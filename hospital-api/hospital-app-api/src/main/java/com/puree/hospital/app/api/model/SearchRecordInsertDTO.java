package com.puree.hospital.app.api.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 插入搜索记录请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/16 10:09
 */
@Data
public class SearchRecordInsertDTO {

    /**
     * 搜索关键字
     */
    @NotBlank(message = "搜索关键字不能为空")
    private String searchValue;

    /**
     * 搜索类型：0-商/药 1-医生 2-擅长 3-科室 4-商品 5-药品
     */
    @NotNull(message = "搜索类型不能为空")
    private Integer searchType;
}

package com.puree.hospital.app.api;


import com.puree.hospital.app.api.factory.RemoteSearchRecordFallbackFactory;
import com.puree.hospital.app.api.model.SearchRecord;
import com.puree.hospital.app.api.model.SearchRecordInsertDTO;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 搜索记录远程服务接口
 *
 * <AUTHOR>
 * @date 2025/7/16 10:08
 */
@FeignClient(contextId = "RemoteSearchRecordService", value = ServiceNameConstants.APP_SERVICE, fallbackFactory = RemoteSearchRecordFallbackFactory.class)
public interface RemoteSearchRecordService {

    /**
     * 获取搜索记录列表
     *
     * @param searchType 搜索类型：4-商品，5-药品
     * @return 搜索记录列表
     */
    @GetMapping("/search-record")
    R<List<SearchRecord>> searchRecordList(@RequestParam(value = "searchType", required = false) Integer searchType);

    /**
     * 删除搜索记录
     *
     * @param id 记录ID
     * @return 操作结果
     */
    @DeleteMapping("/search-record/{id}")
    R<String> deleteSearchRecord(@PathVariable("id") Long id);

    /**
     * 插入搜索记录
     *
     * @param insertDTO 插入搜索记录请求参数
     * @return 操作结果
     */
    @PostMapping("/search-record")
    R<String> insertSearchRecord(@RequestBody SearchRecordInsertDTO insertDTO);
}

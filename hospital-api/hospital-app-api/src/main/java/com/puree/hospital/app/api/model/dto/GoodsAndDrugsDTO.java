package com.puree.hospital.app.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/2/8 15:03
 */
@Data
public class GoodsAndDrugsDTO {
    /**
     * 业务类型 0-OTC,  1-商品
     */
    private String type;
    /**
     * 购物车ID
     */
    private Long cartId;
    /**
     * 业务ID
     */
    private Long businessId;
    /**
     * 销售价
     */
    private BigDecimal sellingPrice;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 子订单ID
     */
    private Long orderId;
    /**
     * 业务类型（0药品 1商品）
     */
    private String businessType;
    /**
     * 处方ID
     */
    private Long prescriptionId;

}

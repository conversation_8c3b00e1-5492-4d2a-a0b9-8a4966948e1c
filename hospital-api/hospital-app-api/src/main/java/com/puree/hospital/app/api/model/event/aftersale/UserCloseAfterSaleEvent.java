package com.puree.hospital.app.api.model.event.aftersale;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <p>
 * 用户主动关闭售后事件
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/6 19:07
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UserCloseAfterSaleEvent extends BaseAfterSaleEvent {

    private static final long serialVersionUID = 305987670745009342L;

    /**
     *  主题
     */
    public static final String TOPIC = "queue:user_close_after_sale_event_topic";

    public UserCloseAfterSaleEvent() {
        setEventType(EventType.USER_CLOSE_AFTER_SALE);
    }
}

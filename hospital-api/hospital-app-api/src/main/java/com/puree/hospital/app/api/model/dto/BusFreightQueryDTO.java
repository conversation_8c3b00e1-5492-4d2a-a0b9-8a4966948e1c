package com.puree.hospital.app.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 运费计算请求参数
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/25 21:14
 */
@Data
public class BusFreightQueryDTO implements Serializable {

    private static final long serialVersionUID = -740396686702994663L;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区
     */
    private String areaName;

    /**
     * 金额（不含运费）
     */
    private BigDecimal amount;

    /**
     * 配送方式 0自提 1快递
     */
    private String deliveryType;

    /**
     * 商品/药品信息
     */
    private List<GoodsAndDrugsDTO> goodsAndDrugsList;
}

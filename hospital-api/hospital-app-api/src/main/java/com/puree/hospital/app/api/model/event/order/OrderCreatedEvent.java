package com.puree.hospital.app.api.model.event.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 订单创建事件
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderCreatedEvent extends BaseOrderEvent {

    private static final long serialVersionUID = 11223344L;
    /**
     * 主题
     */
    public static final String TOPIC = "queue:total_order_created_event_topic";

    public OrderCreatedEvent() {
        setEventType(EventType.CREATED);
    }
}

package com.puree.hospital.insurance.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.insurance.api.RemotePreprocessorService;
import com.puree.hospital.insurance.api.model.OutpatientRegisterReqDTO;
import com.puree.hospital.insurance.api.model.OutpatientRegisterResultDTO;
import com.puree.hospital.insurance.api.model.UniformRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 * @date 2023/6/27 18:47
 */
@Slf4j
public class RemotePreprocessorFallbackFactory implements FallbackFactory<RemotePreprocessorService> {

    @Override
    public RemotePreprocessorService create(Throwable cause) {
        log.error("Insurance服务调用失败:{}", cause.getMessage());
        return new RemotePreprocessorService() {
            @Override
            public AjaxResult uniformRequest(UniformRequest req) {
                return AjaxResult.error("调用医保退费接口失败");
            }

            @Override
            public R<OutpatientRegisterResultDTO> outpatientRegister(OutpatientRegisterReqDTO reqDTO) {
                return R.fail("调用医保门诊挂号失败");
            }
        };
    }
}

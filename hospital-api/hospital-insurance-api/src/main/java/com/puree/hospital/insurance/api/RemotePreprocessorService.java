package com.puree.hospital.insurance.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.insurance.api.factory.RemotePaymentFallbackFactory;
import com.puree.hospital.insurance.api.model.OutpatientRegisterReqDTO;
import com.puree.hospital.insurance.api.model.OutpatientRegisterResultDTO;
import com.puree.hospital.insurance.api.model.UniformRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/6/27 18:47
 */
@FeignClient(contextId = "RemotePreprocessorService", value = ServiceNameConstants.INSURANCE,
        fallbackFactory = RemotePaymentFallbackFactory.class)
public interface RemotePreprocessorService {

    /**
     * 统一请求
     *
     * @param req 请求参数
     * @return 统一请求结果
     */
    @PostMapping("/preprocessor/uniformRequest")
    AjaxResult uniformRequest(@RequestBody UniformRequest req);

    /**
     * 门诊挂号
     *
     * @param reqDTO 请求参数
     * @return 门诊挂号结果
     */
    @PostMapping("/preprocessor/outpatient-register")
    R<OutpatientRegisterResultDTO> outpatientRegister(@RequestBody OutpatientRegisterReqDTO reqDTO);
}

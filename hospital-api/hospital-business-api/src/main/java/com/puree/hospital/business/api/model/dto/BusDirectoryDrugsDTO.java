package com.puree.hospital.business.api.model.dto;

import com.puree.hospital.common.api.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class BusDirectoryDrugsDTO extends BaseEntity {

    private static final long serialVersionUID = 5752400914004694305L;

    /** 目录药品ID */
    private List<Long> directoryDrugsIds;
    /** 医院药店ID */
    private Long hospitalPharmacyId;
    /** 配送企业ID */
    private Long enterpriseId;
    /** 目录类型（1院内 2院外） */
    private Integer directoryType;
    /** 药品名称 */
    private String drugsName;
    /** 标准通用名 */
    private String standardCommonName;
    /** 本位码 */
    private String drugsStandardCode;
    /** 国药准字(英文简写) */
    private String nmpn;
    /** 药品分类 */
    private Long classifyId;
    /** 状态 */
    private Integer status;
    /** 药品类型 */
    private Long drugsType;
    /**
     * 处方标识
     */
    private Long prescriptionIdentification;
    private String drugsNumber;
    private String drugsManufacturer;
    private Long hospitalId;

    /** 药品类型  0西药 1中药*/
    private Integer type;

    /** 药品id*/
    private Long drugsId;
    /**
     * 药品 IDS
     */
    private List<Long> drugsIds;

    /** 煎煮方法*/
    private Long decoctingMethod;
    /** 医保类型*/
    private Long medicalInsuranceType;
    /** 中药饮片类型*/
    private Long drugsDosageForm;

    private boolean checkGrounding;

    private int pageSize;
    private int pageNum;

    /**
     * 排除医院ID 列表
     */
    private List<Long> excludeHospitalIds;

    /**
     * 数据过滤
     */
    private StockFilterEnum stockFilter = StockFilterEnum.ALL;


    @Getter
    public enum StockFilterEnum {
        /**
         * 全部数据
         */
        ALL("all", "全部数据"),

        /**
         * 有库存
         */
        HAS_STOCK("has_stock", "药品有库存"),
        /**
         * 无库存
         */
        NO_STOCK("no_stock", "药品无库存");
        /**
         * 值
         */
        private final String value;
        /**
         * 描述
         */
        private final String desc;

        StockFilterEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

}

package com.puree.hospital.business.api.model.enums;

/***
 * 渠道身份类型
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/24
 */
public enum ChannelIdentityTypeEnum {
    CHANNEL, AGENT;

    public static boolean isChannel(String identityType) {
        return CHANNEL.name().equalsIgnoreCase(identityType);
    }

    public static boolean isAgent(String identityType) {
        return AGENT.name().equalsIgnoreCase(identityType);
    }
}

package com.puree.hospital.business.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2023/02/23/17:17
 * @Description:
 */
@Data
public class BusHospitalOrderDTO {
    /**
     * 总订单id
     */
    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 收货人
     */
    private String receivingUser;
    /**
     * 收货号码
     */
    private String receivingTel;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区/县
     */
    private String area;
    /**
     * 详细地址
     */
    private String detailedAddress;

    /**
     * 运费计算
     */
    private FreightDTO freightList;
    /**
     * 订单状态
     */
    private String status;
    /**
     * 取消理由
     */
    private String  cancelResult;

    /**
     * 物流公司
     */
    private String  logisticsCompany;
    /**
     * 运单号
     */
    private String  deliveryNo;

    /**
     * 药品商品ID
     */
    private Long drugsGoodsId;


    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 物流公司代码
     */
    private String expressCode;

    /**
     * 运费
     */
    private BigDecimal freight;
    /**
     * 是否换货单（0否 1是）
     */
    private Integer barterOrNot;
    /**
     * 配送企业id
     */
    private Long enterpriseId;
    /**
     * 配送企业名
     */
    private String enterpriseName;
    /**
     * 交易订单金额
     */
    private BigDecimal totalPrice;

    /**
     * 药品溯源码信息
     */
    private List<BusDrugTraceCodeDTO> drugTraceCodeList;
}

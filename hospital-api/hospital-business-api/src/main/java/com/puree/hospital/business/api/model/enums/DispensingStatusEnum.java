package com.puree.hospital.business.api.model.enums;

import lombok.Getter;

/**
 * 处方复核状态
 *
 * <AUTHOR>
 * @date 2025/8/1 14:44:36
 */
@Getter
public enum DispensingStatusEnum {

    DISPENSING_PENDING("0", "待审核"),
    // "3" 审核未通过（作废）
    DISPENSING_NOT_PASS("3", "审核未通过"),
    DISPENSING_PASS("2", "审核通过");

    private final String code;
    private final String name;

    DispensingStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean isPass(String code) {
        return DISPENSING_PASS.code.equals(code);
    }

    public static boolean isNotPass(String code) {
        return DISPENSING_NOT_PASS.code.equals(code);
    }

}

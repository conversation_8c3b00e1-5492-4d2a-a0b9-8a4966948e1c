package com.puree.hospital.business.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName CancelDTO
 * <AUTHOR>
 * @Description 第三方发货DTO
 * @Date 2024/11/18 15:13
 * @Version 1.0
 */
@Data
public class ThirdPartyDeliverDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处方号
     */
    @NotBlank(message = "处方号不能为空")
    private String prescriptionNo;
    /**
     * 处方ID
     */
    private Long prescriptionId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     *  药品ID
     */
    private Long drugsId;
    /**
     * 快递单号
     */
    @NotBlank(message = "快递单号 不能为空")
    private String expressNo;

    /**
     * 药品溯源码信息
     */
    private List<BusDrugTraceCodeDTO> drugTraceCodeList;
}

package com.puree.hospital.business.api.event;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 注册患者事件
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/2 19:22
 */
@Slf4j
@Data
public class RegisterPatientEvent implements Serializable {

    private static final long serialVersionUID = 2386363631667737115L;

    public static final String TOPIC = "queue:register-patient-event";

    /**
     * 群组id
     */
    private String phoneNumber;

    /**
     * 事件类型
     */
    private List<EventType> eventType;
    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 事件类型枚举
     */
    @Getter
    public enum EventType {
        /**
         * 注册患者更新合作渠道信息
         */
        UPDATE_CHANNEL
    }
}

package com.puree.hospital.common.datasource.monitor;

import com.baomidou.dynamic.datasource.support.DbHealthIndicator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * <p>
 * 药品检测监控
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/20 12:16
 */
@Slf4j
@AllArgsConstructor
public class DataSourceHealthMonitor {

    private final DbHealthIndicator dbHealthIndicator;

    /**
     * 定时检查数据库连接
     */
    @Scheduled(fixedRate = 60000)
    public void scheduledHealthCheck() {
        Health health = dbHealthIndicator.health();
        log.debug("DB Health: {}", health);
    }

}

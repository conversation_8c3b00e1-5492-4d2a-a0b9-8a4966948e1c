package com.puree.hospital.common.datasource.config;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.dynamic.datasource.support.DbHealthIndicator;
import com.baomidou.dynamic.datasource.support.HealthCheckAdapter;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;

import javax.sql.DataSource;

/**
 * <p>
 * 检测检测
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/20 12:04
 */
@ConditionalOnClass(AbstractHealthIndicator.class)
public class DataSourceHealthIndicatorConfiguration {

    @Bean("dynamicDataSourceHealthCheck")
    public DbHealthIndicator healthIndicator(DataSource dataSource,
                                             DynamicDataSourceProperties dynamicDataSourceProperties,
                                             HealthCheckAdapter healthCheckAdapter) {
        return new DbHealthIndicator(dataSource, dynamicDataSourceProperties.getHealthValidQuery(), healthCheckAdapter);
    }
}

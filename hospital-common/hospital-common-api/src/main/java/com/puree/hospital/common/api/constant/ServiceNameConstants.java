package com.puree.hospital.common.api.constant;

/**
 * 服务名称
 * 
 * <AUTHOR>
public class ServiceNameConstants
{
    /**
     * 认证服务的serviceid
     */
    public static final String AUTH_SERVICE = "hospital-auth";

    /**
     * 系统模块的serviceid
     */
    public static final String SYSTEM_SERVICE = "hospital-system";

    /**
     * 业务模块的serviceid
     */
    public static final String BUSINESS_SERVICE = "hospital-business";

    /**
     * 运营模块的serviceid
     */
    public static final String OPERATE_SERVICE = "hospital-operate";

    /**
     * IM模块的serviceid
     */
    public static final String IM_SERVICE = "hospital-im";

    /**
     * tool模块的serviceid
     */
    public static final String TOOL_SERVICE = "hospital-tool";

    /**
     * supplier模块的serviceid
     */
    public static final String SUPPLIER_SERVICE = "hospital-supplier";

    /**
     * app模块的serviceid
     */
    public static final String APP_SERVICE = "hospital-app";

    /**
     * five模块的serviceid
     */
    public static final String FIVE_SERVICE = "hospital-five";
    /**
     * order订单模块的serviceid
     */
    public static final String ORDER_SERVICE = "hospital-order";
    /**
     * pay支付模块的serviceid
     */
    public static final String PAY = "hospital-pay";
    /**
     * ehr健康档案模块的serviceid
     */
    public static final String EHR = "hospital-ehr";
    /**
    * suppliermanger众爱云仓的serviceid
    */
    public static final String SUPPLIERMANGER="hospital-suppliermanger";
    /**
     * 商品模块的serviceid
     */
    public static final String SHOP = "hospital-shop";
    /**
     * 医保模块的serviceid
     */
    public static final String INSURANCE = "hospital-insurance";
    /**
     * 新患教的 serviceId
     */
    public static final String TUTORIAL_V2 = "tutorial-v2";

    /**
     * 随访的 serviceId
     */
    public static final String FOLLOW_UP = "follow-up";
}
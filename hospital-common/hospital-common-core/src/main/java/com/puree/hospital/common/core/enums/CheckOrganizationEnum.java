package com.puree.hospital.common.core.enums;

/**
 * @ClassName: CheckOrganizationEnum
 * @Date 2022/12/16 16:31
 * <AUTHOR>
 * @Description: 检测机构
 * @Version 1.0
 */
public enum CheckOrganizationEnum {
    XK("0", "旗舰版A"),
    H<PERSON>("1", "绘云"),
    SONKA("2", "双佳"),
    EHOME("3", "标准版A"),
    TDFT("4", "脑心健"),
    JHY("5", "九合一");

    private final String code;
    private final String info;

    CheckOrganizationEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    /**
     * @Param code
     * @Return CheckOrganizationEnum
     * @Description 获取枚举
     * <AUTHOR>
     * @Date 2024/9/5 15:25
     **/
    public static CheckOrganizationEnum getByCode(String code) {
        for (CheckOrganizationEnum item : CheckOrganizationEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}

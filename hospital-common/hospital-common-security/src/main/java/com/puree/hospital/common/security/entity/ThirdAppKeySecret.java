package com.puree.hospital.common.security.entity;

import cn.hutool.core.codec.Base58;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.nio.Buffer;
import java.nio.ByteBuffer;

/**
 * 第三方 APP KEY 和 应用密钥
 */
@Slf4j
@Getter
public class ThirdAppKeySecret {
    // 类级别常量定义
    private static final int BYTE_OFFSET = 2;
    private static final int SEGMENT_LENGTH = 6; // LONG_BYTES - BYTE_OFFSET
    private static final String ERROR_HOSPITAL_ID = "医院ID不能为空或小于等于0";
    private static final String ERROR_KEY_GENERATION = "生成应用密钥失败";


    /**
     * key
     */
    private final String appKey;
    /**
     * 密钥
     */
    private String appSecret;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 时间戳
     */
    private long timestamp;

    /**
     * 初始化构造函数
     * @param hospitalId 医院ID
     */
    public ThirdAppKeySecret(Long hospitalId){
        this.appKey = generateAppKey(hospitalId);
        this.appSecret = generateAppSecret();
    }

    public ThirdAppKeySecret(String appKey){
        this.appKey = appKey;
        byte[] decode = Base58.decode(appKey);
        this.hospitalId = extractHospitalId(decode);
        this.timestamp = extractTimestamp(decode);
    }

    /**
     * 生成应用密钥，使用UUID和当前时间戳
     * @return Base58编码的密钥
     */
    public static String generateAppSecret() {
        UUID uuid = UUID.randomUUID();
        long timestamp = System.currentTimeMillis();

        ByteBuffer buffer = ByteBuffer.allocate(16 + 8);
        buffer.putLong(uuid.getMostSignificantBits());
        buffer.putLong(uuid.getLeastSignificantBits());
        buffer.putLong(timestamp);

        return Base58.encode(buffer.array());
    }


    /**
     * 生成医院应用密钥
     * @param hospitalId 医院ID
     * @return 应用密钥
     * @throws IllegalArgumentException 当医院ID无效时
     */
    public static String generateAppKey(Long hospitalId) {
        //输入验证
        validateHospitalId(hospitalId);

        try {
            byte[] timestampBytes = generateTimestampBytes();
            byte[] hospitalBytes = generateHospitalBytes(hospitalId);

            // 合并和处理字节数组
            return Base58.encode(combineBytes(
                    reverseBytes(randomizeTimestamp(timestampBytes)),
                    hospitalBytes
            ));
        } catch (Exception e) {
            log.error(ERROR_KEY_GENERATION, e);
            throw new ServiceException(ERROR_KEY_GENERATION);
        }
    }

    /**
     * 合并字节数组
     * @param first 第一个字节数组
     * @param second 第二个字节数组
     * @return 合并后的字节数组
     */
    private static byte[] combineBytes(byte[] first, byte[] second) {
        byte[] result = new byte[first.length + second.length];
        System.arraycopy(first, 0, result, 0, first.length);
        System.arraycopy(second, 0, result, first.length, second.length);
        return result;
    }

    /**
     * 验证医院ID
     * @param hospitalId 医院ID
     */
    private static void validateHospitalId(Long hospitalId) {
        if (hospitalId == null || hospitalId <= 0) {
            throw new IllegalArgumentException(ERROR_HOSPITAL_ID);
        }
    }

    /**
     * 时间戳字节数组
     * @return 时间戳字节数组
     */
    private static byte[] generateTimestampBytes() {
        byte[] result = new byte[SEGMENT_LENGTH];
        byte[] buffer = ByteBuffer.allocate(8)
                .putLong(System.currentTimeMillis())
                .array();
        System.arraycopy(buffer, BYTE_OFFSET, result, 0, SEGMENT_LENGTH);
        return result;
    }

    /**
     * 获取医院ID字节数组
     * @param hospitalId 医院ID
     * @return 医院ID字节数组
     */
    private static byte[] generateHospitalBytes(Long hospitalId) {
        byte[] result = new byte[SEGMENT_LENGTH];
        byte[] buffer = ByteBuffer.allocate(8)
                .putLong(hospitalId)
                .array();
        System.arraycopy(buffer, BYTE_OFFSET, result, 0, SEGMENT_LENGTH);
        return result;
    }

    /**
     * 随机化时间戳
     * @param timestamp 时间戳
     * @return 时间戳
     */
    private static byte[] randomizeTimestamp(byte[] timestamp) {
        return randomTimestamp(timestamp, 0);
    }

    /**
     * 随机时间戳
     * @param modifiedTimestampPart 修改后的时间戳部分
     * @param offset 修改的起始位置
     * @return 时间戳 byte 数组
     */
    public static byte[] randomTimestamp(byte[] modifiedTimestampPart,int offset) {
        int randomBits = RandomUtil.randomInt(0,64);
        byte firstByte = modifiedTimestampPart[offset];
        firstByte = (byte) ((firstByte & 0x03) | (randomBits << 2));
        modifiedTimestampPart[offset] = firstByte;
        return modifiedTimestampPart;
    }

    /**
     * 从应用密钥中提取 时间戳
     * @param decoded 解码后的字节数组
     * @return 时间戳
     */
    public Long extractTimestamp(byte[] decoded) {
        try {
            // 验证解码后的字节数组长度
            if (decoded.length < 11) {  // 6 + 5
                throw new IllegalArgumentException("无效的appKey格式");
            }

            decoded = restoreTimestamp(decoded,5);
            return getByteBufferByBytes(decoded, 0,true).getLong();
        } catch (IllegalArgumentException e) {
            log.error("appKey解析失败: {}", decoded, e);
            throw e;
        }
    }

    /**
     * 还原时间戳
     * @param modifiedTimestampPart 修改后的时间戳
     * @return 还原的时间戳
     */
    public byte[] restoreTimestamp(byte[] modifiedTimestampPart,int offset) {
        byte[] restored = modifiedTimestampPart.clone();
        restored[offset] = (byte) (restored[offset] & 0x03);
        return restored;
    }

    /**
     * 从应用密钥中提取医院ID
     * @param decoded 解码后的字节数组
     * @return 医院ID
     */
    public Long extractHospitalId(byte[] decoded) {
        try {
            // 验证解码后的字节数组长度
            if (decoded.length < 11) {  // 6 + 5
                throw new IllegalArgumentException("无效的appKey格式");
            }
            return getByteBufferByBytes(decoded,6,false).getLong();
        } catch (IllegalArgumentException e) {
            log.error("appKey解析失败: {}", decoded, e);
            throw e;
        }
    }

    /**
     * 获取 ByteBuffer
     * @param decoded 解码后的字节数组
     * @param offset 开始位置
     * @param reverse 是否到排序
     * @return ByteBuffer
     */
    public ByteBuffer getByteBufferByBytes(byte[] decoded, int offset, boolean reverse){
        // 创建固定大小的字节缓冲区
        ByteBuffer buffer = ByteBuffer.allocate(8);
        // 填充前导零字节
        buffer.putShort((short) 0);
        byte[] result = new byte[6];
        System.arraycopy(decoded, offset, result, 0, 6);

        if (reverse){
            reverseBytes(result);
        }
        // 复制后6个字节到缓冲区
        buffer.put(result, 0, 6);
        // 重置缓冲区位置以准备读取
        ((Buffer)buffer).flip();
        return buffer;
    }

    /**
     * 倒排
     *
     * @param array 字节数组
     * @return 倒排后的数组
     */
    private static byte[] reverseBytes(byte[] array) {
        int left = 0;
        int right = array.length - 1;
        while (left < right) {
            byte temp = array[left];
            array[left++] = array[right];
            array[right--] = temp;
        }
        return array;
    }


}

package com.puree.hospital.common.security.aspect;

import com.puree.hospital.common.core.enums.CallbackResultEnum;
import com.puree.hospital.common.core.utils.SHA256Util;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.SystemClockUtil;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.common.security.annotation.SignatureVerify;
import com.puree.hospital.common.security.service.SignatureKeyService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/10/10 16:42
 */
@Slf4j
@Aspect
@Component
@Deprecated
public class SignatureVerifyAspect {
    @Resource
    private RedisService redisService;
    @Resource
    private SignatureKeyService openApiAuth;

    /**
     * 请求与服务器时间误差
     */
    public final static long TIME_INTERVAL_SECONDS = 60L;
    /**
     * 重复请求限制时间
     */
    public final static long REPEAT_LIMIT_SECONDS = 10L;
    /**
     * 重复请求redis前缀
     */
    public final static String REPEAT_LIMIT_PREFIX = "open_api:repeat:limit:";

    @Pointcut("@annotation(com.puree.hospital.common.security.annotation.SignatureVerify)")
    private void pointCut() {

    }

    /**
     * 针对v1签名校验
     *
     * @param joinPoint 切入点
     * @return 方法执行返回
     * @throws Throwable 方法执行异常信息
     */
    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        // 获取注解的值
        SignatureVerify apiAuth = method.getAnnotation(SignatureVerify.class);
        //如果是v2版本，直接返回
        if (null == apiAuth || SignatureVerify.SignVersionEnum.V2.equals(apiAuth.signVersion())) {
            return joinPoint.proceed();
        }
        long currentTime = SystemClockUtil.currentTimeMillis();
        String timestamp = "";
        String appid = "";
        String sign = "";
        String params = "";

        // 遍历方法参数查找HttpServletRequest对象

       Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg instanceof HttpServletRequest) {
                HttpServletRequest request = (HttpServletRequest) arg;
                appid = request.getHeader("X-App-Id");
                timestamp = request.getHeader("X-Timestamp");
                sign = request.getHeader("X-Payload-Signature");
            } else {
                if (SignatureVerify.SignParamEnum.REQUEST_BODY.equals(apiAuth.signParam())) {
                    params = (String) arg;
                }else if(SignatureVerify.SignParamEnum.NO_PARAMETERS.equals(apiAuth.signParam())){
                    params = "";
                } else {
                    //异常返回
                    return exceptionReturn(method
                            ,CallbackResultEnum.SIGN_PARAM_ERROR.getCode()
                            ,CallbackResultEnum.SIGN_PARAM_ERROR.getInfo());
                }
            }
        }

        // 参数校验
        if (StringUtils.isEmpty(appid) || StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(sign)) {
            //异常返回
            return exceptionReturn(method
                    ,CallbackResultEnum.VALIDATE_PARAMS_ERROR.getCode()
                    ,CallbackResultEnum.VALIDATE_PARAMS_ERROR.getInfo());
        }

        // 时间戳误差60s
        if (Math.abs((currentTime - Long.parseLong(timestamp)) / 1000) > TIME_INTERVAL_SECONDS) {
            //异常返回
            return exceptionReturn(method
                    ,CallbackResultEnum.TIMESTAMP_INVALID.getCode()
                    ,CallbackResultEnum.TIMESTAMP_INVALID.getInfo());
        }

        // 重复请求限制
        boolean exists = redisService.hasKey(REPEAT_LIMIT_PREFIX + sign);
        if (exists) {
            //异常返回
            return exceptionReturn(method
                    ,CallbackResultEnum.REPEAT_REQUEST.getCode()
                    ,CallbackResultEnum.REPEAT_REQUEST.getInfo());
        }
        try {
            String appSecret = openApiAuth.getSecret(appid);
            if (StringUtils.isEmpty(appSecret)) {
                //异常返回
                return exceptionReturn(method
                        ,CallbackResultEnum.NO_EXIT_INSTITUTION.getCode()
                        ,CallbackResultEnum.NO_EXIT_INSTITUTION.getInfo());
            }

            // 验证签名
            String plain = appid + appSecret + timestamp + params;

            if (SignatureVerify.SignAlgorithmEnum.SHA256.equals(apiAuth.algorithm())) {
                boolean verified = SHA256Util.verified(plain, sign);
                if (!verified) {
                    //异常返回
                    return exceptionReturn(method
                            ,CallbackResultEnum.SIGN_NOT_PASSED.getCode()
                            ,CallbackResultEnum.SIGN_NOT_PASSED.getInfo());
                }
            } else {
                //异常返回
                return exceptionReturn(method
                        ,CallbackResultEnum.SIGNATURE_ALGORITHM_ERROR.getCode()
                        ,CallbackResultEnum.SIGNATURE_ALGORITHM_ERROR.getInfo());
            }

            // 将时间戳存进redis
            redisService.setCacheObject(REPEAT_LIMIT_PREFIX + sign, appid, REPEAT_LIMIT_SECONDS, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("SignatureVerifyAspect open api验签异常", e);
            //异常返回
            return exceptionReturn(method
                    ,CallbackResultEnum.SIGN_NOT_PASSED.getCode()
                    ,CallbackResultEnum.SIGN_NOT_PASSED.getInfo());
        }
        return joinPoint.proceed();
    }

    /**
     * 异常返回
     *
      * @param method 请求方法
     * @param code    异常编码
     * @param info    异常信息
     * @return object
     * @throws InstantiationException 类初始化异常
     * @throws IllegalAccessException 类访问异常
     */
    private Object exceptionReturn(Method method, Integer code, String info) throws InstantiationException, IllegalAccessException {
        Class<?> returnType = method.getReturnType();
        Object o = returnType.newInstance();
        if (o instanceof com.puree.hospital.common.api.domain.AjaxResult) {
            return com.puree.hospital.common.api.domain.AjaxResult
                    .error(info);
        }else {
            return AjaxResult.error(code, null, info);
        }
    }

}


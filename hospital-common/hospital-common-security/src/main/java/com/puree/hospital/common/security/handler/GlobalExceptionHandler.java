package com.puree.hospital.common.security.handler;

import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.core.exception.CheckedException;
import com.puree.hospital.common.core.exception.DemoModeException;
import com.puree.hospital.common.core.exception.InnerAuthException;
import com.puree.hospital.common.core.exception.PreAuthorizeException;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.security.token.TokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
@RestControllerAdvice
public class GlobalExceptionHandler
{
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @Autowired
    private TokenService tokenService;
    /**
     * 权限异常
     */
    @ExceptionHandler(PreAuthorizeException.class)
    public AjaxResult handlePreAuthorizeException(PreAuthorizeException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限校验失败'{}'", requestURI, e);
        tokenService.deleteToken();
        return AjaxResult.error(HttpStatus.FORBIDDEN, null,"没有权限，请联系管理员授权");
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public AjaxResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
            HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestURI, e);
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e, HttpServletRequest request)
    {
        log.error("业务异常:"+e.getMessage(), e);
        Integer code = e.getCode();
        return StringUtils.isNotNull(code) ? AjaxResult.error(code, null, e.getMessage()) : AjaxResult.error(e.getMessage());
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生未知异常.", requestURI, e);
        return AjaxResult.error("系统繁忙，请稍后再试！"+e.getMessage());
    }
    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(CheckedException.class)
    public AjaxResult handleRuntimeException(CheckedException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}',发生参数检查异常.", requestURI, e);
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e);
        return AjaxResult.error("系统繁忙，请稍后再试！"+e.getMessage());
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e)
    {
       // log.error("自定义验证异常:{}"+e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return AjaxResult.error(message);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e)
    {
        FieldError fieldError = e.getFieldError();
        String field = fieldError.getField();
        String defaultMessage = fieldError.getDefaultMessage();
        String format = StringUtils.format("字段：{}，{}", field, defaultMessage);
        return AjaxResult.error(format);
    }

    /**
     * 内部认证异常
     */
    @ExceptionHandler(InnerAuthException.class)
    public AjaxResult handleInnerAuthException(InnerAuthException e)
    {
        log.error("内部认证异常", e);
        return AjaxResult.error("系统繁忙，请稍后再试！"+e.getMessage());
    }

    /**
     * 演示模式异常
     */
    @ExceptionHandler(DemoModeException.class)
    public AjaxResult handleDemoModeException(DemoModeException e)
    {
        return AjaxResult.error("演示模式，不允许操作");
    }


    /**
     * 参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public AjaxResult handleException(IllegalArgumentException e, HttpServletRequest request)
    {
        log.error("请求地址：{}，请求参数校验异常：", request.getRequestURI(), e);
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 唯一索引异常捕获
     * @param e 唯一索引异常
     * @return 错误信息
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public AjaxResult handleDuplicateKeyException(DuplicateKeyException e) {
        log.error(e.getMessage(), e);
        return AjaxResult.error( "当前数据已存在,请勿重复操作");
    }


//    /**
//     * 数据校验全局处理
//     * BindException是@Valid使用校验失败时产生的异常
//     */
//    @ExceptionHandler(BindException.class)
//    public AjaxResult BindExceptionHandler(BindException e)
//    {
//        //获取实体类定义的校验注解字段上的message作为异常信息，@NotEmpty(message = "售后状态不能为空！")异常信息即为"售后状态不能为空！"
//        return AjaxResult.error(e.getBindingResult().getFieldError().getDefaultMessage());
//    }
}

package com.puree.hospital.common.logback.listener;

import com.puree.hospital.common.logback.nacos.NacosLogConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/***
 * 监听日志级别配置变化
 */
@Component
@RefreshScope
@Slf4j
public class PureeLogListener {

    @Autowired
    private NacosLogConfig nacosLogConfig;

    /**
     * 监听配置中心的配置变化
     * @param event - 配置变更事件
     */
    @EventListener
    public void handleEnvironmentChange(EnvironmentChangeEvent event) {
        if (event.getKeys().stream().anyMatch(key -> key.startsWith("log.filter"))) {
            // 重载日志文件-日志级别配置
            nacosLogConfig.setLogLevel();
        }
        if (event.getKeys().stream().anyMatch(key -> key.startsWith("log.config"))) {
            // 重载日志文件-日志级别配置
            nacosLogConfig.readNacosLog();
        }
    }
}
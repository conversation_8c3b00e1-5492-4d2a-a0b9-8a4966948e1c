package com.puree.hospital.common.logback.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "log.config")
@RefreshScope
public class PureeLogConfigProperties {
    /**
    * 日志的配置文件名
    */
    private String file;
    /**
     * 对应的日志文件在nacos的group-默认是log
     */
    private String group = "log";
}

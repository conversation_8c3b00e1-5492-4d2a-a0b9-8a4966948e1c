package com.puree.hospital.common.logback.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "log")
@RefreshScope
public class PureeLogFilterProperties {
    /**
     * 日志的配置包名和对应的日志级别
     */
    private Map<String, String> filter;
}

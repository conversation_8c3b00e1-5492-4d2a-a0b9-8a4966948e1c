package com.puree.hospital.common.logback.nacos;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.joran.JoranConfigurator;
import ch.qos.logback.core.joran.spi.JoranException;
import ch.qos.logback.core.util.StatusPrinter;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.puree.hospital.common.logback.properties.PureeLogConfigProperties;
import com.puree.hospital.common.logback.properties.PureeLogFilterProperties;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.Executor;

/***
 * @Author: liuwangda
 * @Date: 2024/9/2 12:00
 */

@Slf4j
@Configuration
public class NacosLogConfig {
    @Autowired
    @Lazy
    private PureeLogConfigProperties pureeLogConfig;
    @Autowired
    @Lazy
    private Environment environment;
    @Autowired
    @Lazy
    private PureeLogFilterProperties pureeLogFilterProperties;
    private final Map<String,PureeLogListener> listenerKeys = new HashMap<>();
    private String lastContent = "";
    @PostConstruct
    public void init() {
        readNacosLog();
    }

    public String readNacosLog() {
        if (pureeLogConfig == null || pureeLogConfig.getFile() == null ||
                pureeLogConfig.getGroup() == null || environment == null) {
            log.error("Nacos log config is null");
            return null;
        }

        // 读取spring本地环境配置
        Properties properties = new Properties();
        properties.setProperty("serverAddr", environment.getProperty("spring.cloud.nacos.config.server-addr"));
        properties.setProperty("namespace", environment.getProperty("spring.cloud.nacos.config.namespace"));
        properties.setProperty("username", environment.getProperty("spring.cloud.nacos.config.username"));
        properties.setProperty("password", environment.getProperty("spring.cloud.nacos.config.password"));
        String content = null; 

        try {
            // 创建ConfigService实例
            ConfigService configService = NacosFactory.createConfigService(properties);

            // 定义配置ID与分组
            String dataId = pureeLogConfig.getFile();
            String group = pureeLogConfig.getGroup();

            
            // 读取配置// 超时时间设为5秒
            content = configService.getConfig(dataId, group, 5000L);
            // 输出配置内容
            setLoggerContext(content);
            // 设置日志级别
            setLogLevel();
            // 装载日志配置成功了之后-添加监听机制
            lastContent = content;
            setListener(dataId, group, configService);
        } catch (NacosException | JoranException e) {
            log.error("Failed to read Nacos log：{}", e.getMessage());
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("Nacos log config error", e);
            throw new RuntimeException(e);
        }


        return content;
    }

    /**
     * 设置监听器
     * @param dataId - 配置ID
     * @param group - 分组
     * @param configService - ConfigService实例Naocs配置
     * @throws NacosException - Nacos异常 - 用于处理监听器的异常
     */
    private void setListener(String dataId, String group, ConfigService configService) throws NacosException {
        String listenerKey = dataId + ":" + group;
        if (listenerKeys.containsKey(listenerKey)) {
            return;
        }
        if (!listenerKeys.isEmpty()) {
            listenerKeys.forEach((key, value) -> {
                String[] split = key.split(":");
                configService.removeListener(split[0], split[1], value);
            });
        }
        // 移除监听器后需要清空
        listenerKeys.clear();
        PureeLogListener pureeLogListener = new PureeLogListener();
        listenerKeys.put(listenerKey, pureeLogListener);
        configService.addListener(dataId, group, pureeLogListener);
    }

    /**
     * 设置自定义的日志级别
     */
    public void setLogLevel() {
        if (pureeLogFilterProperties == null || pureeLogFilterProperties.getFilter() == null ||
                pureeLogFilterProperties.getFilter().isEmpty()) {
            return;
        }
        // 本地测试
        log.info("Log filter configuration changed: {}" , pureeLogFilterProperties.getFilter());
        pureeLogFilterProperties.getFilter().forEach((key, value) -> {
            ((LoggerContext) LoggerFactory.getILoggerFactory()).getLogger(key)
                    .setLevel(Level.toLevel(value,Level.INFO));
        });
    }

    /**
     * 重新装载日志配置文件
     * @param content - 日志配置文件内容
     * @throws JoranException - Joran异常 - 用于处理日志配置文件的异常（包括读取的异常）
     */
    private void setLoggerContext(String content) throws JoranException {
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        JoranConfigurator configurator = new JoranConfigurator();
        configurator.setContext(context);
        // 重新设置日志，清空之前的配置
        context.reset();
        configurator.doConfigure(new ByteArrayInputStream(content.getBytes()));
        StatusPrinter.printInCaseOfErrorsOrWarnings(context);
    }

    class PureeLogListener implements Listener {
        @Override
        public Executor getExecutor() {
            return null;
        }

        @Override
        public void receiveConfigInfo(String configInfo) {
            try {
                // 拿到环境配置
                setLoggerContext(configInfo);
                // 设置自定义级别
                setLogLevel();
            } catch (JoranException e) {
                // 装载上次的日志配置文件
                try {
                    log.error("设置日志环境配置失败", e);
                    // 重新设置日志-为上次的日志配置
                    setLoggerContext(lastContent);
                    setLogLevel();
                } catch (JoranException ex) {
                    throw new RuntimeException(ex);
                }
                throw new RuntimeException(e);
            }

        }
    }
}

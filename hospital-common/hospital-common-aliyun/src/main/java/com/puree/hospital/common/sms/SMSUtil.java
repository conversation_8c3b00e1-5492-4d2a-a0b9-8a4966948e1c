package com.puree.hospital.common.sms;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.AddShortUrlRequest;
import com.aliyun.dysmsapi20170525.models.AddShortUrlResponse;
import com.aliyun.dysmsapi20170525.models.QueryShortUrlRequest;
import com.aliyun.dysmsapi20170525.models.QueryShortUrlResponse;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.puree.hospital.common.core.constant.TemplateMsgConstants;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.sms.model.ConsultationNoReplyParam;
import com.puree.hospital.common.sms.model.TurnChainParam;
import com.puree.hospital.common.sms.model.VerifyCodeSMSParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
@Deprecated
public class SMSUtil {
    @Resource
    private SMSProperties smsProperties;

    private Client createClient() throws Exception {
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(smsProperties.getAccessKeyId())
                // 您的AccessKey Secret
                .setAccessKeySecret(smsProperties.getAccessKeySecret())
                // 访问的域名
                .setEndpoint(smsProperties.getEndpoint());
        return new Client(config);
    }

    private final static String  SM_CD = "该账号验证码获取冷却中，请一分钟后再试";
    private final static String  SM_Error = "触发分钟级流控Permits:1";

    private boolean sendSMS(SendSmsRequest sendSmsRequest) throws Exception {
        System.out.println("SendSmsRequest：" + JSON.toJSONString(sendSmsRequest));
        Client client = createClient();
        Validator.validateMobile(sendSmsRequest.getPhoneNumbers(),"请输入正确的手机号码");
        SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
        System.out.println("sendSmsResponse：" + JSON.toJSONString(sendSmsResponse));
        if (sendSmsResponse.getBody().getCode().equals("OK")) {
            return true;
        }
        String message = sendSmsResponse.getBody().getMessage();
        if (StrUtil.equals(message,SM_Error)){
            throw new Exception(SM_CD);
        }
        throw new Exception(message);
    }

    /**
     * 发送验证码短信
     *
     * @param param
     * @return
     */
    public boolean sendVerifyCodeSMS(VerifyCodeSMSParam param) throws Exception{
        Map<String,String> map = new ConcurrentHashMap<>();
        map.put("code",param.getCode());
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(param.getPhoneNumbers())
                .setSignName(param.getHospitalSign())
                .setTemplateCode(smsProperties.getVerifyTemplateCode())
                .setTemplateParam(JSON.toJSONString(map));
        return sendSMS(sendSmsRequest);
    }

    /**
     * 发送通知短信
     *
     * @param param
     * @return
     */
    public boolean sendConsultationNoReplySMS(ConsultationNoReplyParam param) throws Exception{
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(param.getPhoneNumbers())
                .setSignName(smsProperties.getSignName())
                .setTemplateCode(smsProperties.getConsultationNoReplyTemplateCode())
                .setTemplateParam(JSON.toJSONString(param));
        return sendSMS(sendSmsRequest);
    }

    /**
     * 转短链接
     * @param turnChainParam
     * @return
     * @throws Exception
     */
    public String turnChain(TurnChainParam turnChainParam) throws Exception {
        Client client = createClient();
        AddShortUrlRequest addShortUrlRequest = new AddShortUrlRequest()
                .setSourceUrl(turnChainParam.getSourceUrl())
                .setShortUrlName(turnChainParam.getShortUrlName())
                .setEffectiveDays(turnChainParam.getEffectiveDays());
        AddShortUrlResponse addShortUrlResponse = client.addShortUrl(addShortUrlRequest);
        if("OK".equals(addShortUrlResponse.getBody().getCode())){
            String shortUrl = addShortUrlResponse.getBody().getData().getShortUrl();
            return shortUrl;
        }else{
            throw new ServiceException("转链失败");
        }
    }

    /**
     * 校验短链是否生成
     * @param shortUrl
     * @return
     * @throws Exception
     */
    public boolean checkChain(String shortUrl) throws Exception {
        Client client = createClient();
        QueryShortUrlRequest queryShortUrlRequest = new QueryShortUrlRequest()
                .setShortUrl(shortUrl);
        // 复制代码运行请自行打印 API 的返回值
        QueryShortUrlResponse queryShortUrlResponse = client.queryShortUrl(queryShortUrlRequest);
        if("OK".equals(queryShortUrlResponse.getBody().getCode())){
            String shortUrlStatus = queryShortUrlResponse.getBody().getData().getShortUrlStatus();
            if("effective".equals(shortUrlStatus)){
                return true;
            }
        }else{
            throw new ServiceException("查询失败");
        }
        return false;
    }

    public void sendPrescriptionApproveSms(String phoneNum, String hospitalSign, JSONObject data){

        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phoneNum)
                .setSignName(hospitalSign)
                .setTemplateCode(smsProperties.getPrescriptionApproveCode())
                .setTemplateParam(data.toJSONString());

        try{
            sendSMS(sendSmsRequest) ;
        }catch (Exception ex) {
            log.error("prescription-approve发送手机短信失败 : {}", ex);
        }

    }

    public void sendFollowUpEventSms(String phoneNum, String hospitalSign, String type , String hospitalName, String wxName) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("hospital", "") ;
        jsonObject.put("name", "微信公众号" + wxName) ;

        SendSmsRequest sendSmsRequest = null ;
        if ( TemplateMsgConstants.FOLLOW_UP_TEXT_TYPE.equals(type) || TemplateMsgConstants.FOLLOW_UP_IMAGE_TYPE.equals(type) ) {
            sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phoneNum)
                    .setSignName(hospitalSign)
                    .setTemplateCode(smsProperties.getFollowUpEventMsgCode())
                    .setTemplateParam(jsonObject.toJSONString());
        }

        if ( TemplateMsgConstants.FOLLOW_UP_FORWARD_TUTORIAL_TYPE.equals(type) ) {
            sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phoneNum)
                    .setSignName(hospitalSign)
                    .setTemplateCode(smsProperties.getFollowUpEventTutorialCode())
                    .setTemplateParam(jsonObject.toJSONString());
        }


        if ( TemplateMsgConstants.FOLLOW_UP_FORWARD_QUESTIONNAIRE_TYPE.equals(type) ) {
            sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phoneNum)
                    .setSignName(hospitalSign)
                    .setTemplateCode(smsProperties.getQuestionCode())
                    .setTemplateParam(jsonObject.toJSONString());

        }

        if ( TemplateMsgConstants.FOLLOW_UP_SUMMARY_REMIND_TYPE.equals(type) ) {

            sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phoneNum)
                    .setSignName(hospitalSign)
                    .setTemplateCode(smsProperties.getFollowUpSummary())
                    .setTemplateParam(jsonObject.toJSONString());
        }

        try{
            sendSMS(sendSmsRequest) ;
        }catch (Exception ex) {
            log.error("follow-up-event发送手机短信失败 : {}", ex);
        }

    }

}

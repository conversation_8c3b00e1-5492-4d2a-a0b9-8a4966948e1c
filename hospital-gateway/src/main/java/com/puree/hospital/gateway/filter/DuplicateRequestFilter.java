package com.puree.hospital.gateway.filter;

import cn.hutool.core.text.CharSequenceUtil;
import com.puree.hospital.common.api.util.IpUtil;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.core.utils.SHA256Util;
import com.puree.hospital.common.core.utils.ServletUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.gateway.config.properties.DuplicateRequestProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName PreventDuplicateFilter
 * <AUTHOR>
 * @Description 防止重复
 * @Date 2024/11/19 12:20
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DuplicateRequestFilter implements GlobalFilter, Ordered {

    private final DuplicateRequestProperties properties;
    private final RedisTemplate<String,String> redisTemplate;

    /**
     * @Param exchange
     * @Param chain
     * @Return Mono<Void>
     * @Description 防重过滤器
     * <AUTHOR>
     * @Date 2024/11/19 19:28
     **/
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        ServerHttpRequest request = exchange.getRequest();

        //请求key
        String uniqueKey = assemblyData(request);
        //唯一ID
        String requestId = SHA256Util.toHexString(SHA256Util.getSHA(uniqueKey));
        String key = CacheConstants.GATEWAY_REQUESTID + requestId;


        Boolean checkRate = false;
        //提前校验黑名单
        if (StringUtils.matches(request.getURI().getPath(), properties.getBlacklist())) {
           checkRate = true;
        }

        //v1版本的兼容
        if (!checkRate && v1Compatible(request.getURI().getPath())) {
            return chain.filter(exchange);
        }

        //新版本 todo 修改为正则试 ^(([a-zA-Z]+):)?(.*)$
        if (!checkRate && StringUtils.matches(request.getURI().getPath(), properties.getWhitelist())){
            return chain.filter(exchange);
        }

        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(key, "1", properties.getRate(), TimeUnit.SECONDS))) {
            log.info("[重复请求] [hash]:{}, [key]:{}", key,uniqueKey);
            return uniqueRequestResponse(exchange, "重复请求");
        } else {
            return chain.filter(exchange);
        }
    }


    /**
     * @Param url
     * @Return boolean
     * @Description 老版本校验
     * <AUTHOR>
     * @Date 2024/11/20 15:14
     **/
    private boolean v1Compatible(String url) {
        List<String> whitelist = properties.getV1CompatibleWhitelist();
        //白名单
        for (String item : whitelist) {
            if (url.indexOf(item) >= 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * @Param exchange
     * @Param msg
     * @Return Mono<Void>
     * @Description 重复请求响应
     * <AUTHOR>
     * @Date 2024/11/19 19:04
     **/
    private Mono<Void> uniqueRequestResponse(ServerWebExchange exchange, String msg) {
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.UNIQUE_ID_NOT_PASSED);
    }

    /**
     * @Param request
     * @Return String 组装数据
     * @Description 组装数据
     * 组成为 ip + header + 请求路径 + 请求参数
     * <AUTHOR>
     * @Date 2024/11/19 18:44
     **/
    public String assemblyData(ServerHttpRequest request) {
        StringBuilder builder = new StringBuilder();
        String ip = IpUtil.getIpAddr(request);
        String header = buildHeader(request);
        String method = request.getMethodValue();
        String path = request.getURI().getPath();
        String query = request.getQueryParams().toString();
        builder.append(ip).append(header).append(method).append(path).append(query);
        return builder.toString();
    }

    /**
     * @Param request
     * @Return String
     * @Description 构建 请求头相关
     * <AUTHOR>
     * @Date 2024/11/19 18:43
     **/
    public String buildHeader(ServerHttpRequest request) {
        StringBuilder builder = new StringBuilder();
        List<String> headerName = properties.getHeaderName();
        for (String header : headerName) {
            String headerValue = request.getHeaders().getFirst(header);
            if (CharSequenceUtil.isNotEmpty(headerValue)){
                builder.append(headerValue);
            }
        }
        return builder.toString();
    }

    @Override
    public int getOrder() {
        return -999;
    }
}
